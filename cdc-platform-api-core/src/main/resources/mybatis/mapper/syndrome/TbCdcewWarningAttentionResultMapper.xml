<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mapper.syndrome.TbCdcewWarningAttentionResultMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.entity.TbCdcewWarningAttentionResult">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="detail_id" jdbcType="VARCHAR" property="detailId" />
    <result column="full_date" jdbcType="DATE" property="fullDate" />
    <result column="full_time" jdbcType="TIMESTAMP" property="fullTime" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="stat_dim_id" jdbcType="VARCHAR" property="statDimId" />
    <result column="stat_dim_name" jdbcType="VARCHAR" property="statDimName" />
    <result column="symptom_type" jdbcType="VARCHAR" property="symptomType" />
    <result column="medical_case_cn" jdbcType="INTEGER" property="medicalCaseCn" />
    <result column="warning_attention_value" jdbcType="DOUBLE" property="warningAttentionValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, event_id, detail_id, full_date, full_time, province_code, province_name, city_code,
    city_name, district_code, district_name, stat_dim_id, stat_dim_name, symptom_type,
    medical_case_cn, warning_attention_value, create_time, update_time
  </sql>

  <insert id="insert" parameterType="com.iflytek.fpva.cdc.entity.TbCdcewWarningAttentionResult">
    insert into tb_cdcew_warning_attention_result (id, event_id, detail_id, full_date, full_time,
      province_code, province_name, city_code, 
      city_name, district_code, district_name, 
      stat_dim_id, stat_dim_name, symptom_type, 
      medical_case_cn, warning_attention_value, create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{event_id,jdbcType=VARCHAR}, #{detail_id,jdbcType=VARCHAR},
            #{fullDate,jdbcType=DATE}, #{fullTime,jdbcType=TIMESTAMP},
      #{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, #{districtName,jdbcType=VARCHAR}, 
      #{statDimId,jdbcType=VARCHAR}, #{statDimName,jdbcType=VARCHAR}, #{symptomType,jdbcType=VARCHAR}, 
      #{medicalCaseCn,jdbcType=INTEGER}, #{warningAttentionValue,jdbcType=DOUBLE},
                  #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsert">
    insert into tb_cdcew_warning_attention_result (id, event_id, detail_id, full_date, full_time,
                                                   province_code, province_name, city_code,
                                                   city_name, district_code, district_name,
                                                   stat_dim_id, stat_dim_name, symptom_type,
                                                   medical_case_cn, warning_attention_value,
                                                   create_time, update_time)
    <foreach collection="list" item="item" separator=",">
      values (#{item.id,jdbcType=VARCHAR}, #{item.event_id,jdbcType=VARCHAR}, #{item.detail_id,jdbcType=VARCHAR},
              #{item.fullDate,jdbcType=DATE}, #{item.fullTime,jdbcType=TIMESTAMP},
              #{item.provinceCode,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, #{item.cityCode,jdbcType=VARCHAR},
              #{item.cityName,jdbcType=VARCHAR}, #{item.districtCode,jdbcType=VARCHAR}, #{item.districtName,jdbcType=VARCHAR},
              #{item.statDimId,jdbcType=VARCHAR}, #{item.statDimName,jdbcType=VARCHAR}, #{item.symptomType,jdbcType=VARCHAR},
              #{item.medicalCaseCn,jdbcType=INTEGER}, #{item.warningAttentionValue,jdbcType=DOUBLE},
              #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>

  </insert>
  <select id="getWarningAttentionResultsByDate" resultType="com.iflytek.fpva.cdc.entity.TbCdcewWarningAttentionResult">
    select
        <include refid="Base_Column_List" />
    from tb_cdcew_warning_attention_result
    where full_date = #{fullDate,jdbcType=DATE}
  </select>

    <select id="getWarningAttentionResultsByEventIds" resultType="com.iflytek.fpva.cdc.entity.TbCdcewWarningAttentionResult">
        select
        <include refid="Base_Column_List" />
        from tb_cdcew_warning_attention_result
        where event_id in
        <foreach item="item" collection="eventIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
</mapper>

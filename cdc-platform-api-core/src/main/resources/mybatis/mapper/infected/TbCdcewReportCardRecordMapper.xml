<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mapper.multichannel.TbCdcewReportCardRecordMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.entity.TbCdcewReportCardRecord">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="disease_report_id" jdbcType="VARCHAR" property="diseaseReportId"/>
        <result column="report_type_code" jdbcType="VARCHAR" property="reportTypeCode" />
        <result column="report_type_name" jdbcType="VARCHAR" property="reportTypeName"/>
        <result column="identity_type_code" jdbcType="VARCHAR" property="identityTypeCode" />
        <result column="identity_type_name" jdbcType="VARCHAR" property="identityTypeName" />
        <result column="identity_no" jdbcType="VARCHAR" property="identityNo"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName" />
        <result column="sex_code" jdbcType="VARCHAR" property="sexCode"/>
        <result column="sex_name" jdbcType="VARCHAR" property="sexName"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="marital_code" jdbcType="VARCHAR" property="maritalCode" />
        <result column="marital_name" jdbcType="VARCHAR" property="maritalName" />
        <result column="education_code" jdbcType="VARCHAR" property="educationCode"/>
        <result column="education_name" jdbcType="VARCHAR" property="educationName" />
        <result column="national_code" jdbcType="VARCHAR" property="nationalCode"/>
        <result column="national_name" jdbcType="VARCHAR" property="nationalName"/>
        <result column="exact_age" jdbcType="VARCHAR" property="exactAge"/>
        <result column="age_unit" jdbcType="VARCHAR" property="ageUnit"/>
        <result column="career_type_code" jdbcType="VARCHAR" property="careerTypeCode" />
        <result column="career_type_name" jdbcType="VARCHAR" property="careerTypeName"/>
        <result column="person_type_code" jdbcType="VARCHAR" property="personTypeCode" />
        <result column="person_type_name" jdbcType="VARCHAR" property="personTypeName"/>
        <result column="belong_code" jdbcType="VARCHAR" property="belongCode" />
        <result column="belong_name" jdbcType="VARCHAR" property="belongName"/>
        <result column="company" jdbcType="VARCHAR" property="company" />
        <result column="telephone" jdbcType="VARCHAR" property="telephone" />
        <result column="contact_telephone" jdbcType="VARCHAR" property="contactTelephone" />
        <result column="living_addr_province" jdbcType="VARCHAR" property="livingAddrProvince" />
        <result column="living_addr_city" jdbcType="VARCHAR" property="livingAddrCity" />
        <result column="living_addr_county" jdbcType="VARCHAR" property="livingAddrCounty"/>
        <result column="living_addr_town" jdbcType="VARCHAR" property="livingAddrTown" />
        <result column="living_addr_village" jdbcType="VARCHAR" property="livingAddrVillage"/>
        <result column="living_addr_door" jdbcType="VARCHAR" property="livingAddrDoor" />
        <result column="living_addr_detail" jdbcType="VARCHAR" property="livingAddrDetail" />
        <result column="household_addr_province" jdbcType="VARCHAR" property="householdAddrProvince" />
        <result column="household_addr_city" jdbcType="VARCHAR" property="householdAddrCity"/>
        <result column="household_addr_county" jdbcType="VARCHAR" property="householdAddrCounty"/>
        <result column="household_addr_town" jdbcType="VARCHAR" property="householdAddrTown"/>
        <result column="household_addr_village" jdbcType="VARCHAR" property="householdAddrVillage" />
        <result column="household_addr_door" jdbcType="VARCHAR" property="householdAddrDoor" />
        <result column="household_addr_detail" jdbcType="VARCHAR" property="householdAddrDetail" />
        <result column="infected_type_code" jdbcType="VARCHAR" property="infectedTypeCode" />
        <result column="infected_type_name" jdbcType="VARCHAR" property="infectedTypeName" />
        <result column="infected_code" jdbcType="VARCHAR" property="infectedCode" />
        <result column="infected_name" jdbcType="VARCHAR" property="infectedName" />
        <result column="other_infection_disease" jdbcType="VARCHAR" property="otherInfectionDisease" />
        <result column="case_code" jdbcType="VARCHAR" property="caseCode" />
        <result column="case_name" jdbcType="VARCHAR" property="caseName" />
        <result column="diagnosis_status_code" jdbcType="VARCHAR" property="diagnosisStatusCode" />
        <result column="diagnosis_status_name" jdbcType="VARCHAR" property="diagnosisStatusName" />
        <result column="first_onset_date" jdbcType="DATE" property="firstOnsetDate" />
        <result column="diagnose_datetime" jdbcType="TIMESTAMP" property="diagnoseDatetime" />
        <result column="aids_diagnose_date" jdbcType="DATE" property="aidsDiagnoseDate" />
        <result column="death_date" jdbcType="DATE" property="deathDate" />
        <result column="hbsag_positive_code" jdbcType="VARCHAR" property="hbsagPositiveCode" />
        <result column="hbsag_positive_name" jdbcType="VARCHAR" property="hbsagPositiveName" />
        <result column="first_hepatitis_b" jdbcType="DATE" property="firstHepatitisB" />
        <result column="alt_value" jdbcType="VARCHAR" property="altValue" />
        <result column="anti_hbc_code" jdbcType="VARCHAR" property="antiHbcCode" />
        <result column="anti_hbc_name" jdbcType="VARCHAR" property="antiHbcName" />
        <result column="liver_puncture_nedle" jdbcType="VARCHAR" property="liverPunctureNedle" />
        <result column="liver_puncture_nedle_des" jdbcType="VARCHAR" property="liverPunctureNedleDes" />
        <result column="recover_blood_serum" jdbcType="VARCHAR" property="recoverBloodSerum" />
        <result column="recover_blood_serum_des" jdbcType="VARCHAR" property="recoverBloodSerumDes" />
        <result column="contact_history_code" jdbcType="VARCHAR" property="contactHistoryCode" />
        <result column="contact_history_des" jdbcType="VARCHAR" property="contactHistoryDes" />
        <result column="std_history" jdbcType="VARCHAR" property="stdHistory" />
        <result column="std_history_des" jdbcType="VARCHAR" property="stdHistoryDes" />
        <result column="public_syringes_number" jdbcType="VARCHAR" property="publicSyringesNumber" />
        <result column="non_marital_sex_number" jdbcType="VARCHAR" property="nonMaritalSexNumber" />
        <result column="homosexual_sex_number" jdbcType="VARCHAR" property="homosexualSexNumber" />
        <result column="infection_pathway" jdbcType="VARCHAR" property="infectionPathway" />
        <result column="infection_pathway_des" jdbcType="VARCHAR" property="infectionPathwayDes" />
        <result column="specimen_sources" jdbcType="VARCHAR" property="specimenSources" />
        <result column="specimen_sources_des" jdbcType="VARCHAR" property="specimenSourcesDes" />
        <result column="inspection_result" jdbcType="VARCHAR" property="inspectionResult" />
        <result column="inspection_result_des" jdbcType="VARCHAR" property="inspectionResultDes" />
        <result column="inspection_date" jdbcType="DATE" property="inspectionDate" />
        <result column="inspection_org" jdbcType="VARCHAR" property="inspectionOrg" />
        <result column="hand_foot_mouth_result" jdbcType="VARCHAR" property="handFootMouthResult" />
        <result column="hand_foot_mouth_result_des" jdbcType="VARCHAR" property="handFootMouthResultDes" />
        <result column="critically_ill_patients" jdbcType="VARCHAR" property="criticallyIllPatients" />
        <result column="covid19_type" jdbcType="VARCHAR" property="covid19Type" />
        <result column="covid19_type_name" jdbcType="VARCHAR" property="covid19TypeName" />
        <result column="terminate_isolation_date" jdbcType="DATE" property="terminateIsolationDate" />
        <result column="contact_status" jdbcType="VARCHAR" property="contactStatus" />
        <result column="correction_disease_name" jdbcType="VARCHAR" property="correctionDiseaseName" />
        <result column="note" jdbcType="VARCHAR" property="note" />
        <result column="report_doctor_id" jdbcType="VARCHAR" property="reportDoctorId" />
        <result column="report_doctor_name" jdbcType="VARCHAR" property="reportDoctorName" />
        <result column="report_org_code" jdbcType="VARCHAR" property="reportOrgCode" />
        <result column="report_org_name" jdbcType="VARCHAR" property="reportOrgName" />
        <result column="fill_date" jdbcType="DATE" property="fillDate" />
        <result column="upload_doctor_id" jdbcType="VARCHAR" property="uploadDoctorId" />
        <result column="upload_doctor_name" jdbcType="VARCHAR" property="uploadDoctorName" />
        <result column="upload_date_time" jdbcType="TIMESTAMP" property="uploadDateTime" />
        <result column="check_doctor_id" jdbcType="VARCHAR" property="checkDoctorId" />
        <result column="check_doctor_name" jdbcType="VARCHAR" property="checkDoctorName" />
        <result column="check_date_time" jdbcType="TIMESTAMP" property="checkDateTime" />
        <result column="check_note" jdbcType="VARCHAR" property="checkNote" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
        <result column="patient_source" jdbcType="VARCHAR" property="patientSource" />
        <result column="other_person_type_name" jdbcType="VARCHAR" property="otherPersonTypeName" />
        <result column="living_addr_province_code" jdbcType="VARCHAR" property="livingAddrProvinceCode" />
        <result column="living_addr_city_code" jdbcType="VARCHAR" property="livingAddrCityCode" />
        <result column="living_addr_county_code" jdbcType="VARCHAR" property="livingAddrCountyCode" />
        <result column="other_contact_history_des" jdbcType="VARCHAR" property="otherContactHistoryDes" />
        <result column="imported_cases_code" jdbcType="VARCHAR" property="importedCasesCode" />
        <result column="imported_cases_name" jdbcType="VARCHAR" property="importedCasesName" />
        <result column="household_addr_province_code" jdbcType="VARCHAR" property="householdAddrProvinceCode" />
        <result column="household_addr_city_code" jdbcType="VARCHAR" property="householdAddrCityCode" />
        <result column="household_addr_county_code" jdbcType="VARCHAR" property="householdAddrCountyCode" />
        <result column="other_national_name" jdbcType="VARCHAR" property="otherNationalName" />
        <result column="other_infection_pathway_des" jdbcType="VARCHAR" property="otherInfectionPathwayDes" />
        <result column="other_specimen_sources_des" jdbcType="VARCHAR" property="otherSpecimenSourcesDes" />
        <result column="household_belong_code" jdbcType="VARCHAR" property="householdBelongCode" />
        <result column="household_belong_name" jdbcType="VARCHAR" property="householdBelongName" />
        <result column="event_id" jdbcType="VARCHAR" property="eventId" />
        <result column="patient_source_code" jdbcType="VARCHAR" property="patientSourceCode" />
        <result column="age_unit_name" jdbcType="VARCHAR" property="ageUnitName" />
        <result column="infected_sub_code" jdbcType="VARCHAR" property="infectedSubCode" />
        <result column="infected_sub_name" jdbcType="VARCHAR" property="infectedSubName" />
        <result column="first_hepatitis_b_others" jdbcType="VARCHAR" property="firstHepatitisBOthers" />
        <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
        <result column="origin_primary_key" jdbcType="VARCHAR" property="originPrimaryKey" />
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
        <result column="business_id" jdbcType="VARCHAR" property="businessId" />
        <result column="clinical_report_time" jdbcType="TIMESTAMP" property="clinicalReportTime" />
        <result column="timely_status" jdbcType="VARCHAR" property="timelyStatus" />
        <result column="disease_type_code" jdbcType="VARCHAR" property="diseaseTypeCode" />
        <result column="disease_type_name" jdbcType="VARCHAR" property="diseaseTypeName" />
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="live_or_travel_country_code" jdbcType="VARCHAR" property="liveOrTravelCountryCode" />
        <result column="live_or_travel_country_name" jdbcType="VARCHAR" property="liveOrTravelCountryName" />
        <result column="other_disease" jdbcType="VARCHAR" property="otherDisease" />
        <result column="living_addr_town_code" jdbcType="VARCHAR" property="livingAddrTownCode" />
        <result column="household_addr_town_code" jdbcType="VARCHAR" property="householdAddrTownCode" />
        <result column="surplus_report_second" jdbcType="VARCHAR" property="surplusReportSecond" />
        <result column="org_id" jdbcType="VARCHAR" property="orgId" />
        <result column="global_person_id" jdbcType="VARCHAR" property="globalPersonId" />
        <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
        <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
        <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
        <result column="full_date" jdbcType="DATE" property="fullDate" />
        <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
        <result column="timely_status_fbk" jdbcType="VARCHAR" property="timelyStatusFbk" />
        <result column="card_operate_source_code" jdbcType="VARCHAR" property="cardOperateSourceCode" />
        <result column="living_address_code" jdbcType="VARCHAR" property="livingAddressCode" />
        <result column="living_address_longitude" jdbcType="VARCHAR" property="livingAddressLongitude" />
        <result column="living_address_latitude" jdbcType="VARCHAR" property="livingAddressLatitude" />
        <result column="company_address_code" jdbcType="VARCHAR" property="companyAddressCode" />
        <result column="company_address_longitude" jdbcType="VARCHAR" property="companyAddressLongitude" />
        <result column="company_address_latitude" jdbcType="VARCHAR" property="companyAddressLatitude" />
        <result column="patient_living_region_id" jdbcType="VARCHAR" property="patientLivingRegionId" />
        <result column="patient_company_id" jdbcType="VARCHAR" property="patientCompanyId" />
        <result column="org_address" jdbcType="VARCHAR" property="orgAddress" />
    </resultMap>
    <sql id="Base_Column_List">
        cr.id, cr.org_code, cr.org_name, cr.disease_report_id, cr.report_type_code, cr.report_type_name, cr.identity_type_code,
        cr.identity_type_name, cr.identity_no, cr.patient_name, cr.parent_name, cr.sex_code, cr.sex_name, cr.birthday,
        cr.marital_code, cr.marital_name, cr.education_code, cr.education_name, cr.national_code, cr.national_name,
        cr.exact_age, cr.age_unit, cr.career_type_code, cr.career_type_name, cr.person_type_code, cr.person_type_name,
        cr.belong_code, cr.belong_name, cr.company, cr.telephone, cr.contact_telephone, cr.living_addr_province,
        cr.living_addr_city, cr.living_addr_county, cr.living_addr_town, cr.living_addr_village, cr.living_addr_door,
        cr.living_addr_detail, cr.household_addr_province, cr.household_addr_city, cr.household_addr_county,
        cr.household_addr_town, cr.household_addr_village, cr.household_addr_door, cr.household_addr_detail,
        cr.infected_type_code, cr.infected_type_name, cr.infected_code, cr.infected_name, cr.other_infection_disease,
        cr.case_code, cr.case_name, cr.diagnosis_status_code, cr.diagnosis_status_name, cr.first_onset_date,
        cr.diagnose_datetime, cr.aids_diagnose_date, cr.death_date, cr.hbsag_positive_code, cr.hbsag_positive_name,
        cr.first_hepatitis_b, cr.alt_value, cr.anti_hbc_code, cr.anti_hbc_name, cr.liver_puncture_nedle,
        cr.liver_puncture_nedle_des, cr.recover_blood_serum, cr.recover_blood_serum_des, cr.contact_history_code,
        cr.contact_history_des, cr.std_history, cr.std_history_des, cr.public_syringes_number, cr.non_marital_sex_number,
        cr.homosexual_sex_number, cr.infection_pathway, cr.infection_pathway_des, cr.specimen_sources,
        cr.specimen_sources_des, cr.inspection_result, cr.inspection_result_des, cr.inspection_date,
        cr.inspection_org, cr.hand_foot_mouth_result, cr.hand_foot_mouth_result_des, cr.critically_ill_patients,
        cr.covid19_type, cr.covid19_type_name, cr.terminate_isolation_date, cr.contact_status, cr.correction_disease_name,
        cr.note, cr.report_doctor_id, cr.report_doctor_name, cr.report_org_code, cr.report_org_name, cr.fill_date,
        cr.upload_doctor_id, cr.upload_doctor_name, cr.upload_date_time, cr.check_doctor_id, cr.check_doctor_name,
        cr.check_date_time, cr.check_note, cr."status", cr.creator, cr.create_time, cr.updater, cr.update_time,
        cr.delete_flag, cr.patient_source, cr.other_person_type_name, cr.living_addr_province_code, cr.living_addr_city_code,
        cr.living_addr_county_code, cr.other_contact_history_des, cr.imported_cases_code, cr.imported_cases_name,
        cr.household_addr_province_code, cr.household_addr_city_code, cr.household_addr_county_code,
        cr.other_national_name, cr.other_infection_pathway_des, cr.other_specimen_sources_des, cr.household_belong_code,
        cr.household_belong_name, cr.event_id, cr.patient_source_code, cr.age_unit_name, cr.infected_sub_code,
        cr.infected_sub_name, cr.first_hepatitis_b_others, cr.source_id, cr.origin_primary_key, cr.reject_reason,
        cr.patient_id, cr.business_id, cr.clinical_report_time, cr.timely_status, cr.disease_type_code,
        cr.disease_type_name, cr.dept_code, cr.dept_name, cr.live_or_travel_country_code, cr.live_or_travel_country_name,
        cr.other_disease, cr.living_addr_town_code, cr.household_addr_town_code, cr.surplus_report_second,
        cr.org_id, cr.global_person_id, cr.source_type, cr.etl_create_datetime, cr.etl_update_datetime,
        cr.full_date, cr.sync_time, cr.timely_status_fbk, cr.card_operate_source_code, cr.living_address_code,
        cr.living_address_longitude, cr.living_address_latitude, cr.company_address_code, cr.company_address_longitude,
        cr.company_address_latitude, cr.patient_living_region_id, cr.patient_company_id, i.org_address
    </sql>
    <sql id="Record_Column_List">
        id
        , org_code, org_name, disease_report_id, report_type_code, report_type_name, identity_type_code,
    identity_type_name, identity_no, patient_name, parent_name, sex_code, sex_name, birthday,
    marital_code, marital_name, education_code, education_name, national_code, national_name,
    exact_age, age_unit, career_type_code, career_type_name, person_type_code, person_type_name,
    belong_code, belong_name, company, telephone, contact_telephone, living_addr_province,
    living_addr_city, living_addr_county, living_addr_town, living_addr_village, living_addr_door,
    living_addr_detail, household_addr_province, household_addr_city, household_addr_county,
    household_addr_town, household_addr_village, household_addr_door, household_addr_detail,
    infected_type_code, infected_type_name, infected_code, infected_name, other_infection_disease,
    case_code, case_name, diagnosis_status_code, diagnosis_status_name, first_onset_date,
    diagnose_datetime, aids_diagnose_date, death_date, hbsag_positive_code, hbsag_positive_name,
    first_hepatitis_b, alt_value, anti_hbc_code, anti_hbc_name, liver_puncture_nedle,
    liver_puncture_nedle_des, recover_blood_serum, recover_blood_serum_des, contact_history_code,
    contact_history_des, std_history, std_history_des, public_syringes_number, non_marital_sex_number,
    homosexual_sex_number, infection_pathway, infection_pathway_des, specimen_sources,
    specimen_sources_des, inspection_result, inspection_result_des, inspection_date,
    inspection_org, hand_foot_mouth_result, hand_foot_mouth_result_des, critically_ill_patients,
    covid19_type, covid19_type_name, terminate_isolation_date, contact_status, correction_disease_name,
    note, report_doctor_id, report_doctor_name, report_org_code, report_org_name, fill_date,
    upload_doctor_id, upload_doctor_name, upload_date_time, check_doctor_id, check_doctor_name,
    check_date_time, check_note, "status", creator, create_time, updater, update_time,
    delete_flag, patient_source, other_person_type_name, living_addr_province_code, living_addr_city_code,
    living_addr_county_code, other_contact_history_des, imported_cases_code, imported_cases_name,
    household_addr_province_code, household_addr_city_code, household_addr_county_code,
    other_national_name, other_infection_pathway_des, other_specimen_sources_des, household_belong_code,
    household_belong_name, event_id, patient_source_code, age_unit_name, infected_sub_code,
    infected_sub_name, first_hepatitis_b_others, source_id, origin_primary_key, reject_reason,
    patient_id, business_id, clinical_report_time, timely_status, disease_type_code,
    disease_type_name, dept_code, dept_name, live_or_travel_country_code, live_or_travel_country_name,
    other_disease, living_addr_town_code, household_addr_town_code, surplus_report_second,
    org_id, global_person_id, source_type, etl_create_datetime, etl_update_datetime,
    full_date, sync_time, timely_status_fbk, card_operate_source_code, living_address_code,
    living_address_longitude, living_address_latitude, company_address_code, company_address_longitude,
    company_address_latitude, patient_living_region_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="com.iflytek.fpva.cdc.entity.TbCdcewReportCardRecord">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_report_card_record cr
        join tb_cdcew_organization_info i
        on cr.org_id = i.org_id
        where cr.id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByPrimaryKeyLists" parameterType="java.lang.String" resultType="com.iflytek.fpva.cdc.entity.TbCdcewReportCardRecord">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_report_card_record cr
        join tb_cdcew_organization_info i
        on cr.org_id = i.org_id
        where cr.id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        order by cr.create_time desc
    </select>

    <select id="findInfectedPatientByEventIdAndFullDate" resultType="com.iflytek.fpva.cdc.model.vo.InfectedPatientVO">
        SELECT
        r.patient_name AS patientName,
        r.original_event_id as originalEventId,
        COALESCE ( trc.PATIENT_ID, tchmi.PATIENT_ID ) AS patientId,
        r.global_person_id AS globalPersonId,
        r.org_id as orgId,
        COALESCE ( trc.SEX_NAME, tchmi.SEX_desc ) AS sexDesc,
        COALESCE ( trc.exact_age, tchmi.diagnose_age ) AS diagnoseAge,
        COALESCE ( trc.age_unit, tchmi.diagnose_age_unit ) AS diagnoseAgeUnit,
        COALESCE (tchmi.diagnostic_name1,r.INFECTED_NAME) AS symptomType,
        COALESCE ( trc.living_address_latitude, tchmi.std_living_addr_latitude::varchar ) AS latitude,
        COALESCE ( trc.living_address_longitude, tchmi.std_living_addr_longitude::varchar) AS longitude,
        COALESCE ( trc.living_addr_detail, tchmi.std_living_addr_detail_desc ) AS livingAddress,
        COALESCE ( trc.company, tchmi.company ) AS companyName,
        COALESCE (tchmi1.diagnostic_name1, r.INFECTED_NAME, tchmi.diagnostic_name1) as diagnoseName,
        COALESCE ( trc.person_type_name, tchmi.career_name ) AS personTypeName,
        COALESCE ( trc.career_Type_Name, tchmi.career_name ) AS careerTypeName,
        COALESCE (trc.company_address_latitude,tchmi.std_company_addr_latitude::varchar) AS companyLatitude,
        COALESCE (trc.company_address_longitude,tchmi.std_company_addr_longitude::varchar )AS companyLongitude,
        COALESCE ( trc.full_date, tchmi.FULL_DATE ) AS fullDate,
        COALESCE ( trc.diagnose_datetime, tchmi.outpatient_time ) AS outPatientTime,
        COALESCE ( trc.REPORT_DOCTOR_NAME, tchmi.doctor_name ) AS doctorName,
        COALESCE ( trc.REPORT_DOCTOR_ID, tchmi.doctor_id ) AS doctorId,
        COALESCE ( trc.TELEPHONE, tchmi.phone ) AS patientPhone,
        r.DETAIL_ID AS detailId,
        r.EVENT_ID AS eventId,
        COALESCE (trc.ID, tchmi.source_key ) AS sourceKey,
        r.province_code AS provinceCode,
        r.province_name AS provinceName,
        r.CITY_CODE AS cityCode,
        r.DISTRICT_CODE AS districtCode,
        r.CITY_NAME AS cityName,
        r.DISTRICT_NAME AS districtName,
        COALESCE (o2.org_name, tchmi1.org_name, o1.org_name) AS orgName,
        COALESCE (o2.org_name, tchmi1.org_name, o1.org_name) AS visitOrgName,
        COALESCE ( trc.org_name, tchmi.stat_dim_name ) AS statDimName,
        COALESCE ( trc.org_id, tchmi.stat_dim_id ) AS statDimId,
        trc.ID AS reportCardId,
        r.data_source_type_code AS dataSourceTypeCode,
        r.is_new_med_record AS isNewMedRecord,
        COALESCE (tchmi.main_suit) AS mainSuit,
        COALESCE (tchmi.illness_history) AS illnessHistory,
        COALESCE (tchmi.aux_exam) AS auxExam,
        COALESCE (tchmi.checkup_other) AS checkupOther,
        COALESCE (tchmi.previous_history) AS previousHistory,
        trc.first_onset_date AS onsetDate,
        COALESCE ( trc.status, '病历生成' ) AS status,
        COALESCE (o1.org_id,o2.org_id) AS reportOrgId,
        COALESCE (o1.source_type,o2.source_type) AS sourceType,
        COALESCE (trc.create_time,tchmi.create_time) AS createTime,
        COALESCE (trc.update_time,tchmi.create_time) AS updateTime,
        r.identity_no AS identityNo,
        trc.reject_reason
        FROM
        tb_cdcew_infected_med_relation r
        LEFT JOIN tb_cdcew_report_card_record trc ON trc.ID = r.source_key
        LEFT JOIN tb_cdcew_his_medical_info tchmi ON tchmi.source_key = r.source_key and trc.id is null
        left join tb_cdcew_his_medical_info tchmi1 ON tchmi1.source_key = trc.event_id
        LEFT JOIN tb_cdcew_organization_info o1 ON tchmi.org_id = o1.org_id
        LEFT JOIN tb_cdcew_organization_info o2 ON trc.org_id = o2.org_id
        where r.EVENT_ID = #{eventId, jdbcType=VARCHAR}
        <if test="beginDate != null">
            and COALESCE ( trc.full_date, tchmi.FULL_DATE ) <![CDATA[>=]]> #{beginDate}
        </if>
        <if test="endDate != null">
            and COALESCE ( trc.full_date, tchmi.FULL_DATE ) <![CDATA[<=]]> #{endDate}
        </if>
        <if test="companyName != null and companyName != ''">
            and (  trc.company  like concat('%',#{companyName,jdbcType=VARCHAR}::varchar,'%')
                 or tchmi.company  like concat('%',#{companyName,jdbcType=VARCHAR}::varchar,'%')
                )
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            and (tchmi.DIAGNOSTIC_NAME1  like concat('%',#{diagnoseName,jdbcType=VARCHAR}::varchar,'%')
            or r.INFECTED_NAME like concat('%',#{diagnoseName,jdbcType=VARCHAR}::varchar,'%'))
        </if>
    </select>

    <select id="countInfectedPatientByEventId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        tb_cdcew_infected_med_relation r
        where r.EVENT_ID = #{eventId, jdbcType=VARCHAR}
    </select>

    <select id="findMedicalInfoByEvents" resultType="com.iflytek.fpva.cdc.model.vo.MedicalInfoVO">
        select distinct trc.PATIENT_NAME as patientName,
        trc.PATIENT_ID as patientId,
        trc.SEX_NAME as sexDesc,
        trc.EXACT_AGE as diagnoseAge,
        o.province_code as provinceCode,
        o.province_name as provinceName,
        o.city_code as cityCode,
        o.city_name as cityName,
        o.district_code as districtCode,
        o.district_name as districtName,
        trc.living_address_latitude as latitude,
        trc.living_address_longitude as longitude,
        trc.living_addr_detail as livingAddress,
        trc.company as companyName,
        trc.company_address_latitude as companyLatitude,
        trc.company_address_longitude as companyLongitude,
        trc.REPORT_DOCTOR_NAME as doctorName,
        trc.REPORT_DOCTOR_ID as doctorId,
        trc.TELEPHONE as patientPhone,
        trc.ID as sourceKey,
        trc.full_date as fullDate,
        trc.event_id as medicalEventId
        from tb_cdcew_report_card_record trc
        left join tb_cdcew_infected_med_relation r on trc.id = r.source_key
        left join tb_cdcew_organization_info o on o.org_id = trc.org_id
        where r.EVENT_ID in
        <foreach item="item" index="index" collection="events"
                 open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </select>

    <select id="getReportCardList" resultType="com.iflytek.fpva.cdc.entity.TbCdcewReportCardRecord">
        select t.* from tb_cdcew_report_card_record t
        left join tb_cdcew_organization_info o on t.org_id = o.org_id
        where 1=1
        <if test="statuses != null and statuses.size() &gt; 0">
            and t.status in
            <foreach item="item" index="index" collection="statuses"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null">
            and t.full_date <![CDATA[>=]]> #{startDate,jdbcType=DATE}
        </if>
        <if test="endDate != null">
            and t.full_date <![CDATA[<=]]> #{endDate,jdbcType=DATE}
        </if>
        <if test="infectedCodes != null and infectedCodes.size() &gt; 0">
            and t.infected_sub_code in
            <foreach item="item" index="index" collection="infectedCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            and t.PATIENT_NAME like concat('%',#{patientName},'%')
        </if>
        <if test="orgList!=null and orgList.size() > 0">
            and t.org_id in
            <foreach item="item" index="index" collection="orgList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportOrgCodes!=null and reportOrgCodes.size() > 0">
            and t.org_id in
            <foreach item="item" index="index" collection="reportOrgCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchOrgId != null">
            and t.org_id = #{searchOrgId,jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null">
            and o.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null">
            and o.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null">
            and o.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
            and o.source_type in
            <foreach item="item" index="index" collection="sourceTypes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientSourceCode != null and patientSourceCode != '9'.toString()">
            and t.patient_source_code = #{patientSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="patientSourceCode == '9'.toString()">
            and (t.patient_source_code is null or t.patient_source_code not in ('2','3'))
        </if>
        <if test="queryType==2">
            and t.timely_status_fbk = '0'
        </if>
        <if test="queryType==3">
            and (t.timely_status_fbk = '1' or t.timely_status_fbk is null or t.timely_status_fbk = '99')
        </if>
        <if test="queryType==4">
            and t.patient_source_code = '2'
        </if>
        <if test="queryType==5">
            and t.patient_source_code = '2'
            and t.timely_status_fbk = '0'
        </if>
        <if test="queryType==6">
            and t.patient_source_code = '2'
            and (t.timely_status_fbk = '1' or t.timely_status_fbk is null or t.timely_status_fbk = '99')
        </if>
        <if test="queryType==7">
            and (t.patient_source_code not in ('2','3') or t.patient_source_code is null)
        </if>
        <if test="queryType==8">
            and (t.patient_source_code not in ('2','3') or t.patient_source_code is null)
            and t.timely_status_fbk = '0'
        </if>
        <if test="queryType==9">
            and (t.patient_source_code not in ('2','3') or t.patient_source_code is null)
            and (t.timely_status_fbk = '1' or t.timely_status_fbk is null or t.timely_status_fbk = '99')
        </if>
        <if test="queryType==10">
            and t.patient_source_code = '3'
        </if>
        <if test="queryType==11">
            and t.patient_source_code = '3'
            and t.timely_status_fbk = '0'
        </if>
        <if test="queryType==12">
            and t.patient_source_code = '3'
            and (t.timely_status_fbk = '1' or t.timely_status_fbk is null or t.timely_status_fbk = '99')
        </if>
        <if test="statType ==  '1'.toString()">
            and t.status in('2','3','4','5','6','7','8')
        </if>
        <if test="statType ==  '2'.toString()">
            and t.status in('2','3','4','5','6')
        </if>
        <if test="statType ==  '3'.toString()">
            and t.status = '2'
        </if>
        <if test="statType ==  '4'.toString()">
            and t.timely_status_fbk = '1'
            and t.status = '2'
        </if>
        <if test="statType ==  '5'.toString()">
            and t.timely_status_fbk = '0'
            and t.status in ('3','4','5','6')
        </if>
        order by t.create_time desc
    </select>
    <select id="getOrgReportCardStatusCountList"
            resultType="com.iflytek.fpva.cdc.model.dto.OrgReportCardStatusCountDto">
        select t.org_id, t.status, count(1) count
        from tb_cdcew_report_card_record t
        where 1=1
        <if test="startDate != null">
            and t.full_date <![CDATA[>=]]> #{startDate,jdbcType=DATE}
        </if>
        <if test="endDate != null">
            and t.full_date <![CDATA[<=]]> #{endDate,jdbcType=DATE}
        </if>
        <if test="infectedCodes != null and infectedCodes.size() &gt; 0">
            and t.infected_sub_code in
            <foreach item="item" index="index" collection="infectedCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orgList!=null and orgList.size() > 0">
            and t.org_id in
            <foreach item="item" index="index" collection="orgList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientSourceCode != null and patientSourceCode != '9'.toString()">
            and t.patient_source_code = #{patientSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="patientSourceCode == '9'.toString()">
            and (t.patient_source_code is null or t.patient_source_code not in ('2','3'))
        </if>
        GROUP BY t.org_id, t.status
    </select>

    <select id="getReportCardCountList"
            resultType="com.iflytek.fpva.cdc.model.dto.infected.ReportCardCountDto">
        select t.timely_status_fbk timelyStatusFbk,
        t.patient_source_code patientSourceCode,
        count(1) count
        from tb_cdcew_report_card_record t
        left join tb_cdcew_organization_info o on t.org_id = o.org_id
        where 1=1
        <if test="statuses != null and statuses.size() &gt; 0">
            and t.status in
            <foreach item="item" index="index" collection="statuses"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null">
            and t.full_date <![CDATA[>=]]> #{startDate,jdbcType=DATE}
        </if>
        <if test="endDate != null">
            and t.full_date <![CDATA[<=]]> #{endDate,jdbcType=DATE}
        </if>
        <if test="infectedCodes != null and infectedCodes.size() &gt; 0">
            and t.infected_sub_code in
            <foreach item="item" index="index" collection="infectedCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientName != null">
            and t.PATIENT_NAME like concat('%',#{patientName},'%')
        </if>
        <if test="orgList!=null and orgList.size() > 0">
            and t.org_id in
            <foreach item="item" index="index" collection="orgList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportOrgCodes!=null and reportOrgCodes.size() > 0">
            and t.org_id in
            <foreach item="item" index="index" collection="reportOrgCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="searchOrgId != null">
            and t.org_id = #{searchOrgId,jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null">
            and o.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null">
            and o.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null">
            and o.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
            and o.source_type in
            <foreach item="item" index="index" collection="sourceTypes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientSourceCode != null and patientSourceCode != '9'.toString()">
            and t.patient_source_code = #{patientSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="patientSourceCode == '9'.toString()">
            and (t.patient_source_code is null or t.patient_source_code not in ('2','3'))
        </if>
        and o.delete_flag = '0'
        group by t.timely_status_fbk,t.patient_source_code
    </select>
    <select id="getReportCardListByEventId" resultType="com.iflytek.fpva.cdc.entity.TbCdcewReportCardRecord">
        select *
        from tb_cdcew_report_card_record
        where id in (
            select source_key from tb_cdcew_infected_med_relation where event_id = #{eventId}
        )
        ORDER BY full_date
    </select>
    <select id="findMedicalInfoBySourceKey" resultType="com.iflytek.fpva.cdc.model.vo.MedicalInfoVO">
        select distinct trc.PATIENT_NAME              as patientName,
                        trc.PATIENT_ID                as patientId,
                        trc.SEX_NAME                  as sexDesc,
                        trc.EXACT_AGE                 as diagnoseAge,
                        o.province_code               as provinceCode,
                        o.province_name               as provinceName,
                        o.city_code                   as cityCode,
                        o.city_name                   as cityName,
                        o.district_code               as districtCode,
                        o.district_name               as districtName,
                        trc.living_address_latitude   as latitude,
                        trc.living_address_longitude  as longitude,
                        trc.living_addr_detail        as livingAddress,
                        trc.company                   as companyName,
                        trc.company_address_latitude  as companyLatitude,
                        trc.company_address_longitude as companyLongitude,
                        trc.REPORT_DOCTOR_NAME        as doctorName,
                        trc.REPORT_DOCTOR_ID          as doctorId,
                        trc.TELEPHONE                 as patientPhone,
                        trc.ID                        as sourceKey,
                        trc.full_date                 as fullDate,
                        trc.event_id                  as medicalEventId
        from tb_cdcew_report_card_record trc
                 left join tb_cdcew_organization_info o on o.org_id = trc.org_id
        where trc.id = #{sourceKey}
    </select>
    <select id="getByEventIds" resultType="com.iflytek.fpva.cdc.model.dto.external.TbCdcewReportCardRecordDto">
        select
        r.*,
        mr.event_id as infectedEventId
        from tb_cdcew_report_card_record r
        left join tb_cdceW_infected_med_relation mr on r.id = mr.source_key
        where mr.event_id in
        <foreach item="item" index="index" collection="eventIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findEpiMedicalInfoByEventId"
            resultType="com.iflytek.fpva.cdc.model.epi.TbCdcHisMedicalInfo">
        select med.id                   as sourceKey,
               med.create_time          as medCreateTime,
               med.create_time,
               med.full_date,
               med.update_time          as medUpdateTime,
               o.province_code,
               o.province_name,
               o.city_code,
               o.city_name,
               o.district_code,
               o.district_name,
               o.org_id,
               o.org_name,
               med.report_doctor_id     as doctorId,
               med.report_doctor_name   as doctorName,
               med.patient_id,
               med.patient_name,
               med.sex_name             as sexDesc,
               med.telephone            as phone,
               med.identity_no          as idcardNo,
               med.living_addr_detail   as residentialAddress,
               med.diagnose_datetime    as diagnosisDateTime,
               med.exact_age            as diagnoseAge,
               med.birthday,
               ts.address_area_name     as company,
               med.infected_code        as diseaseCode,
               med.infected_name        as diseaseName,
               o.stat_org_id            as statDimId,
               o.stat_org_name          as statDimName,
               med.age_unit             as ageCode,
               med.age_unit_name        as ageName,
               med.sex_code             as sexCode,
               ti.address_town_code     as streetCode,
               ti.address_town_name     as streetName,
               ti.address_longitude     as longitude,
               ti.address_latitude      as latitude,
               ti.address_area_code     as areaCode,
               ti.address_area_name     as areaName,
               ts.address_longitude     as addressLongitude,
               ts.address_latitude      as addressLatitude,
               ts.address_province_code as addressProvinceCode,
               ts.address_province_name as addressProvinceName,
               ts.address_city_code     as addressCityCode,
               ts.address_city_name     as addressCityName,
               ts.address_district_code as addressDistrictCode,
               ts.address_district_name as addressDistrictName,
               ts.address_town_code     as addressTownCode,
               ts.address_town_name     as addressTownName,
               ts.address_detail_desc   as addressDetailDesc,
               ts.address_area_code     as addressAreaCode,
               ts.address_area_name     as addressAreaName,
               med.identity_type_code   as idTypeCode,
               med.identity_type_name   as idTypeName,
               o.org_longitude          as orgLongitude,
               o.org_latitude           as orgLatitude
        from tb_cdcew_report_card_record med
                 left join tb_cdcew_organization_info o on med.org_id = o.org_id
                 left join tb_cdcew_address_standard ti on med.living_address_code = ti.address_area_code
                 left join tb_cdcew_address_standard ts on med.company_address_code = ts.address_area_code
        where med.id in
              (select source_key from tb_cdcew_infected_med_relation where event_id = #{eventId,jdbcType=VARCHAR})
    </select>
    <select id="findEpiHisMedicalInfoByEventId" resultType="com.iflytek.fpva.cdc.model.epi.TbCdcHisMedicalInfo">
        select med.source_key,
               med.med_create_time,
               med.create_time,
               med.full_date,
               med.med_update_time,
               med.med_type,
               med.main_suit,
               med.pulse,
               med.illness_history,
               med.previous_history,
               med.family_history,
               med.irritability_history,
               med.aux_exam,
               med.diastolic_pressure,
               med.mean_pressure,
               med.systolic_pressure,
               med.temperature,
               med.respiratory_rate,
               med.blood_oxygen,
               med.heart_rate,
               med.fasting_glucose,
               med.postprandial_glucose,
               med.weight,
               med.height,
               med.checkup_other,
               med.assistant_diagnose_result,
               med.province_code,
               med.province_name,
               med.city_code,
               med.city_name,
               med.district_code,
               med.district_name,
               med.org_id,
               med.org_name,
               med.doctor_id,
               med.doctor_name,
               med.doctor_phone,
               med.patient_id,
               med.patient_name,
               med.sex_desc,
               med.phone,
               med.idcard_no,
               med.residential_address,
               med.outpatient_time,
               med.outpatient_time      as diagnosisDateTime,
               med.diagnose_age,
               med.birthday,
               med.company_name         as company,
               med.diagnostic_code1,
               med.diagnostic_name1,
               med.diagnostic_code2,
               med.diagnostic_name2,
               med.diagnostic_code3,
               med.diagnostic_name3,
               med.symptomcontent,
               med.stat_dim_id,
               med.stat_dim_name,
               med.data_source_type_code,
               med.diagnose_age_unit    as ageName,
               med.sex_code             as sexCode,
               med.std_living_addr_town_code as streetCode,
               med.std_living_addr_town_name as streetName,
               med.std_living_addr_longitude as longitude,
               med.std_living_addr_latitude as latitude,
               med.std_living_address_code as areaCode,
               med.std_living_address as areaName,
               med.std_company_addr_longitude as addressLongitude,
               med.std_company_addr_latitude as addressLatitude,
               med.std_company_addr_province_code as addressProvinceCode,
               med.std_company_addr_province_name as addressProvinceName,
               med.std_company_addr_city_code as addressCityCode,
               med.std_company_addr_city_name as addressCityName,
               med.std_company_addr_district_code as addressDistrictCode,
               med.std_company_addr_district_name as addreddDistrictName,
               med.std_company_addr_town_code as addressTownCode,
               med.std_company_addr_town_name as addressTownName,
               med.std_company_addr_detail_desc as addressDetailDesc,
               med.std_company_address_code as addressAreaCode,
               med.std_company_address as addressAreaName,
               med.identity_type_code as idTypeCode,
               med.identity_type_name as idTypeName,
               o.org_longitude as orgLongitude,
               o.org_latitude as orgLatitude
        from tb_cdcew_his_medical_info med
                 left join tb_cdcew_organization_info o on med.org_id = o.org_id
                 left join tb_cdcew_infected_med_relation r on med.source_key = r.source_key
        where r.event_id = #{warnEventId,jdbcType=VARCHAR}

    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonEventAnalysisMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.entity.TbCdcewUnknownReasonEventAnalysis">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="conclusions" jdbcType="SMALLINT" property="conclusions" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="education_involved" jdbcType="SMALLINT" property="educationInvolved" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="investigation_time" jdbcType="TIMESTAMP" property="investigationTime" />
    <result column="investigation_method" jdbcType="SMALLINT" property="investigationMethod" />
    <result column="epi_history" jdbcType="SMALLINT" property="epiHistory" />
    <result column="pathogen_detection" jdbcType="SMALLINT" property="pathogenDetection" />
    <result column="total_case_num" jdbcType="INTEGER" property="totalCaseNum" />
    <result column="dead_case_num" jdbcType="INTEGER" property="deadCaseNum" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="filling_date" jdbcType="DATE" property="fillingDate" />
    <result column="disease_name" jdbcType="VARCHAR" property="diseaseName" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="other_disease_name" jdbcType="VARCHAR" property="otherDiseaseName" />
    <result column="positive_event_type" jdbcType="SMALLINT" property="positiveEventType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, event_id, conclusions, comments, education_involved, create_time, update_time, 
    "status", org_name, org_id, investigation_time, investigation_method, epi_history, 
    pathogen_detection, total_case_num, dead_case_num, creator_name, creator_id, filling_date, 
    disease_name, disease_code, other_disease_name, positive_event_type
  </sql>
  <insert id="insert">
      insert into "tb_cdcew_unknown_reason_event_analysis" (id, event_id, conclusions, comments,
                                                            education_involved, create_time, update_time,
                                                            "status", org_name, org_id,
                                                            investigation_time, investigation_method,
                                                            epi_history, pathogen_detection, total_case_num,
                                                            dead_case_num, creator_name, creator_id,
                                                            filling_date, disease_name, disease_code,
                                                            other_disease_name, positive_event_type)
      values (#{id,jdbcType=VARCHAR}, #{eventId,jdbcType=VARCHAR}, #{conclusions,jdbcType=SMALLINT},
              #{comments,jdbcType=VARCHAR},
              #{educationInvolved,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP},
              #{updateTime,jdbcType=TIMESTAMP},
              #{status,jdbcType=SMALLINT}, #{orgName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR},
              #{investigationTime,jdbcType=TIMESTAMP}, #{investigationMethod,jdbcType=SMALLINT},
              #{epiHistory,jdbcType=SMALLINT}, #{pathogenDetection,jdbcType=SMALLINT}, #{totalCaseNum,jdbcType=INTEGER},
              #{deadCaseNum,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{creatorId,jdbcType=VARCHAR},
              #{fillingDate,jdbcType=DATE}, #{diseaseName,jdbcType=VARCHAR}, #{diseaseCode,jdbcType=VARCHAR},
              #{otherDiseaseName,jdbcType=VARCHAR}, #{positiveEventType,jdbcType=SMALLINT})
  </insert>
    <select id="selectByEventId" resultType="com.iflytek.fpva.cdc.entity.TbCdcewUnknownReasonEventAnalysis">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_unknown_reason_event_analysis
        where event_id = #{eventId,jdbcType=VARCHAR}
        order by create_time desc
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonWarningDetailMapper">


    <update id="updateProcessingStatusByEventId">
        update tb_cdcew_unknown_reason_warning_detail
        set PROCESSING_STATUS=#{updatedProcessingStatus,jdbcType=INTEGER}
        where event_id=#{eventId,jdbcType=VARCHAR}
    </update>
</mapper>
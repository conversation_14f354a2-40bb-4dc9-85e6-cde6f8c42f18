<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympSourceMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.entity.TbCdcewSympSource">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="full_date" jdbcType="DATE" property="fullDate" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="stat_dim_id" jdbcType="VARCHAR" property="statDimId" />
    <result column="stat_dim_name" jdbcType="VARCHAR" property="statDimName" />
    <result column="stat_unit_type" jdbcType="VARCHAR" property="statUnitType" />
    <result column="symptom_code" jdbcType="VARCHAR" property="symptomCode" />
    <result column="symptom_name" jdbcType="VARCHAR" property="symptomName" />
    <result column="record_count" jdbcType="INTEGER" property="recordCount" />
    <result column="person_count" jdbcType="INTEGER" property="personCount" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, full_date, province_code, province_name, city_code, city_name, district_code, 
    district_name, stat_dim_id, stat_dim_name, stat_unit_type, symptom_code,
    symptom_name, record_count, person_count, etl_update_datetime
  </sql>
  <select id="findBySymptomCodeAndStatDimIdAndFullDate"
          resultType="com.iflytek.fpva.cdc.entity.TbCdcewSympSource">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcew_symp_source
    where symptom_code = #{symptomCode,jdbcType=VARCHAR}
    and stat_dim_id = #{statDimId,jdbcType=VARCHAR}
    and full_date = #{fullDate} limit 1
  </select>

  <select id="getOrgMedCountByStatDim" resultType="com.iflytek.fpva.cdc.model.resultmap.OrgMedCount">
    select sum(person_count) as count,
    stat_dim_name as hospitalName,
    stat_dim_id as hospitalSourceKey,
    FULL_DATE as date
    from tb_cdcew_symp_source d
    where
    SYMPTOM_CODE = #{symptomType}
    and FULL_DATE between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    and stat_dim_id in
    <foreach collection="statDimIds" close=")" index="index" item="item" open="(" separator=",">
      #{item,jdbcType=VARCHAR}
    </foreach>
    group by stat_dim_name, stat_dim_id, FULL_DATE
  </select>

  <select id="getMedCountByStatDim" resultType="com.iflytek.fpva.cdc.model.vo.TimeTrendVO">
    SELECT sc.day_short_desc         as date,
           COALESCE(person_count, 0) as medicalNum
    FROM sys_calendar sc
           LEFT JOIN tb_cdcew_symp_source tcsss
                     ON tcsss.full_date = sc.day_short_desc and tcsss.symptom_code = #{symptomType}
                       and tcsss.stat_Dim_Id = #{statDimId}
    where sc.day_short_desc between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}

  </select>
  <select id="getOrgMedCountByStreet" resultType="com.iflytek.fpva.cdc.model.resultmap.OrgMedCount">
      select SUM(person_count) as count,
        STAT_DIM_NAME as hospitalName,
        STAT_DIM_ID as hospitalSourceKey,
        FULL_DATE as date
      from tb_cdcew_symp_source d
      where DISTRICT_CODE = #{districtCode}
        and SYMPTOM_CODE = #{symptomCode}
        and FULL_DATE = #{date}
        and exists (select 1 from tb_cdcew_region t
          where t.region_id = d.stat_dim_id)
      group by STAT_DIM_NAME, STAT_DIM_ID, FULL_DATE
  </select>

  <select id="getMedCountByOrgList" resultType="com.iflytek.fpva.cdc.model.vo.TimeTrendVO">
    SELECT sc.day_short_desc         as date,
           sum( COALESCE(person_count, 0)) as medicalNum
    FROM sys_calendar sc
    LEFT JOIN tb_cdcew_symp_source tcsss
    ON tcsss.full_date = sc.day_short_desc and tcsss.symptom_code = #{symptomType}
    and tcsss.stat_Dim_Id in
    <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
      #{item}
    </foreach>
    where sc.day_short_desc between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    group by sc.day_short_desc
  </select>
</mapper>
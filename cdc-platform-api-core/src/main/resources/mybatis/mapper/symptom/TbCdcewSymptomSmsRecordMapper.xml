<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSymptomSmsRecordMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.entity.TbCdcewSymptomSmsRecord">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="rule_id" jdbcType="VARCHAR" property="ruleId"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="business_person_id" jdbcType="VARCHAR" property="businessPersonId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , event_id, rule_id, phone, batch_id, create_time, business_person_id

    </sql>
    <insert id="batchInsert" parameterType="com.iflytek.fpva.cdc.entity.TbCdcewSymptomSmsRecord">
        insert into "tb_cdcew_symptom_sms_record" (id, event_id, rule_id,
        phone, batch_id, create_time,business_person_id
        )
        values
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id,jdbcType=VARCHAR}, #{element.eventId,jdbcType=VARCHAR}, #{element.ruleId,jdbcType=VARCHAR},
            #{element.phone,jdbcType=VARCHAR}, #{element.batchId,jdbcType=VARCHAR},
            #{element.createTime,jdbcType=TIMESTAMP},#{element.businessPersonId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="getEventIdInRecord" resultType="java.lang.String">
        select event_id from tb_cdcew_symptom_sms_record
        where event_id in
        <foreach close=")" collection="eventIds" index="index" item="element" open="(" separator=",">
            (#{element})
        </foreach>
        and phone = #{phone}
    </select>
    <select id="getEventIdInPersonRecord" resultType="java.lang.String">
        select event_id from tb_cdcew_symptom_sms_record
        where event_id in
        <foreach close=")" collection="eventIds" index="index" item="element" open="(" separator=",">
            (#{element})
        </foreach>
        and business_person_id = #{personId}
    </select>
</mapper>
package com.iflytek.fpva.cdc.model.dto;


import lombok.Data;

/**
 * 门诊日志导出excel
 *
 * <AUTHOR>
 * @date 2022-07-12 21:07
 */
@Data
public class OutpatientReportLogExcel {
//    @ExcelColumn(name = "门诊号", column = 1)
    private String outpatientId;
//    @ExcelColumn(name = "就诊日期", column = 2)
    private String visitDate;
//    @ExcelColumn(name = "姓名", column = 3)
    private String patientName;
//    @ExcelColumn(name = "性别", column = 4)
    private String sexName;
//    @ExcelColumn(name = "年龄", column = 5)
    private String age;
//    @ExcelColumn(name = "职业", column = 6)
    private String occupation;
//    @ExcelColumn(name = "机构类型", column = 7)
    private String sourceTypeName;
//    @ExcelColumn(name = "上报机构", column = 8)
    private String orgName;
//    @ExcelColumn(name = "科室名称", column = 9)
    private String deptName;
//    @ExcelColumn(name = "医生名称", column = 10)
    private String doctorName;
//    @ExcelColumn(name = "证件号", column = 11)
    private String patientIdNumber;
//    @ExcelColumn(name = "诊断名称", column = 12)
    private String diagnoseName;
//    @ExcelColumn(name = "发病日期", column = 13)
    private String startSickDate;
//    @ExcelColumn(name = "初诊/复诊", column = 14)
    private String firstDiagFlag;
//    @ExcelColumn(name = "现住址", column = 15)
    private String livingAddress;
}

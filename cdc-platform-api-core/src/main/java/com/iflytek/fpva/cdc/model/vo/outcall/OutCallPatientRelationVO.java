package com.iflytek.fpva.cdc.model.vo.outcall;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
@Data
public class OutCallPatientRelationVO {

    @ApiModelProperty("拨出人员")
    private String creatorName;
    @ApiModelProperty("拨出机构名称")
    private String creatorOrgName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("外呼时间")
    private Date createTime;


    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("关联人员")
    private String personName;

    @ApiModelProperty("与患者关系")
    private String relationType;
    @ApiModelProperty("是否有相似症状：1-有；2-没有")
    private Integer isSick;

    @ApiModelProperty("联系电话")
    private String personPhone;
    @ApiModelProperty("接通状态 1-已接通; 2-未接通; 3-失败")
    private Integer connectStatus;

    @ApiModelProperty("外呼结果")
    private String callResult;

    @ApiModelProperty("音频URL")
    private String audioUrl;

}

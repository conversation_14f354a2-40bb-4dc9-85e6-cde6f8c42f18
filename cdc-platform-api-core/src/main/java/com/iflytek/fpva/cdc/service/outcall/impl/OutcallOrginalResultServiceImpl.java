package com.iflytek.fpva.cdc.service.outcall.impl;
import java.time.LocalDateTime;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallOrginalResult;
import com.iflytek.fpva.cdc.mapper.outcall.TbCdcewOutcallOrginalResultMapper;
import com.iflytek.fpva.cdc.service.outcall.OutcallOrginalResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 外呼原始结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
@Service
public class OutcallOrginalResultServiceImpl extends ServiceImpl<TbCdcewOutcallOrginalResultMapper, TbCdcewOutcallOrginalResult> implements OutcallOrginalResultService {
    @Resource
    private BatchUidService batchUidService;
    private static final String TB_CDCEW_OUTCALL_ORGINAL_RESULT = "tb_cdcew_outcall_orginal_result";

    @Override
    public void saveData(String callBatchId, String jsonString, String type) {
        TbCdcewOutcallOrginalResult add = new TbCdcewOutcallOrginalResult();
        add.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_OUTCALL_ORGINAL_RESULT)) );
        add.setCallResult(jsonString);
        add.setCallBatchId(callBatchId);
        add.setType(type);
        add.setCreateTime(LocalDateTime.now());
        baseMapper.insert(add);
    }
}

package com.iflytek.fpva.cdc.mapper.symptom;

import com.iflytek.fpva.cdc.entity.TbCdcewSympEventPersonRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewSympEventPersonRelationMapper {

    void batchInsert(List<TbCdcewSympEventPersonRelation> personRelationList);

    /**
     * 通过外呼状态查询外呼对象信息
     *
     * @param outCallStatus 外呼状态
     * @return detail集合
     */
    List<TbCdcewSympEventPersonRelation> findByOutCallStatusAndOutCallBatchIdIsNotNull(@Param("outCallStatus") Integer outCallStatus);

    void updateByPrimaryKeySelective(TbCdcewSympEventPersonRelation tbCdcEventPersonRelation);

    List<TbCdcewSympEventPersonRelation> listByEventId(String eventId);
}
package com.iflytek.fpva.cdc.service.outcall;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallPatientRelation;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.fpva.cdc.model.vo.MedOutCallCountVO;
import com.iflytek.fpva.cdc.model.vo.outcall.OutCallPatientRelationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 事件其他相关人群-周边居民 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface OutcallPatientRelationService extends IService<TbCdcewOutcallPatientRelation> {

    List<TbCdcewOutcallPatientRelation> listByEventIdAndConfigType(String eventId, String warningType);

    List<TbCdcewOutcallPatientRelation> findByOutCallStatusAndOutCallBatchIdIsNotNull(Integer code, String configType);
    MedOutCallCountVO getMedOutCallCount(String eventId, String warningType);

    List<OutCallPatientRelationVO> findOutCallResult(String eventId, String warningType);
}

package com.iflytek.fpva.cdc.model.vo.serviceTicket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date :2024/3/12 10:13
 * @description:ServiceTicketReqVO
 */
@ApiModel( description="工单审核请求参数")
@Data
public class ServiceTicketAuditReqVO {
    @NotNull
    @ApiModelProperty(value = "0-已撤回; 1-待反馈; 2-待议; 3-予以采纳; 4-已拒绝")
    private Integer status;
    @ApiModelProperty(value = "备注")
    private String comment;
    @NotNull
    @ApiModelProperty(value = "主键")
    private String id;


}

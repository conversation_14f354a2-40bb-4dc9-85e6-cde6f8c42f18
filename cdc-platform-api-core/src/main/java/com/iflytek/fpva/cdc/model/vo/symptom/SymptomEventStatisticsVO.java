package com.iflytek.fpva.cdc.model.vo.symptom;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SymptomEventStatisticsVO {

    @ApiModelProperty("统计基本信息")
    private StatisticsBasicVO statVO;

    @ApiModelProperty("响应排名列表")
    private List<StatisticsRankingVO> rankingList;

    // 基本统计VO
    @Data
    public static class StatisticsBasicVO {

        @ApiModelProperty("信号数")
        private Integer eventCount;

        @ApiModelProperty("信号数同比")
        private Double eventCountTrend;

        @ApiModelProperty("信号数环比")
        private Double qoqEventCount;

        @ApiModelProperty("响应及时信号数")
        private Integer responseTimelyCount;

        @ApiModelProperty("响应及时信号数同比")
        private Double responseTimelyCountTrend;

        @ApiModelProperty("响应及时信号数环比")
        private Double qoqResponseTimelyCount;

        @ApiModelProperty("处置及时信号数")
        private Integer processTimelyCount;

        @ApiModelProperty("处置及时信号数同比")
        private Double processTimelyCountTrend;

        @ApiModelProperty("处置及时信号数环比")
        private Double qoqProcessTimelyCount;

        @ApiModelProperty("响应及时率")
        private Double responseTimelyRate;

        @ApiModelProperty("响应及时率同比")
        private Double responseTimelyRateTrend;

        @ApiModelProperty("响应及时率环比")
        private Double qoqResponseTimelyRate;

        @ApiModelProperty("处置及时率")
        private Double processTimelyRate;

        @ApiModelProperty("处置及时率同比")
        private Double processTimelyRateTrend;

        @ApiModelProperty("处置及时率环比")
        private Double qoqProcessTimelyRate;

        @ApiModelProperty("处置完成信号数")
        private Integer processCompletedCount;

        @ApiModelProperty("处置完成信号数同比")
        private Double yoyProcessCompletedCount;

        @ApiModelProperty("处置完成信号数环比")
        private Double qoqProcessCompletedCount;

        @ApiModelProperty("处置完成信号率")
        private Double processCompletedRate;

        @ApiModelProperty("处置完成信号率同比")
        private Double yoyProcessCompletedRate;

        @ApiModelProperty("处置完成率环比")
        private Double qoqProcessCompletedRate;
    }

    // 统计排名VO
    @Data
    public static class StatisticsRankingVO {

        @ApiModelProperty("区县名称")
        @Sensitive(type = SensitiveTypeEnum.REGION)
        private String districtName;

        @ApiModelProperty("信号数")
        private Integer count;

        @ApiModelProperty("响应及时信号数")
        private Integer responseTimelyCount;

        @ApiModelProperty("响应及时率")
        private Double responseTimelyRate;

        @ApiModelProperty("处置及时信号数")
        private Integer processTimelyCount;

        @ApiModelProperty("处置及时率")
        private Double processTimelyRate;

        @ApiModelProperty("处置完成信号数")
        private Integer processCompletedCount;

        @ApiModelProperty("处置完成信号率")
        private Double processCompletedRate;
    }
}

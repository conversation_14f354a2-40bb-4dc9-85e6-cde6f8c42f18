package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.entity.HisMedicalInfo;
import com.iflytek.fpva.cdc.model.vo.PatientExtendVO;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMedicalInfoDto;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedRecordVO;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.poison.*;
import com.iflytek.fpva.cdc.model.vo.outpatient.OutpatientRecordVO;
import com.iflytek.fpva.cdc.model.vo.unknown.UnknownReasonRecordVO;

import java.util.Date;
import java.util.List;

public interface HisMedicalInfoService {

    /**
     * 根据组合主键查询病历详情
     *
     * @param sourceKey 组合主键
     * @param eventId   事件id
     * @return 病历详情
     */
    CdcHisMedicalInfoDetailVo getOne(String sourceKey, String eventId);

    /**
     * 根据组合主键查询病历详情
     *
     * @param sourceKey 组合主键
     * @param eventId   事件id
     * @return 病历详情
     */
    CdcHisMedicalInfoDetailVo getCustomizedOne(String sourceKey, String eventId);

    CdcHisMedicalInfoDetailVo getPeriodOne(String sourceKey, String eventId);

    /**
     * 根据事件id查询病历（去重）
     *
     * @param eventId 事件id
     * @param total   是否取总计数据。false时只取预警信号第一次发生日期的数据
     * @return 病历列表
     */
    List<HisMedicalInfoVo> findByEvent(String eventId, boolean total);

    /**
     * 根据事件ID + 事件开始/结束时间查询病例列表
     *
     * @param eventId   事件ID
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    List<MedicalInfoVO> findMedByEventTimeRange(String eventId, String beginDate, String endDate);

    /**
     * 根据病历集合查询人群分布
     *
     * @param list 病历集合
     * @return 人群分布
     */
    PopulationDistribution findByEvent(List<HisMedicalInfo> list);

    /**
     * 与事件同一区县下不同卫生院的病历分布
     *
     * @param eventId 事件id
     * @param total   是否取总计数据。false时只取预警信号第一次发生日期的数据
     * @return 分布
     */
    OrgMedicalVO orgMedDistribution(String eventId, boolean total, String endTime);

    /**
     * 事件下病历按照住址分布
     *
     * @param eventId 事件id
     * @return 分布
     */
    List<MedicalAddressDistributionVO> getMedicalAddressDistributionByEvent(String eventId);

    List<PoisonMedicalInfoDto> getPoisonRecordByEventId(String eventId, String patientName, Integer sortType, String doctorName, String companyName);

    List<OutpatientRecordVO> getOutpatientRecordByEventId(String eventId, Date beginDate, Date endDate);

    HeatstrokePoisonPatientExtendVO getHeatstrokeMedicalDetail(List<String> sourceKeyLists, String loginUserId);

    FoodPoisonPatientExtendVO getFoodMedicalDetail(List<String> sourceKeyLists, String loginUserId);

    List<UnknownReasonRecordVO> getUnknownReasonRecordsByEventId(String eventId, String patientName, Integer sortType, String doctorName, String companyName);

    List<CustomizedRecordVO> getCustomizedInfoByEventId(String eventId, String patientName, Integer sortType, String doctorName, String companyName);

    PatientExtendVO getMedicalDetail(List<String> sourceKeyLists, String loginUserId);

    Object getCustomizedPoisonMedicalDetail(List<String> sourceKeyLists, String eventId, String loginUserId);


    /**
     * 一氧化碳中毒病例报卡详情
     * @param sourceKeyLists
     * @param loginUserId
     * @return
     */
    CarbonMonoxidePoisonPatientExtendVO getCarbonMonoxideMedicalDetail(List<String> sourceKeyLists, String loginUserId);

    /**
     * 农药中毒病例报卡详情
     * @param sourceKeyLists
     * @param loginUserId
     * @return
     */
    PesticidePoisonPatientExtendVO getPesticideMedicalDetail(List<String> sourceKeyLists, String loginUserId);


     List<PoisonMedicalInfoDto> getDetailsByEventId(String eventId, String patientName, String doctorName, String companyName, Integer sortType);

     List<CustomizedRecordVO> getCustomziedDetailsByEventId(String eventId, String patientName, String doctorName, String companyName, Integer sortType);

     List<PoisonMedicalInfoDto> getPoisonHisByEventId(String eventId, String patientName, String doctorName, String companyName);
}

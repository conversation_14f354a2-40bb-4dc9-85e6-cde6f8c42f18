package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.entity.preventionControl.TbCdcewPreventionControlSmsRule;
import com.iflytek.fpva.cdc.model.dto.*;
import com.iflytek.fpva.cdc.model.preventionControl.dto.PreventionControlSmsRuleParam;
import com.iflytek.fpva.cdc.model.dto.*;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface SmsRuleService {
    void addSymptomSmsRule(List<TbCdcewSymptomSmsRule> ruleList, String loginUserId);

    void updateSymptomSmsRule(List<TbCdcewSymptomSmsRule> ruleList, String loginUserId);

    void upsertSymptomSmsRule(SymptomSmsRuleParam rule, String loginUserId);

    void deleteSymptomSmsRule(String id, String loginUserId);

    List<TbCdcewSymptomSmsRule> getSymptomSmsRuleList(String businessPersonId);

    PageData<SymptomSmsRuleList> getSymptomList(Integer pageIndex, Integer pageSize, String symptomCode, String ruleStatus);

    ResponseEntity<byte[]> exportSymptomSmsRuleList(List<String> symptomCodeList, String symptomCode, String ruleStatus);


    void addInfectedSmsRule(List<TbCdcewInfectedSmsRule> ruleList, String loginUserId);

    void updateInfectedSmsRule(List<TbCdcewInfectedSmsRule> ruleList, String loginUserId);

    void upsertInfectedSmsRule(InfectedSmsRuleParam rule, String loginUserId);

    void deleteInfectedSmsRule(String id, String loginUserId);

    List<TbCdcewInfectedSmsRule> getInfectedSmsRuleList(String businessPersonId);

    ResponseEntity<byte[]> exportInfectedSmsRuleList(List<String> infectedCodeList, String infectedCode, String ruleStatus);

    PageData<InfectedSmsRuleList> getInfectedList(Integer pageIndex, Integer pageSize, String infectedCode, String ruleStatus);

    void batchInsertSmsRule(TbCdcewInfectedSmsRule tbCdcewInfectedSmsRule);


    void addScSymptomSmsRule(List<TbCdcewSympSmsRule> ruleList, String loginUserId);

    void updateScSymptomSmsRule(List<TbCdcewSympSmsRule> ruleList, String loginUserId);

    void upsertScSymptomSmsRule(ScSymptomSmsRuleParam rule, String loginUserId);

    List<TbCdcewSympSmsRule> getScSymptomSmsRuleList(String businessPersonId);

    /**
     * 中毒预警短信规则
     */
    void addPoisonSmsRule(List<TbCdcewPoisonSmsRule> ruleList, String loginUserId);

    void updatePoisonSmsRule(List<TbCdcewPoisonSmsRule> ruleList, String loginUserId);

    void upsertPoisonSmsRule(PoisonSmsRuleParam rule, String loginUserId);

    void deletePoisonSmsRule(String id, String loginUserId);

    List<TbCdcewPoisonSmsRule> getPoisonSmsRuleList(String businessPersonId);

    List<CascadeVO> getPoisonList(String loginUserId);


    void upsertOutpatientSmsRule(OutpatientSmsRuleParam rule, String loginUserId);

    void deleteOutpatientSmsRule(String id, String loginUserId);

    List<TbCdcewOutpatientSmsRule> getOutpatientSmsRuleList(String businessPersonId);


    void addCustomizedSmsRule(List<TbCdcewCustomizedSmsRule> ruleList, String loginUserId);

    void updateCustomizedSmsRule(List<TbCdcewCustomizedSmsRule> ruleList, String loginUserId);

    void upsertCustomizedSmsRule(CustomizedSmsRuleParam rule, String loginUserId);

    void deleteCustomizedSmsRule(String id, String loginUserId);

    List<TbCdcewCustomizedSmsRule> getCustomizedSmsRuleList(String businessPersonId);

    List<CascadeVO> getCustomizedList(String loginUserId);

    void deleteSmsRuleByBusinessPersonId(String loginUserId, String businessId);

    void upsertUnknownReasonSmsRule(UnknownReasonSmsRuleParam rule, String loginUserId);

    void deleteUnknownReasonSmsRule(String id, String loginUserId);

    List<TbCdcewUnknownReasonSmsRule> getUnknownReasonSmsRuleList(String businessPersonId);


    void upsertPreventionControlSmsRule(PreventionControlSmsRuleParam rule, String loginUserId);

    List<TbCdcewPreventionControlSmsRule> getPreventionControlSmsRuleList(String businessPersonId);

    void deletePreventionControlSmsRule(String id, String loginUserId);
}

package com.iflytek.fpva.cdc.model.vo.compare;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.constant.enums.ClinicTypeEnum;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewReportCardUnmatchResult;
import com.iflytek.fpva.cdc.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Data
public class ReportCardCompareResultVO {

    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    @ApiModelProperty("姓名")
    private String patientName;

    @ApiModelProperty("性别")
    private String sexDesc;

    @ApiModelProperty("年龄")
    private String age;

    @ApiModelProperty("诊断名称")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String diagnoseNameList;

    private String doctorId;

    @ApiModelProperty("接诊医生")
    @Sensitive(type = SensitiveTypeEnum.DOCTOR)
    private String doctorName;

    @ApiModelProperty("就诊时间")
    private Date visitDatetime;

    private String dataSrc;

    @ApiModelProperty("就诊类型")
    private String dataSrcName;

    @ApiModelProperty("区域")
    @Sensitive(type = SensitiveTypeEnum.ORG_ABBR)
    private String areaName;

    private String orgId;

    @ApiModelProperty("机构")
    @Sensitive(type = SensitiveTypeEnum.ORG)
    private String orgName;

    public static ReportCardCompareResultVO fromEntity(TbCdcewReportCardUnmatchResult unmatchResult) {
        ReportCardCompareResultVO resultVO = new ReportCardCompareResultVO();
        BeanUtils.copyProperties(unmatchResult, resultVO);

        resultVO.setDataSrcName(ClinicTypeEnum.getDescByCode(unmatchResult.getDataSrc()));

        String provinceName = StringUtils.isNotEmpty(unmatchResult.getProvinceName()) ? unmatchResult.getProvinceName() : "";
        String cityName = StringUtils.isNotEmpty(unmatchResult.getCityName()) ? unmatchResult.getCityName() : "";
        String districtName = StringUtils.isNotEmpty(unmatchResult.getDistrictName()) ? unmatchResult.getDistrictName() : "";
        String areaName = provinceName.concat(cityName).concat(districtName);
        resultVO.setAreaName(areaName);

        String age = unmatchResult.getAge();
        if (StringUtils.isNotEmpty(age)) {
            String ageUnitName = unmatchResult.getAgeUnitName();
            if (StringUtils.isEmpty(ageUnitName)) {
                ageUnitName = "岁";
            }
            resultVO.setAge(age.concat(ageUnitName));
        }

        // 住院病例诊断取 in_diagnose
        if (ClinicTypeEnum.IN_HOSPITAL.getCode().equals(unmatchResult.getDataSrc())) {
            resultVO.setDiagnoseNameList(unmatchResult.getInDiagnose());
        }

        return resultVO;
    }

}

package com.iflytek.fpva.cdc.service.common;



import com.iflytek.fpva.cdc.model.rce.EventRule;

import java.util.List;
/**
 * <AUTHOR>
 * @date :2023/8/29 15:36
 * @description:TbCdcewRceMsgRecordService
 */
public interface TbCdcewRceMsgRecordService {
    /**
     *
     * @param webUrl Web端跳转地址
     * @param htmlTxt html文本
     * @param targetIds 用户列表
     * @param configType 信号类型syndrome,infected,poison,symptom,outpatient,unknownReason,preventionControl,customized
     * @param eventId 信号id
     * @param ruleId 规则id
     * @param type
     */
    void sendUrlMsg(String webUrl, String htmlTxt, List<String> targetIds,String configType,String eventId,String ruleId,Integer type,boolean rcsSwitch);

    /**
     *
     * @param webUrl Web端跳转地址
     * @param htmlTxt html文本
     * @param targetId 用户列表
     * @param configType 信号类型syndrome,infected,poison,symptom,outpatient,unknownReason,preventionControl,customized
     * @param eventRules 信号id 规则id
     * @param type
     */
    void sendUrlMsg(String webUrl, String htmlTxt,String targetId, String configType, List<EventRule> eventRules, Integer type ,boolean rcsSwitch);
}

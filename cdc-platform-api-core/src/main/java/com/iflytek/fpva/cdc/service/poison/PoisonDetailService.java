package com.iflytek.fpva.cdc.service.poison;

import com.iflytek.fpva.cdc.model.dto.poison.PoisonMedicalInfoDto;
import com.iflytek.fpva.cdc.model.vo.EventListVO;
import com.iflytek.fpva.cdc.model.vo.EventVO;
import com.iflytek.fpva.cdc.model.vo.OrgMedicalVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.entity.TbCdcewPoisonEventAnalysis;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.poison.PoisonEventExVO;
import com.iflytek.fpva.cdc.model.vo.poison.PoisonRecordVO;
import com.iflytek.fpva.cdc.model.vo.time.PoisonTimeVO;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
public interface PoisonDetailService {

    EventVO buildEventVo(String eventId);

    EventVO eventDetail(String eventId, String loginUserId);

    PoisonEventExVO humanDistribution(String eventId, String beginDate, String endDate, String loginUserId);

    PageData<PoisonMedicalInfoDto> getPoisonRecordList(String eventId, String loginUserId, String patientName, Integer pageIndex, Integer pageSize, Integer sortType, String doctorName, String companyName);

    EventListVO mapRelation(String eventId, String loginUserId);

    List<Date> getEventTimeLine(String eventId);

    List<Map<String, Object>> getEventMedCountByDate(String eventId, String startTime, String endTime);

    OrgMedicalVO regionMedDistribution(String eventId, boolean total);

    TbCdcewPoisonEventAnalysis createEventAnalysisResult(EventAnalysisResultVO eventAnalysisResultVO, String loginUserId, String loginUserName);

    List<PoisonTimeVO> queryTimeTrend(String eventId, Integer timeType);

    byte[] exportPoisonRecordList(String eventId, String loginUserId, Integer sortType, String doctorName, String companyName, String formType);

    ResponseEntity<byte[]> exportPoisonRecordList(String eventId, String loginUserId, Integer sortType);
}

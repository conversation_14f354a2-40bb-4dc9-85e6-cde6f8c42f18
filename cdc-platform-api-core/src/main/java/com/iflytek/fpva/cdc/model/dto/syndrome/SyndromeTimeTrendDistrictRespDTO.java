package com.iflytek.fpva.cdc.model.dto.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
/**
 * <AUTHOR>
 * @date :2023/11/8 10:35
 * @description:SyndromeTimeTrendDistrictRespDTO
 */
@Data
public class SyndromeTimeTrendDistrictRespDTO {
    @ApiModelProperty("区县名称")
    private String districtCode;

    @ApiModelProperty("区县名称编码")
    private String districtName;

    @ApiModelProperty("市名称")
    private String cityCode;

    @ApiModelProperty("市编码")
    private String cityName;

    @ApiModelProperty("日期")
    private String fullDate;

    @ApiModelProperty("月-日")
    private String shortFullDate;

    @ApiModelProperty("本期")
    private Long medCnt;

    @ApiModelProperty("去年病例数")
    private Long lastYearMedCnt;

    @ApiModelProperty("去年同期")
    private String ratePeriod;

    @ApiModelProperty("n天平均值")
    private String avgMedCnt;

    @ApiModelProperty("ruleId")
    private String ruleId;

    @ApiModelProperty("是否异常 Y:是;N:否")
    private String isAbnormal;

    @ApiModelProperty("参考阈值")
    private String referenceValue;


    @ApiModelProperty("配置规则")
    private TbCdcmrDiseaseMonitorRule rule;

    private List<SyndromeTimeTrendDistrictRespDTO> dataList;
}

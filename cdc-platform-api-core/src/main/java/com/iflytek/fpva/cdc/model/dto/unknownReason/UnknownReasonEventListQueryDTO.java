package com.iflytek.fpva.cdc.model.dto.unknownReason;

import com.iflytek.fpva.cdc.entity.RegionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class UnknownReasonEventListQueryDTO {

    private int pageIndex;

    @Range(max = 40, min = 20)
    private int pageSize;

    @ApiModelProperty("疾病编码")
    private Collection<String> symptom;

    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date minFullDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date maxFullDate;

    @ApiModelProperty("具体的处理状态 0-未处理 1-处理中 2-人工排除 3-阳性事件 不传-全部")
    private Collection<Integer> statusCollection;

    @ApiModelProperty("机构id")
    private String orgId;

    @ApiModelProperty("1-事件发生时间由近到远 2-事件发生时间由远到近 3-关注度大到小 4-关注度小到大" +
            "6-更新时间由近到远 7-更新时间由远到近 8-病历数量由多到少 9-病历数量由少到多")
    @Range(min = 1, max = 9)
    private int sortType;

    @ApiModelProperty("响应超时状态 0.未超时 1.响应超时")
    private Integer responseTimeOutStatus;

    @ApiModelProperty("处置超时状态 0.未超时 1.响应超时")
    private Integer processingTimeOutStatus;

    @ApiModelProperty("调查结论：0: 疑似  1：排除 2：继续关注 3：确认事件")
    private String conclusions;

    @ApiModelProperty("机构类型：110.等级医院; 120.基层医疗;")
    private String sourceType;

    @ApiModelProperty("信号编号")
    private String eventNum;

    @ApiModelProperty("时间筛选类型：1-发生时间；2-更新时间")
    private Integer dateType;

    @ApiModelProperty("DJYY-等级医院；JCYL-基层医疗；XXDW-学校单位；JDQY-街道区域")
    private String eventType;

    @ApiModelProperty("区域列表")
    List<RegionInfo> regionInfoList;
}

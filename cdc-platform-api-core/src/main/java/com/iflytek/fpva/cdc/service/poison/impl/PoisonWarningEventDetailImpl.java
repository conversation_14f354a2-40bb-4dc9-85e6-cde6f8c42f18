package com.iflytek.fpva.cdc.service.poison.impl;

import com.google.common.collect.Lists;
import com.iflytek.fpva.cdc.constant.TimeType;
import com.iflytek.fpva.cdc.constant.enums.DataSourceTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.EventTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewPoisonWarningEvent;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonWarningEventDetailMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonWarningEventMapper;
import com.iflytek.fpva.cdc.model.StatDimKey;
import com.iflytek.fpva.cdc.model.dto.EventDetailQueryDTO;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.OrgMedicalVO;
import com.iflytek.fpva.cdc.model.vo.TimeTrendVO;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.WarningEventDetailService;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.EventDetailUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class PoisonWarningEventDetailImpl implements WarningEventDetailService {

    @Resource
    private TbCdcewPoisonWarningEventMapper tbCdcewPoisonWarningEventMapper;
    @Resource
    private TbCdcewPoisonWarningEventDetailMapper tbCdcewPoisonWarningEventDetailMapper;

    @Resource
    private OrgService orgService;

    @Override
    public String getWarningEventType() {
        return WarningTypeCodeEnum.POISON.getName();
    }

    @Override
    public List<TimeTrendVO> timeTrend(EventDetailQueryDTO queryDto) throws ParseException {
        TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(queryDto.getEventId());

        String poisonName = event.getPoisonName();
        DateFormat dateFormat = DateFormat.getDateTimeInstance();

        Date endDate = event.getEndDate() == null ? new Date() : event.getEndDate();
        Date beginDate = DateUtils.addDays(endDate, -30);
        if (TimeType.YEAR == queryDto.getTimeType()) {
            endDate = DateUtils.addYears(endDate, -1);
            beginDate = DateUtils.addYears(beginDate, -1);
        }

        return TimeTrendVO.build30Day(endDate, poisonName, tbCdcewPoisonWarningEventDetailMapper.getPoisonTimeTrendByOrg(event.getStatDimId(), poisonName,
                dateFormat.parse(dateFormat.format(beginDate)),
                dateFormat.parse(dateFormat.format(endDate))));


    }

    @Override
    public OrgMedicalVO orgDistribution(EventDetailQueryDTO queryDto) {
        TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(queryDto.getEventId());
        Pair<String, String> datePair = EventDetailUtils.getOrgDistributionDateStr(event.getBeginDate(), event.getEndDate(), queryDto.isNeedTotal());
        String yesterday = datePair.getLeft();
        String today = datePair.getRight();
        String sourceType = event.getSourceType();
        String districtCode = event.getDistrictCode();
        List<OrgMedCount> yesterdayList;
        List<OrgMedCount> todayList;
        if (com.iflytek.fpva.cdc.util.StringUtils.isNotEmpty(sourceType)) {
            List<String> sourceTypeList = Lists.newArrayList(sourceType);
            yesterdayList = tbCdcewPoisonWarningEventDetailMapper.orgMedDistribution(sourceTypeList, districtCode, DateFormatUtils.formatDate(yesterday), event.getPoisonCode());
            todayList = tbCdcewPoisonWarningEventDetailMapper.orgMedDistribution(sourceTypeList, districtCode, DateFormatUtils.formatDate(today), event.getPoisonCode());
        } else {
            if (EventTypeEnum.XXDW.getName().equals(event.getEventType())) {
                yesterdayList = tbCdcewPoisonWarningEventDetailMapper.companyMedDistribution(districtCode, DateFormatUtils.formatDate(yesterday), event.getPoisonCode());
                todayList = tbCdcewPoisonWarningEventDetailMapper.companyMedDistribution(districtCode, DateFormatUtils.formatDate(today), event.getPoisonCode());
            } else {
                yesterdayList = tbCdcewPoisonWarningEventDetailMapper.regionMedDistribution(districtCode, DateFormatUtils.formatDate(yesterday), event.getPoisonCode());
                todayList = tbCdcewPoisonWarningEventDetailMapper.regionMedDistribution(districtCode, DateFormatUtils.formatDate(today), event.getPoisonCode());
            }
        }

        // 当poison_source表没有那一天的数据时，将该区县下的机构填充加入，病历数置为0
        List<StatDimKey> statDimKeyList = orgService.getStatDimKeysBy(districtCode, event.getEventType());
        return OrgMedicalVO.of(yesterday, today, statDimKeyList, yesterdayList, todayList);

    }

    @Override
    public int updateIsEsById(Integer isEs, String id) {
        return tbCdcewPoisonWarningEventMapper.updateIsEsById(isEs, id);
    }

    @Override
    public String sourceName(String type) {
        return DataSourceTypeEnum.getDescByValue(type);
    }

}

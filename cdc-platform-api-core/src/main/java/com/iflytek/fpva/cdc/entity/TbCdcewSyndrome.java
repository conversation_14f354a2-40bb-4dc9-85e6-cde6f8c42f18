package com.iflytek.fpva.cdc.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * tb_cdcew_syndrome
 * <AUTHOR>
@Data
public class TbCdcewSyndrome implements Serializable {
    /**
     * 业务主键
     */
    private String id;

    /**
     * 症候群编码
     */
    private String syndromeCode;

    /**
     * 症候群名称
     */
    private String syndromeName;

    /**
     * 症状组合编码（判别条件）
     */
    private String symptomType;

    /**
     * 症状组合名称（判别条件）
     */
    private String symptomName;

    /**
     * 条件1
     */
    private String condition1;

    /**
     * 条件2
     */
    private String condition2;

    /**
     * 条件3
     */
    private String condition3;

    /**
     * 条件1正则规则
     */
    private String condition1Regexp;

    /**
     * 条件2正则规则
     */
    private String condition2Regexp;

    /**
     * 条件3正则规则
     */
    private String condition3Regexp;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
    /**
     * 潜伏期
     */
    private Integer incubation;
    /**
     * 病历窗口期
     */
    private Integer hisWindow;
    /**
     * 预测时间
     */
    private Integer forecastWindow;
}
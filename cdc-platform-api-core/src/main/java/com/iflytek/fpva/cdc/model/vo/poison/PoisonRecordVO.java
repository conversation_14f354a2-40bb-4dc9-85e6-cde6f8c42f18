package com.iflytek.fpva.cdc.model.vo.poison;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.math3.util.Pair;

import java.util.Date;
import java.util.List;

@Data
public class PoisonRecordVO {

    private String sourceKey;
    private List<String> sourceKeyList;

    private String patientId;

    private String patientName;

    private String identityNo;

    private String sexDesc;

    private String diagnoseAge;

    private String diagnoseAgeUnit;

    private String diagnoseName;

    private String personTypeName;

    private String doctorId;

    private String doctorName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date fullDate;

    @ApiModelProperty("就诊时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date outPatientTime;

    private String companyName;

    private String companyCode;

    private String statDimId;

    private String statDimName;

    private String orgName;

    private Double longitude;

    private Double latitude;

    private String livingAddress;

    private Integer isNewMedRecord;

    private String sourceType;

    /**
     * 获取经纬度
     *
     * @return
     */
    @JsonIgnore
    public Pair<Double, Double> getLongitudeAndLatitude() {
        return new Pair<>(longitude, latitude);
    }

    private String companyAddressLongitude;

    private String companyAddressLatitude;
}

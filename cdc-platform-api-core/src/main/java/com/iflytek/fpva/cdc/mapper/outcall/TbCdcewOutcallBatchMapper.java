package com.iflytek.fpva.cdc.mapper.outcall;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallBatch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewOutcallBatchMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcewOutcallBatch record);

    int insertSelective(TbCdcewOutcallBatch record);

    TbCdcewOutcallBatch selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcewOutcallBatch record);

    int updateByPrimaryKey(TbCdcewOutcallBatch record);

    List<TbCdcewOutcallBatch> listByEventId(String eventId);

    List<TbCdcewOutcallBatch> listByEventIdAndConfigType(@Param("eventId") String eventId, @Param("warningType")String warningType);
}
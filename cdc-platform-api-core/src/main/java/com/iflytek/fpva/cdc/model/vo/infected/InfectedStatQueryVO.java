package com.iflytek.fpva.cdc.model.vo.infected;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class InfectedStatQueryVO {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    private Date minFullDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    private Date maxFullDate;

    @NotNull
    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("区县编码")
    private String districtCode;

    @ApiModelProperty("症状")
    private Collection<String> symptom;

    @ApiModelProperty("1-事件发生事件由近到远 2-事件发生时间由远到近 3-关注度大到小 4-关注度小到大")
    @Range(min = 1, max = 4)
    private int sortType;

    private int pageIndex;

    private int pageSize;

    @ApiParam("调查结论：1.疑似散发、2.疑似聚集、3.疑似爆发、4.疑似流行、5.疑似大流行、6.排除、7.持续关注")
    private List<Integer> conclusions;
}

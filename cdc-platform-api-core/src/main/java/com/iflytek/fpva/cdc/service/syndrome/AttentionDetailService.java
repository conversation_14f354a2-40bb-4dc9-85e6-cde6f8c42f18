package com.iflytek.fpva.cdc.service.syndrome;

import com.iflytek.fpva.cdc.entity.AttentionCondition;
import com.iflytek.fpva.cdc.model.vo.MedicalInfoVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.StudentVO;
import com.iflytek.fpva.cdc.model.vo.distribution.*;
import com.iflytek.fpva.cdc.model.vo.patient.DocMedDistribution;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AttentionDetailService {

    /**
     * 根据病历列表获取医生病历分布
     *
     * @param list 病历列表
     * @return 分布
     */
    List<DocMedDistribution> docMedDistribution(List<MedicalInfoVO> list);

    /**
     * 地区 病历分布
     *
     * @param ac   关注事件
     * @param list 病历列表
     * @return 分布
     */
    List<OrgMedicalDistributionVO> orgMedDistribution(AttentionCondition ac, List<MedicalInfoVO> list);


    /**
     * 日期 病历分布
     *
     * @param list 病历列表
     * @return 分布
     */
    List<DateMedDistribution> dateMedDistribution(List<MedicalInfoVO> list);


    /**
     * 性别 病历分布
     *
     * @param list 病历列表
     * @return 分布
     */
    List<GenderMedDistribution> genderMedDistribution(List<MedicalInfoVO> list);


    /**
     * 年龄病历分布
     *
     * @param list 病历列表
     * @return 分布
     */
    List<AgeMedDistribution> ageMedDistribution(List<MedicalInfoVO> list);


    /**
     * 分页查询关注事件下的病历列表
     *
     * @param loginUserId 登录用户id
     * @param id          关注事件id
     * @param pageIndex   页码
     * @param pageSize    每页条数
     * @return 病历列表
     */
    PageData<MedicalInfoVO> medicals(String loginUserId, String id, int pageIndex, int pageSize);

    /**
     * 分页查询关注事件下的病历列表
     *
     * @param loginUserId 登录用户id
     * @param id          关注事件id
     * @param pageIndex   页码
     * @param pageSize    每页条数
     * @return 病历列表
     */
    PageData<MedicalInfoVO> infectedMedicals(String loginUserId, String id, int pageIndex, int pageSize);

    /**
     * 分页查询关注事件下的学生列表
     *
     * @param loginUserId 登录用户id
     * @param id          关注事件id
     * @param pageIndex   页码
     * @param pageSize    每页条数
     * @return 学生列表
     */
    PageData<StudentVO> students(String loginUserId, String id, int pageIndex, int pageSize);

    MedicalInfoVO medical(String loginUserId, String sourceKey);

    MedicalInfoVO infectedMedical(String loginUserId, String sourceKey);
}

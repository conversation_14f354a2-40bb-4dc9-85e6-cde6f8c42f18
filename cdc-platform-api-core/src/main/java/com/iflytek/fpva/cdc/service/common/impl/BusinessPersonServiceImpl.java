package com.iflytek.fpva.cdc.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.apiService.AdminServiceApi;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.model.vo.UploadExcelVO;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.constant.Desensitize;
import com.iflytek.fpva.cdc.constant.Gender;
import com.iflytek.fpva.cdc.constant.enums.PlatformAdminTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.PlatformPositionEnum;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewBusinessPersonMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewOrganizationInfoMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewPersonDataAuthMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewPositionMapper;
import com.iflytek.fpva.cdc.mapper.infected.InfectedWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcewInfectedInfoMapper;
import com.iflytek.fpva.cdc.mapper.poison.PoisonWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonInfoMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympSymptomMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.SyndromeWarningEventMapper;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcewSyndromeMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonDiseaseMapper;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedNameVO;
import com.iflytek.fpva.cdc.model.dto.PositionDto;
import com.iflytek.fpva.cdc.model.po.AddUapUserPo;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.po.UapUserPo;
import com.iflytek.fpva.cdc.model.preventionControl.vo.PreventionControlNameVO;
import com.iflytek.fpva.cdc.model.resultmap.MatchedResult;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.infected.InfectedCascadeVO;
import com.iflytek.fpva.cdc.model.vo.org.OrgVO;
import com.iflytek.fpva.cdc.model.vo.time.OutpatientNameVO;
import com.iflytek.fpva.cdc.model.vo.unknown.UnknownReasonNameVO;
import com.iflytek.fpva.cdc.service.common.*;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.infected.CdcInfectedEventService;
import com.iflytek.fpva.cdc.service.infected.CdcReportCardService;
import com.iflytek.fpva.cdc.service.preventionControl.PreventionControlEventService;
import com.iflytek.fpva.cdc.service.outpatient.OutpatientEventService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.service.symptom.SympEventService;
import com.iflytek.fpva.cdc.service.syndrome.CdcEventService;
import com.iflytek.fpva.cdc.service.unknownReason.UnknownReasonEventService;
import com.iflytek.fpva.cdc.common.utils.StorageClientUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import com.iflytek.zhyl.uap.ext.pojo.UapMdmEmpUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BusinessPersonServiceImpl implements BusinessPersonService {

    @Value("${usercenter.portal.appcode:portal}")
    String portalAppCode;

    @Value("${role.name.notDesensitization}")
    String notDesensitizationRoleName;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcewBusinessPersonMapper businessPersonMapper;

    @Resource
    private FileService fileService;

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    private StorageClientUtil storageClientUtil;

    @Resource
    private OrgService orgService;

    @Resource
    private TbCdcewPositionMapper tbCdcewPositionMapper;

    @Resource
    private RestService restService;

    @Resource
    private TbCdcewPersonDataAuthMapper tbCdcewPersonDataAuthMapper;

    @Resource
    private PositionService positionService;

    @Resource
    private TbCdcewOrganizationInfoMapper tbCdcOrganizationInfoMapper;

    @Resource
    private CdcReportCardService cdcReportCardService;

    @Resource
    private CdcEventService cdcEventService;

    @Resource
    private SympEventService sympEventService;

    @Resource
    private PoisonEventService poisonEventService;

    @Resource
    private SmsRuleService smsRuleService;

    @Resource
    private OutpatientEventService outpatientEventService;

    @Resource
    private UnknownReasonEventService unknownReasonEventService;

    @Resource
    PreventionControlEventService preventionControlEventService;

    @Resource
    private CdcInfectedEventService cdcInfectedEventService;

    @Resource
    private SyndromeWarningEventMapper syndromeWarningEventMapper;

    @Resource
    private InfectedWarningEventMapper infectedWarningEventMapper;

    @Resource
    private PoisonWarningEventMapper poisonWarningEventMapper;

    @Resource
    private AdminServiceApi adminServiceApi;

    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;

    @Value("${outcall.city.role.name:科长|科员}")
    private String outCallCityRoleName;

    @Value("${outcall.district.role.name:智能插件-防保科|智能插件-审核角色}")
    private String outCallDistrictRoleName;
    @Resource
    private TbCdcewSyndromeMapper tbCdcewSyndromeMapper;

    @Resource
    private TbCdcewInfectedInfoMapper tbCdcewInfectedInfoMapper;

    @Resource
    TbCdcewSympSymptomMapper tbCdcewSympSymptomMapper;

    @Resource
    TbCdcewPoisonInfoMapper tbCdcewPoisonInfoMapper;
    @Resource
    TbCdcewUnknownReasonDiseaseMapper tbCdcewUnknownReasonDiseaseMapper;


    @Override
    @Transactional
    public void add(BusinessPersonVO vo, String loginUserName) {

        List<String> allUserPhoneList = businessPersonMapper.findAllNotDeletedUserPhone();
        List<TbCdcewPosition> positionList = positionService.getPositionListByLoginUserName(loginUserName);
        List<String> positionNameList = positionList.stream().map(TbCdcewPosition::getPositionName).collect(Collectors.toList());

        MatchedResult matchedResult = isMatchedBusinessPerson(vo, positionNameList, allUserPhoneList);
        if (!matchedResult.getFlag()) {
            throw new MedicalBusinessException(matchedResult.getCode(), matchedResult.getMsg());
        }
        Date date = new Date();
        TbCdcewBusinessPerson person = new TbCdcewBusinessPerson();
        person.setId(String.valueOf(batchUidService.getUid("tb_cdcew_business_person")));
        person.setProvinceCode(vo.getProvinceCode());
        person.setProvinceName(vo.getProvinceName());
        person.setCityCode(vo.getCityCode());
        person.setCityName(vo.getCityName());
        person.setDistrictCode(vo.getDistrictCode());
        person.setDistrictName(vo.getDistrictName());
        person.setStatDimId(vo.getStatDimId());
        person.setStatDimName(vo.getStatDimName());
        person.setName(vo.getName());
        person.setPhone(vo.getPhone());
        person.setPositionName(vo.getPositionName());
        person.setStatus(CommonConstants.STATUS_VALID);
        person.setCreator(loginUserName);
        person.setCreateTime(date);
        person.setUpdater(loginUserName);
        person.setUpdateTime(date);
        person.setDeptName(vo.getDeptName());
        person.setPositionCode(vo.getPositionCode());
        person.setSexName(vo.getSexName());
        person.setDesensitize(vo.getDesensitize());
        UapUserPo uapUserPo = UapUserPo.fromBusinessPersonVO(vo);
        String userId = restService.addNewUapUser(uapUserPo);
        person.setUapUserId(userId);
        String positionCode = vo.getPositionCode();
        Set<String> roleIds = new HashSet<>();
        List<TbCdcewPersonDataAuth> tbCdcewPersonDataAuthList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(vo.getInfectedList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.INFECTED.getCode()));
            for (InfectedNameVO infectedNameVO : vo.getInfectedList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE);
                dataAuth.setSymptomName(infectedNameVO.getInfectedName());
                dataAuth.setSymptomId(infectedNameVO.getInfectedCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE));
        }

        if (CollectionUtil.isNotEmpty(vo.getSymptomList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.SYMPTOM.getCode()));
            for (SymptomNameVO symptomNameVO : vo.getSymptomList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_SYNDROME);
                dataAuth.setSymptomName(symptomNameVO.getSymptomName());
                dataAuth.setSymptomId(symptomNameVO.getSymptomCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_SYNDROME));
        }

        if (CollectionUtil.isNotEmpty(vo.getSchoolSymptomList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.SCHOOL_SYMPTOM.getCode()));
            for (SchoolSymptomNameVO schoolSymptomNameVO : vo.getSchoolSymptomList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM);
                dataAuth.setSymptomName(schoolSymptomNameVO.getSchoolSymptomName());
                dataAuth.setSymptomId(schoolSymptomNameVO.getSchoolSymptomCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM));
        }
        if (CollectionUtil.isNotEmpty(vo.getPoisonList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.POISON.getCode()));
            for (PoisonNameVO poisonNameVO : vo.getPoisonList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_POISON);
                dataAuth.setSymptomName(poisonNameVO.getPoisonName());
                dataAuth.setSymptomId(poisonNameVO.getPoisonCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_POISON));
        }
        if (CollectionUtil.isNotEmpty(vo.getCustomizedList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.CUSTOMIZED.getCode()));
            for (CustomizedNameVO customizedNameVO : vo.getCustomizedList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_CUSTOMIZED);
                dataAuth.setSymptomName(customizedNameVO.getCustomizedName());
                dataAuth.setSymptomId(customizedNameVO.getCustomizedCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_CUSTOMIZED));
        }
        if (CollectionUtil.isNotEmpty(vo.getOutpatientList())) {
            roleIds.addAll(outpatientEventService.getOutpatientRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.OUTPATIENT.getCode()));
            for (OutpatientNameVO outpatientNameVO : vo.getOutpatientList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_OUTPATIENT);
                dataAuth.setSymptomName(outpatientNameVO.getOutpatientTypeName());
                dataAuth.setSymptomId(outpatientNameVO.getOutpatientTypeCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_OUTPATIENT));
        }
        if (CollectionUtil.isNotEmpty(vo.getUnknownReasonList())) {
            roleIds.addAll(unknownReasonEventService.getUnknownReasonRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.UNKNOWN_REASON.getCode()));
            for (UnknownReasonNameVO unknownReasonNameVO : vo.getUnknownReasonList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_UNKNOWN_REASON);
                dataAuth.setSymptomName(unknownReasonNameVO.getDiseaseName());
                dataAuth.setSymptomId(unknownReasonNameVO.getDiseaseCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_UNKNOWN_REASON));
        }
        if (CollectionUtil.isNotEmpty(vo.getPreventionControlList())) {
            roleIds.addAll(preventionControlEventService.getPreventionControlRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.PREVENTION_CONTROL.getCode()));
            for (PreventionControlNameVO nameVO : vo.getPreventionControlList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_PREVENTION_CONTROL);
                dataAuth.setSymptomName(nameVO.getPreventionControlName());
                dataAuth.setSymptomId(nameVO.getPreventionControlCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_PREVENTION_CONTROL));
        }
        if (CollectionUtils.isEmpty(vo.getInfectedList()) &&
            CollectionUtils.isEmpty(vo.getSymptomList()) &&
            CollectionUtils.isEmpty(vo.getSchoolSymptomList()) &&
            CollectionUtils.isEmpty(vo.getPoisonList()) &&
            CollectionUtils.isEmpty(vo.getUnknownReasonList()) &&
            CollectionUtils.isEmpty(vo.getPreventionControlList()) &&
            CollectionUtils.isEmpty(vo.getCustomizedList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPosition(positionCode));
        }
        if (person.getDesensitize() == 0) {
            roleIds.add(restService.getUapRoleIdByAppCodeAndRoleName(portalAppCode, notDesensitizationRoleName));
        }
        if (!tbCdcewPersonDataAuthList.isEmpty()) {
            tbCdcewPersonDataAuthMapper.batchInsert(tbCdcewPersonDataAuthList);
        }
        businessPersonMapper.insert(person);
        for (String roleId : roleIds) {
            restService.addNewUserRole(userId, roleId);
        }

    }

    @Override
    @Transactional
    public void batchAdd(List<BusinessPersonVO> businessPersonVOList, String loginUserName) {

        if (CollectionUtils.isEmpty(businessPersonVOList)) {
            return;
        }

        List<BusinessPersonVO> result = new ArrayList<>();

        List<TbCdcewPosition> positionList = positionService.getPositionListByLoginUserName(loginUserName);
        List<String> positionNameList = positionList.stream().map(TbCdcewPosition::getPositionName).collect(Collectors.toList());
        List<String> allUserPhoneList = businessPersonMapper.findAllNotDeletedUserPhone();

        //过滤掉不符合要求的数据
        businessPersonVOList.forEach(businessPersonVO -> {
            if (isMatchedBusinessPerson(businessPersonVO, positionNameList, allUserPhoneList).getFlag()) {
                result.add(businessPersonVO);
            }
        });

        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 批量新增时  去除重复手机号
        List<String> repeatPhoneList = new ArrayList<>();
        Map<String, List<BusinessPersonVO>> collect = businessPersonVOList.stream().collect(Collectors.groupingBy(BusinessPersonVO::getPhone));
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                repeatPhoneList.add(k);
            }
        });

        List<BusinessPersonVO> finalResult = result.stream().filter(businessPersonVO -> !repeatPhoneList.contains(businessPersonVO.getPhone())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalResult)) {
            return;
        }
        this.batchAddUapRolePerson(finalResult, loginUserName);

    }

    /**
     * 批量添加数据权限 表格导入
     */
    private void batchAddDataAuth(List<TbCdcewBusinessPerson> businessPersonList) {
        List<TbCdcewPersonDataAuth> finalList = new ArrayList<>();
        businessPersonList.forEach(e -> {
            List<TbCdcewPersonDataAuth> dataAuthList = getDataAuthList();
            List<TbCdcewPosition> positionList = tbCdcewPositionMapper.getPositionList();
            List<String> positionCodeList = positionList.stream().filter(position -> ((position.getDataAuthSwitch() != null) && (1 == position.getDataAuthSwitch()))).map(TbCdcewPosition::getPositionCode).collect(Collectors.toList());
            if (positionCodeList.contains(e.getPositionCode())) {
                dataAuthList.forEach(dataAuth -> {
                    dataAuth.setBusinessPersonId(e.getId());
                });
            }
            finalList.addAll(dataAuthList);
        });
        if (!finalList.isEmpty()) {
            tbCdcewPersonDataAuthMapper.batchInsert(finalList);
        }
    }

    public List<TbCdcewPersonDataAuth> getDataAuthList() {
        List<TbCdcewPersonDataAuth> tbCdcewPersonDataAuthList = new ArrayList<>();
        List<InfectedCascadeVO> infectedList = cdcReportCardService.getInfectedInfoList();
        List<CascadeVO> symptomList = cdcEventService.getSyndromeList();
        List<CascadeVO> scSymptomList = sympEventService.getSymptoms();
        List<CascadeVO> poisonNameList = poisonEventService.getPoisonNameList();
        infectedList.forEach(e -> {
            e.getChildren().forEach(c -> {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setSymptomId(c.getCode());
                dataAuth.setSymptomName(c.getValue());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE);
                tbCdcewPersonDataAuthList.add(dataAuth);
            });
        });
        symptomList.forEach(e -> {
            TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
            dataAuth.setSymptomId(e.getValue());
            dataAuth.setSymptomName(e.getLabel());
            dataAuth.setDataType(CommonConstants.DATA_AUTH_SYNDROME);
            tbCdcewPersonDataAuthList.add(dataAuth);
        });
        scSymptomList.forEach(e -> {
            TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
            dataAuth.setSymptomId(e.getValue());
            dataAuth.setSymptomName(e.getLabel());
            dataAuth.setDataType(CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM);
            tbCdcewPersonDataAuthList.add(dataAuth);
        });
        // 不赋予中毒类型数据权限
//        poisonNameList.forEach(e -> {
//            e.getChildren().forEach(c->{
//                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
//                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
//                dataAuth.setSymptomId(c.getValue());
//                dataAuth.setSymptomName(c.getLabel());
//                dataAuth.setDataType(CommonConstants.DATA_AUTH_POISON);
//                tbCdcewPersonDataAuthList.add(dataAuth);
//            });
//        });
        return tbCdcewPersonDataAuthList;
    }

    /**
     * 批量新增 需要在uap中同步添加用户的角色
     */
    private void batchAddUapRolePerson(List<BusinessPersonVO> list, String loginUserName) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 批量新增用户
        List<UapUserPo> uapUserPos = list.stream().map(UapUserPo::fromBusinessPersonVO).collect(Collectors.toList());
        List<AddUapUserPo> addUapUserPoList = new ArrayList<>();
        uapUserPos.forEach(uapUserPo -> {
            AddUapUserPo addUapUserPo = new AddUapUserPo();
            addUapUserPo.setUapUser(uapUserPo);
            addUapUserPoList.add(addUapUserPo);
        });
        // 调用uap批量新增用户接口
        List<AddUapUserPo> addUapUserPos = restService.batchAddUapUser(addUapUserPoList);

        List<UapUserPo> uapUserResults = addUapUserPos.stream().map(AddUapUserPo::getUapUser).collect(Collectors.toList());

        list.forEach(businessPersonVO -> {
            String phone = businessPersonVO.getPhone();
            String uapUserId = null;

            List<UapUserPo> collect1 = uapUserResults.stream().filter(uapUserPo -> phone.equals(uapUserPo.getLoginName())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect1)) {
                UapUserPo uapUserPo = collect1.get(0);
                uapUserId = uapUserPo.getId();
            } else {
                // 先判断机构 新增的用户在用户中心中已存在，但机构不同时   此时不能进行新增操作
                // 机构相同时 uap侧执行update操作
                UapOrgPo orgPo = restService.getUserOrg(phone);

                if (businessPersonVO.getStatDimId().equals(orgPo.getId())) {
                    UapUserPo uapUserPo = UapUserPo.fromBusinessPersonVO(businessPersonVO);
                    uapUserId = restService.getUapUserIdByName(phone);
                    uapUserPo.setId(uapUserId);
                    restService.updateUapUser(uapUserPo);
                }
            }
            businessPersonVO.setUapUserId(uapUserId);
        });

        Date date = new Date();
        List<TbCdcewBusinessPerson> businessPersonList = list.stream().filter(businessPersonVO -> !StringUtils.isEmpty(businessPersonVO.getUapUserId()))
                .map(TbCdcewBusinessPerson::fromBusinessPersonVO).collect(Collectors.toList());
        businessPersonList.forEach(person -> {
            person.setId(String.valueOf(batchUidService.getUid("tb_cdcew_business_person")));
            person.setCreator(loginUserName);
            person.setCreateTime(date);
            person.setUpdater(loginUserName);
            person.setUpdateTime(date);
        });

        // 按照职位分别给不同职位的人赋角色
        List<PositionDto> allRoleList = positionService.getAllRoleList();
        //  暂时不赋予 中毒类数据权限 自定义角色权限也不给
        List<PositionDto> finalAllRoleList = allRoleList.stream()
                .filter(tbCdcewPosition -> (!PlatformAdminTypeEnum.POISON.getCode().equals(tbCdcewPosition.getPositionTypeCode())
                && !PlatformAdminTypeEnum.CUSTOMIZED.getCode().equals(tbCdcewPosition.getPositionTypeCode())))
                .collect(Collectors.toList());
        Map<String, List<TbCdcewBusinessPerson>> collect1 = businessPersonList.stream()
                .collect(Collectors.groupingBy(TbCdcewBusinessPerson::getPositionCode));
        collect1.forEach((k, v) -> {

            //得到用户id集合 用,分割
            List<String> uapUserIdList = v.stream().map(TbCdcewBusinessPerson::getUapUserId).collect(Collectors.toList());
            String userIds = "";

            for (String s : uapUserIdList) {
                userIds = userIds.concat(s).concat(",");
            }
            userIds = userIds.substring(0, userIds.length() - 1);

            List<PositionDto> tbCdcewPositions = finalAllRoleList.stream().filter(tbCdcewPosition -> k.equals(tbCdcewPosition.getPositionCode())).collect(Collectors.toList());
            // 以此将各个角色赋给对应的用户集合
            String finalUserIds = userIds;
            tbCdcewPositions.forEach(tbCdcewPosition -> restService.addNewUserRole(finalUserIds, tbCdcewPosition.getUapRoleId()));

        });

        //处理非脱敏角色
        List<TbCdcewBusinessPerson> nonDesensitizeList = businessPersonList.stream().filter(tbCdcewBusinessPerson -> tbCdcewBusinessPerson.getDesensitize() == 0).collect(Collectors.toList());
        if (!nonDesensitizeList.isEmpty()) {
            String userIds = "";
            for (String s : nonDesensitizeList.stream().map(TbCdcewBusinessPerson::getUapUserId).collect(Collectors.toList())) {
                userIds = userIds.concat(s).concat(",");
            }
            userIds = userIds.substring(0, userIds.length() - 1);
            restService.addNewUserRole(userIds, restService.getUapRoleIdByAppCodeAndRoleName(portalAppCode, notDesensitizationRoleName));
        }

        // 批量新增用户到业务侧的用户表
        List<List<TbCdcewBusinessPerson>> batchList = CollectionUtil.split(businessPersonList, CommonConstants.MAX_BATCH_SIZE);
        for (List<TbCdcewBusinessPerson> item : batchList) {
            businessPersonMapper.insertBatch(item);
        }
        this.batchAddDataAuth(businessPersonList);
    }

    /**
     * 校验是否是符合要求的业务人员
     */
    private MatchedResult isMatchedBusinessPerson(BusinessPersonVO businessPersonVO, List<String> positionNameList, List<String> phoneList) {

        MatchedResult matchedResult = new MatchedResult();
        matchedResult.setFlag(true);

        String positionName = businessPersonVO.getPositionName();
        String positionCode = businessPersonVO.getPositionCode();
        String name = businessPersonVO.getName();
        String sexName = businessPersonVO.getSexName();
        String phone = businessPersonVO.getPhone();
        String statDimId = businessPersonVO.getStatDimId();
        String statDimName = businessPersonVO.getStatDimName();
        String deptName = businessPersonVO.getDeptName();

        // 校验角色
        if (StringUtils.isBlank(positionName) || StringUtils.isBlank(positionCode)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("角色必须填写");
            matchedResult.setCode("11441009");
            return matchedResult;
        } else if (!positionNameList.contains(positionName)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("角色名称不存在");
            matchedResult.setCode("11441010");
            return matchedResult;
        }

        // 校验姓名
        if (StringUtils.isBlank(name)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("姓名必须填写");
            matchedResult.setCode("11441011");
            return matchedResult;
        } else if (!com.iflytek.fpva.cdc.util.StringUtils.isAllChineseCharacter(name)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("姓名必须是全中文");
            matchedResult.setCode("11441012");
            return matchedResult;
        } else if (name.length() > 25) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("姓名超长,限制25字");
            matchedResult.setCode("11441013");
            return matchedResult;
        }

        // 校验性别
        if (StringUtils.isEmpty(sexName)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("性别必须填写");
            matchedResult.setCode("11441014");
            return matchedResult;
        } else if (!sexName.equals(Gender.MALE) && !sexName.equals(Gender.FEMALE)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("性别必须是男或女");
            matchedResult.setCode("11441015");
            return matchedResult;
        }

        // 校验机构
        if (StringUtils.isBlank(statDimId) || StringUtils.isBlank(statDimName)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("机构不能为空");
            matchedResult.setCode("11441016");
            return matchedResult;
        }

        // 校验手机号
        if (StringUtils.isBlank(phone)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("手机号必须填写");
            matchedResult.setCode("11441017");
            return matchedResult;
        }
        if (!StringUtils.isNumeric(phone)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("手机号必须是数字");
            matchedResult.setCode("11441018");
            return matchedResult;
        } else if (phone.length() != 11) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("手机号必须是11位");
            matchedResult.setCode("11441019");
            return matchedResult;
        } else if (phoneList.contains(phone)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("用户手机号已在系统中存在");
            matchedResult.setCode("11441020");
            return matchedResult;
        }

        // 校验科室
        if (!StringUtils.isEmpty(deptName) && deptName.length() > 15) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("科室名称超长,限制15字");
            matchedResult.setCode("11441021");
        }

        return matchedResult;
    }

    /**
     *  构建无数据权限对象
     * @param personId
     * @param dataType
     * @return
     */
    public TbCdcewPersonDataAuth buildNoDataAuth(String personId,Integer dataType){
        TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
        dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
        dataAuth.setBusinessPersonId(personId);
        dataAuth.setDataType(dataType);
        dataAuth.setSymptomName(CommonConstants.NOT_DATA_VAL);
        dataAuth.setSymptomId(CommonConstants.NOT_DATA_AUTH);
        return dataAuth;
    }

    @Override
    @Transactional
    public void update(BusinessPersonVO vo, String loginUserId, String loginUserName) {
        Date date = new Date();

        TbCdcewBusinessPerson businessPerson = businessPersonMapper.selectByPrimaryKey(vo.getId());
        if (businessPerson == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + vo.getId());
        }

        if (businessPerson.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除");
        }

        List<TbCdcewPosition> positionList = positionService.getPositionListByLoginUserName(loginUserName);
        List<String> positionNameList = positionList.stream().map(TbCdcewPosition::getPositionName).collect(Collectors.toList());
        List<String> allUserPhoneList = new ArrayList<>();
        MatchedResult matchedResult = isMatchedBusinessPerson(vo, positionNameList, allUserPhoneList);
        if (!matchedResult.getFlag()) {
            throw new MedicalBusinessException(matchedResult.getCode(), matchedResult.getMsg());
        }

        TbCdcewBusinessPerson person = new TbCdcewBusinessPerson();
        person.setId(vo.getId());
        person.setProvinceCode(vo.getProvinceCode());
        person.setProvinceName(vo.getProvinceName());
        person.setCityCode(vo.getCityCode());
        person.setCityName(vo.getCityName());
        person.setDistrictCode(vo.getDistrictCode());
        person.setDistrictName(vo.getDistrictName());
        person.setStatDimId(vo.getStatDimId());
        person.setStatDimName(vo.getStatDimName());
        person.setName(vo.getName());
        person.setPhone(vo.getPhone());
        person.setStatus(vo.getStatus());
        person.setUpdater(loginUserName);
        person.setUpdateTime(date);
        person.setPositionCode(vo.getPositionCode());
        person.setPositionName(vo.getPositionName());
        person.setSexName(vo.getSexName());
        person.setDeptName(vo.getDeptName());
        person.setUapUserId(businessPerson.getUapUserId());
        person.setDesensitize(vo.getDesensitize());

        Set<String> roleIds = new HashSet<>();
        List<TbCdcewPersonDataAuth> tbCdcewPersonDataAuthList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(vo.getInfectedList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.INFECTED.getCode()));
            for (InfectedNameVO infectedNameVO : vo.getInfectedList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE);
                dataAuth.setSymptomName(infectedNameVO.getInfectedName());
                dataAuth.setSymptomId(infectedNameVO.getInfectedCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE));
        }

        if (CollectionUtil.isNotEmpty(vo.getSymptomList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.SYMPTOM.getCode()));
            for (SymptomNameVO symptomNameVO : vo.getSymptomList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_SYNDROME);
                dataAuth.setSymptomName(symptomNameVO.getSymptomName());
                dataAuth.setSymptomId(symptomNameVO.getSymptomCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_SYNDROME));
        }
        if (CollectionUtil.isNotEmpty(vo.getSchoolSymptomList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.SCHOOL_SYMPTOM.getCode()));
            for (SchoolSymptomNameVO schoolSymptomNameVO : vo.getSchoolSymptomList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM);
                dataAuth.setSymptomName(schoolSymptomNameVO.getSchoolSymptomName());
                dataAuth.setSymptomId(schoolSymptomNameVO.getSchoolSymptomCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM));
        }
        if (CollectionUtil.isNotEmpty(vo.getPoisonList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.POISON.getCode()));
            for (PoisonNameVO poisonNameVO : vo.getPoisonList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_POISON);
                dataAuth.setSymptomName(poisonNameVO.getPoisonName());
                dataAuth.setSymptomId(poisonNameVO.getPoisonCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_POISON));
        }
        if (CollectionUtil.isNotEmpty(vo.getOutpatientList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.OUTPATIENT.getCode()));
            for (OutpatientNameVO outpatientNameVO : vo.getOutpatientList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_OUTPATIENT);
                dataAuth.setSymptomName(outpatientNameVO.getOutpatientTypeName());
                dataAuth.setSymptomId(outpatientNameVO.getOutpatientTypeCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_OUTPATIENT));
        }
        if (CollectionUtil.isNotEmpty(vo.getUnknownReasonList())) {
            roleIds.addAll(unknownReasonEventService.getUnknownReasonRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.UNKNOWN_REASON.getCode()));
            for (UnknownReasonNameVO unknownReasonNameVO : vo.getUnknownReasonList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_UNKNOWN_REASON);
                dataAuth.setSymptomName(unknownReasonNameVO.getDiseaseName());
                dataAuth.setSymptomId(unknownReasonNameVO.getDiseaseCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_UNKNOWN_REASON));
        }
        if (CollectionUtil.isNotEmpty(vo.getPreventionControlList())) {
            roleIds.addAll(preventionControlEventService.getPreventionControlRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.PREVENTION_CONTROL.getCode()));
            for (PreventionControlNameVO nameVO : vo.getPreventionControlList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_PREVENTION_CONTROL);
                dataAuth.setSymptomName(nameVO.getPreventionControlName());
                dataAuth.setSymptomId(nameVO.getPreventionControlCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_PREVENTION_CONTROL));
        }
        if (CollectionUtil.isNotEmpty(vo.getCustomizedList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(vo.getPositionCode(), PlatformAdminTypeEnum.CUSTOMIZED.getCode()));
            for (CustomizedNameVO customizedNameVO : vo.getCustomizedList()) {
                TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
                dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcew_person_data_auth")));
                dataAuth.setBusinessPersonId(person.getId());
                dataAuth.setDataType(CommonConstants.DATA_AUTH_CUSTOMIZED);
                dataAuth.setSymptomName(customizedNameVO.getCustomizedName());
                dataAuth.setSymptomId(customizedNameVO.getCustomizedCode());
                tbCdcewPersonDataAuthList.add(dataAuth);
            }
        } else {
            //无数据权限填充no_data_auth
            tbCdcewPersonDataAuthList.add(buildNoDataAuth(person.getId(),CommonConstants.DATA_AUTH_CUSTOMIZED));
        }

        if (CollectionUtils.isEmpty(vo.getInfectedList()) &&
            CollectionUtils.isEmpty(vo.getSymptomList()) &&
            CollectionUtils.isEmpty(vo.getSchoolSymptomList()) &&
            CollectionUtils.isEmpty(vo.getPoisonList()) &&
            CollectionUtils.isEmpty(vo.getUnknownReasonList()) &&
            CollectionUtils.isEmpty(vo.getPreventionControlList()) &&
            CollectionUtils.isEmpty(vo.getCustomizedList())) {
            roleIds.addAll(positionService.getPositionRoleIdsByPosition(vo.getPositionCode()));
        }
        if (person.getDesensitize() == 0) {
            roleIds.add(restService.getUapRoleIdByAppCodeAndRoleName(portalAppCode, notDesensitizationRoleName));
        }
        tbCdcewPersonDataAuthMapper.deleteByBusinessPersonId(vo.getId());
        if (!tbCdcewPersonDataAuthList.isEmpty()) {
            tbCdcewPersonDataAuthMapper.batchInsert(tbCdcewPersonDataAuthList);
        }

        List<String> existRoleIds = positionService.getPositionRoleIdsByPosition(businessPerson.getPositionCode());
        if (businessPerson.getDesensitize() == 0) {
            existRoleIds.add(restService.getUapRoleIdByAppCodeAndRoleName(portalAppCode, notDesensitizationRoleName));
        }
        for (String existRoleId : existRoleIds) {
            restService.deleteUserRole(businessPerson.getUapUserId(), existRoleId);
        }
        for (String roleId : roleIds) {
            restService.addNewUserRole(businessPerson.getUapUserId(), roleId);
        }

        businessPersonMapper.updateByPrimaryKeySelective(person);

        UapUserPo uapUserPo = UapUserPo.fromBusinessPersonVO(vo);
        restService.updateUapUser(uapUserPo);

    }

    @Override
    @Transactional
    public void delete(String id, String loginUserId, String loginUserName) {

        TbCdcewBusinessPerson businessPerson = businessPersonMapper.selectByPrimaryKey(id);
        if (businessPerson == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + id);
        }
        if (businessPerson.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除，不允许再次删除");
        }

        List<String> existRoleIds = positionService.getPositionRoleIdsByPosition(businessPerson.getPositionCode());
        for (String existRoleId : existRoleIds) {
            restService.deleteUserRole(businessPerson.getUapUserId(), existRoleId);
        }
        businessPersonMapper.updateStatusById(id, CommonConstants.STATUS_DELETE, loginUserName);
        smsRuleService.deleteSmsRuleByBusinessPersonId(loginUserId, id);
    }

    @Override
    @Transactional
    public void updateStatus(String id, Integer status, String loginUserId, String loginUserName) {


        if (status != CommonConstants.STATUS_INVALID && status != CommonConstants.STATUS_VALID) {
            throw new MedicalBusinessException("11441005", "无效的状态：" + status);
        }

        TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(id);
        if (person == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + id);
        }

        if (status == CommonConstants.STATUS_INVALID && status.equals(person.getStatus())) {
            throw new MedicalBusinessException("11441024", "人员已处于禁用状态，不允许再次禁用");
        }

        if (status == CommonConstants.STATUS_VALID && status.equals(person.getStatus())) {
            throw new MedicalBusinessException("11441025", "人员已处于启用状态，不允许再次启用");
        }

        if (person.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除");
        }

        // 禁用用户
        if (status == CommonConstants.STATUS_INVALID) {
            TbCdcewBusinessPerson businessPerson = businessPersonMapper.selectByPrimaryKey(id);
            List<String> existRoleIds = positionService.getPositionRoleIdsByPosition(businessPerson.getPositionCode());
            for (String existRoleId : existRoleIds) {
                restService.deleteUserRole(businessPerson.getUapUserId(), existRoleId);
            }
        }

        // 启用用户
        if (status == CommonConstants.STATUS_VALID) {
            TbCdcewBusinessPerson businessPerson = businessPersonMapper.selectByPrimaryKey(id);
            List<String> existRoleIds = new ArrayList<>();
            List<TbCdcewPersonDataAuth> dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
            Map<Integer, List<TbCdcewPersonDataAuth>> collect = dataAuths.stream().collect(Collectors.groupingBy(TbCdcewPersonDataAuth::getDataType));

            if (collect.containsKey(CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE)) {
                existRoleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(businessPerson.getPositionCode(), PlatformAdminTypeEnum.INFECTED.getCode()));
            }
            if (collect.containsKey(CommonConstants.DATA_AUTH_SYNDROME)) {
                existRoleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(businessPerson.getPositionCode(), PlatformAdminTypeEnum.SYMPTOM.getCode()));
            }
            if (collect.containsKey(CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM)) {
                existRoleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(businessPerson.getPositionCode(), PlatformAdminTypeEnum.SCHOOL_SYMPTOM.getCode()));
            }
            if (collect.containsKey(CommonConstants.DATA_AUTH_POISON)) {
                existRoleIds.addAll(positionService.getPositionRoleIdsByPositionAndType(businessPerson.getPositionCode(), PlatformAdminTypeEnum.POISON.getCode()));
            }
            if (!collect.containsKey(CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE) && !collect.containsKey(CommonConstants.DATA_AUTH_SYNDROME)
                    && !collect.containsKey(CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM) && !collect.containsKey(CommonConstants.DATA_AUTH_POISON)) {
                existRoleIds.addAll(positionService.getPositionRoleIdsByPosition(businessPerson.getPositionCode()));
            }

            for (String existRoleId : existRoleIds) {
                restService.addNewUserRole(businessPerson.getUapUserId(), existRoleId);
            }

        }
        businessPersonMapper.updateStatusById(id, status, loginUserName);
    }

    @Override
    public void updateDesensitize(String id, Integer desensitize, String loginUserId, String loginUserName) {


        TbCdcewBusinessPerson businessPerson = businessPersonMapper.selectByPrimaryKey(id);
        if (businessPerson == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + id);
        }

        if (businessPerson.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除");
        }

        Set<String> roleIds = new HashSet<>();

        if (desensitize == 0) {
            roleIds.add(restService.getUapRoleIdByAppCodeAndRoleName(portalAppCode, notDesensitizationRoleName));
        }

        List<String> existRoleIds = new ArrayList<>();
        if (businessPerson.getDesensitize() == 0) {
            existRoleIds.add(restService.getUapRoleIdByAppCodeAndRoleName(portalAppCode, notDesensitizationRoleName));
        }
        for (String existRoleId : existRoleIds) {
            restService.deleteUserRole(businessPerson.getUapUserId(), existRoleId);
        }
        for (String roleId : roleIds) {
            restService.addNewUserRole(businessPerson.getUapUserId(), roleId);
        }
        businessPerson.setUpdateTime(new Date());
        businessPerson.setUpdater(loginUserName);
        businessPerson.setDesensitize(desensitize);
        businessPersonMapper.updateByPrimaryKeySelective(businessPerson);

    }

    @Override
    public PageData<BusinessPersonVO> list(String loginUserName, BusinessPersonQueryVO queryVO) {

        // 筛选条件未填写机构时  默认按当前用户进行机构筛选
        List<String> statDimIdList = new ArrayList<>();
        if (StringUtils.isEmpty(queryVO.getStatDimId())) {
            List<OrgVO> orgListForBusinessPerson = orgService.getOrgListForBusinessPerson(loginUserName, null);
            statDimIdList = orgListForBusinessPerson.stream().map(OrgVO::getOrgId).collect(Collectors.toList());
        } else {
            statDimIdList.add(queryVO.getStatDimId());
        }
        queryVO.setStatDimIdList(statDimIdList);

        PageMethod.startPage(queryVO.getPageIndex(), queryVO.getPageSize());
        List<TbCdcewBusinessPerson> list = businessPersonMapper.listByQueryVO(queryVO);
        PageInfo<TbCdcewBusinessPerson> pageInfo = new PageInfo<>(list);
        PageData<BusinessPersonVO> pageData = PageData.fromPageInfo(pageInfo);

        List<BusinessPersonVO> retList = list.stream().map(BusinessPersonVO::fromEntity).collect(Collectors.toList());

        List<String> businessIds = retList.stream().map(BusinessPersonVO::getId).collect(Collectors.toList());
        if (!businessIds.isEmpty()) {
            List<TbCdcewPersonDataAuth> personDataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonIds(businessIds);
            for (BusinessPersonVO businessPerson : retList) {
                List<SymptomNameVO> symptomNameVOList = new ArrayList<>();
                List<InfectedNameVO> infectedNameVOList = new ArrayList<>();
                List<SchoolSymptomNameVO> schoolSymptomNameVOList = new ArrayList<>();
                List<PoisonNameVO> poisonNameVOList = new ArrayList<>();
                List<OutpatientNameVO> outpatientNameVOList = new ArrayList<>();
                List<UnknownReasonNameVO> unknownReasonNameVOList = new ArrayList<>();
                List<PreventionControlNameVO> preventionControlNameVOList = new ArrayList<>();
                List<CustomizedNameVO> customizedNameVOList = new ArrayList<>();
                for (TbCdcewPersonDataAuth personDataAuth : personDataAuths) {
                    if (personDataAuth.getBusinessPersonId().equals(businessPerson.getId())) {
                        if (CommonConstants.DATA_AUTH_SYNDROME == personDataAuth.getDataType()) {
                            SymptomNameVO symptomNameVO = new SymptomNameVO();
                            symptomNameVO.setSymptomName(personDataAuth.getSymptomName());
                            symptomNameVO.setSymptomCode(personDataAuth.getSymptomId());
                            symptomNameVOList.add(symptomNameVO);
                        } else if (CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE == personDataAuth.getDataType()) {
                            InfectedNameVO infectedNameVO = new InfectedNameVO();
                            infectedNameVO.setInfectedName(personDataAuth.getSymptomName());
                            infectedNameVO.setInfectedCode(personDataAuth.getSymptomId());
                            infectedNameVOList.add(infectedNameVO);
                        } else if (CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM == personDataAuth.getDataType()) {
                            SchoolSymptomNameVO schoolSymptomNameVO = new SchoolSymptomNameVO();
                            schoolSymptomNameVO.setSchoolSymptomCode(personDataAuth.getSymptomId());
                            schoolSymptomNameVO.setSchoolSymptomName(personDataAuth.getSymptomName());
                            schoolSymptomNameVOList.add(schoolSymptomNameVO);
                        } else if (CommonConstants.DATA_AUTH_POISON == personDataAuth.getDataType()) {
                            PoisonNameVO poisonNameVO = new PoisonNameVO();
                            poisonNameVO.setPoisonName(personDataAuth.getSymptomName());
                            poisonNameVO.setPoisonCode(personDataAuth.getSymptomId());
                            poisonNameVOList.add(poisonNameVO);
                        } else if (CommonConstants.DATA_AUTH_OUTPATIENT == personDataAuth.getDataType()) {
                            OutpatientNameVO outpatientNameVO = new OutpatientNameVO();
                            outpatientNameVO.setOutpatientTypeName(personDataAuth.getSymptomName());
                            outpatientNameVO.setOutpatientTypeCode(personDataAuth.getSymptomId());
                            outpatientNameVOList.add(outpatientNameVO);
                        } else if (CommonConstants.DATA_AUTH_UNKNOWN_REASON == personDataAuth.getDataType()) {
                            UnknownReasonNameVO unknownReasonNameVO = new UnknownReasonNameVO();
                            unknownReasonNameVO.setDiseaseName(personDataAuth.getSymptomName());
                            unknownReasonNameVO.setDiseaseCode(personDataAuth.getSymptomId());
                            unknownReasonNameVOList.add(unknownReasonNameVO);
                        }else if (CommonConstants.DATA_AUTH_PREVENTION_CONTROL == personDataAuth.getDataType()) {
                            PreventionControlNameVO nameVO = new PreventionControlNameVO();
                            nameVO.setPreventionControlName(personDataAuth.getSymptomName());
                            nameVO.setPreventionControlCode(personDataAuth.getSymptomId());
                           preventionControlNameVOList.add(nameVO);
                        }else if (CommonConstants.DATA_AUTH_CUSTOMIZED == personDataAuth.getDataType()) {
                            CustomizedNameVO customizedNameVO = new CustomizedNameVO();
                            customizedNameVO.setCustomizedName(personDataAuth.getSymptomName());
                            customizedNameVO.setCustomizedCode(personDataAuth.getSymptomId());
                            customizedNameVOList.add(customizedNameVO);
                        }
                    }
                }
                businessPerson.setSymptomList(symptomNameVOList);
                businessPerson.setInfectedList(infectedNameVOList);
                businessPerson.setSchoolSymptomList(schoolSymptomNameVOList);
                businessPerson.setPoisonList(poisonNameVOList);
                businessPerson.setOutpatientList(outpatientNameVOList);
                businessPerson.setUnknownReasonList(unknownReasonNameVOList);
                businessPerson.setPreventionControlList(preventionControlNameVOList);
                businessPerson.setCustomizedList(customizedNameVOList);
            }
        }
        List<TbCdcewPosition> positionList = tbCdcewPositionMapper.getPositionList();
        retList.forEach(e -> getSwitch(e, positionList));
        pageData.setData(retList);
        return pageData;
    }

    private void getSwitch(BusinessPersonVO businessPersonVO, List<TbCdcewPosition> positionList) {
        positionList.forEach(p -> {
            if (p.getPositionCode().equals(businessPersonVO.getPositionCode())) {
                businessPersonVO.setSmsSwitch(p.getSmsSwitch());
                businessPersonVO.setDataAuthSwitch(p.getDataAuthSwitch());
            }
        });
    }

    @Override
    public List<BusinessPersonVO> listByOutCall(String loginUserName, String orgId, String statDimId, String symptomId, Integer dataType) {
        if (userCenterVersion.equals("5.0.0")) {
            return listByOutCallV2(loginUserName, orgId, statDimId, symptomId, dataType);
        }

        // 根据当前用户的级别来进行判断  分别往不同的人群发送外呼信息
        //  市级用户  发送给他选择的下属区级的预警管理员  (市级用户还需要根据数据权限做限制，发送给能看这个症状的区级预警管理员)
        //  区级用户  发送给该信号所对应的机构的防保科医生

        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (Objects.isNull(org)) {
            throw new MedicalBusinessException("11463001", "用户账号异常,机构不存在,loginUserName:" + loginUserName);
        }

        BusinessPersonQueryVO queryVO = new BusinessPersonQueryVO();
        queryVO.setStatus(CommonConstants.STATUS_VALID);

        List<String> statDimIdList = new ArrayList<>();
        List<TbCdcewBusinessPerson> list;

        // 判定依据： 无 districtCode 即为市级账号
        if (StringUtils.isEmpty(org.getDistrictCode())) {
            // 市级用户外呼对象是预警管理员
            queryVO.setPositionCode(PlatformPositionEnum.PLATFORM_ADMIN.getCode());

            statDimIdList.add(orgId);
            queryVO.setStatDimIdList(statDimIdList);

            list = businessPersonMapper.listByQueryVO(queryVO);
            List<String> finalBusinessPersonIds = new ArrayList<>();

            // 找到该区县下所有的预警管理员之后 需要对这些预警管理员的数据权限进行进一步过滤
            List<String> businessIds = list.stream().map(TbCdcewBusinessPerson::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(businessIds)) {

                List<TbCdcewPersonDataAuth> personDataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonIds(businessIds);
                Map<String, List<TbCdcewPersonDataAuth>> collect = personDataAuths.stream().collect(Collectors.groupingBy(TbCdcewPersonDataAuth::getBusinessPersonId));
                String tempSymptomId = "";
                if (CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE == dataType.intValue()
                        && !StringUtils.isEmpty(symptomId) && symptomId.length() > 3){
                    //传染病数据权限在父类
                    tempSymptomId = symptomId.substring(0,3);
                }
                log.info("外呼疾病编码#{},外呼dataType#{},临时疾病编码#{}",symptomId,dataType,tempSymptomId);
                for (Map.Entry<String, List<TbCdcewPersonDataAuth>> listEntry : collect.entrySet()) {
                    String k = listEntry.getKey();
                    List<TbCdcewPersonDataAuth> v = listEntry.getValue();
                    List<String> symptomIds = v.stream()
                            .filter(a -> a.getDataType().equals(dataType))
                            .map(TbCdcewPersonDataAuth::getSymptomId)
                            .filter(da -> StringUtils.isNoneBlank(da))
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(symptomIds) &&
                            (symptomIds.contains(symptomId) || symptomIds.contains(tempSymptomId))) {
                        finalBusinessPersonIds.add(k);
                    }
                }

            }
            list = list.stream().filter(tbCdcewBusinessPerson -> finalBusinessPersonIds.contains(tbCdcewBusinessPerson.getId())).collect(Collectors.toList());
        } else {
            // 区级用户外呼对象是防保科医生
            queryVO.setPositionCode(PlatformPositionEnum.PLUGIN_PPS.getCode());

            statDimIdList.add(statDimId);
            queryVO.setStatDimIdList(statDimIdList);

            // 得到信号对应机构下面的所有的防保科医生
            list = businessPersonMapper.listByQueryVO(queryVO);
        }


        return list.stream().map(BusinessPersonVO::fromEntity).collect(Collectors.toList());
    }

    private List<BusinessPersonVO> listByOutCallV2(String loginUserName, String orgId, String statDimId, String symptomId, Integer dataType) {

        // 根据当前用户的级别来进行判断  分别往不同的人群发送外呼信息
        //  市级用户  发送给他选择的下属区级的预警管理员  (市级用户还需要根据数据权限做限制，发送给能看这个症状的区级预警管理员)
        //  区级用户  发送给该信号所对应的机构的防保科医生

        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (Objects.isNull(org)) {
            throw new MedicalBusinessException("11463001", "用户账号异常,机构不存在,loginUserName:" + loginUserName);
        }


        // 判定依据： 无 districtCode 即为市级账号
        if (StringUtils.isEmpty(org.getDistrictCode())) {
            List<UapMdmEmpUserDto> orgUsers = restService.getOrgUsers(orgId);
            List<UapMdmEmpUserDto> filterOrgUsers = orgUsers.stream().filter(e -> e.getUserRoleInfoVos().stream().anyMatch(a -> Arrays.stream(outCallCityRoleName.split("\\|")).anyMatch(p -> p.equals(a.getName())))).collect(Collectors.toList());
            // 找到该区县下所有的预警管理员之后 需要对这些预警管理员的数据权限进行进一步过滤
            List<UapMdmEmpUserDto> finalFilterOrgUsers = new ArrayList<>();
            dealFinalOrgUsers(symptomId, dataType, filterOrgUsers, finalFilterOrgUsers);
            return finalFilterOrgUsers.stream().map(BusinessPersonVO::fromEntity).collect(Collectors.toList());
        } else {
            List<UapMdmEmpUserDto> orgUsers = restService.getOrgUsers(statDimId);
            List<UapMdmEmpUserDto> filterOrgUsers = orgUsers.stream().filter(e -> e.getUserRoleInfoVos().stream().anyMatch(a -> Arrays.stream(outCallDistrictRoleName.split("\\|")).anyMatch(p -> p.equals(a.getName())))).collect(Collectors.toList());
            // 找到该区县下所有的预警管理员之后 需要对这些预警管理员的数据权限进行进一步过滤
            List<UapMdmEmpUserDto> finalFilterOrgUsers = new ArrayList<>();
            dealFinalOrgUsers(symptomId, dataType, filterOrgUsers, finalFilterOrgUsers);
            return finalFilterOrgUsers.stream().map(BusinessPersonVO::fromEntity).collect(Collectors.toList());
        }


    }

    private void dealFinalOrgUsers(String symptomId, Integer dataType, List<UapMdmEmpUserDto> filterOrgUsers, List<UapMdmEmpUserDto> finalFilterOrgUsers) {
        if (!CollectionUtils.isEmpty(filterOrgUsers)) {
            for (UapMdmEmpUserDto user : filterOrgUsers) {
                List<TbCdcewPersonDataAuth> personDataAuths1 = restService.getInfectedDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths2 = restService.getSyndromeDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths3 = restService.getSchoolSymptomDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths4 = restService.getPoisonDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths5 = restService.getOutpatientDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths6 = restService.getUnknownReasonDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths7 = restService.getPreventionControlDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths8 = restService.getCustomizedDataAuthByLoginUserId(user.getId()).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
                List<TbCdcewPersonDataAuth> personDataAuths = new ArrayList<>();
                personDataAuths.addAll(personDataAuths1);
                personDataAuths.addAll(personDataAuths2);
                personDataAuths.addAll(personDataAuths3);
                personDataAuths.addAll(personDataAuths4);
                personDataAuths.addAll(personDataAuths5);
                personDataAuths.addAll(personDataAuths6);
                personDataAuths.addAll(personDataAuths7);
                personDataAuths.addAll(personDataAuths8);
                String tempSymptomId = "";
                if (CommonConstants.DATA_AUTH_POISON == dataType.intValue() && !org.springframework.util.StringUtils.isEmpty(symptomId)){
                    //中毒数据权限在第一层
                    tempSymptomId = symptomId.substring(0,2);
                }else if (CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE == dataType.intValue()
                        && !org.springframework.util.StringUtils.isEmpty(symptomId) && symptomId.length() > 3){
                    //传染病数据权限在父类
                    tempSymptomId = symptomId.substring(0,3);
                }
                log.info("外呼疾病编码#{},外呼dataType#{},临时疾病编码#{}",symptomId,dataType,tempSymptomId);
                List<String> symptomIds = personDataAuths
                        .stream()
                        .filter(a -> a.getDataType().equals(dataType))
                        .map(TbCdcewPersonDataAuth::getSymptomId)
                        .filter(da -> StringUtils.isNoneBlank(da))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(symptomIds) &&
                        (symptomIds.contains(symptomId) || symptomIds.contains(tempSymptomId))) {
                    finalFilterOrgUsers.add(user);
                }else if (CollectionUtils.isEmpty(symptomIds)){
                    //5.0没有配置数据权限就是拥有所有权限
                    finalFilterOrgUsers.add(user);
                }
            }
        }
    }

    @Override
    public byte[] downloadTemplate(String loginUserName) {
        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (Objects.isNull(org)) {
            throw new MedicalBusinessException("11463001", "用户账号异常,机构不存在,loginUserName:" + loginUserName);
        }

        // 获取所有的角色列表
        List<TbCdcewPosition> positionList = positionService.getPositionListByLoginUserName(loginUserName);

        //校验是否超出文件导出最大值
        restService.checkExportMax(positionList);
        // 性别列表
        String[] sexNames = {Gender.MALE, Gender.FEMALE};

        String[] desensitize = {Desensitize.DESENSITIZE, Desensitize.NONDESENSITIZE};

        Workbook workbook = new XSSFWorkbook();
        // 市级账号和区级账号不一样  市级账号不能创建防保科这一角色
        // 判定依据： 无 districtCode 即为市级账号
        if (StringUtils.isEmpty(org.getDistrictCode())) {

            List<OrgVO> orgList = orgService.getOrgListForBusinessPerson(loginUserName, null);

            createCityLevelTemplateSheet(positionList, sexNames, desensitize, orgList, workbook);

        } else {
            createDistrictLevelTemplateSheet(positionList, sexNames, desensitize, workbook);

            // 得到机构的下级机构列表
            List<TbCdcewOrganizationInfo> subOrgList = tbCdcOrganizationInfoMapper.findByHigherOrg(org.getId());
            // 设置机构下拉框数据
            createOrgSheet(positionList, org, subOrgList, workbook);
        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        return outputStream.toByteArray();
    }

    private void createOrgSheet(List<OrgVO> orgList, Workbook workbook) {
        Sheet orgSheet = workbook.createSheet("org");
        workbook.setSheetHidden(workbook.getSheetIndex(orgSheet), true);
    }

    private void createOrgSheet(List<TbCdcewPosition> positionList, UapOrgPo org, List<TbCdcewOrganizationInfo> subOrgList, Workbook workbook) {
        Sheet orgSheet = workbook.createSheet("org");
        workbook.setSheetHidden(workbook.getSheetIndex(orgSheet), true);

        // 创建角色行
        Row positionRow = orgSheet.createRow(0);
        List<String> positionNameList = positionList.stream().map(TbCdcewPosition::getPositionName).collect(Collectors.toList());
        for (int i = 0; i < positionNameList.size(); i++) {
            createCellAndSetValue(positionRow, positionNameList.get(i), i);
        }


        List<String> orgNameList;
        List<String> singleOrgList = new ArrayList<>();
        singleOrgList.add(org.getName());
        // 创建机构行
        for (int i = 0; i < positionNameList.size(); i++) {
            String positionName = positionNameList.get(i);

            // 防保科角色/诊疗医生 需要选择下级机构
            // 组长/组员 只能选择自身机构
            if (positionName.equals(PlatformPositionEnum.PLUGIN_PPS.getName())) {
                orgNameList = subOrgList.stream().map(TbCdcewOrganizationInfo::getOrgName).collect(Collectors.toList());
            } else {
                orgNameList = singleOrgList;
            }

            for (int j = 0; j < orgNameList.size(); j++) {
                String orgName = orgNameList.get(j);
                Row row = orgSheet.getRow(j + 1);
                if (row == null) {
                    row = orgSheet.createRow(j + 1);
                }
                createCellAndSetValue(row, orgName, i);
            }

            // 设置名称管理器
            String formula = getFormula("org", 2, orgNameList.size() + 1, (char) ('A' + i));
            Name name = workbook.createName();
            name.setNameName(positionName);
            name.setRefersToFormula(formula);
        }
    }

    private void createDistrictLevelTemplateSheet(List<TbCdcewPosition> positionList, String[] sexNames, String[] desensitize, Workbook workbook) {
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        Row firstRow = sheet.createRow(0);
        createCellAndSetValue(firstRow, "角色", 0);
        createCellAndSetValue(firstRow, "机构", 1);
        createCellAndSetValue(firstRow, "姓名", 2);
        createCellAndSetValue(firstRow, "性别", 3);
        createCellAndSetValue(firstRow, "手机号", 4);
        createCellAndSetValue(firstRow, "科室", 5);
        createCellAndSetValue(firstRow, "是否脱敏", 6);


        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 8000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 4000);
        sheet.setColumnWidth(6, 4000);


        // 设置角色验证规则
        XSSFDataValidationHelper helper = new XSSFDataValidationHelper(sheet);
        DataValidationConstraint districtConstraint = helper.createExplicitListConstraint(positionList.stream().map(TbCdcewPosition::getPositionName).toArray(String[]::new));
        CellRangeAddressList districtRangeAddressList = new CellRangeAddressList(1, 1000, 0, 0);
        DataValidation districtDataValidation = helper.createValidation(districtConstraint, districtRangeAddressList);

        //验证
        districtDataValidation.createErrorBox("error", "请选择正确角色");
        districtDataValidation.setShowErrorBox(true);
        districtDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(districtDataValidation);

        // 设置性别校验规则
        DataValidationConstraint sexConstraint = helper.createExplicitListConstraint(sexNames);
        CellRangeAddressList sexRangeAddressList = new CellRangeAddressList(1, 1000, 3, 3);
        DataValidation sexDataValidation = helper.createValidation(sexConstraint, sexRangeAddressList);
        sexDataValidation.createErrorBox("error", "请选择正确性别");
        sexDataValidation.setShowErrorBox(true);
        sexDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(sexDataValidation);

        //设置脱敏校验规则
        DataValidationConstraint desensitizeConstraint = helper.createExplicitListConstraint(desensitize);
        CellRangeAddressList desensitizeRangeAddressList = new CellRangeAddressList(1, 1000, 6, 6);
        DataValidation desensitizeDataValidation = helper.createValidation(desensitizeConstraint, desensitizeRangeAddressList);
        desensitizeDataValidation.createErrorBox("error", "请选择正确脱敏类型");
        desensitizeDataValidation.setShowErrorBox(true);
        desensitizeDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(desensitizeDataValidation);

        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
        for (int i = 1; i < 1000; i++) {
            String formula = "INDIRECT($A" + (i + 1) + ")";
            XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper.createFormulaListConstraint(formula);
            CellRangeAddressList regions = new CellRangeAddressList(i, i, 1, 1);
            XSSFDataValidation dataValidation = (XSSFDataValidation) dvHelper.createValidation(dvConstraint, regions);
            dataValidation.setEmptyCellAllowed(false);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);

            // 设置输入信息提示信息
            dataValidation.createErrorBox("下拉选择提示", "请使用下拉方式选择合适的值！");
            sheet.addValidationData(dataValidation);
        }
    }

    private void createCityLevelTemplateSheet(List<TbCdcewPosition> positionList, String[] sexNames, String[] desensitize, List<OrgVO> orgVOList, Workbook workbook) {
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        Row firstRow = sheet.createRow(0);
        createCellAndSetValue(firstRow, "角色", 0);
        createCellAndSetValue(firstRow, "机构", 1);
        createCellAndSetValue(firstRow, "姓名", 2);
        createCellAndSetValue(firstRow, "性别", 3);
        createCellAndSetValue(firstRow, "手机号", 4);
        createCellAndSetValue(firstRow, "科室", 5);
        createCellAndSetValue(firstRow, "是否脱敏", 6);

        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 8000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 4000);

        XSSFDataValidationHelper helper = new XSSFDataValidationHelper(sheet);

        // 设置角色验证规则
        DataValidationConstraint roleConstraint = helper.createExplicitListConstraint(positionList.stream().map(TbCdcewPosition::getPositionName).toArray(String[]::new));
        CellRangeAddressList roleRangeAddressList = new CellRangeAddressList(1, 1000, 0, 0);
        DataValidation roleDataValidation = helper.createValidation(roleConstraint, roleRangeAddressList);
        //验证
        roleDataValidation.createErrorBox("error", "请选择正确角色");
        roleDataValidation.setShowErrorBox(true);
        roleDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(roleDataValidation);

        // 设置性别校验规则
        DataValidationConstraint sexConstraint = helper.createExplicitListConstraint(sexNames);
        CellRangeAddressList sexRangeAddressList = new CellRangeAddressList(1, 1000, 3, 3);
        DataValidation sexDataValidation = helper.createValidation(sexConstraint, sexRangeAddressList);
        sexDataValidation.createErrorBox("error", "请选择正确性别");
        sexDataValidation.setShowErrorBox(true);
        sexDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(sexDataValidation);

        //设置脱敏校验规则
        DataValidationConstraint desensitizeConstraint = helper.createExplicitListConstraint(desensitize);
        CellRangeAddressList desensitizeRangeAddressList = new CellRangeAddressList(1, 1000, 6, 6);
        DataValidation desensitizeDataValidation = helper.createValidation(desensitizeConstraint, desensitizeRangeAddressList);
        desensitizeDataValidation.createErrorBox("error", "请选择正确脱敏类型");
        desensitizeDataValidation.setShowErrorBox(true);
        desensitizeDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(desensitizeDataValidation);


        // 设置机构校验规则
        DataValidationConstraint orgConstraint = helper.createExplicitListConstraint(orgVOList.stream().map(OrgVO::getOrgName).toArray(String[]::new));
        CellRangeAddressList orgRangeAddressList = new CellRangeAddressList(1, 1000, 1, 1);
        DataValidation orgDataValidation = helper.createValidation(orgConstraint, orgRangeAddressList);
        orgDataValidation.createErrorBox("error", "请选择正确机构");
        orgDataValidation.setShowErrorBox(true);
        orgDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(orgDataValidation);
    }

    @Override
    public UploadResultVO uploadFile(MultipartFile file) {
        UploadResultVO uploadResultVO = new UploadResultVO();
        uploadResultVO.setAttachmentId(fileService.upload(file).get(0));
        return uploadResultVO;
    }

    @Override
    @Transactional
    public UploadResultVO batchAdd(String attachmentId, String loginUserName) {

        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (Objects.isNull(org)) {
            throw new MedicalBusinessException("11463001", "用户账号异常,机构不存在,loginUserName:" + loginUserName);
        }

        List<TbCdcewPosition> positionList = positionService.getPositionListByLoginUserName(loginUserName);

        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(attachmentId);
        if (Objects.isNull(tbCdcAttachment)) {
            log.error("附件不存在");
            throw new MedicalBusinessException("11441034", "附件不存在");
        }

        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);

        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(storageObject.getInputStream());
        } catch (IOException e) {
            log.error("读取上传文件失败", e);
            throw new MedicalBusinessException("11441035", "读取上传文件失败");
        }

        XSSFSheet sheet = workbook.getSheet("Sheet1");
        UploadExcelVO batchAddResult;
        List<BusinessPersonVO> businessPersonVOList;
        // 区级用户和市级用户模板不一样 需要分别处理
        // 判定依据： 无 districtCode 即为市级账号
        if (StringUtils.isEmpty(org.getDistrictCode())) {
            List<OrgVO> orgList = orgService.getOrgListForBusinessPerson(loginUserName, null);
            batchAddResult = getCityLevelBatchAddResult(workbook, sheet, positionList, orgList);
            businessPersonVOList = batchAddResult.getBusinessPersonVOList();

            //市级用户设置额外信息
            businessPersonVOList.forEach(businessPersonVO -> {
                OrgVO orgVO = getOrgByOrgName(orgList, businessPersonVO.getStatDimName());

                if (Objects.nonNull(orgVO)) {
                    businessPersonVO.setProvinceCode(orgVO.getProvinceCode());
                    businessPersonVO.setProvinceName(orgVO.getProvinceName());
                    businessPersonVO.setCityCode(orgVO.getCityCode());
                    businessPersonVO.setCityName(orgVO.getCityName());
                    businessPersonVO.setDistrictCode(orgVO.getDistrictCode());
                    businessPersonVO.setDistrictName(orgVO.getDistrictName());
                    businessPersonVO.setStatDimId(orgVO.getOrgId());
                    businessPersonVO.setStatDimName(orgVO.getOrgName());
                }
                businessPersonVO.setPositionCode(getPositionCodeByPositionName(positionList, businessPersonVO.getPositionName()));
            });

        } else {
            // 得到机构的下级机构列表及自身
            List<TbCdcewOrganizationInfo> subOrgList = tbCdcOrganizationInfoMapper.findByHigherOrg(org.getId());
            TbCdcewOrganizationInfo tbCdcOrganization = TbCdcewOrganizationInfo.fromUapOrgPo(org);
            subOrgList.add(tbCdcOrganization);

            batchAddResult = getDistrictLevelBatchAddResult(workbook, sheet, positionList, subOrgList);
            businessPersonVOList = batchAddResult.getBusinessPersonVOList();

            //区级用户设置额外信息
            businessPersonVOList.forEach(businessPersonVO -> {
                businessPersonVO.setProvinceCode(org.getProvinceCode());
                businessPersonVO.setProvinceName(org.getProvince());
                businessPersonVO.setCityCode(org.getCityCode());
                businessPersonVO.setCityName(org.getCity());
                businessPersonVO.setDistrictCode(org.getDistrictCode());
                businessPersonVO.setDistrictName(org.getDistrict());
                businessPersonVO.setStatDimId(getOrgIdByOrgName(subOrgList, businessPersonVO.getStatDimName()));
                businessPersonVO.setStatDimName(businessPersonVO.getStatDimName());
                businessPersonVO.setPositionCode(getPositionCodeByPositionName(positionList, businessPersonVO.getPositionName()));
            });
        }

        // 批量新增用户
        this.batchAdd(businessPersonVOList, loginUserName);

        UploadResultVO uploadResultVO = batchAddResult.getUploadResultVO();
        XSSFWorkbook workbook1 = batchAddResult.getWorkbook();
        ByteArrayOutputStream outputStream = generateOutputStream(workbook1);
        String resultAttachmentId = fileService.upload(outputStream.toByteArray(), tbCdcAttachment.getAttachmentName());
        uploadResultVO.setAttachmentId(attachmentId);
        uploadResultVO.setResultAttachmentId(resultAttachmentId);
        return uploadResultVO;
    }

    // 区级模板校验结果
    private UploadExcelVO getDistrictLevelBatchAddResult(XSSFWorkbook workbook, XSSFSheet sheet, List<TbCdcewPosition> positionList, List<TbCdcewOrganizationInfo> organizationList) {
        List<String> positionNameList = positionList.stream().map(TbCdcewPosition::getPositionName).collect(Collectors.toList());
        List<String> allUserPhoneList = businessPersonMapper.findAllNotDeletedUserPhone();
        List<String> orgNameList = organizationList.stream().map(TbCdcewOrganizationInfo::getOrgName).collect(Collectors.toList());

        // 得到excel中相同的电话号码
        List<String> excelPhoneList = new ArrayList<>();
        List<String> repeatPhoneList = new ArrayList<>();
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {
            Row row = sheet.getRow(index);
            String phone = getStringValue(row.getCell(3));
            excelPhoneList.add(phone);
        }
        Map<String, List<String>> collect = excelPhoneList.stream().collect(Collectors.groupingBy(String::toString));
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                repeatPhoneList.add(k);
            }
        });

        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        List<BusinessPersonVO> businessPersonVOList = new ArrayList<>();
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {

            boolean isSatisfactoryData = true;

            Row row = sheet.getRow(index);
            String positionName = getStringValue(row.getCell(0));
            String orgName = getStringValue(row.getCell(1));
            String name = getStringValue(row.getCell(2));
            String sexName = getStringValue(row.getCell(3));
            String phone = getStringValue(row.getCell(4));
            String deptName = getStringValue(row.getCell(5));
            String desensitize = getStringValue(row.getCell(6));
            List<Integer> errorList = new ArrayList<>();

            StringBuilder sb = new StringBuilder("");

            // 校验角色
            if (StringUtils.isBlank(positionName)) {
                sb.append("角色必须填写;");
                errorList.add(0);
                isSatisfactoryData = false;
            } else if (!positionNameList.contains(positionName)) {
                sb.append("角色名称不存在;");
                errorList.add(0);
                isSatisfactoryData = false;
            }

            // 校验机构
            if (StringUtils.isBlank(orgName)) {
                sb.append("机构必须填写;");
                errorList.add(1);
                isSatisfactoryData = false;
            } else if (!orgNameList.contains(orgName)) {
                sb.append("机构名称不存在;");
                errorList.add(1);
                isSatisfactoryData = false;
            }

            // 校验姓名
            if (StringUtils.isBlank(name)) {
                sb.append("姓名必须填写;");
                errorList.add(2);
                isSatisfactoryData = false;
            } else if (!com.iflytek.fpva.cdc.util.StringUtils.isAllChineseCharacter(name)) {
                sb.append("姓名必须是全中文;");
                errorList.add(2);
                isSatisfactoryData = false;
            } else if (name.length() > 25) {
                sb.append("姓名超长,限制25字;");
                errorList.add(2);
                isSatisfactoryData = false;
            }

            // 校验性别
            if (StringUtils.isBlank(sexName)) {
                sb.append("性别必须填写;");
                errorList.add(3);
                isSatisfactoryData = false;
            } else if (!sexName.equals(Gender.MALE) && !sexName.equals(Gender.FEMALE)) {
                sb.append("性别只能是男或女;");
                errorList.add(3);
                isSatisfactoryData = false;
            }

            // 校验手机号
            if (StringUtils.isBlank(phone)) {
                sb.append("手机号必须填写;");
                errorList.add(4);
                isSatisfactoryData = false;
            }
            if (!StringUtils.isNumeric(phone)) {
                sb.append("手机号必须是数字");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (phone.length() != 11) {
                sb.append("手机号必须是11位");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (allUserPhoneList.contains(phone)) {
                sb.append("用户手机号已在系统中存在");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (repeatPhoneList.contains(phone)) {
                sb.append("不能导入重复的手机号");
                errorList.add(4);
                isSatisfactoryData = false;
            }

            // 校验科室
            if (!StringUtils.isEmpty(deptName) && deptName.length() > 15) {
                sb.append("科室名称超长,限制15字;");
                errorList.add(5);
                isSatisfactoryData = false;
            }

            // 校验脱敏
            if (StringUtils.isBlank(desensitize)) {
                sb.append("脱敏必须填写;");
                errorList.add(6);
                isSatisfactoryData = false;
            } else if (!desensitize.equals(Desensitize.DESENSITIZE) && !desensitize.equals(Desensitize.NONDESENSITIZE)) {
                sb.append("脱敏只能是脱敏或不脱敏;");
                errorList.add(6);
                isSatisfactoryData = false;
            }

            // 添入报错信息
            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {
                failedCount++;
                createCellAndSetValue(row, errorMsg, 7);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

            if (isSatisfactoryData) {
                BusinessPersonVO businessPersonVO = new BusinessPersonVO();
                businessPersonVO.setPositionName(positionName);
                businessPersonVO.setName(name);
                businessPersonVO.setSexName(sexName);
                businessPersonVO.setPhone(phone);
                businessPersonVO.setStatDimName(orgName);
                businessPersonVO.setDeptName(deptName);
                businessPersonVO.setStatus(1);
                if (desensitize.equals(Desensitize.DESENSITIZE)) {
                    businessPersonVO.setDesensitize(1);
                } else if (desensitize.equals(Desensitize.NONDESENSITIZE)) {
                    businessPersonVO.setDesensitize(0);
                }
                businessPersonVOList.add(businessPersonVO);
            }

        }


        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);

        UploadExcelVO uploadExcelVO = new UploadExcelVO();
        uploadExcelVO.setUploadResultVO(resultVO);
        uploadExcelVO.setWorkbook(workbook);
        uploadExcelVO.setBusinessPersonVOList(businessPersonVOList);
        return uploadExcelVO;
    }

    // 市级模板校验结果
    private UploadExcelVO getCityLevelBatchAddResult(XSSFWorkbook workbook, XSSFSheet sheet, List<TbCdcewPosition> positionList, List<OrgVO> orgList) {
        List<String> positionNameList = positionList.stream().map(TbCdcewPosition::getPositionName).collect(Collectors.toList());
        List<String> allUserPhoneList = businessPersonMapper.findAllNotDeletedUserPhone();
        List<String> orgNameList = orgList.stream().map(OrgVO::getOrgName).collect(Collectors.toList());

        // 得到excel中相同的电话号码
        List<String> excelPhoneList = new ArrayList<>();
        List<String> repeatPhoneList = new ArrayList<>();
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {
            Row row = sheet.getRow(index);
            String phone = getStringValue(row.getCell(3));
            excelPhoneList.add(phone);
        }
        Map<String, List<String>> collect = excelPhoneList.stream().collect(Collectors.groupingBy(String::toString));
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                repeatPhoneList.add(k);
            }
        });

        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        List<BusinessPersonVO> businessPersonVOList = new ArrayList<>();
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {

            boolean isSatisfactoryData = true;

            Row row = sheet.getRow(index);
            String positionName = getStringValue(row.getCell(0));
            String orgName = getStringValue(row.getCell(1));
            String name = getStringValue(row.getCell(2));
            String sexName = getStringValue(row.getCell(3));
            String phone = getStringValue(row.getCell(4));
            String deptName = getStringValue(row.getCell(5));
            String desensitize = getStringValue(row.getCell(6));


            List<Integer> errorList = new ArrayList<>();

            StringBuilder sb = new StringBuilder("");

            // 校验角色
            if (StringUtils.isBlank(positionName)) {
                sb.append("角色必须填写;");
                errorList.add(0);
                isSatisfactoryData = false;
            } else if (!positionNameList.contains(positionName)) {
                sb.append("角色名称不存在;");
                errorList.add(0);
                isSatisfactoryData = false;
            }

            // 校验机构
            if (StringUtils.isBlank(orgName)) {
                sb.append("机构必须填写;");
                errorList.add(1);
                isSatisfactoryData = false;
            } else if (!orgNameList.contains(orgName)) {
                sb.append("机构名称不存在;");
                errorList.add(1);
                isSatisfactoryData = false;
            }

            // 校验姓名
            if (StringUtils.isBlank(name)) {
                sb.append("姓名必须填写;");
                errorList.add(2);
                isSatisfactoryData = false;
            } else if (!com.iflytek.fpva.cdc.util.StringUtils.isAllChineseCharacter(name)) {
                sb.append("姓名必须是全中文;");
                errorList.add(2);
                isSatisfactoryData = false;
            } else if (name.length() > 25) {
                sb.append("姓名超长,限制25字;");
                errorList.add(2);
                isSatisfactoryData = false;
            }

            // 校验性别
            if (StringUtils.isBlank(sexName)) {
                sb.append("性别必须填写;");
                errorList.add(3);
                isSatisfactoryData = false;
            } else if (!sexName.equals(Gender.MALE) && !sexName.equals(Gender.FEMALE)) {
                sb.append("性别只能是男或女;");
                errorList.add(3);
                isSatisfactoryData = false;
            }


            // 校验手机号
            if (StringUtils.isBlank(phone)) {
                sb.append("手机号必须填写;");
                errorList.add(4);
                isSatisfactoryData = false;
            }
            if (!StringUtils.isNumeric(phone)) {
                sb.append("手机号必须是数字");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (phone.length() != 11) {
                sb.append("手机号必须是11位;");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (allUserPhoneList.contains(phone)) {
                sb.append("用户手机号已在系统中存在;");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (repeatPhoneList.contains(phone)) {
                sb.append("不能导入重复的手机号;");
                errorList.add(4);
                isSatisfactoryData = false;
            }

            // 校验科室
            if (!StringUtils.isEmpty(deptName) && deptName.length() > 15) {
                sb.append("科室名称超长,限制15字;");
                errorList.add(5);
                isSatisfactoryData = false;
            }

            // 校验脱敏
            if (StringUtils.isBlank(desensitize)) {
                sb.append("脱敏必须填写;");
                errorList.add(6);
                isSatisfactoryData = false;
            } else if (!desensitize.equals(Desensitize.DESENSITIZE) && !desensitize.equals(Desensitize.NONDESENSITIZE)) {
                sb.append("脱敏只能是脱敏或不脱敏;");
                errorList.add(6);
                isSatisfactoryData = false;
            }

            // 添入报错信息
            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {
                failedCount++;
                createCellAndSetValue(row, errorMsg, 7);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

            if (isSatisfactoryData) {
                BusinessPersonVO businessPersonVO = new BusinessPersonVO();
                businessPersonVO.setPositionName(positionName);
                businessPersonVO.setStatDimName(orgName);
                businessPersonVO.setName(name);
                businessPersonVO.setSexName(sexName);
                businessPersonVO.setPhone(phone);
                businessPersonVO.setDeptName(deptName);
                businessPersonVO.setStatus(1);
                if (desensitize.equals(Desensitize.DESENSITIZE)) {
                    businessPersonVO.setDesensitize(1);
                } else {
                    businessPersonVO.setDesensitize(0);
                }
                businessPersonVOList.add(businessPersonVO);
            }
        }


        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);

        UploadExcelVO uploadExcelVO = new UploadExcelVO();
        uploadExcelVO.setUploadResultVO(resultVO);
        uploadExcelVO.setWorkbook(workbook);
        uploadExcelVO.setBusinessPersonVOList(businessPersonVOList);
        return uploadExcelVO;
    }

    public List<TbCdcewPersonDataAuth> getSyndromeDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getSyndromeDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);

                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_SYNDROME == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());

                dealNoDataAuth(dataAuths, businessPerson);
            }

        }

        return dataAuths;


    }

    /**
     * 处理表中没有数据权限的
     * @param dataAuths
     * @param businessPerson
     */
    private static void dealNoDataAuth(List<TbCdcewPersonDataAuth> dataAuths, TbCdcewBusinessPerson businessPerson) {
        //数据权限为空时    1.系统管理员，拥有所有  2.历史的报卡管理员,没有数据（脚本刷数据进去）;  报卡管理员 预警管理员 表中无记录就是没有数据权限
        if (PlatformPositionEnum.REPORTCARD_TOTAL_ADMIN.getCode().equals(businessPerson.getPositionCode())){
            dataAuths.clear();
        } else if (CollectionUtils.isEmpty(dataAuths) &&
                (PlatformPositionEnum.PLATFORM_ADMIN.getCode().equals(businessPerson.getPositionCode())
                  || PlatformPositionEnum.REPORTCARD_ADMIN.getCode().equals(businessPerson.getPositionCode()))){
            TbCdcewPersonDataAuth dataAuth = new TbCdcewPersonDataAuth();
            dataAuth.setSymptomId(CommonConstants.NOT_DATA_AUTH);
            dataAuth.setSymptomName(CommonConstants.NOT_DATA_VAL);
            dataAuths.add(dataAuth);
        }
    }

    public List<TbCdcewPersonDataAuth> getInfectedDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getInfectedDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);
                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_INFECTIOUS_DISEASE == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }

        }
        return dataAuths;

    }

    public List<TbCdcewPersonDataAuth> getSymptomDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getSchoolSymptomDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());

        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);
                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_SCHOOL_SYMPTOM == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }
        }
        return dataAuths;
    }

    public List<TbCdcewPersonDataAuth> getPoisonDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getPoisonDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());

        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);

                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_POISON == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }
        }
        return dataAuths;
    }

    public List<TbCdcewPersonDataAuth> getOutpatientDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getOutpatientDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);
                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_OUTPATIENT == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }

        }
        return dataAuths;
    }

    public List<TbCdcewPersonDataAuth>  getUnknownReasonDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getUnknownReasonDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);
                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_UNKNOWN_REASON == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }
        }
        return dataAuths;
    }

    public List<TbCdcewPersonDataAuth> getPreventionControlDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getPreventionControlDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);
                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_PREVENTION_CONTROL == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }
        }
        return dataAuths;
    }

    public List<TbCdcewPersonDataAuth> getCustomizedDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            dataAuths = adminServiceApi.getCustomizedDataAuthByLoginUserId(loginUserId).stream().map(TbCdcewPersonDataAuth::fromEntity).collect(Collectors.toList());
        } else {
            List<TbCdcewBusinessPerson> businessPersonList = businessPersonMapper.findByUapUserIdAndStatus(loginUserId, CommonConstants.STATUS_VALID);
            if (!CollectionUtils.isEmpty(businessPersonList)) {
                TbCdcewBusinessPerson businessPerson = businessPersonList.get(0);
                dataAuths = tbCdcewPersonDataAuthMapper.findByBusinessPersonId(businessPerson.getId());
                dataAuths = dataAuths.stream().filter(tbCdcewPersonDataAuth -> CommonConstants.DATA_AUTH_CUSTOMIZED == tbCdcewPersonDataAuth.getDataType()).collect(Collectors.toList());
                dealNoDataAuth(dataAuths, businessPerson);
            }
        }
        return dataAuths;
    }

    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        cell.setCellValue(value);
    }

    private String getFormula(String sheetName, int start, int end, char index) {
        StringBuilder formula = new StringBuilder();
        formula.append(sheetName)
                .append("!")
                .append("$")
                .append(index)
                .append("$")
                .append(start)
                .append(":")
                .append("$")
                .append(index)
                .append("$")
                .append(end);
        return formula.toString();
    }

    private ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11441036", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    private String getStringValue(Cell cell) {

        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:  //字符串类型
                return cell.getStringCellValue();
            case BOOLEAN:   //布尔类型
                return String.valueOf(cell.getBooleanCellValue());
            case NUMERIC:  //数值类型
                return String.format("%.0f", cell.getNumericCellValue());
            default:
                return null;
        }

    }


    private String getPositionCodeByPositionName(List<TbCdcewPosition> list, String positionName) {
        List<TbCdcewPosition> collect = list.stream().filter(tbCdcewPosition -> positionName.equals(tbCdcewPosition.getPositionName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            return collect.get(0).getPositionCode();
        }
        return "";
    }

    private String getOrgIdByOrgName(List<TbCdcewOrganizationInfo> list, String orgName) {
        List<TbCdcewOrganizationInfo> collect = list.stream().filter(tbCdcewOrganizationInfo -> orgName.equals(tbCdcewOrganizationInfo.getOrgName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            return collect.get(0).getOrgId();
        }
        return "";
    }

    private OrgVO getOrgByOrgName(List<OrgVO> list, String orgName) {
        List<OrgVO> collect = list.stream().filter(orgVO -> orgName.equals(orgVO.getOrgName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            return collect.get(0);
        }
        return null;
    }


    @Override
    public List<String> getConfiguredDiseaseCodesByAuth(String loginUserId, String warnTypeCode) {
        List<TbCdcewPersonDataAuth> dataAuths = new ArrayList<>();
        List<String> allList = new ArrayList<>();
        if (WarningTypeCodeEnum.SYNDROME.getType().equals(warnTypeCode)) {
            dataAuths = getSyndromeDataAuthByLoginUserId(loginUserId);
            List<TbCdcewSyndrome> dbList = tbCdcewSyndromeMapper.listAll();
            allList = dbList.stream().map(TbCdcewSyndrome::getSyndromeCode).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.INFECTIOUS.getType().equals(warnTypeCode)) {
            dataAuths = getInfectedDataAuthByLoginUserId(loginUserId);
            final List<TbCdcewInfectedInfo> dbList = tbCdcewInfectedInfoMapper.findAll();
            allList = dbList.stream().map(TbCdcewInfectedInfo::getInfectedCode).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.SC_SYMPTOM.getType().equals(warnTypeCode)) {
            dataAuths = getSymptomDataAuthByLoginUserId(loginUserId);
            final List<TbCdcewSympSymptom> dbList = tbCdcewSympSymptomMapper.findAll();
            allList = dbList.stream().map(TbCdcewSympSymptom::getSymptomCode).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.POISON.getType().equals(warnTypeCode)) {
            dataAuths = getPoisonDataAuthByLoginUserId(loginUserId);
            final List<TbCdcewPoisonInfo> dbList = tbCdcewPoisonInfoMapper.findAll();
            allList = dbList.stream().map(TbCdcewPoisonInfo::getPoisonCode).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.OUTPATIENT.getType().equals(warnTypeCode)) {
            dataAuths = getOutpatientDataAuthByLoginUserId(loginUserId);
            final List<CascadeVO> dbList = restService.getOutpatientType();
            allList = dbList.stream().map(CascadeVO::getValue).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.UNKNOWN_REASON.getType().equals(warnTypeCode)) {
            dataAuths = getUnknownReasonDataAuthByLoginUserId(loginUserId);
            final List<TbCdcewUnknownReasonDisease> dbList = tbCdcewUnknownReasonDiseaseMapper.getAll();
            allList = dbList.stream().map(TbCdcewUnknownReasonDisease::getDiseaseCode).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.PREVENTION_CONTROL.getType().equals(warnTypeCode)) {
            dataAuths = getPreventionControlDataAuthByLoginUserId(loginUserId);
            final List<CascadeVO> dbList = preventionControlEventService.getPreventionControl();
            allList = dbList.stream().map(CascadeVO::getValue).distinct().collect(Collectors.toList());
        } else if (WarningTypeCodeEnum.CUSTOMIZED.getType().equals(warnTypeCode)) {
            dataAuths = getCustomizedDataAuthByLoginUserId(loginUserId);
            final List<CascadeVO> dbList = restService.getCustomizedWarnTaskCascadeList();
            allList = dbList.stream().map(CascadeVO::getValue).distinct().collect(Collectors.toList());
        } else {
            throw new MedicalBusinessException("11441017", "未知的warningTypeCode");
        }

        if (CollectionUtils.isEmpty(dataAuths)){
            return allList;
        }

        final List<String> tempList = allList;
        final List<String> dbAuthList = dataAuths.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());

        final List<String> collect = dbAuthList.stream()
                .filter(v -> tempList.contains(v))
                .collect(Collectors.toList());
        return  CollectionUtils.isEmpty(collect) ? dbAuthList: collect;
    }

    @Override
    public List<String> getCompletedDiseaseCodesByAuth(String loginUserId, String warnTypeCode) {
        List<String> diseaseCodes = new ArrayList<>();
        //1，获取配置的病种代码
        List<String> configuredDiseaseCodes = getConfiguredDiseaseCodesByAuth(loginUserId, warnTypeCode);
        //2，查询符合条件的病种子类
        if (WarningTypeCodeEnum.SYNDROME.getType().equals(warnTypeCode)) {
            if (!CollectionUtils.isEmpty(configuredDiseaseCodes)) {
                diseaseCodes = new ArrayList<>(cdcEventService.getSymptomsFromSyndromes(configuredDiseaseCodes));
            }
        } else if (WarningTypeCodeEnum.INFECTIOUS.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(cdcInfectedEventService.getInfectedCodeList(configuredDiseaseCodes));
        } else if (WarningTypeCodeEnum.SC_SYMPTOM.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(configuredDiseaseCodes);
        } else if (WarningTypeCodeEnum.POISON.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(configuredDiseaseCodes);
        } else if (WarningTypeCodeEnum.OUTPATIENT.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(configuredDiseaseCodes);
        } else if (WarningTypeCodeEnum.UNKNOWN_REASON.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(configuredDiseaseCodes);
        } else if (WarningTypeCodeEnum.PREVENTION_CONTROL.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(configuredDiseaseCodes);
        } else if (WarningTypeCodeEnum.CUSTOMIZED.getType().equals(warnTypeCode)) {
            diseaseCodes = new ArrayList<>(configuredDiseaseCodes);
        } else {
            throw new MedicalBusinessException("11441017", "未知的warningTypeCode");
        }
        return diseaseCodes;
    }

    public void addChildrenPoison(List<TbCdcewPoisonInfo> poisonInfos,
                                  List<TbCdcewPoisonInfo> result,
                                  Map<String, List<TbCdcewPoisonInfo>> poisonParentMap){

        for (TbCdcewPoisonInfo poisonInfo : poisonInfos){
            result.add(poisonInfo);
            if (poisonParentMap.containsKey(poisonInfo.getPoisonCode())){
                addChildrenPoison(poisonParentMap.get(poisonInfo.getPoisonCode()), result, poisonParentMap);
            }
        }
    }

    @Override
    public List<String> getQueryDiseaseCodeBy(List<String> diseaseTypes, List<String> symptom, String loginUserId, String warnTypeCode) {

        List<String> authDiseaseCodes = getCompletedDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.getTypeByName(warnTypeCode));
        switch (warnTypeCode){
            case Constants.SC_SYMPTOM:
            case Constants.OUTPATIENT:
            case Constants.UNKNOWN_REASON:
            case Constants.PREVENTION_CONTROL:
            case Constants.CUSTOMIZED:
                //症状、门诊、不明原因、联防联控、自定义没有子类
                //如果有传参，结果取权限code与查询code的交集；否则直接取权限中的疾病code
                return CollectionUtils.isEmpty(diseaseTypes) ? authDiseaseCodes : com.iflytek.fpva.cdc.util.StringUtils.intersection(diseaseTypes, authDiseaseCodes);
            case Constants.SYNDROME:
                //不传参数，查询该用户权限下所有的病种code
                if(CollectionUtils.isEmpty(diseaseTypes) && CollectionUtils.isEmpty(symptom)){
                    return authDiseaseCodes;
                }
                //子类code为空时，查询顶层code下所有的子类，然后与该用户的权限code取交集
                if(CollectionUtils.isEmpty(symptom)){
                    List<String> infectedCodes = syndromeWarningEventMapper.getAllDiseaseCodeBySyndrome(diseaseTypes);
                    return com.iflytek.fpva.cdc.util.StringUtils.intersection(infectedCodes, authDiseaseCodes);
                }
                //若子类不为空，则按照子类code和疾病的权限code交集进行查询
                return com.iflytek.fpva.cdc.util.StringUtils.intersection(symptom, authDiseaseCodes);
            case Constants.INFECTIOUS:
                //不传参数，查询该用户权限下所有的病种code
                if(CollectionUtils.isEmpty(diseaseTypes) && CollectionUtils.isEmpty(symptom)){
                    return authDiseaseCodes;
                }
                //子类code为空时，查询顶层code下所有的子类，然后与该用户的权限code取交集
                if(CollectionUtils.isEmpty(symptom)){
                    List<String> infectedCodes = infectedWarningEventMapper.getAllDiseaseCodeByInfectedTypeCode(diseaseTypes);
                    return com.iflytek.fpva.cdc.util.StringUtils.intersection(infectedCodes, authDiseaseCodes);
                }
                //若子类不为空，则按照子类code和疾病的权限code交集进行查询
                return com.iflytek.fpva.cdc.util.StringUtils.intersection(symptom, authDiseaseCodes);
            case Constants.POISON:
                //中毒权限分配在父类，这里根据父类获取所有中毒code
                authDiseaseCodes = getPoisonCodes(authDiseaseCodes);
                //不传参数，查询该用户权限下所有的病种code
                if(CollectionUtils.isEmpty(diseaseTypes) && CollectionUtils.isEmpty(symptom)){
                    return authDiseaseCodes;
                }
                //子类code为空时，查询顶层code下所有的子类，然后与该用户的权限code取交集
                if(CollectionUtils.isEmpty(symptom)){
                    List<String> poisonCodes = poisonWarningEventMapper.getAllDiseaseCodeByPoisonTypeCode(diseaseTypes);
                    poisonCodes.addAll(diseaseTypes);
                    return com.iflytek.fpva.cdc.util.StringUtils.intersection(poisonCodes, authDiseaseCodes);
                }
                //若子类不为空，则按照子类code和疾病的权限code交集进行查询
                return com.iflytek.fpva.cdc.util.StringUtils.intersection(symptom, authDiseaseCodes);
            default:
                break;
        }
        return new ArrayList<>();
    }

    /**
     * 中毒权限分配在父类， 这里根据父类code拿到所有子类code
     * */
    public List<String> getPoisonCodes(List<String> codes){
        if (codes == null || codes.size() == 0){
            return new ArrayList<>();
        }
        List<TbCdcewPoisonInfo> poisonInfoList = tbCdcewPoisonInfoMapper.findAll();
        Map<String, List<TbCdcewPoisonInfo>> poisonParentMap = poisonInfoList.stream()
                                                                             .filter(p -> !p.getPoisonCode().equals(p.getPoisonParentCode()))
                                                                             .collect(Collectors.groupingBy(TbCdcewPoisonInfo::getPoisonParentCode));
        List<TbCdcewPoisonInfo> result = new ArrayList<>();
        List<TbCdcewPoisonInfo> filter =  poisonInfoList.stream().filter(p -> codes.contains(p.getPoisonCode())).collect(Collectors.toList());
        addChildrenPoison(filter, result, poisonParentMap);
        return result.stream().map(TbCdcewPoisonInfo::getPoisonCode).collect(Collectors.toList());
    }
}

package com.iflytek.fpva.cdc.service.outcall;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallOrginalResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 外呼原始结果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22
 */
public interface OutcallOrginalResultService extends IService<TbCdcewOutcallOrginalResult> {

    void saveData(String callBatchId, String jsonString, String type);
}

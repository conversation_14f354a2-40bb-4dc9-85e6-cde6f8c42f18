package com.iflytek.fpva.cdc.service.common;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.iflytek.fpva.cdc.common.utils.ApiTool;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedWarnVO;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedWarningTaskVO;
import com.iflytek.fpva.cdc.model.dto.*;
import com.iflytek.fpva.cdc.model.dto.admin.*;
import com.iflytek.fpva.cdc.model.dto.infected.*;
import com.iflytek.fpva.cdc.model.dto.mdm.PatientBaseInfo;
import com.iflytek.fpva.cdc.model.dto.mdm.PatientDetailResult;
import com.iflytek.fpva.cdc.model.dto.outpatient.OutpatientMappingResult;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMappingResult;
import com.iflytek.fpva.cdc.model.dto.school.DailyRecordDtoParam;
import com.iflytek.fpva.cdc.model.dto.school.DailyRecordParam;
import com.iflytek.fpva.cdc.model.dto.school.SchoolParams;
import com.iflytek.fpva.cdc.model.dto.suspect.DwdChMedicalSuspectRecordDto;
import com.iflytek.fpva.cdc.model.dto.suspect.SuspectRequestParam;
import com.iflytek.fpva.cdc.model.dto.syndrome.*;
import com.iflytek.fpva.cdc.model.epi.EpiResponse;
import com.iflytek.fpva.cdc.model.epi.EpiResultDto;
import com.iflytek.fpva.cdc.model.epi.EpiResultResponse;
import com.iflytek.fpva.cdc.model.epi.RecordWarnCreateVO;
import com.iflytek.fpva.cdc.model.po.*;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.client.*;
import com.iflytek.fpva.cdc.model.vo.es.EsTaskResult;
import com.iflytek.fpva.cdc.model.vo.es.EsTaskVO;
import com.iflytek.fpva.cdc.model.vo.infected.InfectedCascadeVO;
import com.iflytek.fpva.cdc.model.vo.monitor.EventMonitorDetailVO;
import com.iflytek.fpva.cdc.service.infected.CdcReportCardService;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.HttpClientUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.uap.ext.pojo.UapMdmEmpUserDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapUserExtendDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RestService {

    @Value("${usercenter.admin.loginName:admin}")
    private String uapAdminLoginName;

    @Value("${adminRule.regionCode:340100}")
    private String regionCode;

    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;
    @Value("${uap-service-ext-service:uap-service-ext-service}")
    private String uapService;
    @Value("${cdc-admin-service:cdc-admin-service}")
    private String cdcAdminService;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HttpClientUtils httpClientUtils;

    @Resource
    private ApiTool apiTool;
    @Autowired
    private CdcReportCardService cdcReportCardService;

    private static final String EI_POSITION_SWITCH_CODE = "cdc-platform-service:ei-position-switch";

    private static final String SMS_AUDIT_CODE = "cdc-platform-service:sms-audit-switch";
    private static final String AI_CONFIRM_SWITCH = "cdc-platform-service:ai-confirm-switch";
    private static final String AI_SCREEN_TOP_SWITCH = "cdc-platform-service:ai-screen-top-switch";
    private static final String SYNDROME_AI_SCREEN_PHONE = "cdc-platform-service:syndrome-ai-screen-top-phone";
    private static final String INFECTED_AI_SCREEN_PHONE = "cdc-platform-service:infected-ai-screen-top-phone";
    private static final String POISON_AI_SCREEN_PHONE = "cdc-platform-service:poison-ai-screen-top-phone";
    private static final String UNKNOWN_REASON_AI_SCREEN_PHONE = "cdc-platform-service:unknown-reason-ai-screen-top-phone";
    private static final String CUSTOMIZED_AI_SCREEN_PHONE = "cdc-platform-service:customized-ai-screen-top-phone";
    private static final String EPI_SWITCH = "auth:epi";
    private static final String SC_SYMPTOM_AI_SCREEN_PHONE = "cdc-platform-service:sc-symptom-ai-screen-top-phone";
    private static final String OUTPATIENT_AI_SCREEN_PHONE = "cdc-platform-service:outpatient-ai-screen-top-phone";
    private static final String PREVENTION_CONTROL_AI_SCREEN_PHONE = "cdc-platform-service:prevention-control-ai-screen-top-phone";
    private static final String USER_EXIST_WARN = "用户名已存在";
    private static final String EXPORT_MAX_CODE = "cdc-platform-service:export-max";
    private static final int EXPORT_MAX_VALUE = 10000;

    private static final String RCE_MSG_SWITCH_CODE = "cdc-platform-service:rce-msg-switch";

    private static final String CSV_TASK_MAX_CODE = "cdc-platform-service:csv_task_max";
    private static final int CSV_TASK_MAX_VALUE = 20;

    private static final String CSV_TASK_MAX_TIMEOUT_CODE = "cdc-platform-service:csv_task_max_timeout";
    /**
     * 默认超时 5分钟
     */
    private static final Long CSV_TASK_MAX_TIMEOUT_VALUE = 300L;
    private static final String CSV_TASK_MAX_PAGE_SIZE_CODE = "cdc-platform-service:csv_task_max_page_size";
    /**
     * 默认分页大小10000条
     */
    private static final Integer CSV_TASK_MAX_PAGE_SIZE_VALUE = 10000;

    public UapOrgPo getUserOrg(String loginUserName) {
        //获取用户所在机构 判断级别 组装数据
        //2020-12-29 rest调用获取机构
        String userUrl = "http://" + uapService + "/v1/pv/TUapUser/withPassword/" + loginUserName;
        String orgId = Objects.requireNonNull(apiTool.doGet(userUrl, UapUserPo.class)).getOrgId();
        if (userCenterVersion.equals("5.0.0")) {
            String orgUrl = "http://" + uapService + "/v1/pv/TUapOrganization/byDepartmentId?departmentId=" + orgId;
            return apiTool.doGet(orgUrl, UapOrgPo.class);
        } else {
            String orgUrl = "http://" + uapService + "/v1/pt/TUapOrganization/" + orgId;
            return Objects.requireNonNull(apiTool.doGet(orgUrl, JSONObject.class)).getObject("uapOrganization", UapOrgPo.class);
        }
    }

    public UapOrganization getOrgById(String orgId) {
        String url = "http://uap-service-ext-service/v1/pt/TUapOrganization/" + orgId;
        JSONObject result = restTemplate.getForObject(url, JSONObject.class);
        return result.getObject("uapOrganization", UapOrganization.class);
    }


    public UapUserPo getUser(String loginUserId) {
        String url = "http://uap-service-ext-service/v1/pt/TUapUser/" + loginUserId;
        JSONObject jsonObject = restTemplate.getForObject(url, JSONObject.class);
        return Optional.ofNullable(jsonObject).map(item -> item.getObject("uapUser", UapUserPo.class)).orElse(new UapUserPo());
    }

    public List<UapUserPo> getUserList(List<String> loginUserIds) {
        String url = "http://uap-service-ext-service/v1/pt/TUapUser/ext/search?loginUserId=" + getUapUserIdByName(uapAdminLoginName);

        UapQueryDto uapQueryDto = new UapQueryDto();
        uapQueryDto.setPageIndex(1);
        uapQueryDto.setPageNumber(1);
        uapQueryDto.setPageSize(Integer.MAX_VALUE);
        UapQueryDto.Filter filter = new UapQueryDto.Filter();
        filter.setUserIds(loginUserIds);
        uapQueryDto.setFilter(filter);

        JSONObject jsonObject = restTemplate.postForObject(url, uapQueryDto, JSONObject.class);

        AddUapUserPo[] entities = Optional.ofNullable(jsonObject).map(item -> item.getObject("entities", AddUapUserPo[].class)).orElse(new AddUapUserPo[]{});
        return Arrays.stream(entities).map(AddUapUserPo::getUapUser).collect(Collectors.toList());
    }


    public String[] getAdminPhone() {
        String url = "http://uap-service-ext-service/v1/pt/TUapAuthDimDict/tree?nameCode=CDCManagerConfig";
        JSONObject jsonObject = restTemplate.getForObject(url, JSONObject.class);
        JSONArray resultArray = Optional.ofNullable(jsonObject).map(item -> item.getJSONArray("nodes")).orElse(new JSONArray());
        List<JSONObject> objectList = resultArray.toJavaList(JSONObject.class);
        for (JSONObject object : objectList) {
            if ("ManagerPhone".equals(object.getString("name"))) {
                return object.getString("value").split("\\|");
            }
        }
        return new String[0];
    }


    public List<UapAuthNode> getMenu(String loginUserId) {
        try {
            String url = "http://uap-service-ext-service/v1/pt/TUapAuthority/user/tree?appCode=cdc-manager&loginUserId=" + loginUserId;
            UapAuthNode result = restTemplate.getForObject(url, UapAuthNode.class);
            String authUrl = "http://uap-service-ext-service/v1/pt/TUapAuthority/auth/search?appCode=cdc-manager&loginUserId=" + loginUserId;
            JSONArray authArray = restTemplate.getForObject(authUrl, JSONArray.class);
            List<UapAuth> authList = authArray.toJavaList(UapAuth.class);
            setAuth(result.getNodes(), authList);
            return result.getNodes();
        } catch (Exception e) {
            throw new MedicalBusinessException("获取菜单失败");
        }
    }

    public CascadeVO getOrgTree(String loginUserName) {
        return getSubOrgTree(getUserOrg(loginUserName).getCode());
    }

    public CascadeVO getSubOrgTree(String code) {
        String url = "http://uap-service-ext-service/v1/pt/TUapOrganization/tree?code=" + code;
        JSONObject result = restTemplate.getForObject(url, JSONObject.class);
        if (result != null) {
            return new GsonBuilder().create().fromJson(result.toJSONString(), CascadeVO.class);
        } else {
            return null;
        }
    }

    public List<EsTaskVO> getEiTaskResult(List<String> eventIds) {
        if (CollectionUtils.isEmpty(eventIds)) {
            return new ArrayList<>();
        }

        String eventRequest = "";
        for (String eventId : eventIds) {
            eventRequest = eventRequest.concat("signalIdList=").concat(eventId).concat("&");
        }

        String url = "http://cdc-epi-invest-service/v1/pt/task/queryInvestTimeList?" + eventRequest;

        log.info(url);
        List<EsTaskVO> result = new ArrayList<>();
        try{
            JSONObject forObject = restTemplate.getForObject(url, JSONObject.class);
            EsTaskResult[] entities = Optional.ofNullable(forObject).map(item -> item.getObject("data", EsTaskResult[].class)).orElse(new EsTaskResult[]{});
            for (EsTaskResult esTaskResult : entities) {
                EsTaskVO esTaskVO = new EsTaskVO();

                esTaskVO.setEventId(esTaskResult.getSignalId());
                esTaskVO.setCreateTime(DateFormatUtils.formatDate(esTaskResult.getBeginTime(), TimeConstant.NORM_DATETIME_PATTERN));
                esTaskVO.setFinishTime(DateFormatUtils.formatDate(esTaskResult.getFinishTime(), TimeConstant.NORM_DATETIME_PATTERN));

                result.add(esTaskVO);
            }
        }catch (Exception e){
            log.info(e.toString());
        }
        return result;
    }
    // 查询流调结果
    public EpiResultDto getEpiResult(String eventId) {
        log.info("开始获取流调结果，eventId:" + eventId);
        String url = "http://cdc-epi-invest-service/v1/pt/task/queryProcessStatusBySignalId?signalId=" + eventId;

        try {
            EpiResultResponse epiResponse = restTemplate.getForObject(url, EpiResultResponse.class);
            assert epiResponse != null;
            if ("200".equals(epiResponse.getCode())) {
                EpiResultDto data = epiResponse.getData();

                log.info("获取流调结果成功，eventId:" + eventId);
                return data;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取流调结果失败，eventId:" + eventId);
        }

        return null;
    }


    public PageInfo<IntelligentWarningListVO> getIntelligentWarningList(IntelligentWarningListQueryVO queryVO, String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/intelligentWarningList?loginUserId=" + loginUserId;
        PageInfo pageInfo = restTemplate.postForObject(url, queryVO, PageInfo.class);
        List list = pageInfo.getList();

        PageInfo<IntelligentWarningListVO> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPage);
        List<IntelligentWarningListVO> result = new ArrayList<>();
        for (Object object : list) {
            String s = JSON.toJSONString(object);
            IntelligentWarningListVO intelligentWarningListVO = JSONObject.parseObject(s, IntelligentWarningListVO.class);
            result.add(intelligentWarningListVO);
        }
        resultPage.setList(result);
        return resultPage;
    }

    public Object getVerifyCount(String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/getVerifyCount?loginUserId=" + loginUserId;
        return restTemplate.getForObject(url, Object.class);
    }

    public List<IntelligentWarningChecksVO> getIntelligentWarningChecks(String eventId, String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/getIntelligentWarningChecks?eventId=" + eventId + "&loginUserId=" + loginUserId;
        IntelligentWarningChecksVO[] forObject = restTemplate.getForObject(url, IntelligentWarningChecksVO[].class);
        assert forObject != null;
        return Arrays.asList(forObject);
    }

    public IntelligentWarningCheckDetailVO getWarningCheckDetail(String checkId, String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/getWarningCheckDetail?checkId=" + checkId + "&loginUserId=" + loginUserId;
        return restTemplate.getForObject(url, IntelligentWarningCheckDetailVO.class);
    }

    public List<IntelligentWarningInspectionVO> getIntelligentWarnInspections(String eventId, String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/getIntelligentWarnInspections?eventId=" + eventId + "&loginUserId=" + loginUserId;
        IntelligentWarningInspectionVO[] forObject = restTemplate.getForObject(url, IntelligentWarningInspectionVO[].class);
        assert forObject != null;
        return Arrays.asList(forObject);
    }

    public List<IWarnInspectionDetailVO> getIWarnInspectionDetail(String inspectId, String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/getIWarnInspectionDetail?inspectId=" + inspectId + "&loginUserId=" + loginUserId;
        IWarnInspectionDetailVO[] forObject = restTemplate.getForObject(url, IWarnInspectionDetailVO[].class);
        assert forObject != null;
        return Arrays.asList(forObject);
    }

    public IntelligentWarningDetailsVO intelligentWarningDetails(String eventId, String loginUserId) {
        String url = "http://cdc-client-service/v1/pt/ai/intelligentWarningDetails?eventId=" + eventId + "&loginUserId=" + loginUserId;
        return restTemplate.getForObject(url, IntelligentWarningDetailsVO.class);
    }

    public List<InfectedCascadeVO> getInfectedConfigList() {
        String url = "http://cdc-admin-service/v1/pt/infectious/queryInfecDisTree";
        String result = restTemplate.getForObject(url, String.class);
        if (result != null) {
            List<InfectedCascadeVO> resultList = new GsonBuilder().create().fromJson(result, new TypeToken<List<InfectedCascadeVO>>() {
            }.getType());
            Collections.sort(resultList);
            return resultList;
        } else {
            return null;
        }
    }

    public List<CascadeVO> getPoisonType() {
        String url = "http://cdc-admin-service/v1/pt/poisoningWarn/getPoisoningType";
        try {
            CascadeVO[] forObject = restTemplate.getForObject(url, CascadeVO[].class);
            assert forObject != null;
            return Arrays.asList(forObject);
        } catch (Exception e) {
            throw new MedicalBusinessException("获取中毒类型分类列表失败");
        }
    }

    public List<CascadeVO> getOutpatientType() {
        String url = "http://cdc-admin-service/v1/pt/outpatientWarn/getOutpatientType";
        try {
            CascadeVO[] forObject = restTemplate.getForObject(url, CascadeVO[].class);
            assert forObject != null;
            return Arrays.asList(forObject);
        } catch (Exception e) {
            throw new MedicalBusinessException("获取门诊类型分类列表失败");
        }
    }

    public Boolean checkUserIsDesensitizationUser(String loginUserId) {
        String url = "http://" + uapService + "/v1/pt/mdmEmp/" + loginUserId;
        UapMdmEmpUserDto userDto = apiTool.doGet(url, UapMdmEmpUserDto.class);
        if (ObjectUtil.isNotEmpty(userDto) && userDto.getExtands() != null) {
            for (UapUserExtendDto extendDto : userDto.getExtands()) {
                if (extendDto.getNameCode().equals("TM")) {
                    if (extendDto.getValue().equals("1")) {
                        return true;
                    } else if (extendDto.getValue().equals("0")) {
                        return false;
                    }
                }
            }
        }
        return false;
    }

    public List<UapMdmEmpUserDto> getOrgUsers(String orgId) {
        String url = "http://uap-service-ext-service/v1/pv/mdmEmp/searchWithRoleInfo";
        Map<String, Object> filter = new HashMap<>();
        filter.put("orgId", orgId);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("filter", filter);
        requestMap.put("pageSize", 10000);
        requestMap.put("pageNumber", 1);
        return Objects.requireNonNull(restTemplate.postForObject(url, requestMap, JSONObject.class)).getJSONArray("entities").toJavaList(UapMdmEmpUserDto.class);
    }

    public List<InfectedCascadeVO> getInfectedSymptomList() {
        return treeToList(cdcReportCardService.getInfectedInfoList());
    }

    public String getEiPositionSwitch(String orgId) {
        return getCdcAdminServiceConfigValue(orgId, EI_POSITION_SWITCH_CODE);
    }

    public String getSmsAuditButton(String orgId) {
        return getCdcAdminServiceConfigValue(orgId, SMS_AUDIT_CODE);
    }

    public boolean getAiConfirmSwitch(String orgId) {
        return "1".equals(getCdcAdminServiceConfigValue(orgId, AI_CONFIRM_SWITCH));
    }

    public String getEpiSwitch(String orgId) {
        return getCdcAdminServiceConfigValue(orgId, EPI_SWITCH);
    }

    public String[] getSyndromeAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, SYNDROME_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getInfectedAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, INFECTED_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getPoisonAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, POISON_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getUnknownReasonAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, UNKNOWN_REASON_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getCustomizedAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, CUSTOMIZED_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getScSymptomAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, SC_SYMPTOM_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getOutpatientAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, OUTPATIENT_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String[] getPreventionControlAIScreenPhone(String orgId) {
        String aiScreenPhone = getCdcAdminServiceConfigValue(orgId, PREVENTION_CONTROL_AI_SCREEN_PHONE);

        if (!StringUtils.isEmpty(aiScreenPhone)) {
            return aiScreenPhone.split("\\|");
        }
        return new String[0];
    }

    public String getCdcAdminServiceConfigValue(String orgId, String configCode) {
        String url = "http://cdc-admin-service/v1/pt/param/config/paramInfoByCode";
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("orgId", orgId);
        requestMap.put("configCode", configCode);
        String configValue = null;

        try {
            configValue = Objects.requireNonNull(restTemplate.postForObject(url, requestMap, JSONObject.class)).getString("configValue");
        } catch (Exception e) {
            log.error("调用基础管理子系统获取配置出错,orgId:" + orgId + ",configCode:" + configCode, e);
        }
        return configValue;
    }

    public Boolean getIsNeededMapping() {
        String url = "http://cdc-admin-service/pt/v1/customizeDistrict/getCustomizeDistrictSwitch";
        String isNeededMapping = restTemplate.getForObject(url, String.class);
        if ("0".equals(isNeededMapping)) {
            return false;
        } else {
            return true;
        }
    }

    //用户角色校验
    public boolean checkRoleByUser(String roleId, String loginUserId) {
        if (userCenterVersion.equals("5.0.0")) {
            String url = "http://" + uapService + "/v1/pt/mdmRole/queryUserRoleList?userId=" + loginUserId;
            List<UapRoleUserPo> result = Objects.requireNonNull(apiTool.doGet(url, JSONArray.class)).toJavaList(UapRoleUserPo.class);
            return checkRole(roleId, result);
        } else {
            String url = "http://uap-service-ext-service/v1/pt/TUapRole/tree?loginUserId=" + loginUserId;
            JSONObject requestMap = new JSONObject();
            UapRoleUserPo result = restTemplate.postForObject(url, requestMap, UapRoleUserPo.class);
            assert result != null;
            return checkRole(roleId, result);
        }
    }


    private boolean checkRole(String roleId, List<UapRoleUserPo> userPos) {
        for (UapRoleUserPo uapRoleUserPo : userPos) {
            if (Objects.equals(roleId, uapRoleUserPo.getId())) {
                return true;
            }
        }
        return false;
    }

    private boolean checkRole(String roleId, UapRoleUserPo userPo) {
        String id = userPo.getId();
        if (roleId.equals(id)) {
            return true;
        }

        List<UapRoleUserPo> nodes = userPo.getNodes();
        if (!CollectionUtils.isEmpty(nodes)) {
            for (UapRoleUserPo uapRoleUserPo : nodes) {
                if (checkRole(roleId, uapRoleUserPo)) {
                    return true;
                }
            }
        }
        return false;
    }


    //将树形结构转化为扁平
    public List<InfectedCascadeVO> treeToList(List<InfectedCascadeVO> sourceTree) {
        List<InfectedCascadeVO> resultList = new ArrayList<>();
        for (InfectedCascadeVO infectedCascadeVO : sourceTree) {
            resultList.add(infectedCascadeVO);
            if (infectedCascadeVO.getChildren() != null) {
                resultList.addAll(treeToList(infectedCascadeVO.getChildren()));
            }
        }
        return resultList;
    }

    private void setAuth(List<UapAuthNode> authNodes, List<UapAuth> authList) {
        for (UapAuthNode authNode : authNodes) {
            for (UapAuth auth : authList) {
                if (auth.getAuthId().equals(authNode.getId())) {
                    authNode.setChecked(true);
                    authNode.setAuthType(auth.getAuthType());
                }
            }
            if (authNode.getNodes() != null && !authNode.getNodes().isEmpty()) {
                setAuth(authNode.getNodes(), authList);
            }
        }
    }


    // 获取病历症状映射结果
    public List<MappingResult> getMappingResult(String dateStr, String syndromeCode, List<String> townshipHospitalIdList) {
        List<MappingResult> mappingResults = new ArrayList<>();

        String url = "http://cdc-analysis-service/symptom/getMappingResult";

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("dateStr", dateStr);
        requestMap.put("syndromeCode", syndromeCode);
        requestMap.put("townshipHospitalIdList", townshipHospitalIdList);

        try {
            MappingResult[] forObject = restTemplate.postForObject(url, requestMap, MappingResult[].class);
            assert forObject != null;
            mappingResults = Arrays.asList(forObject);
        } catch (Exception e) {
            log.error("调用analysis服务,查询病历症状映射结果报错");
            return mappingResults;
        }

        return mappingResults;
    }

    public List<PoisonMappingResult> getPoisonMappingResult(String dateStr, String poisonCode, List<String> townshipHospitalIdList) {
        List<PoisonMappingResult> mappingResults = new ArrayList<>();

        String url = "http://cdc-analysis-service/poison/getPoisonMappingResult";

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("dateStr", dateStr);
        requestMap.put("syndromeCode", poisonCode);
        requestMap.put("townshipHospitalIdList", townshipHospitalIdList);

        try {
            PoisonMappingResult[] forObject = restTemplate.postForObject(url, requestMap, PoisonMappingResult[].class);
            assert forObject != null;
            mappingResults = Arrays.asList(forObject);
        } catch (Exception e) {
            log.error("调用analysis服务,查询病历症状映射结果报错");
            return mappingResults;
        }

        return mappingResults;
    }

    public List<OutpatientMappingResult> getOutpatientMappingResult(String dateStr, String outpatientTypeCode, List<String> townshipHospitalIdList) {
        List<OutpatientMappingResult> mappingResults = new ArrayList<>();
        //todo
        String url = "http://cdc-analysis-service/outpat/getOutpatientDeptMedicalInfo";

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("dateStr", dateStr);
        requestMap.put("syndromeCode", outpatientTypeCode);
        requestMap.put("townshipHospitalIdList", townshipHospitalIdList);

        try {
            OutpatientMappingResult[] forObject = restTemplate.postForObject(url, requestMap, OutpatientMappingResult[].class);
            assert forObject != null;
            mappingResults = Arrays.asList(forObject);
        } catch (Exception e) {
            log.error("调用analysis服务,查询病历症状映射结果报错");
            return mappingResults;
        }

        return mappingResults;
    }

    public DwdChMedicalRecord getMedicalRecordByEventId(String eventId) {
        String url = "http://cdc-analysis-service/infected/getMedicalRecordByEventId?eventId=" + eventId;

        DwdChMedicalRecord dwdChMedicalRecord = null;
        try {
            dwdChMedicalRecord = restTemplate.getForObject(url, DwdChMedicalRecord.class);
        } catch (Exception e) {
            log.error("调用analysis服务,根据eventId查询病历结果报错");
        }
        return dwdChMedicalRecord;
    }

    public String addNewUapUser(UapUserPo userPo) {
        try {
            String url = "http://uap-service-ext-service/v1/pt/TUapUser?loginUserId=" + getUapUserIdByName(uapAdminLoginName);
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("uapUser", userPo);
            UapUserPo uapUserPo = restTemplate.postForObject(url, requestMap, UapUserPo.class);
            return uapUserPo.getId();
        } catch (HttpClientErrorException exception) {
            if (JSONObject.parseObject(exception.getResponseBodyAsString()).getString("msg").equals(USER_EXIST_WARN)) {
                String loginUserId = getUapUserIdByName(userPo.getLoginName());
                UapOrgPo orgPo = getUserOrg(userPo.getLoginName());
                if (!userPo.getOrgId().equals(orgPo.getId())) {
                    throw new MedicalBusinessException("11463006", "用户已在系统中[" + orgPo.getName() + "]下存在");
                }
                userPo.setId(loginUserId);
                updateUapUser(userPo);
                return loginUserId;
            } else {
                throw new MedicalBusinessException("11463000", JSONObject.parseObject(exception.getResponseBodyAsString()).getString("msg"));
            }
        }
    }

    public List<AddUapUserPo> batchAddUapUser(List<AddUapUserPo> list) {
        List<AddUapUserPo> addUapUserPosList = new ArrayList<>();
        try {
            String url = "http://uap-service-ext-service/v1/pv/TUapUser/batchAdd";
            AddUapUserPo[] addUapUserPos = restTemplate.postForObject(url, list, AddUapUserPo[].class);
            assert addUapUserPos != null;
            addUapUserPosList = Arrays.asList(addUapUserPos);
        } catch (Exception e) {
            log.error("UAP批量新增用户出错");
            return addUapUserPosList;
        }
        return addUapUserPosList;
    }

    public void updateUapUser(UapUserPo userPo) {
        try {
            String url = "http://uap-service-ext-service/v1/pt/TUapUser/{id}?loginUserId=" + getUapUserIdByName(uapAdminLoginName);
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("uapUser", userPo);
            restTemplate.put(url, requestMap, userPo.getId());
        } catch (HttpClientErrorException exception) {
            throw new MedicalBusinessException("11463000", JSONObject.parseObject(exception.getResponseBodyAsString()).getString("msg"));
        }
    }

    public void addNewUserRole(String userId, String roleId) {
        String url = "http://uap-service-ext-service/v1/pt/TUapRole/user?loginUserId=" + getUapUserIdByName(uapAdminLoginName) + "&userIds=" + userId + "&roleId=" + roleId;
        restTemplate.put(url, null);
    }

    public void deleteUserRole(String userId, String roleId) {
        String url = "http://uap-service-ext-service/v1/pt/TUapRole/user?loginUserId=" + getUapUserIdByName(uapAdminLoginName) + "&userIds=" + userId + "&roleId=" + roleId;
        restTemplate.delete(url);
    }

    public String getUapUserIdByName(String loginName) {
        String url = "http://uap-service-ext-service/v1/pv/TUapUser/withPassword/" + loginName;
        UapUserPo userPo = restTemplate.getForObject(url, UapUserPo.class);
        if (Objects.nonNull(userPo)) {
            return userPo.getId();
        } else {
            return "";
        }
    }

    public List<UapRole> getUapRoleListByAppCode(String appCode) {
        String url = "http://" + uapService + "/v1/pt/TUapRole/byAppCode?appCode=" + appCode;
        List<UapRole> result = new ArrayList<>();

        try {
            UapRole[] uapRoles = apiTool.doGet(url, UapRole[].class);
            assert uapRoles != null;
            result = Arrays.asList(uapRoles);
        } catch (Exception ignored) {
            log.error("调用/v1/pt/TUapRole/byAppCode出错",ignored);

        }
        return result;
    }

    public String getUapRoleIdByAppCodeAndRoleName(String appCode, String roleName) {
        String uapRoleId = null;

        List<UapRole> uapRoleList = getUapRoleListByAppCode(appCode);
        for (UapRole uapRole : uapRoleList) {
            if (roleName.equals(uapRole.getName())) {
                uapRoleId = uapRole.getId();
            }
        }
        return uapRoleId;
    }

    public Object detailByDtos(String loginUserId, DailyRecordDtoParam param) {
        String url = "http://cdc-data-service/v1/pt/school/detailByRecordDtos?loginUserId=" + loginUserId;
        log.info("调用edr查询打卡记录，loginUserId:{}", loginUserId);
        try {
            SchoolParams schoolParams = new SchoolParams();
            schoolParams.setRecordDtos(param.getSymptomRecordDtoList());
            return restTemplate.postForObject(url, schoolParams, Object.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用edr查询打卡记录出错");
        }
        return null;
    }


    public Object detailByIds(String loginUserId, DailyRecordParam param) {
        String url = "http://cdc-data-service/v1/pt/school/detailByIds?loginUserId=" + loginUserId;
        log.info("调用edr查询打卡记录，loginUserId:{}", loginUserId);
        try {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("businessIds", param.getSymptomRecordIdList());
            return restTemplate.postForObject(url, queryMap, Object.class);
        } catch (Exception e) {
            log.error("调用edr查询打卡记录出错");
        }
        return null;
    }

    public Object detailByIdsGroupByDay(String loginUserId, DailyRecordParam param) {

        String url = "http://cdc-data-service/v1/pt/school/detailByIdsGroupByDay?loginUserId=" + loginUserId;
        log.info("调用edr查询打卡记录，loginUserId:{}", loginUserId);
        try {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("businessIds", param.getSymptomRecordIdList());
            return restTemplate.postForObject(url, queryMap, Object.class);
        } catch (Exception e) {
            log.error("调用edr查询打卡记录出错");
        }
        return null;
    }

    public EpiResponse sponsorEpiInvest(RecordWarnCreateVO recordWarnCreateVO) {
        String url = "http://cdc-epi-invest-service/v1/pt/recordWarn/add";
        log.info("调用流调接口发起流调任务，信息：" + recordWarnCreateVO);

        try {
            return restTemplate.postForObject(url, recordWarnCreateVO, EpiResponse.class);
        } catch (HttpClientErrorException e) {
            String responseBodyAsString = e.getResponseBodyAsString();
            EpiResponse epiResponse = JSONObject.parseObject(responseBodyAsString, EpiResponse.class);
            log.error("发起流调任务失败,流调信息:" + recordWarnCreateVO);
            throw new MedicalBusinessException("发起流调任务失败," + epiResponse.getMessage());
        }
    }

    public List<MedicalWarn> getInfectedMedicalWarnList() {
        String url = "http://cdc-admin-service/v1/pt/medicalWarnRule/queryAll";
        log.info("调用基础管理系统接口获取传染病配置规则");
        try {
            QueryWarnRuleInfoFilter queryWarnRuleInfoFilter = new QueryWarnRuleInfoFilter();
            return Arrays.asList(Objects.requireNonNull(restTemplate.postForObject(url, queryWarnRuleInfoFilter, MedicalWarn[].class)));
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统传染病配置规则失败");
        }
    }

    public List<SchoolWarnDto> getSchoolSymptomMedicalWarnList() {
        String url = "http://cdc-admin-service/v1/pt/schoolSymptomWarn/getAllList";
        log.info("调用基础管理系统接口获取学校症状配置规则");
        try {
            return Arrays.asList(Objects.requireNonNull(restTemplate.getForObject(url, SchoolWarnDto[].class)));
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统学校症状配置规则失败");
        }
    }

    public List<PoisonWarnDto> getPoisonWarnList() {
        String url = "http://cdc-admin-service/v1/pt/poisoningWarn/getAllList";
        log.info("调用基础管理系统接口获取中毒配置规则");
        try {
            return Arrays.asList(Objects.requireNonNull(restTemplate.getForObject(url, PoisonWarnDto[].class)));
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统中毒症状配置规则失败");
        }
    }

    public List<UnknownReasonWarnDto> getUnknownReasonWarnList() {
        String url = "http://cdc-admin-service/v1/pt/unknownReasonWarn/getAllList";
        log.info("调用基础管理系统接口获取不明原因配置规则");
        try {
            UnknownReasonWarnDto[] warnDtos = restTemplate.getForObject(url, UnknownReasonWarnDto[].class);
            return warnDtos == null ? new ArrayList<>() : Lists.newArrayList(warnDtos);
        } catch (Exception e) {
            log.error("调用基础管理服务:不明原因疾病规则接口错误：url:{}, {}", url, e);
        }
        return new ArrayList<>();
    }

    public List<OutpatientWarnDto> getOutpatientWarnList() {
        String url = "http://cdc-admin-service/v1/pt/outpatientWarn/getAllList";
        log.info("调用基础管理系统接口获取门诊配置规则");
        try {
            return Arrays.asList(Objects.requireNonNull(restTemplate.getForObject(url, OutpatientWarnDto[].class)));
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统门诊症状配置规则失败");
        }
    }

    public List<PreventionControlWarnDto> getPreventionControlWarnList() {
        String url = "http://cdc-admin-service/v1/pt/preventionControlWarn/getAllList";
        log.info("调用基础管理系统接口获取联防联控配置规则");
        try {
            PreventionControlWarnDto[] warnDtos = restTemplate.getForObject(url, PreventionControlWarnDto[].class);
            return warnDtos == null ? new ArrayList<>() : Lists.newArrayList(warnDtos);
        } catch (Exception e) {
            log.error("调用基础管理服务:联防联控规则接口错误：url:{}, {}", url, e);
        }
        return new ArrayList<>();
    }


    public List<CustomizedWarningTaskVO> getCustomizedWarnTaskList() {
        String url = "http://cdc-admin-service/v1/pt/customizedWarn/getAllEnabledWarningTask";
        log.info("调用基础管理系统接口获取自定义信号名称列表");
        try {
            return Arrays.asList(Objects.requireNonNull(restTemplate.getForObject(url, CustomizedWarningTaskVO[].class)));
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统自定义信号名称列表失败");
        }
    }

    public CustomizedWarnVO getWarningRuleByTaskId(String taskId) {
        String url = "http://cdc-admin-service/v1/pt/customizedWarn/getWarningRuleByTaskId?taskId=" + taskId;
        log.info("调用基础管理系统接口获取自定义信号名称列表");
        try {
            return restTemplate.getForObject(url, CustomizedWarnVO.class);
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统自定义信号名称列表失败");
        }
    }

    public List<CascadeVO> getCustomizedWarnTaskCascadeList() {
        String url = "http://cdc-admin-service/pt/v1/dataAuth/getCustomizedNameList";
        log.info("调用基础管理系统接口获取自定义信号名称列表");
        try {
            return Arrays.asList(Objects.requireNonNull(restTemplate.getForObject(url, CascadeVO[].class)));
        } catch (Exception e) {
            throw new MedicalBusinessException("查询基础管理系统自定义信号名称列表失败");
        }
    }

    public PatientDetailResult getMdmResult(PatientBaseInfo patientBaseInfo) {
        String url = "http://cdc-data-transform-service/v1/pt/patient/detail";

        try {
            return restTemplate.postForObject(url, patientBaseInfo, PatientDetailResult.class);
        } catch (Exception e) {
            log.error("获取主索引globalPatientId失败,{}", e.getMessage());
            throw new MedicalBusinessException("获取主索引globalPatientId失败");
        }
    }

    public List<DwdChMedicalSuspectRecordDto> getSuspectMedicalRecord(SuspectRequestParam requestParam) {
        String url = "http://cdc-data-service/v1/pt/edr/infections/abnormalMedReportsList";

        try {
            DwdChMedicalSuspectRecordDto[] dwdChMedicalSuspectRecordDtos = restTemplate.postForObject(url, requestParam, DwdChMedicalSuspectRecordDto[].class);
            assert dwdChMedicalSuspectRecordDtos != null;
            return new ArrayList<>(Arrays.asList(dwdChMedicalSuspectRecordDtos));
        } catch (Exception e) {
            log.error("获取异常病例数据失败,{}", e.getMessage());
            throw new MedicalBusinessException("获取异常病例数据失败");
        }
    }

    public List<MedicalHistoryLog> getMedicalHistoryLogLists(List<String> eventIdLists, String loginUserId) {

        String url = "http://cdc-data-service/v1/pt/edr/historylogList?loginUserId=" + loginUserId;
        log.info("调用edr查询诊断日志，loginUserId:{}", loginUserId);
        try {
            MedicalHistoryLog[] medicalHistoryLogLists = restTemplate.postForObject(url, eventIdLists, MedicalHistoryLog[].class);
            assert medicalHistoryLogLists != null;
            return new ArrayList<>(Arrays.asList(medicalHistoryLogLists));
        } catch (Exception e) {
            log.error("获取诊断日志列表失败,{}", e.getMessage());
            throw new MedicalBusinessException("获取诊断日志列表失败");
        }
    }

    public List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getInfectedSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrInfectedSmsRule.class);
    }

    public void deleteInfectedSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deleteInfectedSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getSymptomSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrSymptomSmsRule.class);
    }

    public void deleteSymptomSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deleteSymptomSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrSympSmsRule> getScSymptomSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getScSymptomSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrSympSmsRule.class);
    }

    public void deleteScSymptomSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deleteScSymptomSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getPoisonSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrPoisonSmsRule.class);
    }

    public void deletePoisonSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deletePoisonSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getOutpatientSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrOutpatientSmsRule.class);
    }

    public void deleteOutpatientSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deleteOutpatientSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getCustomizedSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrCustomizedSmsRule.class);
    }

    public void deleteCustomizedSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deleteCustomizedSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrUserDataAuth> getSyndromeDataAuthByLoginUserId(String loginUserId) {
        return apiTool.doGet("http://" + cdcAdminService + "/pt/v1/dataAuth/getSyndromeDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUserDataAuth> getInfectedDataAuthByLoginUserId(String loginUserId) {
        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getInfectedDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUserDataAuth> getSchoolSymptomDataAuthByLoginUserId(String loginUserId) {
        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getSchoolSymptomDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUserDataAuth> getPoisonDataAuthByLoginUserId(String loginUserId) {
        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getPoisonDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUserDataAuth> getOutpatientDataAuthByLoginUserId(String loginUserId) {

        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getOutpatientDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUserDataAuth> getPreventionControlDataAuthByLoginUserId(String loginUserId) {

        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getPreventionControlDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUserDataAuth> getUnknownReasonDataAuthByLoginUserId(String loginUserId) {
        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getUnknownReasonDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }

    public List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getUnknownReasonSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrUnknownReasonSmsRule.class);
    }

    public void deleteUnknownReasonSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deleteUnknownReasonSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleListByCodeList(List<String> codeList) {
        return restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/getPreventionControlSmsRuleListByCodeList", codeList, JSONArray.class).toJavaList(TbCdcmrPreventionControlSmsRule.class);
    }

    public void deletePreventionControlSmsRuleByLoginUserId(String loginUserId) {
        restTemplate.postForObject("http://cdc-admin-service/pt/v1/smsRule/deletePreventionControlSmsRuleByLoginUserId" + loginUserId, null, JSONObject.class);
    }

    public List<TbCdcmrUserDataAuth> getCustomizedDataAuthByLoginUserId(String loginUserId) {
        return restTemplate.getForObject("http://cdc-admin-service/pt/v1/dataAuth/getCustomizedDataAuthByLoginUserId?loginUserId=" + loginUserId, JSONArray.class).toJavaList(TbCdcmrUserDataAuth.class);
    }


    public String logExportSave(LogExportReqDto queryVO, String loginUserId) {
        String url = "http://cdc-admin-service/v1/pt/logExport/save?loginUserId=" + loginUserId;
        return restTemplate.postForObject(url, queryVO, String.class);
    }

    public String logExportUpdate(LogExportReqDto queryVO, String loginUserId) {
        String url = "http://cdc-admin-service/v1/pt/logExport/update?loginUserId=" + loginUserId;
        return restTemplate.postForObject(url, queryVO, String.class);
    }


    public List<WarningGradeVO> getWarningGradeList(String version) {
        String url = "http://cdc-admin-service/" + version + "/pt/warningGrade/list";
        List<WarningGradeVO> list = null;

        try {
            list = restTemplate.postForObject(url, null, JSONArray.class).toJavaList(WarningGradeVO.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取分级配置出错", e);
        }
        return list;
    }

    public Map<String, WarningGradeVO> getWarningGradeMap(String version) {
        final List<WarningGradeVO> list = this.getWarningGradeList(version);
        final HashMap<String, WarningGradeVO> map = new HashMap<>(list.size() * 2);
        list.forEach(warningGradeVO -> map.put(warningGradeVO.getGradeCode(), warningGradeVO));
        return map;
    }

    public List<WarningGradeEmergencyPlanVO> getWarningGradeEmergencyPlan(String diseaseCode, String configType, String version) {
        String url = "http://cdc-admin-service/" + version + "/pt/warningGradeEmergencyPlan/getByTypeAndDisease?diseaseCode=" + diseaseCode + "&configType=" + configType;
        List<WarningGradeEmergencyPlanVO> ret = null;

        try {
            ret = restTemplate.getForObject(url, JSONArray.class).toJavaList(WarningGradeEmergencyPlanVO.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取应急预案出错", e);
        }
        return ret;

    }

    public ResponseEntity<byte[]> downloadAttachmentFromAdmin(String id, String loginUserId) {
        String url = "http://cdc-admin-service/pt/v1/fileManage/download?id=" + id + "&loginUserId=" + loginUserId;
        try {
            return restTemplate.getForEntity(url, byte[].class);
        } catch (Exception e) {
            log.error("调用基础管理子系统下载接口", e);
        }
        return null;
    }

    public List<WarningGradeVO> getGradeListByDiseaseCode(String version, String diseaseCode, String configType) {
        String url = "http://cdc-admin-service/" + version + "/pt/warningGrade/getGradeByTypeAndCode?diseaseCode=" + diseaseCode + "&configType=" + configType;
        List<WarningGradeVO> list = null;

        try {
            list = restTemplate.getForObject(url, JSONArray.class).toJavaList(WarningGradeVO.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取分级配置出错", e);
        }
        return list;
    }

    public String getWarnRuleIdByTaskId(String taskId) {
        String url = "http://cdc-admin-service/v1/pt/customizedWarn/getWarningRuleIdByTaskId?taskId=" + taskId;
        try {
            return restTemplate.getForObject(url, String.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取规则id出错", e);
        }
        return null;
    }

    public int getExportMax() {
        final String configValue = getCdcAdminServiceConfigValue(null, EXPORT_MAX_CODE);
        try {
            return Integer.valueOf(configValue);
        } catch (Exception e) {
            log.error("返回值有误#{}", configValue, e);
        }
        return EXPORT_MAX_VALUE;
    }

    public void checkExportMax(Collection collection) {
        int exportMax = getExportMax();
        if (collection != null && collection.size() > exportMax) {
            throw new MedicalBusinessException("最多导出" + exportMax + "条数据");
        }

    }
    public void checkExportMax(int count) {
        int exportMax = getExportMax();
        if (count > exportMax) {
            throw new MedicalBusinessException("最多导出" + exportMax + "条数据");
        }

    }

    public String getTrendForecastStatus(String configCode, String configGroup) {
        String url = "http://cdc-admin-service/v1/pt/param/config/getTrendForecastStatus?configCode=" + configCode + "&configGroup=" + configGroup;
        try {
            return restTemplate.getForObject(url, String.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取症候群趋势预测权限开关出错", e);
        }
        return null;
    }

    public Boolean getRceSwitch() {
        final String configValue = getCdcAdminServiceConfigValue(null, RCE_MSG_SWITCH_CODE);
        try {
            return CommonConstants.STATUS_VALID == Integer.parseInt(Optional.ofNullable(configValue).orElse(CommonConstants.STR_ZERO));
        } catch (Exception e) {
            log.error("返回值有误#{}", configValue, e);
        }
        return false;
    }


    public String getUapUserIdByPhone(String phone) {
        //非新版uap不用请求此接口
        if (!userCenterVersion.equals("5.0.0")){
            return "";
        }
        String url = "http://uap-service-ext-service/v1/pt/mdmEmp/byPhone?phone=" + phone;
        try {
            UapUserPo userPo = restTemplate.getForObject(url, UapUserPo.class);
            if (Objects.nonNull(userPo)) {
                return userPo.getId();
            }
        }catch (Exception e){
            log.error("getUapUserIdByPhone出错#",  e);
        }
        return "";
    }

    public EventMonitorDetailVO getMonitorDetail(SyndromeMonitorMedicalListQueryDTO queryDTO) {
        String url = "http://cdc-data-service/v1/pt/signalMonitor/getMonitorDetail" ;
        EventMonitorDetailVO vo = new EventMonitorDetailVO();
        try {
            vo = restTemplate.postForObject(url,queryDTO, EventMonitorDetailVO.class);
        }catch (Exception e){
            log.error("getMonitorDetail#",  e);
        }
        return vo;
    }

    public List<SyndromeMonitorRespDTO> syndromeMonitorDistributionAndStats(SyndromeMonitorReqDTO reqDTO) {

        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/distributionAndStats";
        log.info("调用症候群监测 症候群病例分布概览&症候群病例数统计，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeMonitorRespDTO[] list = restTemplate.postForObject(url, reqDTO, SyndromeMonitorRespDTO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用症候群监测 症候群病例分布概览&症候群病例数统计失败", e);
            throw new MedicalBusinessException("调用症候群监测 症候群病例分布概览&症候群病例数统计");
        }
    }

    public List<SyndromeMonitorRespDTO> syndromeMonitorTimeTrend(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/timeTrend";
        log.info("调用症候群监测 症候群病例趋势图，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeMonitorRespDTO[] list = restTemplate.postForObject(url, reqDTO, SyndromeMonitorRespDTO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用症候群监测 症候群病例趋势图失败", e);
            throw new MedicalBusinessException("调用症候群监测 症候群病例趋势图失败");
        }
    }

    public List<SyndromeMonitorRespDTO> abnormalTimeTrend(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/abnormalTimeTrend";
        log.info("调用症候群监测 监测异常趋势图，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeMonitorRespDTO[] list = restTemplate.postForObject(url, reqDTO, SyndromeMonitorRespDTO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用症候群监测 监测异常趋势图", e);
            throw new MedicalBusinessException("调用症候群监测 监测异常趋势图");
        }
    }

    public SyndromeGeographyTopRespDTO geographyTop(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/geography/top";
        log.info("调用症候群监测 地理分布-数量与排名，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeGeographyTopRespDTO ret = restTemplate.postForObject(url, reqDTO, SyndromeGeographyTopRespDTO.class);
            return ret;
        } catch (Exception e) {
            log.error("调用症候群监测 地理分布-数量与排名", e);
            throw new MedicalBusinessException("调用症候群监测 地理分布-数量与排名");
        }
    }

    public SyndromeMapRespDTO geographyMap(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/geography/map";
        log.info("调用症候群监测 地理分布-地图点位，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeMapRespDTO ret = restTemplate.postForObject(url, reqDTO, SyndromeMapRespDTO.class);
            return ret;
        } catch (Exception e) {
            log.error("调用症候群监测 地理分布-地图点位", e);
            throw new MedicalBusinessException("调用症候群监测 地理分布-地图点位");
        }
    }

    public List<SyndromeTimeTrendRespDTO> lineChart(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/timeTrend/lineChart";
        log.info("调用症候群监测 时间趋势-病例数时间趋势分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeTimeTrendRespDTO[] ret = restTemplate.postForObject(url, reqDTO, SyndromeTimeTrendRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用症候群监测 时间趋势-病例数时间趋势分析", e);
            throw new MedicalBusinessException("调用症候群监测 时间趋势-病例数时间趋势分析");
        }

    }

    public List<SyndromeTimeTrendDistrictRespDTO> tableChart(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/timeTrend/tableChart";
        log.info("调用症候群监测 时间趋势-病例数按时间区域统计分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SyndromeTimeTrendDistrictRespDTO[] ret = restTemplate.postForObject(url, reqDTO, SyndromeTimeTrendDistrictRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用症候群监测 时TimeTrendDistrictRespDTO间趋势-病例数按时间区域统计分析", e);
            throw new MedicalBusinessException("调用症候群监测 时间趋势-病例数按时间区域统计分析");
        }


    }

    public SexAgeRespDTO sexAgeGroup(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/personStats/sexAgeGroup";
        log.info("调用症候群监测 六大症候群症状监测-人群分布-病例性别、年龄占比分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SexAgeRespDTO ret = restTemplate.postForObject(url, reqDTO, SexAgeRespDTO.class);
            return ret;
        } catch (Exception e) {
            log.error("调用症候群监测 六大症候群症状监测-人群分布-病例性别、年龄占比分析", e);
            throw new MedicalBusinessException("六大症候群症状监测-人群分布-病例性别、年龄占比分析");
        }
    }

    public List<SexAgeDistributionRespDTO> sexAgeDistribution(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/personStats/sexAgeDistribution";
        log.info("调用症候群监测 人群分布-病例年龄分布分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SexAgeDistributionRespDTO[] ret = restTemplate.postForObject(url, reqDTO, SexAgeDistributionRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用症候群监测 人群分布-病例年龄分布分析", e);
            throw new MedicalBusinessException("调用症候群监测 人群分布-病例年龄分布分析");
        }

    }

    public SexAgeRespDTO casesAgeRatio(InfectionMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/casesAgeRatio";
        log.info("调用传染病监测-人群分布-病例年龄占比分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            if (CollectionUtils.isEmpty(reqDTO.getInfectedCodeList())) {
                reqDTO.setInfectedCodeList(Arrays.asList(reqDTO.getInfectedCode()));
            }
            SexAgeRespDTO ret = restTemplate.postForObject(url, reqDTO, SexAgeRespDTO.class);
            return ret;
        } catch (Exception e) {
            log.error("调用症候群监测 六大症候群症状监测-人群分布-病例性别、年龄占比分析", e);
            throw new MedicalBusinessException("六大症候群症状监测-人群分布-病例性别、年龄占比分析");
        }
    }

    public List<SexAgeDistributionRespDTO> casesAgeDistribution(InfectionMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/casesAgeDistribution";
        log.info("调用传染病监测-人群分布-病例年龄分布分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            if (CollectionUtils.isEmpty(reqDTO.getInfectedCodeList())) {
                reqDTO.setInfectedCodeList(Arrays.asList(reqDTO.getInfectedCode()));
            }
            SexAgeDistributionRespDTO[] ret = restTemplate.postForObject(url, reqDTO, SexAgeDistributionRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用传染病监测 人群分布-病例年龄分布分析", e);
            throw new MedicalBusinessException("调用传染病监测 人群分布-病例年龄分布分析");
        }

    }

    public List<SexAgeTableRespDTO> crowdAreaStatistics(InfectionMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/crowdAreaStatistics";
        log.info("调用传染病监测-人群分布-病例数按人群区域统计分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            if (CollectionUtils.isEmpty(reqDTO.getInfectedCodeList())) {
                reqDTO.setInfectedCodeList(Arrays.asList(reqDTO.getInfectedCode()));
            }
            SexAgeTableRespDTO[] ret = restTemplate.postForObject(url, reqDTO, SexAgeTableRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用传染病监测-人群分布-病例数按人群区域统计分析", e);
            throw new MedicalBusinessException("调用传染病监测 人群分布-病例数按人群区域统计分析");
        }

    }

    public List<TimeTrendDistrictRespDTO> timeAreaStatistics(InfectionMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/timeAreaStatistics";
        log.info("调用传染病监测-时间趋势-病例数按时间区域统计分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            if (CollectionUtils.isEmpty(reqDTO.getInfectedCodeList())) {
                reqDTO.setInfectedCodeList(Arrays.asList(reqDTO.getInfectedCode()));
            }
            TimeTrendDistrictRespDTO[] ret = restTemplate.postForObject(url, reqDTO, TimeTrendDistrictRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用传染病监测-时间趋势-病例数按时间区域统计分析", e);
            throw new MedicalBusinessException("调用传染病监测-人群分布-病例数按时间区域统计分析");
        }
    }

    public List<CasesTimeTrendRespDTO> casesTimeTrend(InfectionMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/casesTimeTrend";
        log.info("调用传染病监测-时间趋势-病例数时间趋势分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            CasesTimeTrendRespDTO[] ret = restTemplate.postForObject(url, reqDTO, CasesTimeTrendRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用传染病监测-时间趋势-病例数时间趋势分析", e);
            throw new MedicalBusinessException("调用传染病监测-人群分布-病例数时间趋势分析");
        }
    }


    public List<SexAgeTableRespDTO> personDistribution(SyndromeMonitorReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/syndromeMonitor/personStats/personDistribution";
        log.info("调用症候群监测 人群分布-病例数按人群区域统计分析，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            SexAgeTableRespDTO[] ret = restTemplate.postForObject(url, reqDTO, SexAgeTableRespDTO[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用症候群监测 人群分布-病例数按人群区域统计分析", e);
            throw new MedicalBusinessException("调用症候群监测 人群分布-病例数按人群区域统计分析");
        }

    }

    public List<TbCdcmrDiseaseMonitorRule> getRuleByIds(List<String> ids) {

        String url = "http://cdc-admin-service/v1/pt/monitor/config/getMonitorConfigByIds";
        log.info("调用症候群监测 监测规则，url#{}, ids#{}",url, ids);
        try {
            TbCdcmrDiseaseMonitorRule[] ret = restTemplate.postForObject(url, ids, TbCdcmrDiseaseMonitorRule[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用症候群监测 监测规则析", e);
            throw new MedicalBusinessException("调用症候群监测 监测规则");
        }

    }

    public List<TbCdcmrDiseaseMonitorRule> getRulesByConfigType(String type) {
        String url = "http://cdc-admin-service/v1/pt/monitor/config/getMonitorConfigMsg?configType="+type;
        log.info("调用症候群监测 监测规则list，url#{}, configType#{}",url, type);
        try {
            TbCdcmrDiseaseMonitorRule[] ret = restTemplate.postForObject(url, null, TbCdcmrDiseaseMonitorRule[].class);
            return new ArrayList<>(Arrays.asList(ret));
        } catch (Exception e) {
            log.error("调用症候群监测 监测规则list", e);
            throw new MedicalBusinessException("调用症候群监测 监测规则list");
        }

    }

    public List<InfectedMonitorStatRespDTO> infectedMonitorStatDistributionAndStats(InfectedMonitorStatReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/distributionAndStats";
        log.info("调用传染病监测 传染病病例分布概览&传染病病例数统计，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            InfectedMonitorStatRespDTO[] list = restTemplate.postForObject(url, reqDTO, InfectedMonitorStatRespDTO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用传染病监测 传染病病例分布概览&传染病病例数统计失败", e);
            throw new MedicalBusinessException("调用传染病监测 传染病病例分布概览&传染病病例数统计");
        }
    }

    public List<InfectedMonitorStatRespDTO> infectedMonitorStatTimeTrend(InfectedMonitorStatReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/timeTrend";
        log.info("调用传染病监测 传染病病例趋势图，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            InfectedMonitorStatRespDTO[] list = restTemplate.postForObject(url, reqDTO, InfectedMonitorStatRespDTO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用传染病监测 传染病病例趋势图失败", e);
            throw new MedicalBusinessException("调用传染病监测 传染病病例趋势图失败");
        }
    }

    public List<InfectedMonitorStatRespDTO> infectedMonitorStatAbnormalTimeTrend(InfectedMonitorStatReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/abnormalTimeTrend";
        log.info("调用传染病监测 监测异常趋势图，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            InfectedMonitorStatRespDTO[] list = restTemplate.postForObject(url, reqDTO, InfectedMonitorStatRespDTO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用传染病监测 监测异常趋势图", e);
            throw new MedicalBusinessException("调用传染病监测 监测异常趋势图");
        }

    }

    public InfectedMonitorStatGeographyTopRespDTO infectedMonitorStatGeographyTop(InfectedMonitorStatReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/geography/top";
        log.info("调用传染病监测 地理分布-数量与排名，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            InfectedMonitorStatGeographyTopRespDTO ret = restTemplate.postForObject(url, reqDTO, InfectedMonitorStatGeographyTopRespDTO.class);
            return ret;
        } catch (Exception e) {
            log.error("调用传染病监测 地理分布-数量与排名", e);
            throw new MedicalBusinessException("调用传染病监测 地理分布-数量与排名");
        }
    }


    public InfectedMonitorStatMapRespDTO infectedMonitorStatGeographyMap(InfectedMonitorStatReqDTO reqDTO) {
        String url = "http://cdc-data-service/v1/pt/infectedMonitorStat/geography/map";
        log.info("调用传染病监测 地理分布-地图点位，url#{}, reqDTO#{}",url, reqDTO.toString());
        try {
            InfectedMonitorStatMapRespDTO ret = restTemplate.postForObject(url, reqDTO, InfectedMonitorStatMapRespDTO.class);
            return ret;
        } catch (Exception e) {
            log.error("调用传染病监测 地理分布-地图点位", e);
            throw new MedicalBusinessException("调用传染病监测 地理分布-地图点位");
        }
    }


    public PageData<AppSyndromeMedicalDetailDTO> medicalDistributionList(SyndromeMonitorMedicalListQueryDTO queryDTO) {
        String url = "http://cdc-data-service/v1/pt/signalMonitor/medicalDistributionList?loginUserId="+queryDTO.getLoginUserId() ;
        PageData pageInfo = restTemplate.postForObject(url,queryDTO, PageData.class);
        List list = pageInfo.getData();

        PageData<AppSyndromeMedicalDetailDTO> resultPage = new PageData<>();
        BeanUtils.copyProperties(pageInfo, resultPage);
        List<AppSyndromeMedicalDetailDTO> result = new ArrayList<>();
        for (Object object : list) {
            String s = JSON.toJSONString(object);
            AppSyndromeMedicalDetailDTO listVO = JSONObject.parseObject(s, AppSyndromeMedicalDetailDTO.class);
            result.add(listVO);
        }
        resultPage.setData(result);
        return resultPage;

    }

    public List<DataSourceConfig> getDataSource() {
        String url = "http://cdc-admin-service/pt/v1/dataSourceConfig/getList";
        try {
            DataSourceConfig[] sourceConfigs = restTemplate.getForObject(url, DataSourceConfig[].class);
            return sourceConfigs == null ? new ArrayList<>() : Lists.newArrayList(sourceConfigs);
        } catch (Exception e) {
            log.error("调用基础管理服务:查询中毒数据源配置错误：url:{}, {}", url, e);
        }
        return new ArrayList<>();
    }

    public List<OutpatientReportLog> outpatientReportLog(ReportLogParams params, String loginUserId, String loginUserName) {
        String url = "http://cdc-data-service/v1/pt/edr/infections/outpatientReportLogList?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用电子病例 门诊病例，url#{}, reqDTO#{}",url, params.toString());
        try {
            OutpatientReportLog[] list = restTemplate.postForObject(url, params, OutpatientReportLog[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用电子病例 门诊病例", e);
            throw new MedicalBusinessException("调用电子病例 门诊病例");
        }
    }
    public int outpatientReportLogCount(ReportLogParams params, String loginUserId, String loginUserName) {
        String url = "http://cdc-data-service/v1/pt/edr/infections/outpatientReportLogListCount?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用电子病例 门诊病例，url#{}, reqDTO#{}",url, params.toString());
        try {
            int count = restTemplate.postForObject(url, params, Integer.class);
            return count;
        } catch (Exception e) {
            log.error("调用电子病例 门诊病例", e);
            throw new MedicalBusinessException("调用电子病例 门诊病例");
        }
    }

    public int getMaxCsvTask() {
        final String configValue = getCdcAdminServiceConfigValue(null, CSV_TASK_MAX_CODE);
        try {
            return Integer.valueOf(configValue);
        } catch (Exception e) {
            log.error("返回值有误#{}", configValue, e);
        }
        return CSV_TASK_MAX_VALUE;
    }

    public Long getMaxCsvTimeoutTask() {
        final String configValue = getCdcAdminServiceConfigValue(null, CSV_TASK_MAX_TIMEOUT_CODE);
        try {
            return Long.valueOf(configValue);
        } catch (Exception e) {
            log.error("返回值有误#{}", configValue, e);
        }
        return CSV_TASK_MAX_TIMEOUT_VALUE;
    }
    public Integer getMaxCsvPageSize() {
        final String configValue = getCdcAdminServiceConfigValue(null, CSV_TASK_MAX_PAGE_SIZE_CODE);
        try {
            return Integer.valueOf(configValue);
        } catch (Exception e) {
            log.error("{}返回值有误#{}",CSV_TASK_MAX_PAGE_SIZE_CODE, configValue, e);
        }
        return CSV_TASK_MAX_PAGE_SIZE_VALUE;
    }

    public List<InHospitalReportLog> inHospitalReportLog(ReportLogParams params, String loginUserId, String loginUserName) {
        String url = "http://cdc-data-service/v1/pt/edr/infections/inHospitalReportLogList?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用电子病例 住院病例，url#{}, reqDTO#{}",url, params.toString());
        try {
            InHospitalReportLog[] list = restTemplate.postForObject(url, params, InHospitalReportLog[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用电子病例 住院病例", e);
            throw new MedicalBusinessException("调用电子病例 住院病例");
        }
    }

    public int inHospitalReportLogCount(ReportLogParams params, String loginUserId, String loginUserName) {
        String url = "http://cdc-data-service/v1/pt/edr/infections/inHospitalReportLogListCount?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用电子病例 住院病例count，url#{}, reqDTO#{}",url, params.toString());
        try {
            Integer count = restTemplate.postForObject(url, params, Integer.class);
            return count;
        } catch (Exception e) {
            log.error("调用电子病例 住院病例count", e);
            throw new MedicalBusinessException("调用电子病例 住院病例count");
        }
    }

    public List<LisInfoVO> getLisInfoAllList(LisInspectionQueryParams params, String loginUserId, String loginUserName) {
        String url = "http://cdc-data-service/v1/pt/lisInfo/getLisInfoAllList?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用导出检验列表，url#{}, reqDTO#{}",url, params.toString());
        try {
            LisInfoVO[] list = restTemplate.postForObject(url, params, LisInfoVO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用导出检验列表", e);
            throw new MedicalBusinessException("调用导出检验列表");
        }
    }
    public Integer getLisInfoAllListCount(LisInspectionQueryParams params, String loginUserId, String loginUserName) {
        String url = "http://cdc-data-service/v1/pt/lisInfo/getLisInfoAllListCount?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用导出检验列表，url#{}, reqDTO#{}",url, params.toString());
        try {
            Integer count = restTemplate.postForObject(url, params, Integer.class);
            return count;
        } catch (Exception e) {
            log.error("调用导出检验列表", e);
            throw new MedicalBusinessException("调用导出检验列表");
        }
    }


    public List<RisInfoVO> getRisInfoAllList(RisInspectionQueryParams params, String loginUserId, String loginUserName) {

        String url = "http://cdc-data-service/v1/pt/risInfo/getRisInfoAllList?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用导出检查列表，url#{}, reqDTO#{}",url, params.toString());
        try {
            RisInfoVO[] list = restTemplate.postForObject(url, params, RisInfoVO[].class);
            return new ArrayList<>(Arrays.asList(list));
        } catch (Exception e) {
            log.error("调用导出检查列表", e);
            throw new MedicalBusinessException("调用导出检查列表");
        }
    }

    public Integer getRisInfoAllListCount(RisInspectionQueryParams params, String loginUserId, String loginUserName) {

        String url = "http://cdc-data-service/v1/pt/risInfo/getRisInfoAllListCount?loginUserId="+loginUserId+"&loginUserName="+loginUserName;
        log.info("调用导出检查列表count，url#{}, reqDTO#{}",url, params.toString());
        try {
            Integer count = restTemplate.postForObject(url, params, Integer.class);
            return count;
        } catch (Exception e) {
            log.error("调用导出检查列表count", e);
            throw new MedicalBusinessException("调用导出检查列表count");
        }
    }

}

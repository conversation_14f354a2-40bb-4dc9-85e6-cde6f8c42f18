package com.iflytek.fpva.cdc.model.vo.poison;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
@Data
public class PoisonEventTypeCountVO {

    @ApiModelProperty(value = "食源性疾病信号数")
    private Integer foodBorneEventCount;

    @ApiModelProperty(value = "农药中毒信号数")
    private Integer pesticideEventCount;

    @ApiModelProperty(value = "职业中毒信号数")
    private Integer occupationalPoisoningEventCount;

    @ApiModelProperty(value = "非职业性一氧化碳中毒信号数")
    private Integer COPoisoningEventCount;

    @ApiModelProperty(value = "高温中暑信号数")
    private Integer heatstrokeEventCount;

    @ApiModelProperty(value = "非职业性一氧化碳中毒信号数")
    private Integer radiateEventCount;

    public PoisonEventTypeCountVO() {
        this.foodBorneEventCount = 0;
        this.pesticideEventCount = 0;
        this.occupationalPoisoningEventCount = 0;
        this.COPoisoningEventCount = 0;
        this.heatstrokeEventCount = 0;
        this.radiateEventCount = 0;
    }
}

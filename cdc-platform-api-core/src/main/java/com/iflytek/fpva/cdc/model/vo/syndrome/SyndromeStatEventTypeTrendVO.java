package com.iflytek.fpva.cdc.model.vo.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SyndromeStatEventTypeTrendVO {

    @ApiModelProperty(value = "症候群编码")
    private String syndromeCode;

    @ApiModelProperty(value = "症候群名称")
    private String syndromeName;

    @ApiModelProperty(value = "信号趋势列表")
    private List<SyndromeStatEventTrendVO> eventTrendList;
}

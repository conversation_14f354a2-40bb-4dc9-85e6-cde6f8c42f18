package com.iflytek.fpva.cdc.service.syndrome.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.*;
import com.iflytek.fpva.cdc.entity.AttentionCondition;
import com.iflytek.fpva.cdc.entity.CdcWarningEvent;
import com.iflytek.fpva.cdc.entity.TbCdcEventScUser;
import com.iflytek.fpva.cdc.entity.TbCdcInfectedWarningEvent;
import com.iflytek.fpva.cdc.mapper.common.AttentionConditionMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewHisMedicalInfoMapper;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcInfectedWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.multichannel.TbCdcewReportCardRecordMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcScCheckInfoMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.CdcWarningEventMapper;
import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.model.vo.MedicalInfoVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.StudentVO;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.distribution.AgeMedDistribution;
import com.iflytek.fpva.cdc.model.vo.distribution.DateMedDistribution;
import com.iflytek.fpva.cdc.model.vo.distribution.GenderMedDistribution;
import com.iflytek.fpva.cdc.model.vo.distribution.OrgMedicalDistributionVO;
import com.iflytek.fpva.cdc.model.vo.patient.DocMedDistribution;
import com.iflytek.fpva.cdc.service.syndrome.AttentionDetailService;
import com.iflytek.fpva.cdc.service.syndrome.CdcEventService;
import com.iflytek.fpva.cdc.service.infected.CdcReportCardService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.util.CollectionUtil;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AttentionDetailServiceImpl implements AttentionDetailService {

    @Autowired
    private AttentionConditionMapper attentionConditionMapper;

    @Resource
    private TbCdcewHisMedicalInfoMapper hisMedicalInfoMapper;

    @Autowired
    private CdcWarningEventMapper cdcWarningEventMapper;

    @Autowired
    private TbCdcInfectedWarningEventMapper tbCdcInfectedWarningEventMapper;

    @Autowired
    private TbCdcScCheckInfoMapper tbCdcScCheckInfoMapper;

    @Autowired
    private TbCdcewReportCardRecordMapper tbCdcewReportCardRecordMapper;

    @Autowired
    private CdcReportCardService cdcReportCardService;

    @Autowired
    private CdcEventService cdcEventService;

    @Autowired
    private TbCdcConfigService tbCdcConfigService;

    @Override
    public List<DocMedDistribution> docMedDistribution(List<MedicalInfoVO> list) {

        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setMinimumFractionDigits(2);
        //直接根据医生id分组
        Map<String, List<MedicalInfoVO>> map = list.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNoneBlank(item.getDoctorId()))
                .collect(Collectors.groupingBy(MedicalInfoVO::getDoctorId));
        List<DocMedDistribution> result = map.values().stream().map(value -> {
            DocMedDistribution dmd = new DocMedDistribution();
            dmd.setDoctorName(value.get(0).getDoctorName());
            dmd.setHospitalName(value.get(0).getStatDimName());
            dmd.setMedicalNum(value.size());
            dmd.setRate(numberFormat.format(dmd.getMedicalNum().doubleValue() / list.size()));
            return dmd;
        }).sorted(Comparator.comparing(DocMedDistribution::getMedicalNum).reversed()).collect(Collectors.toList());
        return result.subList(0, Math.min(10, result.size()));
    }

    @Override
    public List<OrgMedicalDistributionVO> orgMedDistribution(AttentionCondition ac, List<MedicalInfoVO> list) {
        //与机构分布不同的是 这里要根据地图级别来确定横坐标是机构还是区县
        Map<String, List<MedicalInfoVO>> map;

        if (ac.getMapLevel().equals(MapLevel.PROVINCE) || ac.getMapLevel().equals(MapLevel.CITY)) {
            //省级或市级地图 横坐标为xx市xx区
            map = list.stream().filter(item -> Objects.nonNull(item.getFullDate())).collect(Collectors.groupingBy(MedicalInfoVO::getDistrictName));
        } else {
            //区县级地图 横坐标为xx卫生院
            map = list.stream().filter(item -> Objects.nonNull(item.getFullDate())).collect(Collectors.groupingBy(MedicalInfoVO::getStatDimName));
        }

        //处理昨日今日时 如何取昨日今日的日期？
        String yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.now().minus(1, ChronoUnit.DAYS));
        String today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.now().minus(0, ChronoUnit.DAYS));

        SimpleDateFormat format = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        List<OrgMedicalDistributionVO> result = new ArrayList<>();
        map.forEach((k, v) -> {
            OrgMedicalDistributionVO vo = new OrgMedicalDistributionVO();
            vo.setOrgName(k);
            vo.setYesterday((int) v.stream().filter(m -> format.format(m.getFullDate()).equals(yesterday)).count());
            vo.setToday((int) v.stream().filter(m -> format.format(m.getFullDate()).equals(today)).count());
            result.add(vo);
        });

        List<OrgMedicalDistributionVO> res = result.stream().sorted(Comparator.comparing(OrgMedicalDistributionVO::getToday).reversed()).collect(Collectors.toList());
        return res.subList(0, Math.min(10, res.size()));

    }


    @Override
    public List<DateMedDistribution> dateMedDistribution(List<MedicalInfoVO> list) {
        Map<Date, List<MedicalInfoVO>> map = list.stream()
                .filter(item -> Objects.nonNull(item.getFullDate()))
                .collect(Collectors.groupingBy(MedicalInfoVO::getFullDate));
        List<DateMedDistribution> result = new ArrayList<>();
        map.forEach((k, v) -> {
            DateMedDistribution dm = new DateMedDistribution();
            dm.setDate(k);
            dm.setMed(v.size());
            result.add(dm);
        });
        result.sort(Comparator.comparing(DateMedDistribution::getDate));
        return result;
    }

    @Override
    public List<GenderMedDistribution> genderMedDistribution(List<MedicalInfoVO> list) {
        list.forEach(medicalInfoVO -> {
            if (Objects.isNull(medicalInfoVO.getSexDesc())) {
                medicalInfoVO.setSexDesc(Gender.UNKNOWN);
            }
        });
        Map<String, List<MedicalInfoVO>> map = list.stream().collect(
                Collectors.groupingBy(MedicalInfoVO::getSexDesc)
        );
        List<GenderMedDistribution> result = new ArrayList<>();
        if (!map.containsKey(Gender.MALE)) {
            map.put(Gender.MALE, new ArrayList<>());
        }
        if (!map.containsKey(Gender.FEMALE)) {
            map.put(Gender.FEMALE, new ArrayList<>());
        }
        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMaximumFractionDigits(2);
        map.forEach((k, v) -> {
            GenderMedDistribution distribution = new GenderMedDistribution();
            distribution.setGender(StringUtil.isEmpty(k) ? Gender.UNKNOWN : k);
            distribution.setMed(v.size());
            distribution.setPercent(nf.format(v.size() / (list.isEmpty() ? 1.0d : list.size() / 1.0d)));
            result.add(distribution);
        });
        return result;
    }

    @Override
    public List<AgeMedDistribution> ageMedDistribution(List<MedicalInfoVO> list) {
        List<AgeMedDistribution> result = new ArrayList<>();
        Age.ALL_RANGE.forEach(range -> {
            AgeMedDistribution distribution = new AgeMedDistribution();
            distribution.setDesc(range.getDesc());
            distribution.setMed((int) list.stream().filter(m -> {
                // 处理年龄
                if (!StringUtil.isEmpty(m.getDiagnoseAge())) {
                    int age = 0;
                    try {
                        age = Integer.parseInt(m.getDiagnoseAge());
                    } catch (NumberFormatException e) {
                        log.info("年龄转换错误！");
                    }
                    return age >= range.getMin()
                            && age < range.getMax();
                } else {
                    return false;
                }
            }).count());
            result.add(distribution);
        });
        return result;
    }

    @Override
    public PageData<MedicalInfoVO> medicals(String loginUserId, String id, int pageIndex, int pageSize) {
        AttentionCondition ac = attentionConditionMapper.findByCreatorAndId(loginUserId, id);
        if (ac == null) {
            throw new MedicalBusinessException("11455001", "未查询到对应的关注事件");
        }
        List<String> symptoms = CollectionUtil.getSplitList(ac.getSymptoms());
        symptoms = cdcEventService.getSymptomsFromSyndromes(symptoms);
        List<String> cityCodes = CollectionUtil.getSplitList(ac.getCityCodeList());
        List<String> districtCodes = CollectionUtil.getSplitList(ac.getDistrictCodeList());
        EventCriteria eventCriteria = EventCriteria.builder().cityCode(cityCodes).districtCode(districtCodes)
                .symptomTypeCollection(symptoms)
                .processingStatusCollection(ProcessingStatus.ALL_STATUS)
                .minBeginTime(ac.getBeginDate())
                .maxBeginTime(ac.getEndDate())
                .sortByAiScreen(false)
                .dateType(CommonConstants.DATE_TYPE_OCCURRENCE)
                .sortType(SortType.EVENT_START_TIME_ASC).build();
        List<CdcWarningEvent> events = cdcWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());

        PageMethod.startPage(pageIndex, pageSize);
        List<MedicalInfoVO> list = new ArrayList<>();
        if (!events.isEmpty()) {
            List<String> eventList = events.stream().map(CdcWarningEvent::getId).collect(Collectors.toList());
            list = hisMedicalInfoMapper.findMedicalInfosByEventIds(eventList);
            list.forEach(medicalInfoVO -> {

                // 处理症状信息  将|隔开  变成去重之后 用,隔开
                String symptomType = medicalInfoVO.getSymptomType();
                if (!StringUtils.isEmpty(symptomType)) {

                    String[] split = symptomType.split("\\|");
                    List<String> strings = Arrays.stream(split).distinct().collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(strings)) {
                        StringBuilder str = new StringBuilder(strings.get(0));
                        for (int i = 1; i < strings.size(); i++) {
                            if (!StringUtils.isEmpty(strings.get(i))) {
                                str.append(",").append(strings.get(i));
                            }
                        }
                        medicalInfoVO.setSymptomType(str.toString());
                    }
                }
            });
        }

        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            list.forEach(DesensitizeVOUtils::desensitizeMedicalInfoVo);
        }

        PageInfo<MedicalInfoVO> pageInfo = new PageInfo<>(list);
        PageData<MedicalInfoVO> data = PageData.fromPageInfo(pageInfo);
        data.setData(pageInfo.getList());
        return data;
    }

    @Override
    public PageData<MedicalInfoVO> infectedMedicals(String loginUserId, String id, int pageIndex, int pageSize) {
        AttentionCondition ac = attentionConditionMapper.findByCreatorAndId(loginUserId, id);
        if (ac == null) {
            throw new MedicalBusinessException("11455001", "未查询到对应的关注事件");
        }
        List<String> symptoms = CollectionUtil.getSplitList(ac.getSymptoms());
        Collection<String> codeList = new HashSet<>();
        if (!CollectionUtils.isEmpty(symptoms)) {
            codeList = getInfectedCodeList(symptoms);
        }
        List<String> cityCodes = CollectionUtil.getSplitList(ac.getCityCodeList());
        List<String> districtCodes = CollectionUtil.getSplitList(ac.getDistrictCodeList());
        EventCriteria eventCriteria = EventCriteria.builder().cityCode(cityCodes).districtCode(districtCodes)
                .symptomTypeCollection(codeList)
                .processingStatusCollection(ProcessingStatus.ALL_STATUS)
                .minBeginTime(ac.getBeginDate())
                .maxBeginTime(ac.getEndDate())
                .sortByAiScreen(false)
                .dateType(CommonConstants.DATE_TYPE_OCCURRENCE)
                .sortType(SortType.EVENT_START_TIME_ASC).build();
        List<TbCdcInfectedWarningEvent> events = tbCdcInfectedWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());

        PageMethod.startPage(pageIndex, pageSize);
        List<MedicalInfoVO> list = new ArrayList<>();
        if (!events.isEmpty()) {
            //根据事件获取具体病历列表 此处是in查询
            list = tbCdcewReportCardRecordMapper.findMedicalInfoByEvents(events);
        }

        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            list.forEach(DesensitizeVOUtils::desensitizeMedicalInfoVo);
        }

        PageInfo<MedicalInfoVO> pageInfo = new PageInfo<>(list);
        PageData<MedicalInfoVO> data = PageData.fromPageInfo(pageInfo);
        data.setData(pageInfo.getList());
        return data;
    }

    @Override
    public PageData<StudentVO> students(String loginUserId, String id, int pageIndex, int pageSize) {
        AttentionCondition ac = attentionConditionMapper.findByCreatorAndId(loginUserId, id);
        if (ac == null) {
            throw new MedicalBusinessException("11455001", "未查询到对应的关注事件");
        }

        List<String> symptoms = CollectionUtil.getSplitList(ac.getSymptoms());
        List<String> cityCodes = CollectionUtil.getSplitList(ac.getCityCodeList());
        List<String> districtCodes = CollectionUtil.getSplitList(ac.getDistrictCodeList());
        EventCriteria eventCriteria = EventCriteria.builder().cityCode(cityCodes).districtCode(districtCodes)
                .symptomTypeCollection(symptoms)
                .processingStatusCollection(ProcessingStatus.ALL_STATUS)
                .minBeginTime(ac.getBeginDate())
                .maxBeginTime(ac.getEndDate())
                .sortByAiScreen(false)
                .dateType(CommonConstants.DATE_TYPE_OCCURRENCE)
                .sortType(SortType.EVENT_START_TIME_ASC).build();
        List<CdcWarningEvent> events = cdcWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());

        if (!events.isEmpty()) {
            PageMethod.startPage(pageIndex, pageSize);
            List<TbCdcEventScUser> eventSchoolUserList = tbCdcScCheckInfoMapper.findByEventIdsIn(events.stream().map(CdcWarningEvent::getId).collect(Collectors.toList()));
            PageInfo<TbCdcEventScUser> pageInfo = new PageInfo<>(eventSchoolUserList);
            PageData<StudentVO> data = PageData.fromPageInfo(pageInfo);

            List<StudentVO> list = pageInfo.getList().stream().map(StudentVO::fromEntity).collect(Collectors.toList());
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                list.forEach(DesensitizeVOUtils::desensitizeStudentVo);
            }
            data.setData(list);
            return data;
        } else {
            return new PageData<>();
        }
    }

    @Override
    public MedicalInfoVO medical(String loginUserId, String sourceKey) {
        MedicalInfoVO medicalInfoVO = hisMedicalInfoMapper.findMedicalInfoBySourceKey(sourceKey);

        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            DesensitizeVOUtils.desensitizeMedicalInfoVo(medicalInfoVO);
        }
        return medicalInfoVO;
    }

    @Override
    public MedicalInfoVO infectedMedical(String loginUserId, String sourceKey) {
        MedicalInfoVO medicalInfoVO = tbCdcewReportCardRecordMapper.findMedicalInfoBySourceKey(sourceKey);
        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            DesensitizeVOUtils.desensitizeMedicalInfoVo(medicalInfoVO);
        }
        return medicalInfoVO;
    }

    private Collection<String> getInfectedCodeList(Collection<String> srcList) {
        List<CascadeVO> infectedCodeList = cdcReportCardService.getInfectedNameList();
        List<String> resultList = new ArrayList<>();
        for (String code : srcList) {
            CascadeVO vo = findInfectedCode(code, infectedCodeList);
            if (vo != null && CollectionUtils.isEmpty(vo.getChildren())) {
                resultList.add(vo.getValue());
            } else if (vo != null && !CollectionUtils.isEmpty(vo.getChildren())) {
                for (CascadeVO childVO : vo.getChildren()) {
                    addInfectedCodes(resultList, childVO);
                }
            }
        }

        if (resultList.isEmpty()) {
            resultList.add("-1");
        }

        return resultList;
    }


    private CascadeVO findInfectedCode(String code, List<CascadeVO> infectedCodeList) {
        for (CascadeVO vo : infectedCodeList) {
            if (code.equals(vo.getValue())) {
                return vo;
            } else if (!CollectionUtils.isEmpty(vo.getChildren())) {
                CascadeVO resultVO = findInfectedCode(code, vo.getChildren());
                if (resultVO != null) {
                    return resultVO;
                }
            }
        }
        return null;
    }

    private void addInfectedCodes(Collection<String> resultList, CascadeVO vo) {
        if (CollectionUtils.isEmpty(vo.getChildren())) {
            resultList.add(vo.getValue());
        } else {
            for (CascadeVO childVO : vo.getChildren()) {
                addInfectedCodes(resultList, childVO);
            }
        }
    }
}

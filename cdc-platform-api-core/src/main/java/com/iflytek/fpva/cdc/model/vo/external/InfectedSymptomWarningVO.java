package com.iflytek.fpva.cdc.model.vo.external;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.fpva.cdc.model.vo.MedicalInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InfectedSymptomWarningVO {

    @ApiModelProperty("事件id")
    private String eventId;

    @ApiModelProperty("症候群编码")
    private String syndromeCode;

    @ApiModelProperty("症候群名称")
    private String syndrome;

    @ApiModelProperty("症状名称")
    private String symptom;

    @ApiModelProperty("信号包含的病历列表")
    private List<MedicalInfoVO> medicalList;

    @ApiModelProperty("信号状态编码")
    private Integer processingStatus;

    @ApiModelProperty("信号状态名称")
    private String processingStatusName;

    @ApiModelProperty("事件发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date eventStartDate;

    @ApiModelProperty("事件发生机构")
    private String place;

}

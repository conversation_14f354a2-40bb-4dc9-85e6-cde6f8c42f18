package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.entity.CdcConfig;
import com.iflytek.fpva.cdc.model.vo.DesensitizedVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TbCdcConfigService {

    /**
     * 根据用户loginUserId获取配置信息
     *
     * @return 配置类
     */
    List<CdcConfig> getAllConfig();

    /**
     * 更新用户配置
     *
     * @param id      用户id
     * @param configs 配置信息
     */
    void updateByConfigKey(String id, String name, List<CdcConfig> configs);

    /**
     * 获取是否AI初筛置顶  -- 症候群
     *
     * @return
     */
    boolean isSyndromeEnableAiScreen();

    /**
     * 获取是否AI初筛置顶  -- 传染病
     *
     * @return
     */
    boolean isInfectedEnableAiScreen();

    /**
     * 获取是否AI初筛置顶  -- 学校症状
     *
     * @return
     */
    boolean isScSymptomEnableAiScreen();

    /**
     * 获取是否AI初筛置顶  -- 学校症状
     *
     * @return
     */
    boolean isPoisonEnableAiScreen();

    boolean isFeverOutpatientEnableAiScreen();

    boolean isBowelOutpatientEnableAiScreen();

    boolean isUnknownReasonEnableAiScreen();

    boolean isPreventionControlEnableAiScreen();

    boolean isCustomizedEnableAiScreen();

    /**
     * 判断是否是普通用户
     *
     * @param loginUserId 用户Id
     */
    boolean isAdminUser(String loginUserId);

    /**
     * 判断是否是普通用户
     *
     * @param loginUserId 用户Id
     */
    boolean isInternUser(String loginUserId);

    /**
     * 判断是否是普通用户
     *
     * @param loginUserId 用户Id
     */
    boolean isOrdinaryUser(String loginUserId);

    /**
     * 获取是否显示脱敏
     *
     * @param loginUserId 用户Id
     * @return
     */
    boolean isDesensitization(String loginUserId);

    boolean isMapNarrow(String loginUserId);

    /**
     * 获取是否uap脱敏
     *
     * @param loginUserId 用户Id
     * @return
     */
    boolean isDesensitizationUser(String loginUserId);


    /**
     * 根据配置Key获取配置Value
     *
     * @param configKey 配置Key
     * @param allConfig 所有配置
     * @return
     */
    Double getCdcConfigValueByConfigKey(List<CdcConfig> allConfig, String configKey);

    long getSyndromeResponseTimeoutConfig();

    long getSyndromeProcessingTimeoutConfig();

    long getInfectedResponseTimeoutConfig();

    long getInfectedProcessingTimeoutConfig();

    long getSchoolResponseTimeoutConfig();

    long getSchoolProcessingTimeoutConfig();

    long getPoisonResponseTimeoutConfig();

    long getPoisonProcessingTimeoutConfig();

    long getUnknownReasonResponseTimeoutConfig();

    long getUnknownReasonProcessingTimeoutConfig();

    long getPreventionControlResponseTimeoutConfig();

    long getPreventionControlProcessingTimeoutConfig();

    long getCustomizedProcessingTimeoutConfig();

    int getSyndromeEventDefaultSort();

    int getInfectedEventDefaultSort();

    int getScSymptomEventDefaultSort();

    int getPoisoningEventDefaultSort();

    int getCustomizedEventDefaultSort();

    int getFeverOutpatientEventDefaultSort();

    int getBowelOutpatientEventDefaultSort();

    int getUnknownReasonEventDefaultSort();

    int getPreventionControlEventDefaultSort();

    boolean isEditWarningGrade(String loginUserId);

    long getBowelOutpatientProcessingTimeoutConfig();

    long getFeverOutpatientProcessingTimeoutConfig();

    DesensitizedVO getDataDesensitization(String loginUserId);

    Integer getHeatStrokeDefaultSort();

    Integer getCarbonDefaultSort();

    Integer getOverexposureDefaultSort();

    Integer getPoisonOtherDefaultSort();

    Integer getPesticideDefaultSort();

    Integer getFoodDefaultSort();
}
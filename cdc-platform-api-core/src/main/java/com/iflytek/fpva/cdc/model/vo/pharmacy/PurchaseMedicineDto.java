package com.iflytek.fpva.cdc.model.vo.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 购药相关信息
 *
 * <AUTHOR>
 * @date 2022-05-20 16:49
 */
@Data
@ApiModel("购药事件信息")
public class PurchaseMedicineDto {
    /**
     * 药店信息
     */
    private Pharmacy pharmacy;
    /**
     * 购药人信息
     */
    private Purchaser purchaser;
    /**
     * 登记人信息
     */
    private Register register;
    /**
     * 药品信息
     */
    @ApiModelProperty("所购药品信息")
    private List<Medicine> medicine;
    /**
     * 处方文件
     */
    @ApiModelProperty("处方文件")
    private String prescriptionUrl;
}

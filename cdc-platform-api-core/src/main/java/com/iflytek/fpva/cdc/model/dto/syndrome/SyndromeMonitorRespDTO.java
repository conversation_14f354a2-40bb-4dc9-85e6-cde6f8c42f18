package com.iflytek.fpva.cdc.model.dto.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date :2023/11/2 18:47
 * @description:SyndromeMonitorRespDTO
 */
@Data
public class SyndromeMonitorRespDTO {
    @ApiModelProperty("月-日")
    private String shortFullDate;

    @ApiModelProperty("时间")
    private String fullDate;

    @ApiModelProperty("症候群编码")
    private String syndromeCode;

    @ApiModelProperty("症候群名称")
    private String syndromeName;

    @ApiModelProperty("占比")
    private String rate;

    @ApiModelProperty("病例数")
    private Long medCnt;

    @ApiModelProperty("去年病例数")
    private Long lastYearMedCnt;

    @ApiModelProperty("去年同期")
    private String ratePeriod;

    @ApiModelProperty("病例总数")
    private Long totalMedCnt;

    @ApiModelProperty("病例总数去年同期")
    private String totalRatePeriod;

    @ApiModelProperty("是否异常; 是:Y, 否:N")
    private String isAbnormal;


    @ApiModelProperty("异常病例数")
    private Long abnormalMedCnt;

    @ApiModelProperty("异常病例总数")
    private Long totalAbnormalMedCnt;

    private String ruleId;

    @ApiModelProperty("配置规则")
    private TbCdcmrDiseaseMonitorRule rule;
}

package com.iflytek.fpva.cdc.model.vo.patient;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class Symptom {

    /**
     * 症状就诊时间
     * **/
    @JsonFormat(pattern = "yyyy-MM-dd" ,timezone = "GMT+8")
    private Date symptomTime;

    /**
     * 症状对应病历数量
     * **/
    private Integer symptomMedNum = 0;

}

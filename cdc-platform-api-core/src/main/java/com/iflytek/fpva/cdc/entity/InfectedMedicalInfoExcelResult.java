package com.iflytek.fpva.cdc.entity;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.constant.enums.ReportCardStatusV2Enum;
import com.iflytek.fpva.cdc.model.vo.InfectedPatientVO;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.Data;

@Data
public class InfectedMedicalInfoExcelResult {
    //姓名
    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;
    //报卡状态
    @ExcelColumn(name = "报卡状态", column = 2)
    private String status;
    //现住址
    @ExcelColumn(name = "现住址", column = 3)
    private String livingAddress;
    //发病时间
    @ExcelColumn(name = "发病时间", column = 4)
    private String onsetDate;
    //性别
    @ExcelColumn(name = "性别", column = 5)
    private String sexDesc;
    //年龄
    @ExcelColumn(name = "年龄", column = 6)
    private String diagnoseAge;
    //来源
    @ExcelColumn(name = "来源", column = 7)
    private String statDimName;
    //诊断
    @ExcelColumn(name = "诊断时间", column = 8)
    private String diagnoseTime;
    //工作单位/学校
    @ExcelColumn(name = "工作单位/学校", column = 9)
    private String company;
    //职业
    @ExcelColumn(name = "人群分类", column = 10)
    private String personTypeName;

    public InfectedMedicalInfoExcelResult(InfectedPatientVO infectedPatientVO) {
        this.patientName = infectedPatientVO.getPatientName();
        this.livingAddress = infectedPatientVO.getLivingAddress();
        this.onsetDate = DateUtils.parseDate(infectedPatientVO.getOnsetDate());
        this.sexDesc = infectedPatientVO.getSexDesc();
        if (StringUtils.isBlank(infectedPatientVO.getDiagnoseAge())) {
            this.diagnoseAge = "-";
        } else {
            this.diagnoseAge = infectedPatientVO.getDiagnoseAge() + infectedPatientVO.getDiagnoseAgeUnit();
        }
        this.statDimName = infectedPatientVO.getStatDimName();
        this.diagnoseTime = DateUtils.parseDate(infectedPatientVO.getOutPatientTime());
        this.personTypeName = infectedPatientVO.getPersonTypeName();
        this.company = infectedPatientVO.getCompanyName();
        this.status = ReportCardStatusV2Enum.convertOriginalStatus(infectedPatientVO.getStatus());
    }

}

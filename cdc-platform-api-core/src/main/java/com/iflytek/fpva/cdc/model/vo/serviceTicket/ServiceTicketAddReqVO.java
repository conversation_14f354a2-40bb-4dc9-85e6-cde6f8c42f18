package com.iflytek.fpva.cdc.model.vo.serviceTicket;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * @date :2024/3/12 10:13
 * @description:ServiceTicketReqVO
 */
@ApiModel( description="工单新增、编辑请求参数")
@Data
public class ServiceTicketAddReqVO {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "标题名称")
    private String titleName;

    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    @ApiModelProperty(value = "问题与意见")
    private String content;

    @ApiModelProperty(value = "附件id集合逗号分隔")
    private String attachments;

}

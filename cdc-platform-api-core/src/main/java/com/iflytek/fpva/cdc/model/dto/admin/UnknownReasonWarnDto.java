package com.iflytek.fpva.cdc.model.dto.admin;

import com.iflytek.fpva.cdc.entity.TbCdcmrUnknownReasonWarnRule;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UnknownReasonWarnDto {

    /**
     * 主键
     */
    private String id;

    /**
     * 不明原因病种编码
     */
    private String diseaseCode;

    /**
     * 不明原因病种名称
     */
    private String diseaseName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开启状态：0禁用1启用
     */
    private Integer status;

    /**
     * 删除标识：0未删除1删除
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 潜伏期(天)
     */
    private Integer incubation;

    /**
     * 预警短信发送类型编码
     */
    private String smsSendTypeCode;

    /**
     * 预警短信发送类型描述
     */
    private String smsSendTypeDesc;

    private List<TbCdcmrUnknownReasonWarnRule> ruleList;

    Integer ruleCount;

}

package com.iflytek.fpva.cdc.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.constant.Gender;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedWarningDetail;
import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedWarningEvent;
import com.iflytek.fpva.cdc.mapper.common.TbCdcHisMedicalExtendMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewHisMedicalInfoMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedWarningDetailMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.poison.*;
import com.iflytek.fpva.cdc.mapper.syndrome.CdcWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcWarningDetailMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcewMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonMedRelationMapper;
import com.iflytek.fpva.cdc.model.StatDimKey;
import com.iflytek.fpva.cdc.model.vo.PatientExtendVO;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedRecordVO;
import com.iflytek.fpva.cdc.model.dto.infected.MedicalHistoryLog;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMedicalInfoDto;
import com.iflytek.fpva.cdc.model.dto.school.SymptomRecordDto;
import com.iflytek.fpva.cdc.model.resultmap.MedicalInfoKey;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.distribution.OrgMedicalDistributionVO;
import com.iflytek.fpva.cdc.model.vo.outpatient.OutpatientRecordVO;
import com.iflytek.fpva.cdc.model.vo.pharmacy.PurchaseMedicineDto;
import com.iflytek.fpva.cdc.model.vo.poison.*;
import com.iflytek.fpva.cdc.model.vo.unknown.UnknownReasonRecordVO;
import com.iflytek.fpva.cdc.service.common.HisMedicalInfoService;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.multichannel.PharmacyService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.service.symptom.CdcScSymptomRecordService;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class HisMedicalInfoServiceImpl implements HisMedicalInfoService {

    @Resource
    private TbCdcewMedRelationMapper cdcMedRelationMapper;

    @Resource
    private TbCdcewHisMedicalInfoMapper tbCdcewHisMedicalInfoMapper;

    @Resource
    private TbCdcWarningDetailMapper tbCdcWarningDetailMapper;

    @Resource
    private CdcWarningEventMapper cdcWarningEventMapper;

    @Resource
    private TbCdcHisMedicalExtendMapper cdcHisMedicalExtendMapper;


    @Resource
    private PharmacyService pharmacyService;

    @Resource
    private CdcScSymptomRecordService cdcScSymptomRecordService;

    @Resource
    private OrgService orgService;

    @Resource
    TbCdcConfigService tbCdcConfigService;

    @Resource
    TbCdcewPoisonMedRelationMapper tbCdcewPoisonMedRelationMapper;

    @Resource
    PoisonEventService poisonEventService;

    @Resource
    RestService restService;

    @Resource
    FoodBorneReportMapper foodBorneReportMapper;

    @Resource
    HeatstrokeReportMapper heatstrokeReportMapper;

    @Resource
    TbCdcewUnknownReasonMedRelationMapper tbCdcewUnknownReasonMedRelationMapper;

    @Resource
    TbCdcewCustomizedWarningEventMapper tbCdcewCustomizedWarningEventMapper;

    @Resource
    TbCdcewCustomizedWarningDetailMapper tbCdcewCustomizedWarningDetailMapper;

    @Resource
    TbCdcewCustomizedMedRelationMapper tbCdcewCustomizedMedRelationMapper;

    @Resource
    TbCdcewRepcardCarbonMethysisReportMapper carbonMethysisReportMapper;

    @Resource
    TbCdcewRepcardPesticidePoisonReportMapper pesticideReportMapper;

    @Override
    public CdcHisMedicalInfoDetailVo getOne(String sourceKey, String eventId) {
        List<String> sourceTypes = cdcWarningEventMapper.findSourceTypeById(eventId);
        CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(eventId);

        CdcHisMedicalInfoDetailVo medicalInfoDetailVo = new CdcHisMedicalInfoDetailVo();
        if (sourceTypes.contains(SourceTypeEnum.HOSPITAL.getCode()) || sourceTypes.contains(SourceTypeEnum.PRIMARY_CARE.getCode()) || EventTypeEnum.JDQY.getName().equals(event.getEventType())) {
            TbCdcewHisMedicalInfo medicalInfo = tbCdcewHisMedicalInfoMapper.findBySourceKey(sourceKey);
            medicalInfoDetailVo.setCdcHisMedicalInfo(medicalInfo);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewHisMedicalInfo(medicalInfo));
        } else if (sourceTypes.contains(SourceTypeEnum.PHARMACY.getCode())) {
            PurchaseMedicineDto purchaseMedicine = pharmacyService.getPurchaseMedicine(sourceKey);
            medicalInfoDetailVo.setPurchaseMedicineDto(purchaseMedicine);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromPurchaseMedicineDto(purchaseMedicine));
        } else if (sourceTypes.contains(SourceTypeEnum.SCHOOL.getCode())) {
            TbCdcewScSymptomRecord tbCdcewScSymptomRecord = cdcScSymptomRecordService.selectByPrimaryKey(sourceKey);
            medicalInfoDetailVo.setStudentVO(tbCdcewScSymptomRecord);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewScSymptomRecord(tbCdcewScSymptomRecord));
        }

        List<TbCdcWarningDetail> detailList = tbCdcWarningDetailMapper.findByEventId(eventId);
        List<String> collect = detailList.stream().map(TbCdcWarningDetail::getId).collect(Collectors.toList());
        List<TbCdcHisMedicalExtend> cdcHisMedicalExtends = cdcHisMedicalExtendMapper.findByWarnDetailIdInAndSourceKeyIn(sourceKey, collect);
        if (CollectionUtils.isNotEmpty(cdcHisMedicalExtends)) {
            TbCdcHisMedicalExtend hisMedicalExtend = cdcHisMedicalExtends.get(0);
            String callResult = hisMedicalExtend.getCallResult();
            Map<String, String> map = JSON.parseObject(callResult, LinkedHashMap.class);
            TbCdcHisMedicalExtendVo hisMedicalExtendVo = TbCdcHisMedicalExtendVo.fromEntity(hisMedicalExtend);
            hisMedicalExtendVo.setCallResult(map);
            medicalInfoDetailVo.setMedicalExtend(hisMedicalExtendVo);
        }

        return medicalInfoDetailVo;
    }

    @Override
    public CdcHisMedicalInfoDetailVo getCustomizedOne(String sourceKey, String eventId) {
        List<String> sourceTypes = tbCdcewCustomizedWarningEventMapper.findSourceTypeById(eventId);
        TbCdcewCustomizedWarningEvent event = tbCdcewCustomizedWarningEventMapper.selectByPrimaryKey(eventId);

        CdcHisMedicalInfoDetailVo medicalInfoDetailVo = new CdcHisMedicalInfoDetailVo();
        if (sourceTypes.contains(SourceTypeEnum.HOSPITAL.getCode()) || sourceTypes.contains(SourceTypeEnum.PRIMARY_CARE.getCode()) || EventTypeEnum.JDQY.getName().equals(event.getEventType())) {
            TbCdcewHisMedicalInfo medicalInfo = tbCdcewHisMedicalInfoMapper.findBySourceKey(sourceKey);
            medicalInfoDetailVo.setCdcHisMedicalInfo(medicalInfo);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewHisMedicalInfo(medicalInfo));
        } else if (sourceTypes.contains(SourceTypeEnum.PHARMACY.getCode())) {
            PurchaseMedicineDto purchaseMedicine = pharmacyService.getPurchaseMedicine(sourceKey);
            medicalInfoDetailVo.setPurchaseMedicineDto(purchaseMedicine);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromPurchaseMedicineDto(purchaseMedicine));
        } else if (sourceTypes.contains(SourceTypeEnum.SCHOOL.getCode()) || EventTypeEnum.XXDW.getName().equals(event.getEventType())) {
            TbCdcewScSymptomRecord tbCdcewScSymptomRecord = cdcScSymptomRecordService.selectByPrimaryKey(sourceKey);
            TbCdcewHisMedicalInfo medicalInfo = tbCdcewHisMedicalInfoMapper.findBySourceKey(sourceKey);
            if (tbCdcewScSymptomRecord != null) {
                medicalInfoDetailVo.setStudentVO(tbCdcewScSymptomRecord);
                medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewScSymptomRecord(tbCdcewScSymptomRecord));
            } else {
                medicalInfoDetailVo.setCdcHisMedicalInfo(medicalInfo);
                medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewHisMedicalInfo(medicalInfo));
            }
        }

        List<TbCdcewCustomizedWarningDetail> detailList = tbCdcewCustomizedWarningDetailMapper.findByEventId(eventId);
        List<String> collect = detailList.stream().map(TbCdcewCustomizedWarningDetail::getId).collect(Collectors.toList());
        List<TbCdcHisMedicalExtend> cdcHisMedicalExtends = cdcHisMedicalExtendMapper.findByWarnDetailIdInAndSourceKeyIn(sourceKey, collect);
        if (CollectionUtils.isNotEmpty(cdcHisMedicalExtends)) {
            TbCdcHisMedicalExtend hisMedicalExtend = cdcHisMedicalExtends.get(0);
            String callResult = hisMedicalExtend.getCallResult();
            Map<String, String> map = JSON.parseObject(callResult, LinkedHashMap.class);
            TbCdcHisMedicalExtendVo hisMedicalExtendVo = TbCdcHisMedicalExtendVo.fromEntity(hisMedicalExtend);
            hisMedicalExtendVo.setCallResult(map);
            medicalInfoDetailVo.setMedicalExtend(hisMedicalExtendVo);
        }

        return medicalInfoDetailVo;
    }

    @Override
    public List<HisMedicalInfoVo> findByEvent(String eventId, boolean total) {

        List<HisMedicalInfoVo> medicalInfoVos = new ArrayList<>();
        List<TbCdcewHisMedicalInfo> medicalInfos;
        if (total) {
            //累计数据
            List<MedicalInfoKey> keys = tbCdcewHisMedicalInfoMapper.findKeysByEvent(eventId);
            List<String> sourceKeys = keys.stream().map(MedicalInfoKey::getSourceKey).collect(Collectors.toList());

            medicalInfos = tbCdcewHisMedicalInfoMapper.getMedicalInfoBySourceKeyList(sourceKeys);
            medicalInfos = medicalInfos.stream().distinct().collect(Collectors.toList());
        } else {
            //事件开始当天数据
            //取该事件当天的那条信号
            TbCdcWarningDetail detail = tbCdcWarningDetailMapper.findFirstByEventId(eventId);
            List<MedicalInfoKey> keys = tbCdcewHisMedicalInfoMapper.findKeysByDetailFirstDay(detail.getId());
            List<String> sourceKeys = keys.stream().map(MedicalInfoKey::getSourceKey).collect(Collectors.toList());
            medicalInfos = tbCdcewHisMedicalInfoMapper.getMedicalInfoBySourceKeyList(sourceKeys);
            medicalInfos = medicalInfos.stream().distinct().collect(Collectors.toList());
        }

        //病例列表中新增外呼状态
        medicalInfos.forEach(medical -> {
            HisMedicalInfoVo medicalInfoVo = new HisMedicalInfoVo();
            List<TbCdcHisMedicalExtend> hisMedicalExtends = cdcHisMedicalExtendMapper.findBySourceKeyAndWarnEventId(
                    medical.getSourceKey(), eventId);
            medicalInfoVo.setHisMedicalInfo(medical);
            //如果扩展表有数据，则插入外呼状态和批次号数据
            if (CollectionUtils.isNotEmpty(hisMedicalExtends)) {
                TbCdcHisMedicalExtend cdcHisMedicalExtend = hisMedicalExtends.get(0);
                medicalInfoVo.setOutCallStatus(cdcHisMedicalExtend.getCallStatus());
                medicalInfoVo.setOutCallBatchId(cdcHisMedicalExtend.getCallBatchId());
            } else {
                medicalInfoVo.setOutCallStatus(MedicalCallStatusEnum.NOT_CALL.getCode());
            }
            List<String> detailIds = cdcMedRelationMapper.findDetailIdBySourceKeyAndEventId(medical.getSourceKey(), eventId);
            if (CollectionUtils.isNotEmpty(detailIds)) {
                //同一事件下同一份病例对应唯一的信号
                medicalInfoVo.setDetailId(detailIds.get(0));
            }
            medicalInfoVos.add(medicalInfoVo);
        });
        return medicalInfoVos;
    }

    @Override
    public List<MedicalInfoVO> findMedByEventTimeRange(String eventId, String beginDate, String endDate) {
        return tbCdcewHisMedicalInfoMapper.findRecordsByEventTimeRange(Arrays.asList(eventId),
                StringUtils.isNotBlank(beginDate) ? TimeConstant.formatDate(beginDate) : null,
                StringUtils.isNotBlank(endDate) ? TimeConstant.formatDate(endDate) : null);
    }

    @Override
    public PopulationDistribution findByEvent(List<HisMedicalInfo> list) {
        PopulationDistribution populationDistribution = new PopulationDistribution();
        long male = list.stream().filter(m -> m.getSexDesc().equals(Gender.MALE)).count();
        long female = list.stream().filter(m -> m.getSexDesc().equals(Gender.FEMALE)).count();
        long unknown = list.size() - male - female;
        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMaximumFractionDigits(2);
        populationDistribution.setMalePercent(nf.format((double) male / list.size()));
        populationDistribution.setFemalePercent(nf.format((double) female / list.size()));
        populationDistribution.setUnknownGenderPercent(nf.format((double) unknown / list.size()));
        return populationDistribution;
    }

    @Override
    public OrgMedicalVO orgMedDistribution(String eventId, boolean total, String endTime) {
        OrgMedicalVO orgMedicalVO = new OrgMedicalVO();
        SimpleDateFormat sdf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);

        CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(eventId);
        String districtCode = event.getDistrictCode();
        String yesterdayStr;
        String todayStr;
        Date date;
        if (total) {
            //统计的时间 如果事件没结束 则取当前时间 如果事件结束了 则取事件结束时间
            date = event.getEndDate();
            if (!org.springframework.util.StringUtils.isEmpty(endTime)) {
                try {
                    date = sdf.parse(endTime);
                } catch (ParseException e) {
                    throw new MedicalBusinessException("11452001", "时间格式化错误:/n endTime:" + endTime);
                }
            }

            if (date == null) {
                yesterdayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.now().minus(1, ChronoUnit.DAYS));
                todayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.now().minus(0, ChronoUnit.DAYS));
            } else {
                yesterdayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
                todayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));
            }
        } else {
            //如果取预警发生第一天 则日期取事件开始时间
            date = event.getBeginDate();
            yesterdayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
            todayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()));
        }

        Date yesterday = DateFormatUtils.formatDate(yesterdayStr);
        Date today = DateFormatUtils.formatDate(todayStr);


        String sourceType = event.getSourceType();
        String eventType = event.getEventType();
        Map<Date, List<OrgMedCount>> dateOrgMedCountMap = tbCdcewHisMedicalInfoMapper.getOrgMedCountBy(yesterday,
                                                                                                       today,
                                                                                                       districtCode,
                                                                                                       event.getSymptomType(),
                                                                                                       eventType)
                                                                                     .stream()
                                                                                     .collect(Collectors.groupingBy(OrgMedCount::getDate));

        List<StatDimKey> statDimKeyList = orgService.getStatDimKeysBy(districtCode, eventType);

        List<OrgMedCount> yesterdayList = dateOrgMedCountMap.getOrDefault(yesterday, new ArrayList<>());
        List<OrgMedCount> todayList = dateOrgMedCountMap.getOrDefault(today, new ArrayList<>());

        List<OrgMedicalDistributionVO> orgMedicalDistributionVOS = OrgMedicalDistributionVO.calcStatDimDistribution(statDimKeyList, yesterdayList, todayList);

        orgMedicalVO.setList(orgMedicalDistributionVOS);
        orgMedicalVO.setYesterday(yesterdayStr);
        orgMedicalVO.setToday(todayStr);

        return orgMedicalVO;
    }

    @Override
    public List<MedicalAddressDistributionVO> getMedicalAddressDistributionByEvent(String eventId) {
        List<MedicalAddressDistributionVO> resultList = cdcWarningEventMapper.getMedicalAddressDistributionByEvent(eventId);
        if (!resultList.isEmpty()) {
            resultList.forEach(n -> {
                if (StringUtils.isBlank(n.getAddressName())) {
                    n.setAddressName("不详");
                }
            });
        }
        return resultList;
    }

    @Override
    public List<PoisonMedicalInfoDto> getPoisonRecordByEventId(String eventId, String pantientName, Integer sortType, String doctorName, String companyName) {

        List<PoisonMedicalInfoDto> poisonRecordVOList = getDetailsByEventId(eventId, pantientName, doctorName, companyName, sortType);

        // 当天的  isNewMedRecord 设置为true
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        poisonRecordVOList.forEach(item -> {
            String fullDate = item.getFullDate() == null ? null : sdf.format(item.getFullDate());
            if (now.equals(fullDate)) {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_YES);
            } else {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_NO);
            }
        });

        //姓名和身份证号都相同且都有值的则需要合并为同一人
        //若只有姓名相同，身份证号为空则保持为不同的人；若姓名为空，身份证号相同也保持为不同的人

        //取出身份证号或者名字为空的情况，只要有一个为空则保持为不同的人
        List<PoisonMedicalInfoDto> keepDifPerson = poisonRecordVOList.stream().filter(poisonPatientVO -> poisonPatientVO.getPatientName() == null || "".equals(poisonPatientVO.getPatientName().trim()) || poisonPatientVO.getIdentityNo() == null || "".equals(poisonPatientVO.getIdentityNo().trim())).collect(Collectors.toList());
        keepDifPerson.forEach(item -> {
            List<String> tmp = new ArrayList<>();
            tmp.add(item.getSourceKey());
            item.setSourceKeyList(tmp);
        });
        //取出身份证号和名字都不为空的情况的报卡
        List<PoisonMedicalInfoDto> poisonPatientMsgNotNull = poisonRecordVOList.stream().filter(poisonPatientVO -> poisonPatientVO.getPatientName() != null && !"".equals(poisonPatientVO.getPatientName().trim()) && poisonPatientVO.getIdentityNo() != null && !"".equals(poisonPatientVO.getIdentityNo().trim())).collect(Collectors.toList());

        if (poisonPatientMsgNotNull.size() > 0) {
            Map<String, Map<String, List<PoisonMedicalInfoDto>>> map = poisonPatientMsgNotNull.stream().collect(Collectors.groupingBy(PoisonMedicalInfoDto::getPatientName, Collectors.groupingBy(PoisonMedicalInfoDto::getIdentityNo)));
            List<PoisonMedicalInfoDto> result = new ArrayList<>();
            map.forEach((k, v) -> {
                v.forEach((k1, v1) -> {
                    //在姓名相同，身份证号相同且都不为空的情况下，则表示同一个人，这里按照报卡创建时间倒序排列取最新的报卡
                    //用于存放同一个人的多张报卡
                    List<String> sourceKeyLists = new ArrayList<>();
                    //这里将同一个人的报卡的sourceKey存到列表中
                    v1.forEach(item -> {
                        sourceKeyLists.add(item.getSourceKey());
                    });
                    //取最新的报卡，并将同一个人的所有报卡存在最新报卡的sourceKeyLists中
                    PoisonMedicalInfoDto newRecord = v1.stream().sorted(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime).reversed()).collect(Collectors.toList()).get(0);
                    newRecord.setSourceKeyList(sourceKeyLists);
                    result.add(newRecord);
                });
            });
            keepDifPerson.addAll(result);
        }
        log.info(String.valueOf(keepDifPerson.size()));

        // todo 排序
        sortType = sortType == null ? 0 : sortType;
        switch (sortType) {
            // 升序排
            case 1:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getIntAge, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                break;
            case 2:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getLivingAddress, dto -> StringUtils.isNotEmpty(dto.getLivingAddress()));
                break;
            case 3:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getDiagnoseName, dto -> StringUtils.isNotEmpty(dto.getDiagnoseName()));
                break;
            case 4:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getCompanyName, dto -> StringUtils.isNotEmpty(dto.getCompanyName()));
                break;
            case 5:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                break;
            case 6:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getBrand, dto -> StringUtils.isNotEmpty(dto.getBrand()));
                break;
            case 7:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getManufacturer, dto -> StringUtils.isNotEmpty(dto.getManufacturer()));
                break;
            case 8:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getEatingTime, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                break;
            case 9:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getEatingAddress, dto -> StringUtils.isNotEmpty(dto.getEatingAddress()));
                break;
            case 10:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getPurchaseLocation, dto -> StringUtils.isNotEmpty(dto.getPurchaseLocation()));
                break;
            case 11:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getSymptomOnsetTime, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                break;
            case 12:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getDoctorName, dto -> StringUtils.isNotEmpty(dto.getDoctorName()));
                break;
            case 13:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getPesticideType, dto -> StringUtils.isNotEmpty(dto.getPesticideType()));
                break;
            case 14:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getPesticideName, dto -> StringUtils.isNotEmpty(dto.getPesticideName()));
                break;
            case 15:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getHappenPlace, dto -> StringUtils.isNotEmpty(dto.getHappenPlace()));
                break;
            case 16:
                keepDifPerson = sortOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getHappenAddress, dto -> StringUtils.isNotEmpty(dto.getHappenAddress()));
                break;
            case 17:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getInStorageTime, Comparator.nullsLast(Comparator.naturalOrder())))
                        .collect(Collectors.toList());
                break;

            // 降序排
            case 101:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getIntAge, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                break;
            case 102:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getLivingAddress, dto -> StringUtils.isNotEmpty(dto.getLivingAddress()));
                break;
            case 103:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getDiagnoseName, dto -> StringUtils.isNotEmpty(dto.getDiagnoseName()));
                break;
            case 104:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getCompanyName, dto -> StringUtils.isNotEmpty(dto.getCompanyName()));
                break;
            case 105:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                break;
            case 106:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getBrand, dto -> StringUtils.isNotEmpty(dto.getBrand()));
                break;
            case 107:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getManufacturer, dto -> StringUtils.isNotEmpty(dto.getManufacturer()));
                break;
            case 108:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getEatingTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                break;
            case 109:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getEatingAddress, dto -> StringUtils.isNotEmpty(dto.getEatingAddress()));
                break;
            case 110:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getPurchaseLocation, dto -> StringUtils.isNotEmpty(dto.getPurchaseLocation()));
                break;
            case 111:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getSymptomOnsetTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                break;
            case 112:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getDoctorName, dto -> StringUtils.isNotEmpty(dto.getDoctorName()));
                break;
            case 113:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getPesticideType, dto -> StringUtils.isNotEmpty(dto.getPesticideType()));
                break;
            case 114:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getPesticideName, dto -> StringUtils.isNotEmpty(dto.getPesticideName()));
                break;
            case 115:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getHappenPlace, dto -> StringUtils.isNotEmpty(dto.getHappenPlace()));
                break;
            case 116:
                keepDifPerson = sortDesOrderPoisonRecordVOList(keepDifPerson, PoisonMedicalInfoDto::getHappenAddress, dto -> StringUtils.isNotEmpty(dto.getHappenAddress()));
                break;
            case 117:
                keepDifPerson = keepDifPerson.stream()
                        .sorted(Comparator.comparing(PoisonMedicalInfoDto::getInStorageTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                break;
            default:
        }

        return keepDifPerson;
    }

    // 特殊排序  降序排
    public List<PoisonMedicalInfoDto> sortDesOrderPoisonRecordVOList(List<PoisonMedicalInfoDto> list, Function<PoisonMedicalInfoDto, String> function, Predicate<PoisonMedicalInfoDto> predicate) {
        List<PoisonMedicalInfoDto> result = new ArrayList<>();

        // 先按诊断时间排序  升序
        list.sort(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.naturalOrder())));

        // 先排不为空的
        List<PoisonMedicalInfoDto> notNullList = list.stream()
                .filter(predicate)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notNullList)) {
            // 先数量排序
            Map<String, List<PoisonMedicalInfoDto>> map = notNullList.stream()
                    .collect(Collectors.groupingBy(function));

            map.forEach((k, subList) -> {
                // 子集按就诊时间降序排
                subList.sort(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                PoisonMedicalInfoDto poisonMedicalInfoDto = subList.get(0);
                int sortIndex = IntStream.range(0, list.size()).filter(i -> list.get(i).equals(poisonMedicalInfoDto)).findFirst().orElse(0);

                subList.forEach(item -> {
                    item.setSortNumber((subList.size() * (list.size() + 1)) + sortIndex);
                });
            });

            // 按照sortNumber排序  降序
            Map<Integer, List<PoisonMedicalInfoDto>> sortMap = notNullList.stream()
                    .collect(Collectors.groupingBy(PoisonMedicalInfoDto::getSortNumber));
            List<Integer> sortNumList = notNullList.stream().map(PoisonMedicalInfoDto::getSortNumber)
                    .distinct()
                    .sorted(Collections.reverseOrder())
                    .collect(Collectors.toList());
            for (int i : sortNumList) {
                List<PoisonMedicalInfoDto> poisonMedicalInfoDtos = sortMap.get(i);
                poisonMedicalInfoDtos.sort(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                result.addAll(poisonMedicalInfoDtos);
            }

        }


        // 再排为空的
        List<PoisonMedicalInfoDto> nullList = list.stream()
                .filter(predicate.negate())
                .sorted(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed())
                .collect(Collectors.toList());
        result.addAll(nullList);

        return result;
    }

    // 特殊排序  升序排
    public List<PoisonMedicalInfoDto> sortOrderPoisonRecordVOList(List<PoisonMedicalInfoDto> list, Function<PoisonMedicalInfoDto, String> function, Predicate<PoisonMedicalInfoDto> predicate) {
        List<PoisonMedicalInfoDto> result = new ArrayList<>();

        // 先按诊断时间排序  升序
        list.sort(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.naturalOrder())));

        // 先排不为空的
        List<PoisonMedicalInfoDto> notNullList = list.stream()
                .filter(predicate)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notNullList)) {
            // 先数量排序
            Map<String, List<PoisonMedicalInfoDto>> map = notNullList.stream()
                    .collect(Collectors.groupingBy(function));

            map.forEach((k, subList) -> {
                // 子集按就诊时间升序排
                subList.sort(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.naturalOrder())));
                PoisonMedicalInfoDto poisonMedicalInfoDto = subList.get(0);
                int sortIndex = IntStream.range(0, list.size()).filter(i -> list.get(i).equals(poisonMedicalInfoDto)).findFirst().orElse(0);

                subList.forEach(item -> {
                    item.setSortNumber((subList.size() * (list.size() + 1)) + sortIndex);
                });
            });

            // 按照sortNumber排序  升序
            Map<Integer, List<PoisonMedicalInfoDto>> sortMap = notNullList.stream()
                    .collect(Collectors.groupingBy(PoisonMedicalInfoDto::getSortNumber));
            List<Integer> sortNumList = notNullList.stream().map(PoisonMedicalInfoDto::getSortNumber)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            for (int i : sortNumList) {
                List<PoisonMedicalInfoDto> poisonMedicalInfoDtos = sortMap.get(i);
                poisonMedicalInfoDtos.sort(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.naturalOrder())));
                result.addAll(poisonMedicalInfoDtos);
            }
        }

        // 再排为空的
        List<PoisonMedicalInfoDto> nullList = list.stream()
                .filter(predicate.negate())
                .sorted(Comparator.comparing(PoisonMedicalInfoDto::getOutPatientTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        result.addAll(nullList);

        return result;
    }

    @Override
    public HeatstrokePoisonPatientExtendVO getHeatstrokeMedicalDetail(List<String> sourceKeyLists, String loginUserId) {
        HeatstrokePoisonPatientExtendVO patientExtendVO = new HeatstrokePoisonPatientExtendVO();
        List<TbCdcewRepcardHeatstrokeReport> heatstrokeReportList = heatstrokeReportMapper.getHeatstrokeReportCard(sourceKeyLists);
        patientExtendVO.setReportCardInfo(heatstrokeReportList);
        if (!CollectionUtils.isEmpty(heatstrokeReportList)) {

            List<String> medicalIdList = new ArrayList<>();
            heatstrokeReportList.forEach(e -> {
                medicalIdList.add(e.getMedicalId());
                e.setStatus(ReportCardStatusV2Enum.convertOriginalStatus(e.getStatus()));
            });
            getPoisonInfo(patientExtendVO, medicalIdList, loginUserId);
            patientExtendVO.setPatientDetailVO(PatientDetailVO.fromTbCdcewRepcardHeatstrokeReport(heatstrokeReportList.get(0)));
            return patientExtendVO;
        }
        return patientExtendVO;
    }

    @Override
    public FoodPoisonPatientExtendVO getFoodMedicalDetail(List<String> sourceKeyLists, String loginUserId) {

        FoodPoisonPatientExtendVO patientExtendVO = new FoodPoisonPatientExtendVO();

        List<TbCdcewRepcardFoodBorneReport> foodBorneReportList = foodBorneReportMapper.getFoodBorneReportCard(sourceKeyLists);
        patientExtendVO.setReportCardInfo(foodBorneReportList);

        if (!CollectionUtils.isEmpty(foodBorneReportList)) {

            List<String> medicalIdList = new ArrayList<>();
            foodBorneReportList.forEach(e -> {
                medicalIdList.add(e.getMedicalId());
                e.setCardStatus(ReportCardStatusV2Enum.convertOriginalStatus(e.getCardStatus()));
            });
            getPoisonInfo(patientExtendVO, medicalIdList, loginUserId);
            patientExtendVO.setPatientDetailVO(PatientDetailVO.fromTbCdcewRepcardFoodBorneReport(foodBorneReportList.get(0)));
            return patientExtendVO;
        }

        return patientExtendVO;
    }

    @Override
    public List<OutpatientRecordVO> getOutpatientRecordByEventId(String eventId, Date beginDate, Date endDate) {
        List<String> searchIds = tbCdcewHisMedicalInfoMapper.getSearchIdsByOutpatientEventId(eventId);

        List<OutpatientRecordVO> result = new ArrayList<>();
        List<String> sourceKeySearchList = new ArrayList<>();
        searchIds.forEach(s -> {
            String[] split = s.split("\\|");
            sourceKeySearchList.addAll(Arrays.asList(split));
        });

        List<MedicalInfoVO> medicalInfos = tbCdcewHisMedicalInfoMapper.getByOutpatientEventIdAndSourceKeys(eventId, sourceKeySearchList, null);
        Map<String, List<MedicalInfoVO>> map = medicalInfos.stream().collect(Collectors.groupingBy(MedicalInfoVO::getSourceKey));

        map.forEach((s, medicalInfoList) -> {

            // patientId 为空时  每一条都是单独的数据
            if (StringUtils.isEmpty(s)) {
                medicalInfoList.forEach(medicalInfoVO -> {
                    OutpatientRecordVO recordVO = new OutpatientRecordVO();

                    BeanUtils.copyProperties(medicalInfoVO, recordVO);
                    recordVO.setSourceKeyList(Collections.singletonList(medicalInfoVO.getSourceKey()));
                    result.add(recordVO);
                });
            } else {
                OutpatientRecordVO recordVO = new OutpatientRecordVO();
                List<String> sourceKeyList = medicalInfoList.stream()
                        .sorted(Comparator.comparing(MedicalInfoVO::getFullDate,
                                Comparator.nullsFirst(Comparator.naturalOrder())))
                        .map(MedicalInfoVO::getSourceKey).collect(Collectors.toList());
                MedicalInfoVO tbCdcewHisMedicalInfo = medicalInfoList.get(0);

                BeanUtils.copyProperties(tbCdcewHisMedicalInfo, recordVO);
                recordVO.setSourceKeyList(sourceKeyList);
                result.add(recordVO);
            }

        });
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        result.forEach(item -> {
            String fullDate = item.getFullDate() == null ? null : sdf.format(item.getFullDate());
            if (now.equals(fullDate)
                    && CommonConstants.IS_NEW_MED_RECORD_YES == Optional.ofNullable(item.getIsNewMedRecord()).orElse(CommonConstants.IS_NEW_MED_RECORD_NO)) {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_YES);
            } else {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_NO);
            }
        });
        return result;
    }

    @Override
    public PatientExtendVO getMedicalDetail(List<String> sourceKeyLists, String loginUserId) {

        PatientExtendVO patientExtendVO = new PatientExtendVO();
        List<TbCdcewHisMedicalInfo> tbCdcewHisMedicalInfoList = tbCdcewHisMedicalInfoMapper.selectBySourceKeyLists(sourceKeyLists);
        if (!tbCdcewHisMedicalInfoList.isEmpty()) {
            //设置对应的病例详情
            patientExtendVO.setPatientDetailVO(PatientDetailVO.fromTbCdcewHisMedicalInfo(tbCdcewHisMedicalInfoList.get(0)));
            //整合单个人所有病例的sourceKey去查询诊断日志
            List<String> keyList = new ArrayList<>();
            tbCdcewHisMedicalInfoList.forEach(tbCdcewHisMedicalInfo -> {
                keyList.add(tbCdcewHisMedicalInfo.getSourceKey());
            });
            //查询诊断日志
            List<MedicalHistoryLog> medicalHistoryLogList = restService.getMedicalHistoryLogLists(keyList, loginUserId);
            Map<String, List<MedicalHistoryLog>> map = medicalHistoryLogList.stream().collect(Collectors.groupingBy(MedicalHistoryLog::getEventId));

            tbCdcewHisMedicalInfoList.forEach(medicalInfo -> {
                medicalInfo.setMedicalHistoryLogs(map.get(medicalInfo.getSourceKey()));
            });
        }
        patientExtendVO.setCdcHisMedicalInfo(tbCdcewHisMedicalInfoList);
        return patientExtendVO;
    }

    @Override
    public CdcHisMedicalInfoDetailVo getPeriodOne(String sourceKey, String eventId) {
        List<String> sourceTypes = cdcWarningEventMapper.findSourceTypeById(eventId);
        CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(eventId);

        CdcHisMedicalInfoDetailVo medicalInfoDetailVo = new CdcHisMedicalInfoDetailVo();
        if (sourceTypes.contains(SourceTypeEnum.HOSPITAL.getCode()) || sourceTypes.contains(SourceTypeEnum.PRIMARY_CARE.getCode()) || EventTypeEnum.JDQY.getName().equals(event.getEventType())) {
            TbCdcewHisMedicalInfo medicalInfo = tbCdcewHisMedicalInfoMapper.findBySourceKey(sourceKey);
            medicalInfoDetailVo.setCdcHisMedicalInfo(medicalInfo);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewHisMedicalInfo(medicalInfo));
        } else if (sourceTypes.contains(SourceTypeEnum.PHARMACY.getCode())) {
            PurchaseMedicineDto purchaseMedicine = pharmacyService.getPurchaseMedicine(sourceKey);
            medicalInfoDetailVo.setPurchaseMedicineDto(purchaseMedicine);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromPurchaseMedicineDto(purchaseMedicine));
        } else if (sourceTypes.contains(SourceTypeEnum.SCHOOL.getCode())) {
            TbCdcewScSymptomRecord tbCdcewScSymptomRecord = cdcScSymptomRecordService.selectByPrimaryKey(sourceKey);
            medicalInfoDetailVo.setStudentVO(tbCdcewScSymptomRecord);
            medicalInfoDetailVo.setPatientDetailVO(PatientDetailVO.fromTbCdcewScSymptomRecord(tbCdcewScSymptomRecord));
        }

        return medicalInfoDetailVo;
    }

    @Override
    public Object getCustomizedPoisonMedicalDetail(List<String> sourceKeyLists, String eventId, String loginUserId) {

        boolean isDensensitization = tbCdcConfigService.isDesensitization(loginUserId);
        HeatstrokePoisonPatientExtendVO heatMedicalDetail = getHeatstrokeMedicalDetail(sourceKeyLists, loginUserId);
        if (!CollectionUtils.isEmpty(heatMedicalDetail.getReportCardInfo())) {
            if (isDensensitization) {
                List<TbCdcewHisMedicalInfo> medicalInfos = heatMedicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(heatMedicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardHeatstrokeReport> heatstrokeReports = heatMedicalDetail.getReportCardInfo();
                heatstrokeReports.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardHeatstrokeReport);
            }
            heatMedicalDetail.setType(PoisonTypeEnum.HEATSTROKE.getCode());
            return heatMedicalDetail;
        }

        FoodPoisonPatientExtendVO foodMedicalDetail = getFoodMedicalDetail(sourceKeyLists, loginUserId);
        if (!CollectionUtils.isEmpty(foodMedicalDetail.getReportCardInfo())) {
            if (isDensensitization) {
                List<TbCdcewHisMedicalInfo> medicalInfos = foodMedicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(foodMedicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardFoodBorneReport> foodBorneReports = foodMedicalDetail.getReportCardInfo();
                foodBorneReports.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardFoodBorneReport);
            }
            foodMedicalDetail.setType(PoisonTypeEnum.FOOD.getCode());
            return foodMedicalDetail;
        }

        CarbonMonoxidePoisonPatientExtendVO monoxideMedicalDetail = getCarbonMonoxideMedicalDetail(sourceKeyLists, loginUserId);
        if (!CollectionUtils.isEmpty(monoxideMedicalDetail.getReportCardInfo())){
            if (isDensensitization) {
                List<TbCdcewHisMedicalInfo> medicalInfos = monoxideMedicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(monoxideMedicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardCarbonMethysisReport> reportCardInfo = monoxideMedicalDetail.getReportCardInfo();
                reportCardInfo.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardCarbonMethysisReport);
            }
            monoxideMedicalDetail.setType(PoisonTypeEnum.CARBON_MONOXIDE.getCode());
            return monoxideMedicalDetail;
        }

        PesticidePoisonPatientExtendVO pesticideMedicalDetail = getPesticideMedicalDetail(sourceKeyLists, loginUserId);
        if (!CollectionUtils.isEmpty(pesticideMedicalDetail.getReportCardInfo())){
            if (isDensensitization) {
                List<TbCdcewHisMedicalInfo> medicalInfos = pesticideMedicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(pesticideMedicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardPesticidePoisonReport> reportCardInfo = pesticideMedicalDetail.getReportCardInfo();
                reportCardInfo.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardPesticideReport);
            }
            pesticideMedicalDetail.setType(PoisonTypeEnum.PESTICIDE.getCode());
            return pesticideMedicalDetail;
        }


        PatientExtendVO patientExtendVO = this.getMedicalDetail(sourceKeyLists, loginUserId);
        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            List<TbCdcewHisMedicalInfo> medicalInfos = patientExtendVO.getCdcHisMedicalInfo();
            medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
            DesensitizeVOUtils.desensitizePatientDetailVO(patientExtendVO.getPatientDetailVO());
        }
        patientExtendVO.setType(null);
        return patientExtendVO;
    }

    @Override
    public List<PoisonMedicalInfoDto> getDetailsByEventId(String eventId, String patientName, String doctorName, String companyName, Integer sortType) {

        List<PoisonMedicalInfoDto> medicalInfos = tbCdcewPoisonMedRelationMapper.getMedicalInfoByPoisonEventId(eventId, patientName,doctorName,companyName, null);
        List<PoisonMedicalInfoDto> retList = new ArrayList<>(medicalInfos);
        //获取当前的信号属于哪一个中毒大类
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(eventId);
        switch (poisonTypeCode) {
            case Constants.FOOD:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getFoodbornePoisoningByPoisonEventId(eventId, patientName, doctorName, companyName, sortType));
                break;
            case Constants.PESTICIDE:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getPesticidePoisoningByPoisonEventId(eventId, patientName, doctorName, companyName, sortType));
                break;
            case Constants.CARBON_MONOXIDE:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getCOPoisoningByPoisonEventId(eventId, patientName, doctorName, companyName, sortType));
                break;
            case Constants.HEATSTROKE:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getHeatstrokeByPoisonEventId(eventId, patientName, doctorName, companyName, sortType));
                break;
            default:
        }
        return retList;
    }

    public List<PoisonMedicalInfoDto> getPoisonHisByEventId(String eventId, String patientName, String doctorName, String companyName) {
        List<PoisonMedicalInfoDto> retList = new ArrayList<>();
        List<String> searchIds = tbCdcewHisMedicalInfoMapper.getSearchIdsByPoisonEventId(eventId);
        if (CollectionUtils.isNotEmpty(searchIds)){
            List<String> sourceKeySearchList = new ArrayList<>();
            searchIds.forEach(s -> {
                String[] split = s.split("\\|");
                sourceKeySearchList.addAll(Arrays.asList(split));
            });
            List<MedicalInfoVO> medicalInfos = tbCdcewHisMedicalInfoMapper.getByPoisonEventIdAndSourceKeys(eventId, sourceKeySearchList, patientName,doctorName,companyName);
            for (MedicalInfoVO medicalInfoVO : medicalInfos) {
                PoisonMedicalInfoDto recordVO = new PoisonMedicalInfoDto();
                BeanUtils.copyProperties(medicalInfoVO, recordVO);
                recordVO.setSourceKeyList(Collections.singletonList(medicalInfoVO.getSourceKey()));
                recordVO.setAge(medicalInfoVO.getDiagnoseAge());
                recordVO.setAgeUnit(medicalInfoVO.getDiagnoseAgeUnit());
                retList.add(recordVO);
            }
        }
        return retList;
    }

    @Override
    public List<UnknownReasonRecordVO> getUnknownReasonRecordsByEventId(String eventId, String patientName, Integer sortType, String doctorName, String companyName) {

        List<UnknownReasonRecordVO> unknownReasonRecordVOList = tbCdcewUnknownReasonMedRelationMapper.getUnknownReasonMedicalByEventId(eventId, patientName, doctorName, companyName, sortType);

        // 当天的  isNewMedRecord 设置为true
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        unknownReasonRecordVOList.forEach(item -> {
            String fullDate = item.getFullDate() == null ? null : sdf.format(item.getFullDate());
            if (now.equals(fullDate)) {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_YES);
            } else {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_NO);
            }
        });

        //姓名和身份证号都相同且都有值的则需要合并为同一人
        //若只有姓名相同，身份证号为空则保持为不同的人；若姓名为空，身份证号相同也保持为不同的人

        //取出身份证号或者名字为空的情况，只要有一个为空则保持为不同的人
        List<UnknownReasonRecordVO> keepDifPerson = unknownReasonRecordVOList.stream().filter(unknownPatientVO -> unknownPatientVO.getPatientName() == null || "".equals(unknownPatientVO.getPatientName().trim()) || unknownPatientVO.getIdentityNo() == null || "".equals(unknownPatientVO.getIdentityNo().trim())).collect(Collectors.toList());
        keepDifPerson.forEach(item -> {
            List<String> tmp = new ArrayList<>();
            tmp.add(item.getSourceKey());
            item.setSourceKeyList(tmp);
        });
        //取出身份证号和名字都不为空的情况的报卡
        List<UnknownReasonRecordVO> unknownPatientMsgNotNull = unknownReasonRecordVOList.stream().filter(unknownPatientVO -> unknownPatientVO.getPatientName() != null && !"".equals(unknownPatientVO.getPatientName().trim()) && unknownPatientVO.getIdentityNo() != null && !"".equals(unknownPatientVO.getIdentityNo().trim())).collect(Collectors.toList());

        if (unknownPatientMsgNotNull.size() > 0) {
            Map<String, Map<String, List<UnknownReasonRecordVO>>> map = unknownPatientMsgNotNull.stream().collect(Collectors.groupingBy(UnknownReasonRecordVO::getPatientName, Collectors.groupingBy(UnknownReasonRecordVO::getIdentityNo)));
            List<UnknownReasonRecordVO> result = new ArrayList<>();
            map.forEach((k, v) -> {
                v.forEach((k1, v1) -> {
                    //在姓名相同，身份证号相同且都不为空的情况下，则表示同一个人，这里按照报卡创建时间倒序排列取最新的报卡
                    //用于存放同一个人的多张报卡
                    List<String> sourceKeyLists = new ArrayList<>();
                    //这里将同一个人的报卡的sourceKey存到列表中
                    v1.forEach(item -> {
                        sourceKeyLists.add(item.getSourceKey());
                    });
                    //取最新的报卡，并将同一个人的所有报卡存在最新报卡的sourceKeyLists中
                    UnknownReasonRecordVO newRecord = v1.stream().sorted(Comparator.comparing(UnknownReasonRecordVO::getOutPatientTime).reversed()).collect(Collectors.toList()).get(0);
                    newRecord.setSourceKeyList(sourceKeyLists);
                    result.add(newRecord);
                });
            });
            keepDifPerson.addAll(result);
        }
        log.info(String.valueOf(keepDifPerson.size()));

        return keepDifPerson;
    }


    @Override
    public List<CustomizedRecordVO> getCustomizedInfoByEventId(String eventId, String patientName, Integer sortType, String doctorName, String companyName) {

        List<CustomizedRecordVO> customizedRecordVOList = getCustomziedDetailsByEventId(eventId, patientName, doctorName, companyName, sortType);

        // 当天的  isNewMedRecord 设置为true
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        customizedRecordVOList.forEach(item -> {
            String fullDate = item.getFullDate() == null ? null : sdf.format(item.getFullDate());
            if (now.equals(fullDate)) {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_YES);
            } else {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_NO);
            }
        });

        //姓名和身份证号都相同且都有值的则需要合并为同一人
        //若只有姓名相同，身份证号为空则保持为不同的人；若姓名为空，身份证号相同也保持为不同的人

        //取出身份证号或者名字为空的情况，只要有一个为空则保持为不同的人
        List<CustomizedRecordVO> keepDifPerson = customizedRecordVOList.stream().filter(poisonPatientVO -> poisonPatientVO.getPatientName() == null || "".equals(poisonPatientVO.getPatientName().trim()) || poisonPatientVO.getIdentityNo() == null || "".equals(poisonPatientVO.getIdentityNo().trim())).collect(Collectors.toList());
        keepDifPerson.forEach(item -> {
            List<String> tmp1 = new ArrayList<>();
            tmp1.add(item.getSourceKey());
            item.setSourceKeyList(tmp1);

            //症状查询需单独处理，如果是症状则使用SymptomRecordDtoList中的结果去查询
            List<SymptomRecordDto> tmp2 = new ArrayList<>();
            SymptomRecordDto tmpRecord = new SymptomRecordDto();
            tmpRecord.setSymptomRecordId(item.getSourceKey());
            tmpRecord.setSourceType(item.getSourceType());
            tmp2.add(tmpRecord);
            item.setSymptomRecordDtoList(tmp2);
        });
        //取出身份证号和名字都不为空的情况的报卡/病例
        List<CustomizedRecordVO> customizedPatientMsgNotNull = customizedRecordVOList.stream().filter(poisonPatientVO -> poisonPatientVO.getPatientName() != null && !"".equals(poisonPatientVO.getPatientName().trim()) && poisonPatientVO.getIdentityNo() != null && !"".equals(poisonPatientVO.getIdentityNo().trim())).collect(Collectors.toList());

        if (customizedPatientMsgNotNull.size() > 0) {
            Map<String, Map<String, List<CustomizedRecordVO>>> map = customizedPatientMsgNotNull.stream().collect(Collectors.groupingBy(CustomizedRecordVO::getPatientName, Collectors.groupingBy(CustomizedRecordVO::getIdentityNo)));
            List<CustomizedRecordVO> result = new ArrayList<>();
            map.forEach((k, v) -> {
                v.forEach((k1, v1) -> {
                    //在姓名相同，身份证号相同且都不为空的情况下，则表示同一个人，这里按照报卡或者病例创建时间倒序排列取最新的报卡/病例
                    //用于存放同一个人的多张报卡/病例
                    List<String> sourceKeyLists = new ArrayList<>();
                    List<SymptomRecordDto> symptomRecordDtoList = new ArrayList<>();
                    //这里将同一个人的报卡/病例的sourceKey存到列表中
                    v1.forEach(item -> {
                        sourceKeyLists.add(item.getSourceKey());
                        SymptomRecordDto symptomRecordDto = new SymptomRecordDto();
                        symptomRecordDto.setSymptomRecordId(item.getSourceKey());
                        symptomRecordDto.setSourceType(item.getSourceType());
                        symptomRecordDtoList.add(symptomRecordDto);
                    });
                    //取最新的报卡/病例，并将同一个人的所有报卡/病例存在最新报卡的sourceKeyLists中
                    CustomizedRecordVO newRecord = v1.stream().sorted(Comparator.comparing(CustomizedRecordVO::getVisitTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList()).get(0);
                    newRecord.setSourceKeyList(sourceKeyLists);
                    newRecord.setSymptomRecordDtoList(symptomRecordDtoList);
                    result.add(newRecord);
                });
            });
            keepDifPerson.addAll(result);
        }
        log.info("按身份证和姓名合并后的人数为：" + String.valueOf(keepDifPerson.size()));
        return keepDifPerson;
    }

    @Override
    public List<CustomizedRecordVO> getCustomziedDetailsByEventId(String eventId, String patientName, String doctorName, String companyName, Integer sortType) {

        List<CustomizedRecordVO> result = new ArrayList<>();

        //病例
        List<CustomizedRecordVO> medicalInfo = tbCdcewCustomizedMedRelationMapper.getCustomizedInfoByEventId(eventId, patientName, doctorName, companyName);
        result.addAll(medicalInfo);

        //传染病报卡
        List<CustomizedRecordVO> infectedReportCard = tbCdcewCustomizedMedRelationMapper.getCustomizedInfectedReportByEventId(eventId, patientName, doctorName, companyName);
        result.addAll(infectedReportCard);

        //食源性报卡
        List<CustomizedRecordVO> foodReportCard = tbCdcewCustomizedMedRelationMapper.getCustomizedFoodReportByEventId(eventId, patientName, doctorName, companyName);
        result.addAll(foodReportCard);

        //高温中暑报卡
        List<CustomizedRecordVO> heatstrokeReportCard = tbCdcewCustomizedMedRelationMapper.getCustomizedHeatstrokeReportByEventId(eventId, patientName, doctorName, companyName);
        result.addAll(heatstrokeReportCard);

        //一氧化碳中毒报卡
        List<CustomizedRecordVO> carbonReportCard = tbCdcewCustomizedMedRelationMapper.getCustomizedCarbonReportByEventId(eventId, patientName, doctorName, companyName);
        result.addAll(carbonReportCard);

        //农药中毒报卡
        List<CustomizedRecordVO> pesticidReportCard = tbCdcewCustomizedMedRelationMapper.getCustomizedPesticideReportByEventId(eventId, patientName, doctorName, companyName);
        result.addAll(pesticidReportCard);

        return result;
    }

    @Override
    public CarbonMonoxidePoisonPatientExtendVO getCarbonMonoxideMedicalDetail(List<String> sourceKeyLists, String loginUserId) {

        CarbonMonoxidePoisonPatientExtendVO patientExtendVO = new CarbonMonoxidePoisonPatientExtendVO();

        List<TbCdcewRepcardCarbonMethysisReport> reportList = carbonMethysisReportMapper.getReportCard(sourceKeyLists);
        patientExtendVO.setReportCardInfo(reportList);

        if (!CollectionUtils.isEmpty(reportList)) {

            List<String> medicalIdList = new ArrayList<>();
            reportList.forEach(e -> {
                medicalIdList.add(e.getMedicalId());
                e.setStatus(ReportCardStatusV2Enum.convertOriginalStatus(e.getStatus()));
            });
            getPoisonInfo(patientExtendVO, medicalIdList, loginUserId);
            patientExtendVO.setPatientDetailVO(PatientDetailVO.fromTbCdcewRepcardCarbonReport(reportList.get(0)));
            return patientExtendVO;
        }

        return patientExtendVO;
    }

    @Override
    public PesticidePoisonPatientExtendVO getPesticideMedicalDetail(List<String> sourceKeyLists, String loginUserId) {

        PesticidePoisonPatientExtendVO patientExtendVO = new PesticidePoisonPatientExtendVO();

        List<TbCdcewRepcardPesticidePoisonReport> reportList = pesticideReportMapper.getReportCard(sourceKeyLists);
        patientExtendVO.setReportCardInfo(reportList);

        if (!CollectionUtils.isEmpty(reportList)) {

            List<String> medicalIdList = new ArrayList<>();
            reportList.forEach(e -> {
                medicalIdList.add(e.getMedicalId());
                e.setStatus(ReportCardStatusV2Enum.convertOriginalStatus(e.getStatus()));
            });
            getPoisonInfo(patientExtendVO, medicalIdList, loginUserId);
            patientExtendVO.setPatientDetailVO(PatientDetailVO.fromTbCdcewRepcardPesticiReport(reportList.get(0)));
            return patientExtendVO;
        }

        return patientExtendVO;
    }

    /**
     * @param poisonPatientExtendVO
     * @param eventIds
     * @param loginUserId
     */
    private void getPoisonInfo(PatientExtendVO poisonPatientExtendVO, List<String> eventIds, String loginUserId) {

        List<TbCdcewHisMedicalInfo> tbCdcewHisMedicalInfoList = tbCdcewHisMedicalInfoMapper.selectByEventIds(eventIds);
        if (!tbCdcewHisMedicalInfoList.isEmpty()) {
            //设置对应的病例详情
            poisonPatientExtendVO.setPatientDetailVO(PatientDetailVO.fromTbCdcewHisMedicalInfo(tbCdcewHisMedicalInfoList.get(0)));
            //整合单个人所有病例的sourceKey去查询诊断日志
            List<String> keyList = new ArrayList<>();
            tbCdcewHisMedicalInfoList.forEach(tbCdcewHisMedicalInfo -> {
                keyList.add(tbCdcewHisMedicalInfo.getSourceKey());
            });
            //查询诊断日志
            List<MedicalHistoryLog> medicalHistoryLogList = restService.getMedicalHistoryLogLists(keyList, loginUserId);
            Map<String, List<MedicalHistoryLog>> map = medicalHistoryLogList.stream().collect(Collectors.groupingBy(MedicalHistoryLog::getEventId));

            tbCdcewHisMedicalInfoList.forEach(medicalInfo -> {
                medicalInfo.setMedicalHistoryLogs(map.get(medicalInfo.getSourceKey()));
            });
        }
        poisonPatientExtendVO.setCdcHisMedicalInfo(tbCdcewHisMedicalInfoList);
    }

}

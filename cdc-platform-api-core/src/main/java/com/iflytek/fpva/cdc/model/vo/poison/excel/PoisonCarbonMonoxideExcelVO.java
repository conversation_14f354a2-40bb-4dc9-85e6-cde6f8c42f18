package com.iflytek.fpva.cdc.model.vo.poison.excel;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMedicalInfoDto;
import com.iflytek.fpva.cdc.model.warningEvent.vo.PoisonMedicalInfoVO;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;

@Data
public class PoisonCarbonMonoxideExcelVO {

    @ApiModelProperty("姓名")
    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;

    @ApiModelProperty("发生场所")
    @ExcelColumn(name = "发生场所", column = 2)
    private String happenPlace;

    @ApiModelProperty("发生地址")
    @ExcelColumn(name = "发生地址", column = 3)
    private String happenAddress;

    @ApiModelProperty("现住址")
    @ExcelColumn(name = "现住址", column = 4)
    private String livingAddress;

    @ApiModelProperty("职业")
    @ExcelColumn(name = "职业", column = 5)
    private String careerName;

    @ApiModelProperty("工作单位/学校")
    @ExcelColumn(name = "工作单位/学校", column = 6)
    private String companyName;

    @ApiModelProperty("诊断时间")
    @ExcelColumn(name = "诊断时间", column = 7)
    private String outPatientTime;

    @ApiModelProperty("诊断")
    @ExcelColumn(name = "诊断", column = 8)
    private String diagnoseName;

    @ApiModelProperty("性别")
    @ExcelColumn(name = "性别", column = 9)
    private String sexDesc;

    @ApiModelProperty("年龄")
    @ExcelColumn(name = "年龄", column = 10)
    private String diagnoseAge;

    @ApiModelProperty("病例来源")
    @ExcelColumn(name = "病例来源", column = 11)
    private String dataSourceOrgName;

    @ApiModelProperty("出现症状时间")
    @ExcelColumn(name = "出现症状时间", column = 12)
    private String symptomOnsetTime;

    @ApiModelProperty("接诊医生")
    @ExcelColumn(name = "接诊医生", column = 13)
    private String doctorName;

    @ApiModelProperty("数据来源")
    @ExcelColumn(name = "数据来源", column = 14)
    private String dataSource;

    @ApiModelProperty("就诊机构")
    @ExcelColumn(name = "就诊机构", column = 15)
    private String visitOrgName;

    public static PoisonCarbonMonoxideExcelVO fromPoisonMedicalInfoDto(PoisonMedicalInfoDto dto) {
        PoisonCarbonMonoxideExcelVO excelVO = new PoisonCarbonMonoxideExcelVO();
        BeanUtils.copyProperties(dto, excelVO);
        if (!StringUtils.isBlank(dto.getAge())) {
            if (!StringUtils.isBlank(dto.getAgeUnit())) {
                excelVO.setDiagnoseAge(dto.getAge() + dto.getAgeUnit());
            } else {
                excelVO.setDiagnoseAge(dto.getAge() + "岁");
            }
        } else {
            excelVO.setDiagnoseAge("-");
        }
        excelVO.setDataSourceOrgName(dto.getVisitOrgName());
        excelVO.setOutPatientTime(DateUtils.parseDate(dto.getOutPatientTime(), DateUtils.DATE_FORMAT));
        excelVO.setSymptomOnsetTime(DateUtils.parseDate(dto.getSymptomOnsetTime(), DateUtils.DATE_FORMAT));
        excelVO.setVisitOrgName(dto.getVisitOrgName());
        // 字段为空时设置默认值 --
        Class<PoisonCarbonMonoxideExcelVO> clazz = PoisonCarbonMonoxideExcelVO.class;
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field:declaredFields) {
            field.setAccessible(true);
            try {
                String s = (String)field.get(excelVO);
                if(StringUtils.isEmpty(s)){
                    field.set(excelVO,"--");
                }
            } catch (IllegalAccessException e) {
                throw new MedicalBusinessException("获取excel对象失败");
            }
        }


        return excelVO;
    }

    public static PoisonCarbonMonoxideExcelVO from(PoisonMedicalInfoVO dto) {
        PoisonCarbonMonoxideExcelVO excelVO = new PoisonCarbonMonoxideExcelVO();
        BeanUtils.copyProperties(dto, excelVO);
        if (!StringUtils.isBlank(dto.getDiagnoseAge())) {
            if (!StringUtils.isBlank(dto.getDiagnoseAgeUnit())) {
                excelVO.setDiagnoseAge(dto.getDiagnoseAge() + dto.getAgeUnit());
            } else {
                excelVO.setDiagnoseAge(dto.getDiagnoseAge() + "岁");
            }
        } else {
            excelVO.setDiagnoseAge("-");
        }
        excelVO.setOutPatientTime(DateUtils.parseDate(dto.getOutPatientTime(), DateUtils.DATE_MONTH_DATE));
        excelVO.setSymptomOnsetTime(DateUtils.parseDate(dto.getSymptomOnsetTime(), DateUtils.DATE_MONTH_DATE));
        excelVO.setVisitOrgName(dto.getVisitOrgName());
        excelVO.setDataSourceOrgName(dto.getVisitOrgName());
        // 字段为空时设置默认值 --
        Class<PoisonCarbonMonoxideExcelVO> clazz = PoisonCarbonMonoxideExcelVO.class;
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field:declaredFields) {
            field.setAccessible(true);
            try {
                String s = (String)field.get(excelVO);
                if(StringUtils.isEmpty(s)){
                    field.set(excelVO,"--");
                }
            } catch (IllegalAccessException e) {
                throw new MedicalBusinessException("获取excel对象失败");
            }
        }


        return excelVO;
    }

}

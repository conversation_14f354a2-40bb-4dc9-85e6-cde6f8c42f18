package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.model.dto.MedicalConfirmDto;
import com.iflytek.fpva.cdc.model.vo.MedicalConfirmVO;

import java.util.List;

public interface MedicalConfirmService {
    /**
     * 查询批次下所有的病历
     *
     * @param batchId 批次ID
     * @return 病历列表
     */
    MedicalConfirmVO getMedicalList(String batchId);

    /**
     * 更新确认结果
     *
     * @param medicalConfirmDtoList 确认结果对象列表
     */
    void updateConfirmResult(List<MedicalConfirmDto> medicalConfirmDtoList);

    /**
     * 发送真实性确认消息
     */
    void sendConfirmMsg();

    /**
     * 更新真实性确认消息（用于外呼回调，刷新消息中心消息状态为已处理)
     *
     * @param batchId 批次ID
     */
    void updateConfirmMsg(String batchId);
}

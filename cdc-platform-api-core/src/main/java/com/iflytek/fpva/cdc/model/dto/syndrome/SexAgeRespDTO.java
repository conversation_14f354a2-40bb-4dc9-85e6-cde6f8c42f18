package com.iflytek.fpva.cdc.model.dto.syndrome;

import com.iflytek.fpva.cdc.model.dto.CountValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SexAgeRespDTO {

    @ApiModelProperty("病例总数")
    Integer totalMedCnt;

    @ApiModelProperty("性别分组")
    private List<CountValue> sexGroup;

    @ApiModelProperty("年龄分组")
    private List<CountValue> ageGroup;
}

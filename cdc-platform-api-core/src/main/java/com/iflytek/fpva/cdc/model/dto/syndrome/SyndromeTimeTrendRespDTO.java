package com.iflytek.fpva.cdc.model.dto.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * @date :2023/11/7 17:09
 * @description:SyndromeTimeTrendRespDTO
 */
@Data
public class SyndromeTimeTrendRespDTO {


    @ApiModelProperty("日期")
    private String fullDate;

    @ApiModelProperty("月-日")
    private String shortFullDate;

    @ApiModelProperty("病例数")
    private Long medCnt;

    @ApiModelProperty("去年病例数")
    private Long lastYearMedCnt;

    @ApiModelProperty("n天平均值")
    private String avgMedCnt;

    private String ruleId;

    @ApiModelProperty("参考阈值")
    private String referenceValue;


    @ApiModelProperty("配置规则")
    private TbCdcmrDiseaseMonitorRule rule;
}

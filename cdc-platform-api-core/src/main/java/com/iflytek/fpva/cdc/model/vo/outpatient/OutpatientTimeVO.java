package com.iflytek.fpva.cdc.model.vo.outpatient;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OutpatientTimeVO {

    /**
     * 时间日期
     */
    @JsonFormat(pattern = "MM-dd", timezone = "GMT+8")
    private Date poisonDate;
    /**
     * 症状名称
     */
    private String outpatientTypeName;
    /**
     * 对应病历数
     */
    private Integer medicalNum;
    /**
     * 3日移动平均值
     */
    private BigDecimal d3Ewma;
    /**
     * 7日移动平均值
     */
    private BigDecimal d7Ewma;
    /**
     * 当日门诊量
     */
    private Integer outpatientNum;
}

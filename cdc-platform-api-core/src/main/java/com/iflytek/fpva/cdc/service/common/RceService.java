package com.iflytek.fpva.cdc.service.common;

import com.alibaba.fastjson.JSONObject;
import com.iflytek.fpva.cdc.model.rce.RceRespVO;
import com.iflytek.fpva.cdc.model.rce.SendUrlMsgReqVO;
import com.iflytek.fpva.cdc.util.RestTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date :2023/8/28 15:52
 * @description:rceService 调用rce协同平台
 */
@Service
@Slf4j
@RefreshScope
public class RceService {
    @Value("${usercenter.platform.appcode:cdc-manager}")
    private String appcode;

    @Value("${rce.cdc.network.url:https://cdcdev.xfzyzl.com/rce/api/cdc-im-task-service/}")
    private String publicUrl;

    @Value("${rce.cdc.api.sendUrlMsg:rceMsg/sendUrlMsg}")
    private String sendUrlMsg;

    @Autowired
    RestTemplateDTO restTemplateDTO;

    private static final String TITLE = "传染病监测预警";


    public RceRespVO sendUrlMsg(SendUrlMsgReqVO reqVO) {
        String url = publicUrl + sendUrlMsg;

        reqVO.setAppCode(appcode);
        reqVO.setTitle(TITLE);
        //1-移动端 2-PC端 3-全部
        reqVO.setPlatformCode("3");
        log.info("调用协同平台接口url#{},入参#{}", url,JSONObject.toJSONString(reqVO));
        final RceRespVO rceRespVO = restTemplateDTO.getRestTemplate().postForObject(url, reqVO, RceRespVO.class);
        log.info("调用协同平台接口返回结果#{}", JSONObject.toJSONString(rceRespVO));

        return rceRespVO;
    }
}

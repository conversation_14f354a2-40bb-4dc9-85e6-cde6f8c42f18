package com.iflytek.fpva.cdc.service.syndrome.impl;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.iflytek.fpva.cdc.common.CountStrategy;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.common.utils.DesensitizedUtils;
import com.iflytek.fpva.cdc.constant.*;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.common.*;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewScSchoolUnitInfoMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.*;
import com.iflytek.fpva.cdc.model.common.CommonOrganizationVO;
import com.iflytek.fpva.cdc.model.dto.AgeQueryDto;
import com.iflytek.fpva.cdc.model.dto.Company;
import com.iflytek.fpva.cdc.model.dto.Street;
import com.iflytek.fpva.cdc.model.dto.syndrome.SyndromeEventListQueryDTO;
import com.iflytek.fpva.cdc.model.po.MappingResult;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.map.MapPoint;
import com.iflytek.fpva.cdc.model.vo.patient.DocMedDistribution;
import com.iflytek.fpva.cdc.model.vo.syndrome.SyndromeStatisticsVO;
import com.iflytek.fpva.cdc.service.common.*;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.service.syndrome.CdcEventService;
import com.iflytek.fpva.cdc.service.syndrome.EventAuditService;
import com.iflytek.fpva.cdc.service.syndrome.EventMarkService;
import com.iflytek.fpva.cdc.service.warningEvent.WarningEventProcessRecordService;
import com.iflytek.fpva.cdc.util.*;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.commons.math3.util.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.SensitiveInterceptor.threadLocal;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CdcEventServiceImpl implements CdcEventService {

    public static final String UNKOWN_AGE = "不详";

    public static final String ABOVE_AGE = "以上";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private CdcEventProcessRecordMapper cdcEventProcessRecordMapper;

    @Resource
    private CdcWarningEventMapper cdcWarningEventMapper;

    @Resource
    private TbCdcWarningDetailMapper tbCdcWarningDetailMapper;

    @Resource
    private TbCdcEwmaTargetMapper tbCdcEwmaTargetMapper;

    @Resource
    private RestService restService;

    @Resource
    private TbCdcEventAnalysisMapper tbCdcEventAnalysisMapper;

    @Resource
    private TbCdcEventAnalysisAttachmentMapper tbCdcEventAnalysisAttachmentMapper;

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    private TbCdcConfigService tbCdcConfigService;

    @Resource
    private TbCdcewConfigMapper tbCdcewConfigMapper;

    @Resource
    private HisMedicalInfoService hisMedicalInfoService;

    @Resource
    private TbCdcewMedRelationMapper medRelationMapper;

    @Resource
    private TbCdcewOrganizationInfoMapper tbCdcOrganizationInfoMapper;

    @Resource
    private TbCdcewCompanyInfoMapper tbCdcewCompanyInfoMapper;


    @Resource
    private AreaService areaService;

    @Resource
    private TbCdcewHisMedicalInfoMapper tbCdcewHisMedicalInfoMapper;

    @Resource
    private TbCdcConfigMapper tbCdcConfigMapper;

    @Resource
    private TbCdcewSyndromeMapper syndromeMapper;

    @Resource
    private BusinessPersonService businessPersonService;

    @Resource
    private OrgService orgService;

    @Resource
    private TbCdcewRegionMapper tbCdcewRegionMapper;

    @Resource
    private TbCdcewScSchoolUnitInfoMapper tbCdcewScSchoolUnitInfoMapper;

    @Resource
    private EventAuditService eventAuditService;

    @Resource
    private EventMarkService eventMarkService;

    @Resource
    private FormConfigService formConfigService;

    @Resource
    TbCdcewPoisonMedRelationMapper tbCdcewPoisonMedRelationMapper;

    @Resource
    PoisonEventService poisonEventService;

    @Resource
    EventMonitorService eventMonitorService;

    @Resource
    private SysCalendarMapper sysCalendarMapper;

    @Resource
    private WarningEventProcessRecordService warningEventProcessRecordService;

    // 卫生院 医院  org_type_code  集合
//    private static final String[] HEALTH_ORG_TYPE_CODE_LIST = new String[]{OrgTypeEnum.HEALTH_CENTER.getCode(), OrgTypeEnum.HOSPITAL.getCode(), OrgTypeEnum.PHARMACY.getCode()};


    @Override
    public PageData<EventVO> getEventList(SyndromeEventListQueryDTO dto, boolean sortByAiScreen, boolean isOrdinaryUser, boolean isDesensitization) {
        // 已完成状态包含人工排除、AI排除、阳性事件三种状态
        //StopWatch stopWatch = new StopWatch();
        if (dto.getStatusCollection() != null && dto.getStatusCollection().contains(ProcessingStatus.FINISHED)) {
            dto.getStatusCollection().remove(ProcessingStatus.FINISHED);
            dto.getStatusCollection().add(ProcessingStatus.REMOVED_MANUAL);
            dto.getStatusCollection().add(ProcessingStatus.REMOVED_AI);
            dto.getStatusCollection().add(ProcessingStatus.POSITIVE);
        }

        //处理研判结论
        List<String> inputConclusions = CollectionUtil.getSplitList(dto.getConclusions());
        List<Integer> conclusions = inputConclusions.stream().map(Integer::parseInt).collect(Collectors.toList());

        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        EventCriteria eventCriteria = EventCriteria.builder().regionInfoList(dto.getRegionInfoList())
                                                             .syndromeCollection(dto.getSymptom())
                                                             .processingStatusCollection(dto.getStatusCollection())
                                                             .minBeginTime(dto.getMinFullDate())
                                                             .maxBeginTime(dto.getMaxFullDate())
                                                             .sortByAiScreen(sortByAiScreen)
                                                             .sortType(dto.getSortType())
                                                             .responseTimeOutStatus(dto.getResponseTimeOutStatus())
                                                             .processingTimeOutStatus(dto.getProcessingTimeOutStatus())
                                                             .dateType(dto.getDateType())
                                                             .eventType(dto.getEventType())
                                                             .conclusions(conclusions)
                                                             .eventNum(dto.getEventNum())
                                                             .markLevel(dto.getMarkLevel())
                                                             .auditLevel(dto.getAuditLevel())
                                                             .auditResult(dto.getAuditResult())
                                                             .loginUserId(dto.getLoginUserId())
                                                             .needMarkAndAudit(dto.getNeedMarkAndAudit())
                                                             .build();
       // stopWatch.start("findByParameterMap");
        List<CdcWarningEvent> events = cdcWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());
        //stopWatch.stop();

        PageInfo<CdcWarningEvent> pageInfo = new PageInfo<>(events);
        PageData<EventVO> pageData = PageData.fromPageInfo(pageInfo);
        List<CdcWarningEvent> list = pageInfo.getList();

        //stopWatch.start("warningGradeMap");
        final Map<String, WarningGradeVO> warningGradeMap = restService.getWarningGradeMap(Constants.CURRENT_VERSION);
        //实体类转所需字段VO
        List<EventVO> result = list.stream().map(v -> EventVO.fromEntityAndMap(v,warningGradeMap)).collect(Collectors.toList());
        result.forEach(item -> item.setEventVOAttentionLevel(isOrdinaryUser, sortByAiScreen));
        //stopWatch.stop();


        List<String> ids = result.stream().map(EventVO::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {

            // 获取 关注度增长率
            getWarningAttentionValueTrend(result);

        }
        if(!ids.isEmpty()){

            //查询信号处理情况，设置处理人处理时间
            List<TbCdcewWarningEventProcessRecord> warningEventProcessRecords = warningEventProcessRecordService.findByEventIdAndProcessingStatusAndType(ids,
                    dto.getStatusCollection(), ProcessTypeEnum.STATUS.getCode(), WarningTypeCodeEnum.SYNDROME.getName());
            Map<String, List<TbCdcewWarningEventProcessRecord>> processRecordsMap = warningEventProcessRecords.stream().collect(Collectors.groupingBy(TbCdcewWarningEventProcessRecord::getEventId));
            //设置信号的处理人以及处理时间
            result.forEach(eventVO -> {
                List<TbCdcewWarningEventProcessRecord> records = processRecordsMap.get(eventVO.getId());
                if (!org.apache.commons.collections.CollectionUtils.isEmpty(records)) {
                    //设置最新 处理人和时间
                    eventVO.setProcessor(records.get(0).getProcessLoginUserName());
                    eventVO.setProcessTime(records.get(0).getProcessTime());
                }
                if (Integer.valueOf(eventVO.getProcessingStatus()).equals(ProcessingStatus.REMOVED_AI)) {
                    eventVO.setProcessor(ProcessingStatus.REMOVED_AI_STR);
                    eventVO.setProcessTime(eventVO.getUpdateTime());
                }
            });
        }
        if (isDesensitization) {
            result.forEach(DesensitizeVOUtils::desensitizeEventVo);
        }
        pageData.setData(result);
        return pageData;
    }

    private void getMarksAndAudits(Map<String, List<TbCdcewEventMark>> marksMap, Map<String, List<TbCdcewEventAudit>> auditsMap, EventVO eventVO) {
        List<TbCdcewEventMark> eventMarkList = marksMap.get(eventVO.getId());
        if (!CollectionUtils.isEmpty(eventMarkList)) {
            StringBuffer sb = new StringBuffer();
            Map<String, List<TbCdcewEventMark>> listByUser = eventMarkList.stream().collect(Collectors.groupingBy(TbCdcewEventMark::getCreatorId));
            listByUser.forEach((k, v) -> {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(v.get(0).getEventLevel());
            });
            eventVO.setMarkLevel(sb.toString());
        } else {
            eventVO.setMarkLevel(CommonConstants.TO_BE_MARK);
        }
        List<TbCdcewEventAudit> eventAuditList = auditsMap.get(eventVO.getId());

        if (!CollectionUtils.isEmpty(eventAuditList)) {
            eventVO.setAuditResult(eventAuditList.get(0).getAuditResult());
            eventVO.setAuditLevel(eventAuditList.get(0).getEventLevel());
            eventVO.setAuditPerson(eventAuditList.get(0).getUpdateName());
        } else {
            eventVO.setAuditResult(CommonConstants.TO_BE_AUDIT);
            eventVO.setAuditLevel(CommonConstants.TO_BE_AUDIT);
        }
    }

    @Override
    public List<String> getSymptomsFromSyndromes(Collection<String> syndromeList) {
        Map<String, List<TbCdcewSyndrome>> syndromeMap = syndromeMapper.listAll()
                .stream().collect(Collectors.groupingBy(TbCdcewSyndrome::getSyndromeCode));
        List<String> symptomTypeList = new ArrayList<>();
        syndromeList.forEach(e -> {
            List<String> tempSymptomTypeList = syndromeMap.getOrDefault(e, new ArrayList<>()).stream().map(TbCdcewSyndrome::getSymptomType).collect(Collectors.toList());
            symptomTypeList.addAll(tempSymptomTypeList);
        });
        symptomTypeList.add("");
        return symptomTypeList;
    }

    @Override
    public void addProcessRecord(String eventId, String loginUserId, String loginUserName, String processType, String processDesc) {
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        //新增一条处理记录
        CdcEventProcessRecord record = new CdcEventProcessRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_event_process_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);
        record.setProcessDesc(processDesc);
        record.setProcessType(processType);
        record.setRecordType(RecordTypeEnum.OPERATION_RECORD.getCode());

        cdcEventProcessRecordMapper.insertSelective(record);

    }

    @Override
    public void setSchoolInfo(EventVO eventVO) {
        if (EventTypeEnum.JDQY.getName().equals(eventVO.getEventType())) {
            TbCdcewRegion tbcdcewRegion = tbCdcewRegionMapper.findById(eventVO.getStatDimId());
            if (Objects.nonNull(tbcdcewRegion)) {
                eventVO.setLongitude(tbcdcewRegion.getRegionLongitude());
                eventVO.setLatitude(tbcdcewRegion.getRegionLatitude());
                eventVO.setMapDistrictCode(tbcdcewRegion.getStandardDistrictCode());
                eventVO.setMapDistrictName(tbcdcewRegion.getStandardDistrictName());
                eventVO.setFeverClinicFlag(0);
            }
            return;
        }

        TbCdcewScSchoolUnitInfo schoolUnitInfo = tbCdcewScSchoolUnitInfoMapper.findByUnitId(eventVO.getStatDimId());
        if (schoolUnitInfo != null) {
            eventVO.setLongitude(schoolUnitInfo.getOrgLongitude());
            eventVO.setLatitude(schoolUnitInfo.getOrgLatitude());
            eventVO.setMapDistrictCode(schoolUnitInfo.getStandardDistrictCode());
            eventVO.setMapDistrictName(schoolUnitInfo.getStandardDistrictName());
            eventVO.setUnitType(schoolUnitInfo.getUnitType());
        }

    }

    // eventVO集合获取 关注度增长率
    private void getWarningAttentionValueTrend(List<EventVO> result) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(result)) {
            return;
        }

        List<String> eventIdList = result.stream().map(EventVO::getId).collect(Collectors.toList());

        Map<String, List<TbCdcWarningDetail>> attentionResultMap =
                tbCdcWarningDetailMapper.getEventDetailAttentionByByEventIds(eventIdList)
                        .stream().collect(Collectors.groupingBy(TbCdcWarningDetail::getEventId));

        for (EventVO eventVO : result) {
            List<TbCdcWarningDetail> attentionResults = attentionResultMap.getOrDefault(eventVO.getId(), new ArrayList<>());
            List<EventAttentionValueVO> eventAttentionValueVOS =
                    attentionResults.stream().map(EventAttentionValueVO::fromEntity)
                            .sorted(Comparator.comparing(EventAttentionValueVO::getFullTime))
                            .collect(Collectors.toList());
            eventVO.setWarningAttentionValueHisList(eventAttentionValueVOS);
            if (eventAttentionValueVOS.size() <= 1) {
                eventVO.setWarningAttentionValueTrend(ChangeTrendEnum.NONE.getType());
            } else {
                EventAttentionValueVO last2nd = eventAttentionValueVOS.get(eventAttentionValueVOS.size() - 2);
                EventAttentionValueVO last1st = eventAttentionValueVOS.get(eventAttentionValueVOS.size() - 1);
                double last2ndAttention = last2nd.getWarningAttentionValue();
                double last1stAttention = last1st.getWarningAttentionValue();
                String type = ChangeTrendEnum.FLAT.getType();
                if (last1stAttention < last2ndAttention) {
                    type = ChangeTrendEnum.FALLING.getType();
                } else if (last1stAttention > last2ndAttention) {
                    type = ChangeTrendEnum.RISING.getType();
                }
                eventVO.setWarningAttentionValueTrend(type);
            }
        }

    }

    @Override
    public SyndromeStatisticsVO statistics(String loginUserName, String loginUserId, String eventType, Integer processingStatus) {

        Set<Integer> statusSet = new HashSet<>();
        if (processingStatus == null) {
            statusSet.add(ProcessingStatus.PENDING);
            statusSet.add(ProcessingStatus.PROCESSING);
            statusSet.add(ProcessingStatus.FINISHED);
        } else {
            statusSet.add(processingStatus);
        }

        //数据权限过滤数据
        Collection<String> symptomList = businessPersonService.getCompletedDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.SYNDROME.getType());
        SyndromeStatisticsVO vo = new SyndromeStatisticsVO();
        vo.setDate(new Date());
        //获取用户所在机构 判断级别 组装数据
        //2020-12-29 rest调用获取机构
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        if (statusSet.contains(ProcessingStatus.PENDING)) {
            EventCriteria pendingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .symptomTypeCollection(symptomList)
                    .eventType(eventType)
                    .build();
            EventCriteria responseTimeOutCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .symptomTypeCollection(symptomList)
                    .responseTimeOutStatus(1)
                    .eventType(eventType)
                    .build();
            EventCriteria processingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .symptomTypeCollection(symptomList)
                    .processingTimeOutStatus(1)
                    .eventType(eventType)
                    .build();
            int pending = cdcWarningEventMapper.countByParameterMap(pendingCriteria.getParameterMap());
            int responseTimeout = cdcWarningEventMapper.countByParameterMap(responseTimeOutCriteria.getParameterMap());
            int processTimeout = cdcWarningEventMapper.countByParameterMap(processingCriteria.getParameterMap());
            vo.setPending(pending);
            vo.setResponseTimeout(responseTimeout);
            vo.setProcessTimeout(processTimeout);
        }
        if (statusSet.contains(ProcessingStatus.PROCESSING)) {
            EventCriteria pendingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .symptomTypeCollection(symptomList)
                    .eventType(eventType)
                    .build();
            EventCriteria responseTimeOutCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .symptomTypeCollection(symptomList)
                    .responseTimeOutStatus(1)
                    .eventType(eventType)
                    .build();
            EventCriteria processingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .symptomTypeCollection(symptomList)
                    .processingTimeOutStatus(1)
                    .eventType(eventType)
                    .build();
            int processing = cdcWarningEventMapper.countByParameterMap(pendingCriteria.getParameterMap());
            int responseTimeout = cdcWarningEventMapper.countByParameterMap(responseTimeOutCriteria.getParameterMap());
            int processTimeout = cdcWarningEventMapper.countByParameterMap(processingCriteria.getParameterMap());
            vo.setProcessing(processing);
            vo.setResponseTimeout(responseTimeout);
            vo.setProcessTimeout(processTimeout);
        }
        if (statusSet.contains(ProcessingStatus.FINISHED)) {
            EventCriteria removedManualCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.REMOVED_MANUAL))
                    .symptomTypeCollection(symptomList)
                    .eventType(eventType)
                    .build();
            EventCriteria removedAiCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.REMOVED_AI))
                    .symptomTypeCollection(symptomList)
                    .eventType(eventType)
                    .build();
            EventCriteria positiveCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.POSITIVE))
                    .symptomTypeCollection(symptomList)
                    .eventType(eventType)
                    .build();
            int removedManual = cdcWarningEventMapper.countByParameterMap(removedManualCriteria.getParameterMap());
            int removedAi = cdcWarningEventMapper.countByParameterMap(removedAiCriteria.getParameterMap());
            int positive = cdcWarningEventMapper.countByParameterMap(positiveCriteria.getParameterMap());
            int finished = removedManual + removedAi + positive;
            vo.setRemovedManual(removedManual);
            vo.setRemovedAi(removedAi);
            vo.setPositive(positive);
            vo.setFinished(finished);
        }
        if (StringUtils.isNotBlank(uapOrg.getDistrictCode())) {
            vo.setAreaName(uapOrg.getDistrict());
        } else if (StringUtils.isNotBlank(uapOrg.getCityCode())) {
            vo.setAreaName(uapOrg.getCity());
        } else {
            vo.setAreaName(uapOrg.getProvince());
        }
        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            vo.setAreaName(DesensitizationUtils.replaceAdministrativeAreaNameX(vo.getAreaName()));
        }

        vo.setTotal(vo.getPending() + vo.getProcessing() + vo.getFinished());

        return vo;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void startProcess(String loginUserId, String eventId, int status, String loginUserName) {
        log.info("症候群手动改变事件状态开始,信息: loginUserId:" + loginUserId + " ,eventId:" + eventId
                + " ,status:" + status + " ,loginUserName:" + loginUserName);
        if (status >= ProcessingStatus.REMOVED_AI) {
            throw new MedicalBusinessException("11441005", "无效的状态");
        }
        CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(eventId);
        //查询不到 或 状态重复 不能进行操作
        if (event == null) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }
        //操作权限检查
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        UapAccessUtil.hasAccess(uapOrg, event.getProvinceCode(), event.getCityCode(), event.getDistrictCode());
        //新增一条处理记录
        CdcEventProcessRecord record = new CdcEventProcessRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_event_process_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessStatus(status);
        record.setProcessLoginUserId(loginUserId);
        record.setProcessType(ProcessTypeEnum.STATUS.getCode());
        record.setRecordType(RecordTypeEnum.OPERATION_RECORD.getCode());
        Date endTime = null;

        switch (status) {
            case ProcessingStatus.PROCESSING:
                record.setProcessDesc(ProcessingStatus.PROCESSING_STR);
                break;
            //如果是排除和上报 则要修改事件的endTime
            case ProcessingStatus.REMOVED_MANUAL:
                record.setProcessDesc(ProcessingStatus.REMOVED_MANUAL_STR);
                endTime = new Date();
                break;
            case ProcessingStatus.POSITIVE:
                record.setProcessDesc(ProcessingStatus.POSITIVE_STR);
                endTime = new Date();
                break;
            default:
                break;
        }
        cdcEventProcessRecordMapper.insertSelective(record);
        //将原始事件表里的processing_status改为正在处理
        cdcWarningEventMapper.updateProcessingStatusAndEndTimeById(status, endTime, eventId,null);
        //2020-11-26 需要将所关联的detail中的状态也同步修改
        tbCdcWarningDetailMapper.updateProcessingStatusByEventId(status, eventId);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateAiScreen(int isAiScreen, String eventId) {
        //1,更新状态
        log.info("症候群事件AI初筛置顶：事件ID-{}: 置顶状态-{}", eventId, isAiScreen);
        cdcWarningEventMapper.updateIsAiScreenById(isAiScreen, eventId);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateCheckedLevel(int checkedLevel, String eventId) {
        //1,更新状态
        log.info("更新事件审查状态：事件ID-{}: 审查状态-{}", eventId, checkedLevel);
        cdcWarningEventMapper.updateCheckedLevelById(checkedLevel, eventId);
    }

    @Override
    public CdcWarningEvent getCdcWaringEventByEventId(String eventId) {
        return cdcWarningEventMapper.selectByPrimaryKey(eventId);
    }

    @Override
    public List<Date> getEventTimeLine(String eventId) {
        List<String> list = Collections.singletonList(eventId);
        List<MedicalInfoVO> result = tbCdcewHisMedicalInfoMapper.findRecordsByEventIdsSort(list, null, null, null,null,null, null);
        List<Date> dateList = result.stream()
                .filter(elem -> Objects.nonNull(elem.getFullDate()))
                .sorted(Comparator.comparing(MedicalInfoVO::getFullDate))
                .map(MedicalInfoVO::getFullDate).collect(Collectors.toList());
        Date endTime = dateList.get(dateList.size() - 1);
        Date startTime = dateList.get(0);
        return cdcWarningEventMapper.getTimeLine(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getWarningEventResourceDistribution(String eventId,String  detailId, String startTime, String endTime,String loginUserId) {
        List<Map<String, Object>> returnList = new ArrayList<>();
        if (!org.springframework.util.StringUtils.isEmpty(detailId)){
            final List<MedicalInfoVO> medicalInfoList = eventMonitorService.getMedicalInfoList(detailId,loginUserId);
            final Map<String, Long> sourceTypeMap = medicalInfoList.stream()
                    .collect(Collectors.groupingBy(v -> org.springframework.util.StringUtils.isEmpty(v.getSourceType())?"":v.getSourceType() , Collectors.counting()));
            sourceTypeMap.forEach((k,v) ->{
                Map<String, Object> map = new HashMap<>();
                map.put("name", k);
                map.put("value", v);
                returnList.add(map);
            });
            return returnList;
        }


        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime;
        Date formatEndTime;
        try {
            formatStartTime = simpleDateFormat.parse(startTime);
            formatEndTime = simpleDateFormat.parse(endTime);
        } catch (ParseException e) {
            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
        }
        int registerCount = Optional.ofNullable(cdcWarningEventMapper.getRegisterMedicalCountByEventId(eventId, formatStartTime, formatEndTime)).orElse(0);
        List<Map<String, Object>> resultList;
        int wmaCount = 0;
        int ghCount = 0;
        int esCount = 0;
        int phCount = 0;
        int scCount = 0;

        //未发起过流调的信号，查询预警平台病历
        resultList = cdcWarningEventMapper.getWarningEventResourceDistribution(eventId, formatStartTime, formatEndTime);
        for (Map<String, Object> tempMap : resultList) {
            Object type = tempMap.get("type");
            if (!Objects.isNull(type)) {
                if (String.valueOf(DataSourceTypeEnum.WISDOM_MEDICAL_ASSISTANT.getValue()).equals(type.toString())) {
                    wmaCount = wmaCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (DataSourceTypeEnum.GRADE_HOSPITAL_OUTPATIENT.getStrValue().equals(type.toString())
                        || String.valueOf(DataSourceTypeEnum.GRADE_HOSPITAL_IN_HOSPITAL.getValue()).equals(type.toString())) {
                    ghCount = ghCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getValue()).equals(type.toString())) {
                    esCount = esCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.GRADE_PHARMACY.getValue()).equals(type.toString())) {
                    phCount = phCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.GRADE_SCHOOL.getValue()).equals(type.toString())) {
                    scCount = scCount + Integer.parseInt(tempMap.get("cn").toString());
                }


            }
        }

        Map<String, Object> result1 = new HashMap<>();
        Map<String, Object> result2 = new HashMap<>();
        Map<String, Object> result3 = new HashMap<>();
        Map<String, Object> result4 = new HashMap<>();
        Map<String, Object> result5 = new HashMap<>();
        Map<String, Object> result6 = new HashMap<>();

        if (wmaCount > 0) {
            result1.put("name", MedDistributionEnum.COMMUNITY.getDesc());
            result1.put("value", wmaCount);
            returnList.add(result1);
        }
        if (ghCount > 0) {
            result2.put("name", MedDistributionEnum.HOSPITAL.getDesc());
            result2.put("value", ghCount);
            returnList.add(result2);
        }
        if (esCount > 0) {
            result3.put("name", MedDistributionEnum.LAB.getDesc());
            result3.put("value", esCount);
            returnList.add(result3);
        }
        if (phCount > 0) {
            result4.put("name", MedDistributionEnum.PHARMACY.getDesc());
            result4.put("value", phCount);
            returnList.add(result4);
        }
        if (scCount > 0) {
            result5.put("name", MedDistributionEnum.SCHOOL.getDesc());
            result5.put("value", scCount);
            returnList.add(result5);
        }
        if(registerCount > 0){
            result6.put("name", MedDistributionEnum.REGISTREPORTING.getDesc());
            result6.put("value", registerCount);
            returnList.add(result6);
        }
        return returnList;
    }

    @Override
    public List<Map<String, String>> getWarningEventSymptomDistribution(String eventId,String  detailId, String startTime, String endTime,String loginUserId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime;
        Date formatEndTime;
        try {
            formatStartTime = simpleDateFormat.parse(startTime);
            formatEndTime = simpleDateFormat.parse(endTime);
        } catch (ParseException e) {
            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
        }
        List<Date> dateList = getEventTimeLine(eventId);
        int index = dateList.indexOf(formatEndTime);
        List<Map<String, String>> symptomResultList = new ArrayList<>();
        Map<String, Integer> presentSymptomMap = new HashMap<>();
        Map<String, Integer> previousSymptomMap = new HashMap<>();
        if (!org.springframework.util.StringUtils.isEmpty(detailId)){
            final TbCdcWarningDetail detail = tbCdcWarningDetailMapper.findById(detailId);
            final List<MedicalInfoVO> medicalInfoList = eventMonitorService.getMedicalInfoList(detailId,loginUserId);
            final List<String> presentArray = medicalInfoList.stream().map(MedicalInfoVO::getKeySymptomTagList).collect(Collectors.toList());
            buildSymptomMap(presentSymptomMap, presentArray);

            final List<String> previousArray = medicalInfoList.stream().filter(v -> !detail.getFullDate().equals(v.getFullDate())).map(MedicalInfoVO::getKeySymptomTagList).collect(Collectors.toList());
            buildSymptomMap(previousSymptomMap, previousArray);

        } else {
            List<String> presentResultList = getSymptomList(eventId, formatStartTime, formatEndTime, presentSymptomMap);
            if (presentResultList == null) return null;
            if (index != 0) {
                if (CollectionUtils.isEmpty(presentResultList)) {
                    return null;
                }
                getSymptomList(eventId, formatStartTime, dateList.get(index - 1), previousSymptomMap);
            }
        }

        presentSymptomMap.forEach((symptom, count) ->
        {
            Map<String, String> symptom1Map = new HashMap<>();
            symptom1Map.put("code", symptom);
            if (previousSymptomMap.containsKey(symptom)) {
                Integer previousCount = previousSymptomMap.get(symptom);
                symptom1Map.put("value1", String.valueOf(previousCount));
                symptom1Map.put("value2", String.valueOf(count - previousCount));
                symptom1Map.put("type", "0");
            } else {
                symptom1Map.put("value1", "0");
                symptom1Map.put("value2", String.valueOf(count));
                symptom1Map.put("type", "1");
            }
            symptomResultList.add(symptom1Map);
            Collections.sort(symptomResultList, (o1, o2) -> -Integer.compare((Integer.parseInt(o1.get("value1")) + Integer.parseInt(o1.get("value2"))), (Integer.parseInt(o2.get("value1")) + Integer.parseInt(o2.get("value2")))));
        });
        return symptomResultList;

    }

    private List<String> getSymptomList(String eventId, Date startTime, Date endTime,  Map<String, Integer> presentSymptomMap) {
        List<String> presentResultList;
        List<String> presentMedicalList = cdcWarningEventMapper.getMedicalIdListFromRelation(eventId, startTime, endTime);
        if (CollectionUtils.isEmpty(presentMedicalList)) {
            return null;
        }
        presentResultList = cdcWarningEventMapper.getWarningEventSymptomDistribution(presentMedicalList);
        buildSymptomMap(presentSymptomMap, presentResultList);

        return presentResultList;
    }

    private static void buildSymptomMap(Map<String, Integer> presentSymptomMap, List<String> presentResultList) {
        for (String symptomArray : presentResultList) {
            if (!StringUtils.isEmpty(symptomArray)) {
                List<String> symptomList = Arrays.stream(symptomArray.split("\\|")).distinct().collect(Collectors.toList());
                for (String symptom : symptomList) {
                    Integer count = presentSymptomMap.computeIfAbsent(symptom, v -> 0);
                    presentSymptomMap.put(symptom, count + 1);
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> getEventMedCountByDate(String eventId,String detailId, String startTime, String endTime,String loginUserId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime;
        Date formatEndTime;
        try {
            formatStartTime = simpleDateFormat.parse(startTime);
            formatEndTime = simpleDateFormat.parse(endTime);
        } catch (ParseException e) {
            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (org.springframework.util.StringUtils.isEmpty(detailId)){
            resultList = cdcWarningEventMapper.getEventMedCountByDate(eventId, formatStartTime, formatEndTime);
        }else {
            final List<MedicalInfoVO> medicalInfoList = eventMonitorService.getMedicalInfoList(detailId,loginUserId);
            TbCdcWarningDetail detail = tbCdcWarningDetailMapper.findById(detailId);

            final Map<String, List<MedicalInfoVO>> map = medicalInfoList.stream()
                    .collect(Collectors.groupingBy(v -> TimeConstant.formatDateStr(v.getFullDate(),TimeConstant.NORM_DATE_PATTERN)));
            formatEndTime = detail.getFullDate();
            //窗口期读数据库
            TbCdcewSyndrome syndrome = syndromeMapper.findBySymptomType(detail.getSymptomType());
            if (syndrome == null){
                //默认7天窗口期
                formatStartTime = DateUtil.offsetDay(detail.getFullDate(),-6);
            } else {
                formatStartTime = DateUtil.offsetDay(detail.getFullDate(),-(syndrome.getIncubation()-1));
            }

            List<SysCalendar> calendarList = sysCalendarMapper.listBetweenDay(formatStartTime,formatEndTime);
            List<String> dates = calendarList.stream().map(e -> TimeConstant.formatDateStr(e.getDayShortDesc())).collect(Collectors.toList());
            for (String date : dates) {
                 Map<String, Object> rMap = new LinkedHashMap<>();
                 rMap.put("dateTime",date);
                 rMap.put("count",0);
                final List<MedicalInfoVO> voList = map.get(date);
                if (!CollectionUtils.isEmpty(voList)) {
                     rMap.put("count", voList.size());
                 }
                 resultList.add(rMap);
            }
        }


        int count = 0;

        for (Map<String, Object> resultMap : resultList) {
            count = count + Integer.parseInt(resultMap.get("count").toString());
            resultMap.put("count", count);
        }
        return resultList;

    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getMedOutCallCount(String eventId, String startTime, String endTime) {
        return new ArrayList<>();
    }

    @Override
    public List<EventAnalysisResultVO> getEventAnalysisResult(String eventId) {

        //1，获取研判信息
        List<TbCdcEventAnalysis> tbCdcEventAnalyses = tbCdcEventAnalysisMapper.selectByEventId(eventId);
        if (CollectionUtils.isEmpty(tbCdcEventAnalyses)) {
            return Collections.EMPTY_LIST;
        }

        //2，获取附件信息
        List<EventAnalysisResultVO> resultList = new ArrayList<>();
        for (TbCdcEventAnalysis tbCdcEventAnalysis : tbCdcEventAnalyses) {
            List<TbCdcAttachment> tbCdcAttachments = tbCdcAttachmentMapper.selectByAnalysisId(tbCdcEventAnalysis.getId());
            EventAnalysisResultVO eventAnalysisResultVO = EventAnalysisResultVO.fromEntity(tbCdcEventAnalysis);
            eventAnalysisResultVO.setAttachmentList(tbCdcAttachments);
            resultList.add(eventAnalysisResultVO);
        }
        return resultList;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public TbCdcEventAnalysis createEventAnalysisResult(EventAnalysisResultVO eventAnalysisResultVO, String loginUserId, String loginUserName) {
        //传入字段校验
        if
        (eventAnalysisResultVO.getOtherDiseaseName() != null && eventAnalysisResultVO.getOtherDiseaseName().length() > 15) {
            throw new MedicalBusinessException("11441027", "疾病名称超出15字限制");
        }
        if ((eventAnalysisResultVO.getDeadCaseNum() < 0) || (eventAnalysisResultVO.getDeadCaseNum() > 999)) {
            throw new MedicalBusinessException("11441028", "死亡病例数不合规");
        }
        if ((eventAnalysisResultVO.getTotalCaseNum() < 0) || (eventAnalysisResultVO.getTotalCaseNum() > 999)) {
            throw new MedicalBusinessException("11441029", "总病例数不合规");
        }
        //1, 增加研判信息
        String analysisId = String.valueOf(batchUidService.getUid("tb_cdcew_event_analysis"));
        TbCdcEventAnalysis tbCdcEventAnalysis = TbCdcEventAnalysis.builder()
                .id(analysisId)
                .conclusions(eventAnalysisResultVO.getConclusions())
                .educationInvolved(eventAnalysisResultVO.getEducationInvolved())
                .comments(eventAnalysisResultVO.getComments())
                .eventId(eventAnalysisResultVO.getEventId())
                .createTime(new Date())
                .orgName(eventAnalysisResultVO.getOrgName())
                .orgId(eventAnalysisResultVO.getOrgId())
                .investigationTime(eventAnalysisResultVO.getInvestigationTime())
                .investigationMethod(eventAnalysisResultVO.getInvestigationMethod())
                .epiHistory(eventAnalysisResultVO.getEpiHistory())
                .pathogenDetection(eventAnalysisResultVO.getPathogenDetection())
                .totalCaseNum(eventAnalysisResultVO.getTotalCaseNum())
                .deadCaseNum(eventAnalysisResultVO.getDeadCaseNum())
                .creatorName(eventAnalysisResultVO.getCreatorName())
                .creatorId(loginUserId)
                .fillingDate(eventAnalysisResultVO.getFillingDate())
                .diseaseCode(eventAnalysisResultVO.getDiseaseCode())
                .diseaseName(eventAnalysisResultVO.getDiseaseName())
                .otherDiseaseName(eventAnalysisResultVO.getOtherDiseaseName())
                .positiveEventType(eventAnalysisResultVO.getPositiveEventType())
                .build();
        //2, 增加附件信息
        List<TbCdcAttachment> attachmentList = eventAnalysisResultVO.getAttachmentList();
        List<TbCdcEventAnalysisAttachment> tbCdcEventAnalysisAttachmentList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(attachmentList)) {
            attachmentList.stream().forEach(item -> {
                String uid = String.valueOf(batchUidService.getUid("tb_cdcew_event_analysis_attachment"));
                TbCdcEventAnalysisAttachment tbCdcEventAnalysisAttachment = new TbCdcEventAnalysisAttachment();
                tbCdcEventAnalysisAttachment.setAnalysisId(analysisId);
                tbCdcEventAnalysisAttachment.setAttachmentId(item.getId());
                tbCdcEventAnalysisAttachment.setId(uid);
                tbCdcEventAnalysisAttachmentList.add(tbCdcEventAnalysisAttachment);
            });
        }


        //3, 存储数据库
        // 添加 研判结论 操作记录
        String processRecordId = addEventRecord(eventAnalysisResultVO.getEventId(), loginUserId, loginUserName, null, RecordTypeEnum.EVENT_ASSESS.getCode());
        tbCdcEventAnalysis.setProcessRecordId(processRecordId);
        tbCdcEventAnalysisMapper.insert(tbCdcEventAnalysis);
        if (!CollectionUtils.isEmpty(tbCdcEventAnalysisAttachmentList)) {
            tbCdcEventAnalysisAttachmentMapper.batchInsert(tbCdcEventAnalysisAttachmentList);
        }
        // 根据研判结论判断
        if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_SUSPECT || eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_CONFIRM) {
            startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.POSITIVE, loginUserName);
        } else if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_REMOVED) {
            startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.REMOVED_MANUAL, loginUserName);
        } else if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_FOCUS) {
            // 持续关注，延长处置超时时间
            CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(eventAnalysisResultVO.getEventId());
            if (event == null) {
                throw new MedicalBusinessException("11450001", "不存在对应ID的事件：" + eventAnalysisResultVO.getEventId());
            }

            // 处置超时状态，不延长处置超时时间
            if (OvertimeStatus.PROCESS_OVERTIME_OUT !=
                    Optional.ofNullable(event.getProcessingTimeOutStatus()).orElse(OvertimeStatus.PROCESS_OVERTIME_NORMAL)) {
                long processTimeout = tbCdcConfigService.getSyndromeProcessingTimeoutConfig();
                Date processLatestTime = DateUtils.addHours(new Date(), (int) processTimeout);
                cdcWarningEventMapper.updateProcessingLatestTimeByEventId(eventAnalysisResultVO.getEventId(), processLatestTime);
            }

            startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.PROCESSING, loginUserName);

        }

        return tbCdcEventAnalysis;
    }


    @Override
    public EventVO eventDetail(String eventId, String loginUserId) {

        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);
        //是否是普通用户
        boolean isOrdinaryUser = tbCdcConfigService.isOrdinaryUser(loginUserId);
        // ai置顶开关是否打开
        boolean isAiScreen = tbCdcConfigService.isSyndromeEnableAiScreen();

        EventVO eventVO = this.buildEventVo(eventId)
                              .setEventVOAttentionLevel(isOrdinaryUser, isAiScreen)
                              .setMedicalCountInfoByTarget()
                              .setEventIsClusterInfo()
                              .setOrgInfo();

        if (isDesensitization) {
            DesensitizeVOUtils.desensitizeEventVo(eventVO);
        }

        return eventVO;
    }

    @Override
    public EventExVO humanDistribution(String eventId, String loginUserId,String detailId, String startTime, String endTime) {
        EventVO eventVO = this.buildEventVo(eventId).setMedicalInfoList(detailId,loginUserId);
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);

        EventExVO eventExVO = new EventExVO();

        List<MedicalInfoVO> medVoList = (List<MedicalInfoVO>) eventVO.getMedicalInfoVOList();
        SimpleDateFormat sdf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date endDate = null;
        Date startDate = null;
        if (!org.springframework.util.StringUtils.isEmpty(startTime) && !org.springframework.util.StringUtils.isEmpty(endTime)){
            try {
                endDate = sdf.parse(endTime);
                startDate = sdf.parse(startTime);

            } catch (ParseException e) {
                throw new MedicalBusinessException("11452001", "时间格式化错误:/n endTime:" + endTime +"/n startTime:"+startTime );
            }
            Date finalStartDate = startDate;
            Date finalEndDate = endDate;
            medVoList= medVoList.stream().filter(medicalInfoVO -> medicalInfoVO.getFullDate().compareTo(finalStartDate) > -1 && medicalInfoVO.getFullDate().compareTo(finalEndDate) < 1)
                    .collect(Collectors.toList());
        }

        CountStrategy<MedicalInfoVO> countStrategy = new CountStrategy<>();
        // 组装年龄、性别统计信息
        HumanDistributionCountVO humanDistributionCountVO = getCountVO(medVoList, countStrategy);
        // 组装通知统计信息
//        List<MatrixVO> noteList = getNoteList(medVoList, countStrategy);
        // 组装医生信息
        List<DocMedDistribution> doctorDataList = getDoctorDataList(medVoList);
        if (isDesensitization) {
            doctorDataList.forEach(DesensitizeVOUtils::desensitizeDocMedDistribution);
        }
        eventExVO.setHumanDistributionCountVO(humanDistributionCountVO);
        //流调兼容
        eventExVO.setCountVO(humanDistributionCountVO);
//        eventExVO.setNoteList(noteList);
        eventExVO.setDocMedDistributionList(doctorDataList);
        return eventExVO;
    }

    @Override
    public EventListVO mapRelation(String eventId,String detailId, String beginDate, String endDate, String loginUserId) {
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);
        EventVO eventVO = this.buildEventVo(eventId).setOrgInfo();
        int isEs = Optional.ofNullable(eventVO.getIsEs()).orElse(0);

        log.info("开始查询构建eventListVO");
        EventListVO eventListVO = getMedicalEventLinkDetailVO(eventId,detailId, beginDate, endDate, isDesensitization, isEs,loginUserId);
        log.info("结束构建eventListVO");
        MapPoint mapPoint = new MapPoint();
        mapPoint.setLatitude(eventVO.getLatitude());
        mapPoint.setLongitude(eventVO.getLongitude());
        mapPoint.setName(eventVO.getStatDimName());
        mapPoint.setKey(eventVO.getStatDimId());
        if (isDesensitization) {
            DesensitizeVOUtils.desensitizeMapPoint(mapPoint);
        }
        eventListVO.setEventLocation(mapPoint);

        Boolean isNeededMapping = restService.getIsNeededMapping();
        if (Boolean.FALSE.equals(isNeededMapping)) {
            eventListVO.setDistrictCode(eventVO.getMapDistrictCode());
            eventListVO.setDistrictName(eventVO.getMapDistrictName());
        } else {
            eventListVO.setDistrictCode(eventVO.getDistrictCode());
            eventListVO.setDistrictName(eventVO.getDistrictName());
        }
        // 设置周边机构病历数
        setSurroundOrgVOList(eventListVO, isDesensitization, eventVO);
        return eventListVO;
    }

    /**
     * 获取病例列表，机构名称分组
     *
     * @return
     */
    private List<RegionVO<MedicalInfoVO>> getMedicalInfoList(List<MedicalInfoVO> medVoList, boolean isDesensitization) {
        Map<String, List<MedicalInfoVO>> medicalInfoMap = medVoList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.isNoneBlank(item.getStatDimName()))
                .collect(Collectors.groupingBy(MedicalInfoVO::getStatDimName));
        List<RegionVO<MedicalInfoVO>> returnList = new ArrayList<>();
        medicalInfoMap.forEach((streetName, medList) -> {
            RegionVO<MedicalInfoVO> regionVO = new RegionVO<>();
            regionVO.setRegionName(streetName);
            regionVO.setCount(medList.size());
            regionVO.setSubList(medList);
            if (isDesensitization) {
                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
                medList.forEach(DesensitizeVOUtils::desensitizeMedicalInfoVo);
            }
            returnList.add(regionVO);
        });
        return returnList;
    }

    /**
     * 获取医院结果集
     *
     * @param eventId
     * @param beginDate
     * @param endDate
     * @return
     */
    private EventListVO getMedicalEventLinkDetailVO(String eventId,String detailId, String beginDate, String endDate,
                                                    boolean isDesensitization, int isEs,String loginUserId) {
        EventListVO eventListVO = new EventListVO();
        log.info("开始查询信号病历");
        List<MedicalInfoVO> medVoList = new ArrayList<>();
        if (org.springframework.util.StringUtils.isEmpty(detailId)){
            medVoList = hisMedicalInfoService.findMedByEventTimeRange(eventId, beginDate, endDate);
        } else {
            medVoList = eventMonitorService.getMedicalInfoList(detailId,loginUserId);
        }

        log.info("结束查询信号病历,size:" + medVoList.size());

        // 获取病历所包含的机构列表
        List<Street> allStreetList = new ArrayList<>();
        List<String> orgSourceKeyList = medVoList.stream().map(MedicalInfoVO::getStatDimId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgSourceKeyList)) {
            log.info("开始查询event病历机构列表");
            List<CommonOrganizationVO> orgList = tbCdcOrganizationInfoMapper.findAllByOrgIds(orgSourceKeyList);
            log.info("结束查询event病历机构列表");
            allStreetList = orgList.stream().map(Street::fromCommonOrgnazitionVO).collect(Collectors.toList());
        }
        List<Company> allCompanyList = medVoList.stream().map(Company::fromMedicalInfoVO).distinct().collect(Collectors.toList());
        eventListVO.setStreetList(getMonitorListByHospital(medVoList, allStreetList, allCompanyList, false, isDesensitization));
        eventListVO.setCompanyList(getCompanyListFromHospital(medVoList, allStreetList, allCompanyList, false, isDesensitization));
        eventListVO.setAddressList(getAddressListFromHospital(medVoList, isDesensitization));
        return eventListVO;
    }

    /**
     * 构建住址列表-从医院数据源提取
     *
     * @param medicalInfoVOList
     * @return
     */
    private List<RegionVO> getAddressListFromHospital(List<MedicalInfoVO> medicalInfoVOList, boolean isDesensitization) {
        List<RegionVO> regionList = new ArrayList<>();
        Map<Pair<Double, Double>, List<MedicalInfoVO>> regionMap = medicalInfoVOList.stream().collect(Collectors.groupingBy(MedicalInfoVO::getLongitudeAndLatitude));
        regionMap.forEach((region, medList) -> {
            List<String> addrList = medList.stream().map(MedicalInfoVO::getLivingAddress).distinct().collect(Collectors.toList());
            String regionName = org.apache.commons.lang3.StringUtils.join(addrList, ",");
            RegionVO<MedicalInfoVO> regionVO = new RegionVO<>();
            regionVO.setRegionName(regionName);
            regionVO.setLongitude(region.getKey());
            regionVO.setLatitude(region.getValue());
            regionVO.setCount(medList.size());
            regionVO.setPicType(PicTypeEnum.ADDRESS.getType());
            regionVO.setRatio(PercentUtil.getRatio(medList.size()));
            if (isDesensitization) {
                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
            }
            regionList.add(regionVO);
        });
        return regionList;
    }

    /**
     * 构建单位列表-从医院的数据源提取
     *
     * @param medicalInfoVOList
     * @return
     */
    private List<CompanyVO> getCompanyListFromHospital(List<MedicalInfoVO> medicalInfoVOList, List<Street> allStreetList, List<Company> allCompanyList, boolean returnRootOnly, boolean isDesensitization) {
        List<CompanyVO> companyList = new ArrayList<>();
        Map<String, List<MedicalInfoVO>> medicalInfoList = medicalInfoVOList.stream().filter(x -> org.apache.commons.lang3.StringUtils.isNoneBlank(x.getCompany())).collect(Collectors.groupingBy(MedicalInfoVO::getCompany));
        Map<String, Company> companyMap = allCompanyList.stream().filter(company -> org.apache.commons.lang.StringUtils.isNotEmpty(company.getCompanyName())).collect(Collectors.toMap(Company::getCompanyName, a -> a, (k1, k2) -> k1));
        medicalInfoList.forEach(
                (companyName, students) -> {
                    Company company = areaService.getCompanyByName(companyName, companyMap);
                    CompanyVO companyVO = CompanyVO.fromEntity(company);
                    companyVO.setCount(students.size());
                    companyVO.setRatio(PercentUtil.getRatio(students.size()));

                    if (!returnRootOnly) {
                        Map<Pair<Double, Double>, List<MedicalInfoVO>> regionMap = students.stream().collect(Collectors.groupingBy(MedicalInfoVO::getLongitudeAndLatitude));
                        List<RegionVO> regionList = new ArrayList<>();
                        regionMap.forEach((region, medicalInfoVOS) -> {
                            List<String> addrList = medicalInfoVOS.stream().map(MedicalInfoVO::getLivingAddress).filter(org.apache.commons.lang3.StringUtils::isNoneBlank).distinct().collect(Collectors.toList());
                            String regionName = org.apache.commons.lang3.StringUtils.join(addrList, ",");
                            RegionVO<StreetVO> regionVO = new RegionVO<>();
                            regionVO.setRegionName(regionName);
                            regionVO.setLongitude(region.getKey());
                            regionVO.setLatitude(region.getValue());
                            regionVO.setCount(medicalInfoVOS.size());

                            List<StreetVO> monitorListByHospital = getMonitorListByHospital(medicalInfoVOS, allStreetList, allCompanyList, true, isDesensitization);
                            regionVO.setSubList(monitorListByHospital);
                            regionVO.setRatio(PercentUtil.getRatio(medicalInfoVOS.size()));
                            regionVO.setPicType(PicTypeEnum.HOSPITAL.getType());
                            if (isDesensitization) {
                                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
                            }
                            regionList.add(regionVO);
                        });
                        companyVO.setRegionList(regionList);
                    }

                    if (isDesensitization) {
                        DesensitizeVOUtils.desensitizeCompanyVo(companyVO);
                    }
                    companyList.add(companyVO);
                }
        );
        return companyList;
    }

    /**
     * 构建监测机构-医院列表
     *
     * @param medicalInfoVOList
     * @param returnRootOnly
     * @return
     */
    private List<StreetVO> getMonitorListByHospital(List<MedicalInfoVO> medicalInfoVOList, List<Street> allStreetList, List<Company> allCompanyList, boolean returnRootOnly, boolean isDesensitization) {
        //组装街道信息
        List<StreetVO> streetList = new ArrayList<>();
        Map<String, List<MedicalInfoVO>> medicalInfoMap = medicalInfoVOList.stream().filter(x -> org.apache.commons.lang3.StringUtils.isNoneBlank(x.getStatDimName())).collect(Collectors.groupingBy(MedicalInfoVO::getStatDimName));
        Map<String, Street> streetMap = allStreetList.stream().filter(street -> StringUtils.isNotEmpty(street.getStreetName())).collect(Collectors.toMap(Street::getStreetName, a -> a, (k1, k2) -> k1));
        medicalInfoMap.forEach(
                (streetName, medicalInfoList) -> {
                    Street street = areaService.getStreetByName(streetName, streetMap);
                    StreetVO streetVO = StreetVO.fromEntity(street);
                    streetVO.setCount(medicalInfoList.size());
                    streetVO.setRatio(PercentUtil.getRatio(medicalInfoList.size()));

                    if (!returnRootOnly) {
                        Map<Pair<Double, Double>, List<MedicalInfoVO>> regionMap = medicalInfoList.stream().collect(Collectors.groupingBy(MedicalInfoVO::getLongitudeAndLatitude));
                        List<RegionVO> regionList = new ArrayList<>();
                        regionMap.forEach((region, medList) -> {
                            List<String> addrList = medList.stream().map(MedicalInfoVO::getLivingAddress).distinct().collect(Collectors.toList());
                            String regionName = org.apache.commons.lang3.StringUtils.join(addrList, ",");
                            RegionVO<CompanyVO> regionVO = new RegionVO<>();
                            regionVO.setRegionName(regionName);
                            regionVO.setLongitude(region.getKey());
                            regionVO.setLatitude(region.getValue());
                            regionVO.setCount(medList.size());

                            List<CompanyVO> companyListFromHospital = getCompanyListFromHospital(medList, allStreetList, allCompanyList, true, isDesensitization);
                            regionVO.setSubList(companyListFromHospital);
                            regionVO.setPicType(PicTypeEnum.HOSPITAL.getType());
                            regionVO.setRatio(PercentUtil.getRatio(medList.size()));
                            if (isDesensitization) {
                                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
                            }
                            regionList.add(regionVO);
                        });
                        streetVO.setRegionList(regionList);
                    }
                    if (isDesensitization) {
                        DesensitizeVOUtils.desensitizeStreetVo(streetVO);
                    }
                    streetList.add(streetVO);
                }
        );
        return streetList;
    }

    private HumanDistributionCountVO getCountVO(List<MedicalInfoVO> medVoList, CountStrategy<MedicalInfoVO> countStrategy) {
        HumanDistributionCountVO humanDistributionCountVO = new HumanDistributionCountVO();

        //年龄统计
        countStrategy.groupByAge(humanDistributionCountVO, medVoList, MedicalInfoVO::getDiagnoseAge);
        //性别统计
        countStrategy.groupByGender(humanDistributionCountVO, medVoList, MedicalInfoVO::getSexDesc);
        return humanDistributionCountVO;
    }

//    private List<MatrixVO> getNoteList(List<MedicalInfoVO> medVoList, CountStrategy<MedicalInfoVO> countStrategy) {
//        List<MatrixVO> matrixVOList = new ArrayList<>();
//        MatrixVO outCallMatrix = new MatrixVO();
//        outCallMatrix.setTitleName(NoteTypeEnum.OUTCALL.getDesc());
//        countStrategy.computeOutCallStatusByMatrixLabel1(outCallMatrix, medVoList, MedicalInfoVO::getStringOutCallStatus);
//        countStrategy.groupByMatrixLabel2(outCallMatrix, medVoList, MedicalInfoVO::getConnectStatus);
//        matrixVOList.add(outCallMatrix);
//
//        MatrixVO smsMatrix = new MatrixVO();
//        smsMatrix.setTitleName(NoteTypeEnum.SMS.getDesc());
//        countStrategy.groupByMatrixLabel1(smsMatrix, medVoList, MedicalInfoVO::getSmsSent);
//        countStrategy.groupByMatrixLabel2(smsMatrix, medVoList, MedicalInfoVO::getSmsSent);
//        matrixVOList.add(smsMatrix);
//
//        MatrixVO zyzlMatrix = new MatrixVO();
//        zyzlMatrix.setTitleName(NoteTypeEnum.ZYZL.getDesc());
//        countStrategy.groupByMatrixLabel1(zyzlMatrix, medVoList, MedicalInfoVO::getZyzlStatus);
//        countStrategy.groupByMatrixLabel2(zyzlMatrix, medVoList, MedicalInfoVO::getZyzlResult);
//        matrixVOList.add(zyzlMatrix);
//        return matrixVOList;
//    }

    /**
     * 由病历列表分析医生病历分布
     *
     * @param medList 病历列表
     * @return 分布
     */
    private List<DocMedDistribution> getDoctorDataList(List<MedicalInfoVO> medList) {

        String dataSourceListEnum = String.valueOf(DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getValue());
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setMinimumFractionDigits(2);
        //直接根据医生id分组
        Map<String, List<MedicalInfoVO>> map = medList.stream()
                .filter(medicalInfoVO -> !dataSourceListEnum.equals(medicalInfoVO.getDataSourceList()))
                .filter(medicalInfoVO -> org.apache.commons.lang3.StringUtils.isNoneBlank(medicalInfoVO.getDoctorId()))
                .collect(Collectors.groupingBy(MedicalInfoVO::getDoctorId));

        MutableInt count = new MutableInt(0);
        for (List<MedicalInfoVO> list : map.values()) {
            count.add(list.size());
        }

        return map.values().stream().map(value -> {
            DocMedDistribution dmd = new DocMedDistribution();
            dmd.setDoctorName(value.get(0).getDoctorName());
            dmd.setHospitalName(value.get(0).getOrgName());
            dmd.setMedicalNum(value.size());
            dmd.setRate(numberFormat.format(dmd.getMedicalNum().doubleValue() / count.intValue()));
            return dmd;
        }).sorted(Comparator.comparing(DocMedDistribution::getMedicalNum).reversed()).collect(Collectors.toList());
    }


    /**
     * 获取 周边机构病历数 以及 周边病历的位置信息
     */
    private void setSurroundOrgVOList(EventListVO eventListVO, boolean isDesensitization, EventVO eventVO) {
        String symptomType = eventVO.getSymptomType();

        // 获取配置表中配置的周边机构的距离
        Double distance;
        CdcConfig cdcConfig = tbCdcConfigMapper.findByConfigKey(CommonConstants.SURROUND_ORG_DISTANCE);
        if (cdcConfig != null) {
            distance = cdcConfig.getConfigValue();
        } else {
            throw new MedicalBusinessException("11457001", "周边机构距离未配置");
        }
        eventListVO.setDistance(distance);

        // 获取统计日期
        String today;
        Date date = eventVO.getEventEndDate();
        if (date == null) {
            today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.now().minus(0, ChronoUnit.DAYS));
        } else {
            today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));
        }

        // 获取信号机构的原始坐标
        String statDimId = eventVO.getStatDimId();
        List<TbCdcewOrganizationInfo> eventOrgList;
        if (EventTypeEnum.JDQY.getName().equals(eventVO.getEventType())) {
            eventOrgList = tbCdcOrganizationInfoMapper.findStreetListBySourceType(Arrays.asList(statDimId), SourceTypeEnum.getSourceTypeList());
        } else {
            eventOrgList = tbCdcOrganizationInfoMapper.findByOrgListBySourceType(Arrays.asList(statDimId), SourceTypeEnum.getSourceTypeList());
        }

        Double latitude;
        Double longitude;
        String sourceType = null;
        List<String> sourceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(eventOrgList)) {
            TbCdcewOrganizationInfo eventOrg = eventOrgList.get(0);
            latitude = eventOrg.getOrgLatitude();
            longitude = eventOrg.getOrgLongitude();
            sourceType = eventOrg.getSourceType();
        } else {
            longitude = eventVO.getLongitude();
            latitude = eventVO.getLatitude();
        }
        //当sourceType为空时，代表街道或者单位信号，此时自定义预警信号取周边的等级医院以及基层医疗作为周边机构
        if(StringUtils.isBlank(sourceType)){
            sourceList.add(SourceTypeEnum.HOSPITAL.getCode());
            sourceList.add(SourceTypeEnum.PRIMARY_CARE.getCode());
        }else {
            sourceList.add(sourceType);
        }

        // 生成周边机构病历数结果
        List<SurroundOrgVO> surroundOrgVOList = new ArrayList<>();

        log.info(String.format("获取周边机构开始: latitude : %s, longitude: %s, distance: %s, sourceList: %s",
                latitude, longitude, distance, String.join(",", sourceList)));
        List<TbCdcewOrganizationInfo> surroundStatOrgList = orgService.findStatOrgByDistanceAndSourceList(latitude, longitude, distance, sourceList);
        log.info("获取周边机构结束: " + surroundStatOrgList.size());
        try {
            log.info("周边机构表：" + ObjectMapperUtil.getInstance().writeValueAsString(surroundOrgVOList));
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
        }
        log.info("开始查询target表");
        List<String> surroundStatOrgIds = surroundStatOrgList.stream().map(TbCdcewOrganizationInfo::getStatOrgId).collect(Collectors.toList());
        List<OrgMedCount> tmpTodayList = tbCdcewHisMedicalInfoMapper.orgMedCountByIds(DateFormatUtils.formatDate(today), symptomType, surroundStatOrgIds);
        Map<String, OrgMedCount> todayOrgCountMap = tmpTodayList.stream().collect(Collectors.toMap(OrgMedCount::getHospitalSourceKey, Function.identity(), (t1, t2) -> t1));
        log.info("结束查询target表,size:" + tmpTodayList.size());

        for (TbCdcewOrganizationInfo surroundStatOrg : surroundStatOrgList) {
            String statOrgId = surroundStatOrg.getStatOrgId();
            if (!Objects.equals(statDimId, statOrgId)) {
                int medCount = Optional.ofNullable(todayOrgCountMap.get(statOrgId)).map(OrgMedCount::getCount).orElse(0);
                SurroundOrgVO surroundOrgVO = new SurroundOrgVO();
                surroundOrgVO.setStatDimId(statOrgId);
                surroundOrgVO.setStatDimName(surroundStatOrg.getStatOrgName());
                surroundOrgVO.setLatitude(surroundStatOrg.getOrgLatitude());
                surroundOrgVO.setLongitude(surroundStatOrg.getOrgLongitude());
                surroundOrgVO.setCount(medCount);
                surroundOrgVOList.add(surroundOrgVO);
            }
        }
        log.info("结束处理周边机构: surroundOrgVOList.size " + surroundOrgVOList.size());

        if (isDesensitization) {
            surroundOrgVOList.forEach(DesensitizeVOUtils::desensitizeSurroundOrgVO);
        }
        eventListVO.setSurroundOrgVOList(surroundOrgVOList);


        List<SurroundMedAddressVO> surroundMedAddressVOList = new ArrayList<>();
        // 周边机构病历打点
        log.info("开始处理周边机构病历");
        if (!CollectionUtils.isEmpty(surroundOrgVOList)) {
            List<String> orgIdList = surroundOrgVOList.stream().map(SurroundOrgVO::getStatDimId).collect(Collectors.toList());

            log.info("开始调analysis接口,获取病历结果");
            List<MappingResult> mappingResultList = restService.getMappingResult(today, symptomType, orgIdList);
            log.info("结束调analysis接口,获取病历结果完成，size:" + mappingResultList.size());

            if (!CollectionUtils.isEmpty(mappingResultList)) {

                mappingResultList.forEach(mappingResult -> {
                    SurroundMedAddressVO surroundMedAddressVO = new SurroundMedAddressVO();
                    surroundMedAddressVO.setSourceKey(mappingResult.getMedicalId());
                    surroundMedAddressVO.setAddressName(mappingResult.getLivingAddress());
                    surroundMedAddressVO.setLatitude(mappingResult.getLivingAddressLatitude());
                    surroundMedAddressVO.setLongitude(mappingResult.getLivingAddressLongitude());
                    surroundMedAddressVO.setPatientId(mappingResult.getPatientId());
                    surroundMedAddressVOList.add(surroundMedAddressVO);
                });
            }
        }
        log.info("结束处理周边机构病历");

        eventListVO.setSurroundMedAddressVOList(surroundMedAddressVOList);
    }

//    public static void main(String[] args) {
//        Date date = new Date();
//        System.out.println(DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN).withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS)));
//    }

    @Override
    public EventVO buildEventVo(String eventId) {
        CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(eventId);
        if (Objects.isNull(event)) {
            log.error("事件ID：{}不存在！", eventId);
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }
        EventVO eventVO = EventVO.fromEntity(event);
        eventVO.setMedCount(tbCdcewHisMedicalInfoMapper.countSourceKeyByEventIds(Collections.singletonList(eventId)));
        return eventVO;
    }

    @Override
    public void setMedicalCountInfo(EventVO eventVO) {
        // 取最大时间
        Date date = (eventVO.getEventEndDate() == null) ? (new Date()) : (eventVO.getEventEndDate());

        String yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
        String today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));

        Date yesterdayDate = DateFormatUtils.formatDate(yesterday);
        Date todayDate = DateFormatUtils.formatDate(today);

        eventVO.setUntil(todayDate);

        List<MedicalInfoVO> medicalInfoVOList = (List<MedicalInfoVO>) eventVO.getMedicalInfoVOList();

        List<MedicalInfoVO> yesterdayMedicalInfoList = medicalInfoVOList.stream().filter(medicalInfoVO -> yesterdayDate.equals(medicalInfoVO.getFullDate())).collect(Collectors.toList());
        List<MedicalInfoVO> todayMedicalInfoList = medicalInfoVOList.stream().filter(medicalInfoVO -> todayDate.equals(medicalInfoVO.getFullDate())).collect(Collectors.toList());


        eventVO.setMedCount(medicalInfoVOList.size());
        eventVO.setTodayMedicalCaseCn(todayMedicalInfoList.size());
        eventVO.setPreMedicalCaseCn(yesterdayMedicalInfoList.size());

        setMedicalTrend(eventVO);
    }

    @Override
    public void setMedicalCountInfoByTarget(EventVO eventVO) {

        // 取最大时间
        Date date = eventVO.getEventEndDate() == null ? new Date() : eventVO.getEventEndDate();

        String yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
        String today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));

        SimpleDateFormat sdf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        try {
            eventVO.setUntil(sdf.parse(today));
        } catch (ParseException e) {
            log.error("日期转换错误:{}", today);
        }

        TbCdcEwmaTarget yesterdayTarget = tbCdcEwmaTargetMapper.findBySymptomStatDimIdFullDate(eventVO.getSymptomType(), eventVO.getStatDimId(), DateFormatUtils.formatDate(yesterday));
        TbCdcEwmaTarget todayTarget = tbCdcEwmaTargetMapper.findBySymptomStatDimIdFullDate(eventVO.getSymptomType(), eventVO.getStatDimId(), DateFormatUtils.formatDate(today));

        // 病历总数
        Integer countByEventId = medRelationMapper.getCountByEventId(eventVO.getId());
        eventVO.setMedCount(countByEventId);
        eventVO.setTodayMedicalCaseCn(Optional.ofNullable(todayTarget).map(TbCdcEwmaTarget::getMedicalCaseCn).orElse(0));
        eventVO.setPreMedicalCaseCn(Optional.ofNullable(yesterdayTarget).map(TbCdcEwmaTarget::getMedicalCaseCn).orElse(0));


        setMedicalTrend(eventVO);
    }

    @Override
    public void setInfectedMedicalCountInfo(EventVO eventVO) {

        // 取最大时间
        Date date = eventVO.getEventEndDate() == null ? new Date() : eventVO.getEventEndDate();
        String yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
        String today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));

        Date yesterdayDate = DateFormatUtils.formatDate(yesterday);
        Date todayDate = DateFormatUtils.formatDate(today);

        eventVO.setUntil(todayDate);

        List<InfectedPatientVO> medicalInfoVOList = (List<InfectedPatientVO>) eventVO.getMedicalInfoVOList();

        List<InfectedPatientVO> yesterdayMedicalInfoList = medicalInfoVOList.stream().filter(medicalInfoVO -> yesterdayDate.equals(medicalInfoVO.getFullDate())).collect(Collectors.toList());
        List<InfectedPatientVO> todayMedicalInfoList = medicalInfoVOList.stream().filter(medicalInfoVO -> todayDate.equals(medicalInfoVO.getFullDate())).collect(Collectors.toList());


        eventVO.setMedCount(medicalInfoVOList.size());
        eventVO.setTodayMedicalCaseCn(todayMedicalInfoList.size());
        eventVO.setPreMedicalCaseCn(yesterdayMedicalInfoList.size());

        setMedicalTrend(eventVO);
    }

    @Override
    public List<CascadeVO> getSyndromeList(String loginUserId) {
        List<TbCdcewSyndrome> syndromeList = syndromeMapper.listAll();
        List<CascadeVO> retList = syndromeList.stream()
                .collect(Collectors.toMap(TbCdcewSyndrome::getSyndromeCode, e -> e, (k1, k2) -> k1))
                .values().stream().map(e -> {
                    CascadeVO vo = new CascadeVO();
                    vo.setLabel(e.getSyndromeName());
                    vo.setValue(e.getSyndromeCode());
                    return vo;
                }).collect(Collectors.toList());
        List<String> diseaseCodesByAuth = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.SYNDROME.getType());
        List<CascadeVO> diseaseList = retList.stream().filter(cascadeVO -> diseaseCodesByAuth.contains(cascadeVO.getValue())).collect(Collectors.toList());
        return (CollectionUtils.isEmpty(diseaseCodesByAuth)) ? retList : diseaseList;
    }

    @Override
    public List<CascadeVO> getSyndromeList() {
        List<TbCdcewSyndrome> syndromeList = syndromeMapper.listAll();
        return syndromeList.stream()
                .collect(Collectors.toMap(TbCdcewSyndrome::getSyndromeCode, e -> e, (k1, k2) -> k1))
                .values().stream().map(e -> {
                    CascadeVO vo = new CascadeVO();
                    vo.setLabel(e.getSyndromeName());
                    vo.setValue(e.getSyndromeCode());
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 设置是否地域聚集性
     */
    public void setEventIsClusterInfo(EventVO eventVO) {
        List<TbCdcWarningDetail> cdcWarningDetails = tbCdcWarningDetailMapper.findByEventId(eventVO.getId());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(cdcWarningDetails)) {
            Optional<TbCdcWarningDetail> maxTbCdcWarningDetail = cdcWarningDetails.stream().max(Comparator.comparing(TbCdcWarningDetail::getFullDate));
            if (maxTbCdcWarningDetail.isPresent()) {
                TbCdcWarningDetail tbCdcWarningDetail = maxTbCdcWarningDetail.get();
                eventVO.setIsCluster(tbCdcWarningDetail.getIsSpatialCluster());
            }
        }
    }

    @Override
    public void setMedicalInfoList(EventVO eventVO) {

        List<String> list = Collections.singletonList(eventVO.getId());
        List<MedicalInfoVO> result = tbCdcewHisMedicalInfoMapper.findRecordsByEventIds(list);

        // 如果发起过流调则用“、”进行分割；否则使用“|”进行分割
        final String separator = "\\|";

        result.forEach(medicalInfoVO -> {

            // 处理 病历是否真实 结果
            // 有智医助理返回结果，优先以智医助理结果为准; 若无智医助理结果，则以外呼结果为准
            // 未接通、失败 --> 未反馈
            // 接通+情况属实 --> 是    接通+情况不实 --> 否  接通+情况不明 --> 不明

            if (CallResultEnum.TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.FALSE.getCode());
            }
            if (CallResultEnum.NOT_TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.TRUE.getCode());
            }
            if (CallResultEnum.UNKNOWN.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.UNKNOWN.getCode());
            }


            // 处理症状信息  将|或、隔开  变成去重之后 用,隔开
            String symptomType = medicalInfoVO.getSymptomType();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isEmpty(symptomType)) {
                String[] split = symptomType.split(separator);
                symptomType = com.iflytek.fpva.cdc.util.StringUtils.join(",", Arrays.stream(split).distinct().toArray(String[]::new));
                medicalInfoVO.setSymptomType(symptomType);
            }

            // 处理发病时间  主诉症状与发病时间一一对应
            String mainSuitSymptomContent = medicalInfoVO.getMainSuitSymptomContent();
            String onsettimecontent = medicalInfoVO.getOnsettimecontent();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuitSymptomContent) && !com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsettimecontent)) {
                String[] mainSuitSplits = mainSuitSymptomContent.split("\\|");
                String[] onsetTimeSplits = onsettimecontent.split("\\|");

                String onsetDate = "";
                for (int i = 0; i < mainSuitSplits.length; i++) {
                    String mainSuit = mainSuitSplits[i];
                    if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuit)) {
                        if (i < (onsetTimeSplits).length) {
                            String onsetTime = onsetTimeSplits[i];
                            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetTime)) {
                                onsetDate = onsetDate.concat(mainSuit).concat(onsetTime).concat(",");
                            }
                        }
                    }
                }
                // 去除最后一个","号
                if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetDate)) {
                    onsetDate = onsetDate.substring(0, onsetDate.length() - 1);
                }
                medicalInfoVO.setOnsetDate(onsetDate);
            }
        });

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        result.forEach(item -> {
            String fullDate = item.getFullDate() == null ? null : sdf.format(item.getFullDate());
            if (now.equals(fullDate)
                    && CommonConstants.IS_NEW_MED_RECORD_YES == Optional.ofNullable(item.getIsNewMedRecord()).orElse(CommonConstants.IS_NEW_MED_RECORD_NO)) {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_YES);
            } else {
                item.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_NO);
            }
        });

        eventVO.setMedicalInfoVOList(result);
    }

    private static void getEnhanceCondition(List<EnhanceCondition> enhanceConditionResultList, MedicalInfoVO medicalInfoVO) {
        Map<String, String> enhanceConditionMap = enhanceConditionResultList.stream().collect(Collectors.toMap(EnhanceCondition::getConditionCode, EnhanceCondition::getConditionName));
        List<String> enhanceConditionList = Arrays.stream(medicalInfoVO.getEnhanceConditionList().split("\\|")).collect(Collectors.toList());
        StringBuffer sb = new StringBuffer();
        enhanceConditionList.forEach(e -> {
            if (sb.length() > 0) {
                sb.append("|");
            }
            sb.append(enhanceConditionMap.getOrDefault(e, "--"));
        });
        medicalInfoVO.setEnhanceConditionList(sb.toString());
    }

    @Override
    public List<EventInfoVO> listMedical(String sourceKey, String eventId, String loginUserId, Integer sortType) {
        List<EventInfoVO> resultList = Lists.newArrayList();
        MedicalInfoVO medicalInfo = tbCdcewHisMedicalInfoMapper.findMedicalInfoBySourceKey(sourceKey);
        EventInfoVO eventInfoVOS = BeanUtils.copyProperties(medicalInfo, EventInfoVO.class);
        eventInfoVOS.setEventId(eventId);
        resultList.add(eventInfoVOS);
        return resultList;
    }

    @Override
    public PageData<MedicalInfoVO> getMedicalInfoList(String eventId, String loginUserId, Integer pageIndex, Integer pageSize, Integer sortType
            , String doctorName, String companyName, String startTime, String endTime, String diagnoseName) {
        EventVO eventVO = buildEventVo(eventId);
        List<String> list = Collections.singletonList(eventVO.getId());

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime = null;
        Date formatEndTime = null;
        if (!org.springframework.util.StringUtils.isEmpty(startTime) && !org.springframework.util.StringUtils.isEmpty(endTime)){
            try {
                formatStartTime = simpleDateFormat.parse(startTime);
                formatEndTime = simpleDateFormat.parse(endTime);
            } catch (ParseException e) {
                throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
            }
        }

        //PageMethod.startPage(pageIndex, pageSize);
        List<MedicalInfoVO> result = tbCdcewHisMedicalInfoMapper.findRecordsByEventIdsSort(list, sortType, doctorName, companyName,formatStartTime,formatEndTime,diagnoseName);
        // 使用“|”进行分割
        final String separator = "\\|";
        List<EnhanceCondition> enhanceConditionResultList = tbCdcewHisMedicalInfoMapper.getEnhanceCondition();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String now = sdf.format(new Date());
        result.forEach(medicalInfoVO -> {

            // 处理 病历是否真实 结果
            // 有智医助理返回结果，优先以智医助理结果为准; 若无智医助理结果，则以外呼结果为准
            // 未接通、失败 --> 未反馈
            // 接通+情况属实 --> 是    接通+情况不实 --> 否  接通+情况不明 --> 不明

            if (CallResultEnum.TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.FALSE.getCode());
            }
            if (CallResultEnum.NOT_TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.TRUE.getCode());
            }
            if (CallResultEnum.UNKNOWN.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.UNKNOWN.getCode());
            }


            // 处理症状信息  将|或、隔开  变成去重之后 用,隔开
            String symptomType = medicalInfoVO.getSymptomType();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isEmpty(symptomType)) {
                String[] split = symptomType.split(separator);
                List<String> strings = Arrays.stream(split).distinct().collect(Collectors.toList());
                symptomType = com.iflytek.fpva.cdc.util.StringUtils.join(",", strings.toArray(new String[strings.size()]));
                medicalInfoVO.setSymptomType(symptomType);
            }

            // 处理发病时间  主诉症状与发病时间一一对应
            String mainSuitSymptomContent = medicalInfoVO.getMainSuitSymptomContent();
            String onsettimecontent = medicalInfoVO.getOnsettimecontent();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuitSymptomContent) && !com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsettimecontent)) {
                String[] mainSuitSplits = mainSuitSymptomContent.split("\\|");
                String[] onsetTimeSplits = onsettimecontent.split("\\|");

                String onsetDate = "";
                for (int i = 0; i < mainSuitSplits.length; i++) {
                    String mainSuit = mainSuitSplits[i];
                    if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuit)) {
                        if (i < (onsetTimeSplits).length) {
                            String onsetTime = onsetTimeSplits[i];
                            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetTime)) {
                                onsetDate = onsetDate.concat(mainSuit).concat(onsetTime).concat(",");
                            }
                        }
                    }
                }
                // 去除最后一个","号
                if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetDate)) {
                    onsetDate = onsetDate.substring(0, onsetDate.length() - 1);
                }
                medicalInfoVO.setOnsetDate(onsetDate);
            }
            //判断是否是新数据
            String fullDate = medicalInfoVO.getFullDate() == null ? null : sdf.format(medicalInfoVO.getFullDate());
            if (now.equals(fullDate)
                    && CommonConstants.IS_NEW_MED_RECORD_YES == Optional.ofNullable(medicalInfoVO.getIsNewMedRecord()).orElse(CommonConstants.IS_NEW_MED_RECORD_NO)) {
                medicalInfoVO.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_YES);
            } else {
                medicalInfoVO.setIsNewMedRecord(CommonConstants.IS_NEW_MED_RECORD_NO);
            }
            //处理病历来源
            medicalInfoVO.setSourceType(DataSourceTypeEnum.getDescByValue(Integer.parseInt(medicalInfoVO.getSourceType())));
            if("1".equals(medicalInfoVO.getIsRegister())){
                medicalInfoVO.setSourceType(MedDistributionEnum.REGISTREPORTING.getDesc());
            }

            //处理增强条件
            getEnhanceCondition(enhanceConditionResultList, medicalInfoVO);

        });

//        if (tbCdcConfigService.isDesensitization(loginUserId)) {
//            result.forEach(DesensitizeVOUtils::desensitizeMedicalInfoVo);
//        }
        result = PersonInfoMergeProcessor.<MedicalInfoVO, MedicalInfoVO>builder()
                .inputValues(result)
                .eventIdFunction(MedicalInfoVO::getOriginalEventId)
                .nameFunction(MedicalInfoVO::getPatientName)
                .idNumberFunction(MedicalInfoVO::getIdentityNo)
                .orgIdFunction(MedicalInfoVO::getOrgId)
                .patientIdFunction(MedicalInfoVO::getGlobalPersonId)
                .convertFunction(MedicalInfoVO::copy)
                .build().mergeAndConvert();
        return PageData.of(result, pageIndex, pageSize);
    }

    @Override
    public byte[] exportMedicalListNew(String eventId, String loginUserId, Integer sortType, String doctorName, String companyName, String formType) {

        EventVO eventVO = buildEventVo(eventId);
        List<String> list = Collections.singletonList(eventVO.getId());

        List<MedicalInfoVO> result = tbCdcewHisMedicalInfoMapper.findRecordsByEventIdsSort(list, sortType, doctorName, companyName,null,null, null);

        //校验是否超出文件导出最大值
        restService.checkExportMax(result);
        // 使用“|”进行分割
        final String separator = "\\|";
        List<EnhanceCondition> enhanceConditionResultList = tbCdcewHisMedicalInfoMapper.getEnhanceCondition();
        result.forEach(medicalInfoVO -> {

            // 处理 病历是否真实 结果
            // 有智医助理返回结果，优先以智医助理结果为准; 若无智医助理结果，则以外呼结果为准
            // 未接通、失败 --> 未反馈
            // 接通+情况属实 --> 是    接通+情况不实 --> 否  接通+情况不明 --> 不明

            if (CallResultEnum.TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.FALSE.getCode());
            }
            if (CallResultEnum.NOT_TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.TRUE.getCode());
            }
            if (CallResultEnum.UNKNOWN.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.UNKNOWN.getCode());
            }

            // 处理症状信息  将|或、隔开  变成去重之后 用,隔开
            String symptomType = medicalInfoVO.getSymptomType();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isEmpty(symptomType)) {
                String[] split = symptomType.split(separator);
                List<String> strings = Arrays.stream(split).distinct().collect(Collectors.toList());
                symptomType = com.iflytek.fpva.cdc.util.StringUtils.join(",", strings.toArray(new String[strings.size()]));
                medicalInfoVO.setSymptomType(symptomType);
            }

            // 处理发病时间  主诉症状与发病时间一一对应
            String mainSuitSymptomContent = medicalInfoVO.getMainSuitSymptomContent();
            String onsettimecontent = medicalInfoVO.getOnsettimecontent();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuitSymptomContent) && !com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsettimecontent)) {
                String[] mainSuitSplits = mainSuitSymptomContent.split("\\|");
                String[] onsetTimeSplits = onsettimecontent.split("\\|");

                String onsetDate = "";
                for (int i = 0; i < mainSuitSplits.length; i++) {
                    String mainSuit = mainSuitSplits[i];
                    if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuit)) {
                        if (i < (onsetTimeSplits).length) {
                            String onsetTime = onsetTimeSplits[i];
                            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetTime)) {
                                onsetDate = onsetDate.concat(mainSuit).concat(onsetTime).concat(",");
                            }
                        }
                    }
                }
                // 去除最后一个","号
                if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetDate)) {
                    onsetDate = onsetDate.substring(0, onsetDate.length() - 1);
                }
                medicalInfoVO.setOnsetDate(onsetDate);
            }
            //处理病历来源
            medicalInfoVO.setSourceType(DataSourceTypeEnum.getDescByValue(Integer.parseInt(medicalInfoVO.getSourceType())));
            //处理增强条件
            getEnhanceCondition(enhanceConditionResultList, medicalInfoVO);
        });

//        if (tbCdcConfigService.isDesensitization(loginUserId)) {
//            result.forEach(DesensitizeVOUtils::desensitizeMedicalInfoVo);
//        }
        boolean isDesensitized = threadLocal.get().getNormalFlag();
        boolean isDiagnoseRole = threadLocal.get().getDiagnoseVO().getDiagnoseFlag();

        for (MedicalInfoVO r : result) {
            if (isDesensitized){
                DesensitizedUtils.desensitizedObject(r);
            }
            if (isDiagnoseRole){
                DesensitizedUtils.desensitizedDiagnoseForVO(r,threadLocal.get().getDiagnoseVO().getDiagnoseNames());
            }
        }

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");


        makeExcelColumn(loginUserId, result, sheet, formType);
        ByteArrayOutputStream outputStream = generateOutputStream(workbook);

        return outputStream.toByteArray();

    }

    private void makeExcelColumn(String loginUserId, List<MedicalInfoVO> result, XSSFSheet sheet, String formType) {
        TbCdcewFormConfig formConfig = formConfigService.getConfigByLoginUserId(loginUserId, formType);
        Row firstRow = sheet.createRow(0);
        if (formConfig != null) {
            int index = 0;
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "姓名", index++);
            String queryString = formConfig.getConfigDesc();
            List<String> queryStringList = Arrays.asList(queryString.split(","));

            index = getIndex(queryStringList, "sexDesc");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "性别", index);
            }
            index = getIndex(queryStringList, "diagnoseAge");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "年龄", index);
            }
            index = getIndex(queryStringList, "livingAddress");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "现住址", index);
            }
            index = getIndex(queryStringList, "mainSuit");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "主诉", index);
            }
            index = getIndex(queryStringList, "keySymptomTagList");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "关键症状", index);
            }
            index = getIndex(queryStringList, "diagnoseName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "诊断", index);
            }
            index = getIndex(queryStringList, "companyName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "工作单位/学校", index);
            }
            index = getIndex(queryStringList, "sourceType");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "病例来源", index);
            }
            index = getIndex(queryStringList, "outPatientTime");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "就诊时间", index);
            }
            index = getIndex(queryStringList, "enhanceConditionList");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "增强条件", index);
            }
            index = getIndex(queryStringList, "doctorName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "接诊医生", index);
            }
            index = getIndex(queryStringList, "illnessHistory");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "现病史", index);
            }
            index = getIndex(queryStringList, "previousHistory");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "既往史", index);
            }
            index = getIndex(queryStringList, "checkupOther");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "体格检查", index);
            }
            index = getIndex(queryStringList, "auxExam");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "辅助检查", index);
            }
            index = getIndex(queryStringList, "fullDate");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "病例入库时间", index);
            }
            index = getIndex(queryStringList, "careerName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "职业", index);
            }
            index = getIndex(queryStringList, "visitOrgName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "就诊机构", index);
            }
            index = getIndex(queryStringList, "medAuthenticity");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "病例是否真实", index);
            }
            index = getIndex(queryStringList, "zyzlResult");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "传染病、食源性疾病等风险", index);
            }
            index = getIndex(queryStringList, "isCluster");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "是否聚集性", index);
            }
            for (int i = 0; i < result.size(); i++) {
                index = 0;
                MedicalInfoVO vo = result.get(i);
                Row row = sheet.createRow(i + 1);
                createCellAndSetValue(row, vo.getPatientName(), index);
                index = getIndex(queryStringList, "sexDesc");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getSexDesc(), index);
                }
                index = getIndex(queryStringList, "diagnoseAge");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getDiagnoseAge() + vo.getDiagnoseAgeUnit(), index);
                }
                index = getIndex(queryStringList, "livingAddress");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getLivingAddress(), index);
                }
                index = getIndex(queryStringList, "mainSuit");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getMainSuit(), index);
                }
                index = getIndex(queryStringList, "keySymptomTagList");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getKeySymptomTagList(), index);
                }
                index = getIndex(queryStringList, "diagnoseName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getDiagnoseName(), index);
                }
                index = getIndex(queryStringList, "companyName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getCompanyName(), index);
                }
                index = getIndex(queryStringList, "sourceType");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getSourceType(), index);
                }
                index = getIndex(queryStringList, "outPatientTime");
                if (index > 0) {
                    createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getOutPatientTime(), com.iflytek.fpva.cdc.util.DateUtils.DATE_FORMAT), index);
                }
                index = getIndex(queryStringList, "enhanceConditionList");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getEnhanceConditionList(), index);
                }
                index = getIndex(queryStringList, "doctorName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getDoctorName(), index);
                }
                index = getIndex(queryStringList, "illnessHistory");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getIllnessHistory(), index);
                }
                index = getIndex(queryStringList, "previousHistory");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getPreviousHistory(), index);
                }
                index = getIndex(queryStringList, "checkupOther");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getCheckupOther(), index);
                }
                index = getIndex(queryStringList, "auxExam");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getAuxExam(), index);
                }
                index = getIndex(queryStringList, "fullDate");
                if (index > 0) {
                    createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getFullDate()), index);
                }
                index = getIndex(queryStringList, "careerName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getCareerName(), index);
                }
                index = getIndex(queryStringList, "visitOrgName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getVisitOrgName(), index);
                }
                index = getIndex(queryStringList, "medAuthenticity");
                if (index > 0) {
                    createCellAndSetValue(row, checkResult(vo.getMedAuthenticity()), index);
                }
                index = getIndex(queryStringList, "zyzlResult");
                if (index > 0) {
                    createCellAndSetValue(row, checkResult(vo.getZyzlResult()), index);
                }
                index = getIndex(queryStringList, "isCluster");
                if (index > 0) {
                    createCellAndSetValue(row, checkResult(vo.getIsCluster()), index);
                }
            }
        } else {
            int index = 0;
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "姓名", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "性别", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "年龄", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "现住址", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "主诉", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "关键症状", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "诊断", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "工作单位/学校", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "病例来源", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "就诊时间", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "增强条件", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "接诊医生", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "现病史", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "既往史", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "体格检查", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "辅助检查", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "病例入库时间", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "职业", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "病例是否真实", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "传染病、食源性疾病等风险", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "是否聚集性", index++);
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "就诊机构", index++);
            for (int i = 0; i < result.size(); i++) {
                index = 0;
                MedicalInfoVO vo = result.get(i);
                Row row = sheet.createRow(i + 1);
                createCellAndSetValue(row, vo.getPatientName(), index++);
                createCellAndSetValue(row, vo.getSexDesc(), index++);
                createCellAndSetValue(row, vo.getDiagnoseAge() + vo.getDiagnoseAgeUnit(), index++);
                createCellAndSetValue(row, vo.getLivingAddress(), index++);
                createCellAndSetValue(row, vo.getMainSuit(), index++);
                createCellAndSetValue(row, vo.getKeySymptomTagList(), index++);
                createCellAndSetValue(row, vo.getDiagnoseName(), index++);
                createCellAndSetValue(row, vo.getCompanyName(), index++);
                createCellAndSetValue(row, vo.getSourceType(), index++);
                createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getOutPatientTime(), com.iflytek.fpva.cdc.util.DateUtils.DATE_FORMAT), index++);
                createCellAndSetValue(row, vo.getEnhanceConditionList(), index++);
                createCellAndSetValue(row, vo.getDoctorName(), index++);
                createCellAndSetValue(row, vo.getIllnessHistory(), index++);
                createCellAndSetValue(row, vo.getPreviousHistory(), index++);
                createCellAndSetValue(row, vo.getCheckupOther(), index++);
                createCellAndSetValue(row, vo.getAuxExam(), index++);
                createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getFullDate()), index++);
                createCellAndSetValue(row, vo.getCareerName(), index++);
                createCellAndSetValue(row, checkResult(vo.getMedAuthenticity()), index++);
                createCellAndSetValue(row, checkResult(vo.getZyzlResult()), index++);
                createCellAndSetValue(row, checkResult(vo.getIsCluster()), index++);
                createCellAndSetValue(row, vo.getVisitOrgName(), index++);
            }
        }
    }

    private int getIndex(List<String> stringList, String queryString) {
        return stringList.indexOf(queryString) + 1;
    }

    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        cell.setCellValue(value);
    }

    private String checkResult(Integer result) {
        if (result == null) {
            return "-";
        } else if (0 == result) {
            return "否";
        } else {
            return "是";
        }
    }

    private ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11451108", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    // 给eventVO所属的卫生院设置是否有发热门诊的标签 和 经纬度
    public void setOrgInfo(EventVO eventVO) {
        if (EventTypeEnum.JDQY.getName().equals(eventVO.getEventType())) {
            TbCdcewRegion tbcdcewRegion = tbCdcewRegionMapper.findById(eventVO.getStatDimId());
            if (Objects.nonNull(tbcdcewRegion)) {
                eventVO.setLongitude(tbcdcewRegion.getRegionLongitude());
                eventVO.setLatitude(tbcdcewRegion.getRegionLatitude());
                eventVO.setMapDistrictCode(tbcdcewRegion.getStandardDistrictCode());
                eventVO.setMapDistrictName(tbcdcewRegion.getStandardDistrictName());
                eventVO.setFeverClinicFlag(0);
            }
            return;
        }

        List<TbCdcewOrganizationInfo> tbCdcewOrganizationInfoList = tbCdcOrganizationInfoMapper.findByOrgListBySourceType(Arrays.asList(eventVO.getStatDimId()), SourceTypeEnum.getSourceTypeList());
        if (!CollectionUtils.isEmpty(tbCdcewOrganizationInfoList)) {
            TbCdcewOrganizationInfo tbCdcewOrganizationInfo = tbCdcewOrganizationInfoList.get(0);
            eventVO.setFeverClinicFlag(tbCdcewOrganizationInfo.getFeverClinicFlag());
            eventVO.setLongitude(tbCdcewOrganizationInfo.getOrgLongitude());
            eventVO.setLatitude(tbCdcewOrganizationInfo.getOrgLatitude());
            eventVO.setMapDistrictCode(tbCdcewOrganizationInfo.getStandardDistrictCode());
            eventVO.setMapDistrictName(tbCdcewOrganizationInfo.getStandardDistrictName());
            eventVO.setHighOrgId(tbCdcewOrganizationInfo.getHigherOrgId());
            eventVO.setHighOrgName(tbCdcewOrganizationInfo.getHigherOrgName());
            eventVO.setOrgSourceType(tbCdcewOrganizationInfo.getSourceType());
        } else {
            //学校在上面机构表里，单位在表:tb_cdcew_company_info
            if (EventTypeEnum.XXDW.getName().equals(eventVO.getEventType())) {
                TbCdcewCompanyInfo tbCdcewCompanyInfo = tbCdcewCompanyInfoMapper.selectByPrimaryKey(eventVO.getStatDimId());
                if (null != tbCdcewCompanyInfo) {
                    eventVO.setLongitude(tbCdcewCompanyInfo.getLongitude());
                    eventVO.setLatitude(tbCdcewCompanyInfo.getLatitude());
                    eventVO.setMapDistrictCode(tbCdcewCompanyInfo.getStandardDistrictCode());
                    eventVO.setMapDistrictName(tbCdcewCompanyInfo.getStandardDistrictName());
                }
            }
            eventVO.setFeverClinicFlag(0);
        }
    }


    private void mergeEsStreetName(List<MedicalInfoVO> list) {
        list.forEach(e -> {
            String[] dataSourceList = Optional.ofNullable(e.getDataSourceList()).orElse("").split("\\|", -1);
            if (ArrayUtils.contains(dataSourceList, DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getStrValue())) {
                if (com.iflytek.fpva.cdc.util.StringUtils.isEmpty(e.getStatDimName())) {
                    e.setStatDimName(DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getDesc());
                } else {
                    e.setStatDimName(e.getStatDimName() + "," + DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getDesc());
                }
            }
        });
    }

    /**
     * 合并流调的关键症状
     *
     * @param list
     */
    private void mergeEsSymptomType(List<MedicalInfoVO> list) {
        list.forEach(e -> {
            String tempSymptomType = com.iflytek.fpva.cdc.util.StringUtils.join("、", e.getSymptomType(), e.getSymptomTypeNew());
            e.setSymptomType(tempSymptomType);
            e.setSymptomTypeNew("");
        });
    }

    // 设置病历增长趋势
    private void setMedicalTrend(EventVO eventVO) {
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        double rate = (eventVO.getTodayMedicalCaseCn() - eventVO.getPreMedicalCaseCn()) / (eventVO.getPreMedicalCaseCn() == 0 ? 1.0 : eventVO.getPreMedicalCaseCn());
        eventVO.setGrowthRate(numberFormat.format(rate));
        if (rate >= 0) {
            eventVO.setMedicalCaseCnTrend(ChangeTrendEnum.RISING.getType());
        } else {
            eventVO.setMedicalCaseCnTrend(ChangeTrendEnum.FALLING.getType());
        }
    }

    @Override
    public List<AgeGroupVO> ageDistribution(String eventId, String loginUserId, AgeQueryDto ageQueryDto) {

        EventVO eventVO = this.buildEventVo(eventId).setMedicalInfoList(ageQueryDto.getDetailId(),loginUserId);
        List<MedicalInfoVO> medVoList = (List<MedicalInfoVO>) eventVO.getMedicalInfoVOList();

        SimpleDateFormat sdf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date endDate = null;
        Date startDate = null;
        if (!org.springframework.util.StringUtils.isEmpty(ageQueryDto.getStartTime()) && !org.springframework.util.StringUtils.isEmpty(ageQueryDto.getEndTime())){
            try {
                endDate = sdf.parse(ageQueryDto.getEndTime());
                startDate = sdf.parse(ageQueryDto.getStartTime());

            } catch (ParseException e) {
                throw new MedicalBusinessException("11452001", "时间格式化错误:/n endTime:" + ageQueryDto.getEndTime() +"/n startTime:"+ageQueryDto.getStartTime() );
            }
            Date finalStartDate = startDate;
            Date finalEndDate = endDate;
            medVoList= medVoList.stream().filter(medicalInfoVO -> medicalInfoVO.getFullDate().compareTo(finalStartDate) > -1 && medicalInfoVO.getFullDate().compareTo(finalEndDate) < 1)
                    .collect(Collectors.toList());
        }


        //用于存储所有病历中患者的年龄
        List<Double> ageList = new ArrayList<>();

        //处理年龄字段可能为空或者年龄字段的字符串可能不为数字的情况，将符合筛选条件的年龄存入ageList列表
        List<String> ageStringList = medVoList.stream().map(MedicalInfoVO::getDiagnoseAge).collect(Collectors.toList());
        AgeGroupVO ageGroupVO = new AgeGroupVO();
        int count = 0;
        for (String s : ageStringList) {
            if (!StringUtils.isNumeric(s) || "".equals(s.trim()) || s == null) {
                count++;
            } else {
                ageList.add(Double.parseDouble(s));
            }
        }
        List<AgeGroupVO> result = new ArrayList<>();
        Integer type = ageQueryDto.getAgeGroupType();
        if (1 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, Arrays.asList(Age.kernelAgeRange));
        }
        if (2 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, Arrays.asList(Age.secondAgeRange));
        }
        if (3 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, ageQueryDto.getAgeGroupList());
        }
        if (count != 0) {
            ageGroupVO.setAgeGroup(UNKOWN_AGE);
            ageGroupVO.setCount(count);
            result.add(ageGroupVO);
        }
        return result;
    }

    @Override
    public String addEventRecord(String eventId, String loginUserId, String loginUserName, String processType, String recordType) {
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);

        //新增一条处理记录
        CdcEventProcessRecord record = new CdcEventProcessRecord();
        String id = String.valueOf(batchUidService.getUid("tb_cdcew_event_process_record"));
        record.setId(id);
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);

        record.setProcessType(processType);
        record.setRecordType(recordType);
        record.setProcessDesc(RecordTypeEnum.getNameByCode(recordType));

        //保存事件处理记录
        cdcEventProcessRecordMapper.insertSelective(record);

        return id;
    }


    @Override
    public PageData<MedicalInfoVO> getPeriodMedicalInfoList(String eventId, String loginUserId, Integer pageIndex, Integer pageSize) {
        EventVO eventVO = buildEventVo(eventId);
        List<String> list = Collections.singletonList(eventVO.getId());

        PageMethod.startPage(pageIndex, pageSize);
        List<MedicalInfoVO> result = tbCdcewHisMedicalInfoMapper.findPeriodRecordsByEventIds(list);
        PageInfo<MedicalInfoVO> pageInfo = new PageInfo<>(result);
        PageData<MedicalInfoVO> pageData = PageData.fromPageInfo(pageInfo);

        // 使用“|”进行分割
        final String separator = "\\|";

        result.forEach(medicalInfoVO -> {

            // 处理 病历是否真实 结果
            // 有智医助理返回结果，优先以智医助理结果为准; 若无智医助理结果，则以外呼结果为准
            // 未接通、失败 --> 未反馈
            // 接通+情况属实 --> 是    接通+情况不实 --> 否  接通+情况不明 --> 不明

            if (CallResultEnum.TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.FALSE.getCode());
            }
            if (CallResultEnum.NOT_TRUE.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.TRUE.getCode());
            }
            if (CallResultEnum.UNKNOWN.getCode().equals(medicalInfoVO.getConfirmedResult())) {
                medicalInfoVO.setMedAuthenticity(MedAuthenticityEnum.UNKNOWN.getCode());
            }


            // 处理症状信息  将|或、隔开  变成去重之后 用,隔开
            String symptomType = medicalInfoVO.getSymptomType();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isEmpty(symptomType)) {
                String[] split = symptomType.split(separator);
                List<String> strings = Arrays.stream(split).distinct().collect(Collectors.toList());
                symptomType = com.iflytek.fpva.cdc.util.StringUtils.join(",", strings.toArray(new String[strings.size()]));
                medicalInfoVO.setSymptomType(symptomType);
            }

            // 处理发病时间  主诉症状与发病时间一一对应
            String mainSuitSymptomContent = medicalInfoVO.getMainSuitSymptomContent();
            String onsettimecontent = medicalInfoVO.getOnsettimecontent();
            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuitSymptomContent) && !com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsettimecontent)) {
                String[] mainSuitSplits = mainSuitSymptomContent.split("\\|");
                String[] onsetTimeSplits = onsettimecontent.split("\\|");

                String onsetDate = "";
                for (int i = 0; i < mainSuitSplits.length; i++) {
                    String mainSuit = mainSuitSplits[i];
                    if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(mainSuit)) {
                        if (i < (onsetTimeSplits).length) {
                            String onsetTime = onsetTimeSplits[i];
                            if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetTime)) {
                                onsetDate = onsetDate.concat(mainSuit).concat(onsetTime).concat(",");
                            }
                        }
                    }
                }
                // 去除最后一个","号
                if (!com.iflytek.fpva.cdc.util.StringUtils.isBlank(onsetDate)) {
                    onsetDate = onsetDate.substring(0, onsetDate.length() - 1);
                }
                medicalInfoVO.setOnsetDate(onsetDate);
            }
        });

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        result.sort(Comparator.comparing(MedicalInfoVO::getSortOutPatientTime).reversed());
        if (tbCdcConfigService.isDesensitization(loginUserId)) {
            result.forEach(DesensitizeVOUtils::desensitizeMedicalInfoVo);
        }

        pageData.setData(result);
        return pageData;
    }

    @Override
    public List<String> getDoctorByEventId(String eventId) {

        return tbCdcewHisMedicalInfoMapper.getDoctorByEventId(eventId);
    }

    @Override
    public List<String> getCompanyByEventId(String eventId) {

        return tbCdcewHisMedicalInfoMapper.getCompanyByEventId(eventId);
    }
}

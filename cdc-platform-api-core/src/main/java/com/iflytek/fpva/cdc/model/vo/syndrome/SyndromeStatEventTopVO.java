package com.iflytek.fpva.cdc.model.vo.syndrome;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SyndromeStatEventTopVO {

    @ApiModelProperty(value = "症候群编码")
    private String syndromeCode;

    @ApiModelProperty(value = "症候群名称")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String syndromeName;

    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "百分百")
    private double percent;

}

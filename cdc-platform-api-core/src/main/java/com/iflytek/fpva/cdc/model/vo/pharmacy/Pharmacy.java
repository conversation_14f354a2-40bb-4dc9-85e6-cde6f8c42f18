package com.iflytek.fpva.cdc.model.vo.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 药店信息
 *
 * <AUTHOR>
 * @date 2022-05-20 16:37
 */
@Data
@ApiModel("药店信息")
public class Pharmacy {

    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private String orgId;
    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String orgName;
    /**
     * 机构地址
     */
    @ApiModelProperty("机构地址")
    private String orgAddress;
    /**
     * 机构类型编码
     */
    @ApiModelProperty("机构类型编码")
    private String orgTypeCode;
    /**
     * 机构类型名称
     */
    @ApiModelProperty("机构类型名称")
    private String orgTypeName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty("统一社会信用代码")
    private String checkCode;
    /**
     * 药店负责人姓名
     */
    @ApiModelProperty("药店负责人姓名")
    private String directorName;
    /**
     * 药店负责人手机号
     */
    @ApiModelProperty("药店负责人手机号")
    private String directorTelephone;
}

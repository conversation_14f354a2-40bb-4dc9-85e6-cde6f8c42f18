package com.iflytek.fpva.cdc.model.vo.symptom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class SymptomEventQueryVO {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    private Date minFullDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    private Date maxFullDate;

    @NotNull
    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("区县编码")
    private String districtCode;

    @ApiModelProperty("症状集合")
    private List<String> symptomList;

    private int pageIndex;

    private int pageSize;
}

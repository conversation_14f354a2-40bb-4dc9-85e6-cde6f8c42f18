package com.iflytek.fpva.cdc.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TbCdcewPoisonMedRelation implements Serializable {

    private String id;

    private Date fullDate;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    private String statDimId;

    private String statDimName;

    private String poisonCode;

    private String poisonName;

    private String sourceKey;

    private String detailId;

    private Date createTime;

    private String eventId;

    private Short dataSourceTypeCode;

    private Short isNewMedRecord;

    private String identityNo;

    private String globalPersonId;

    private String patientName;

    private static final long serialVersionUID = 1L;
}

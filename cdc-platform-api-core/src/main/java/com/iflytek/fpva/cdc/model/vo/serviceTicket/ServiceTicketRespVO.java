package com.iflytek.fpva.cdc.model.vo.serviceTicket;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 工单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel( description="工单表")
public class ServiceTicketRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "工单号")
    private String orderNo;

    @ApiModelProperty(value = "标题名称")
    private String titleName;

    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    @ApiModelProperty(value = "问题与意见")
    private String content;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "附件id集合逗号分隔")
    private String attachments;

    private String auditorId;

    @ApiModelProperty(value = "审核人名称")
    private String auditorName;

    @ApiModelProperty(value = "创建人机构编码")
    private String orgCode;

    @ApiModelProperty(value = "创建人机构名称")
    private String orgName;

    @ApiModelProperty(value = "创建人科室部门id")
    private String deptId;

    @ApiModelProperty(value = "创建人科室部门name")
    private String deptName;

    private Integer deleteFlag;

    @ApiModelProperty(value = "0-已撤回; 1-待反馈; 2-待议; 3-予以采纳; 4-已拒绝")
    private Integer status;

    @ApiModelProperty(value = "创建人id")
    private String creator;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "附件列表")
    private List<TbCdcAttachment> attachmentList;
}

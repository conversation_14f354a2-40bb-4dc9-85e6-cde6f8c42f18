package com.iflytek.fpva.cdc.mapper.symptom;

import com.iflytek.fpva.cdc.entity.TbCdcewSympEventRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface TbCdcewSympEventRecordMapper {

    int insert(TbCdcewSympEventRecord record);

    List<TbCdcewSympEventRecord> findByEventIdAndProcessingStatusAndType(@Param("eventIds") List<String> eventIds,
                                                                         @Param("processingStatusCollection") Collection<Integer> processingStatusCollection, @Param("processType") String processType);

    List<TbCdcewSympEventRecord> getSingleProcessorMsg(@Param("eventId") String eventId,
                                                       @Param("processingStatus") Integer processingStatus,
                                                       @Param("processType") String processType);

    List<TbCdcewSympEventRecord> findByEventIdIn(@Param("eventIdList") List<String> eventIdList);
}
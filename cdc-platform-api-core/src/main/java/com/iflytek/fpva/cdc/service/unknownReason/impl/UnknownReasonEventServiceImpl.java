package com.iflytek.fpva.cdc.service.unknownReason.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.constant.Age;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.constant.ProcessingStatus;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewPositionMapper;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.*;
import com.iflytek.fpva.cdc.model.dto.AgeQueryDto;
import com.iflytek.fpva.cdc.model.warningEvent.dto.EventTypeCountDTO;
import com.iflytek.fpva.cdc.model.dto.unknownReason.UnknownReasonEventListQueryDTO;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.po.UapRole;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.unknown.UnknownReasonRecordVO;
import com.iflytek.fpva.cdc.model.warningEvent.vo.EventTypeCountVO;
import com.iflytek.fpva.cdc.service.common.BusinessPersonService;
import com.iflytek.fpva.cdc.service.common.HisMedicalInfoService;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.unknownReason.UnknownReasonEventService;
import com.iflytek.fpva.cdc.service.warningEvent.WarningEventProcessRecordService;
import com.iflytek.fpva.cdc.util.AgeGroupUtils;
import com.iflytek.fpva.cdc.util.CollectionUtil;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import com.iflytek.fpva.cdc.util.UapAccessUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UnknownReasonEventServiceImpl implements UnknownReasonEventService {

    @Resource
    BusinessPersonService businessPersonService;

    @Resource
    RestService restService;

    @Resource
    TbCdcConfigService tbCdcConfigService;

    @Resource
    OrgService orgService;

    @Resource
    TbCdcewUnknownReasonWarningEventMapper tbCdcewUnknownReasonWarningEventMapper;

    @Resource
    TbCdcewUnknownReasonWarningDetailMapper tbCdcewUnknownReasonWarningDetailMapper;

    @Resource
    BatchUidService batchUidService;

    @Resource
    HisMedicalInfoService hisMedicalInfoService;

    @Resource
    TbCdcewUnKnownReasonEventRecordMapper tbCdcewUnKnownReasonEventRecordMapper;

    @Resource
    TbCdcewUnknownReasonDiseaseMapper tbCdcewUnknownReasonDiseaseMapper;

    @Resource
    TbCdcewUnknownReasonEventAnalysisMapper tbCdcewUnknownReasonEventAnalysisMapper;

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    TbCdcewPositionMapper tbCdcewPositionMapper;

    @Resource
    private WarningEventProcessRecordService warningEventProcessRecordService;

    @Override
    public ScStatisticsVO statistics(String loginUserName, String loginUserId, Integer processingStatus, String eventType) {
        Set<Integer> statusSet = new HashSet<>();
        if (processingStatus == null) {
            statusSet.add(ProcessingStatus.PENDING);
            statusSet.add(ProcessingStatus.PROCESSING);
            statusSet.add(ProcessingStatus.FINISHED);
        } else {
            statusSet.add(processingStatus);
        }

        List<String> symptomList = new ArrayList<>();
//        PersonDataAuthPo personDataAuthPo = businessPersonService.getUnknownReasonDataAuthByLoginUserId(loginUserId);
//        if (personDataAuthPo.getIsPlatformAdmin()) {
//            List<TbCdcewPersonDataAuth> dataAuthList = personDataAuthPo.getList();
//            List<String> collect = dataAuthList.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());
//            // 避免空集合
//            collect.add("");
//            symptomList = collect;
//        }
        symptomList = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.UNKNOWN_REASON.getType());

        ScStatisticsVO vo = new ScStatisticsVO();
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        if (statusSet.contains(ProcessingStatus.PENDING)) {
            EventCriteria pendingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria responseTimeOutCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .eventType(eventType)
                    .responseTimeOutStatus(1)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria processingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .eventType(eventType)
                    .processingTimeOutStatus(1)
                    .symptomTypeCollection(symptomList)
                    .build();
            int pending = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(pendingCriteria.getParameterMap());
            int responseTimeout = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(responseTimeOutCriteria.getParameterMap());
            int processTimeout = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(processingCriteria.getParameterMap());
            vo.setPending(pending);
            vo.setResponseTimeout(responseTimeout);
            vo.setProcessTimeout(processTimeout);
        }
        if (statusSet.contains(ProcessingStatus.PROCESSING)) {
            EventCriteria pendingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria responseTimeOutCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .responseTimeOutStatus(1)
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria processingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .processingTimeOutStatus(1)
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            int processing = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(pendingCriteria.getParameterMap());
            int responseTimeout = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(responseTimeOutCriteria.getParameterMap());
            int processTimeout = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(processingCriteria.getParameterMap());
            vo.setProcessing(processing);
            vo.setResponseTimeout(responseTimeout);
            vo.setProcessTimeout(processTimeout);
        }
        if (statusSet.contains(ProcessingStatus.FINISHED)) {
            EventCriteria removedManualCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.REMOVED_MANUAL))
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria removedAiCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.REMOVED_AI))
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria positiveCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.POSITIVE))
                    .eventType(eventType)
                    .symptomTypeCollection(symptomList)
                    .build();
            int removedManual = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(removedManualCriteria.getParameterMap());
            int removedAi = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(removedAiCriteria.getParameterMap());
            int positive = tbCdcewUnknownReasonWarningEventMapper.countByParameterMap(positiveCriteria.getParameterMap());
            int finished = removedManual + removedAi + positive;
            vo.setRemovedManual(removedManual);
            vo.setRemovedAi(removedAi);
            vo.setPositive(positive);
            vo.setFinished(finished);
        }
        vo.setTotal(vo.getPending() + vo.getProcessing() + vo.getFinished());
        return vo;
    }

    @Override
    public PageData<EventVO> getUnknownReasonEventList(UnknownReasonEventListQueryDTO dto, String loginUserName, String loginUserId) {
        //crud权限控制
        UapOrgPo orgPo = restService.getUserOrg(loginUserName);
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(dto.getRegionInfoList())) {
            dto.getRegionInfoList().forEach(e -> {
                UapAccessUtil.hasAccess(orgPo, e.getProvinceCode(), e.getCityCode(), e.getDistrictCode());
            });
        }

        //是否是普通用户
        boolean isOrdinaryUser = tbCdcConfigService.isOrdinaryUser(loginUserId);
        //是否脱敏显示
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);
        boolean sortByAiScreen = tbCdcConfigService.isUnknownReasonEnableAiScreen();

        if (CollectionUtils.isEmpty(dto.getSymptom())) {
            List<String> collect = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.UNKNOWN_REASON.getType());
            dto.setSymptom(collect);
        }
        // 已完成状态包含人工排除、AI排除、阳性事件三种状态
        if (dto.getStatusCollection() != null && dto.getStatusCollection().contains(ProcessingStatus.FINISHED)) {
            dto.getStatusCollection().remove(ProcessingStatus.FINISHED);
            dto.getStatusCollection().add(ProcessingStatus.REMOVED_MANUAL);
            dto.getStatusCollection().add(ProcessingStatus.REMOVED_AI);
            dto.getStatusCollection().add(ProcessingStatus.POSITIVE);
        }

        //处理研判结论
        List<String> inputConclusions = CollectionUtil.getSplitList(dto.getConclusions());
        List<Integer> conclusions = inputConclusions.stream().map(Integer::parseInt).collect(Collectors.toList());

        // 机构查询条件
        List<String> allOrgIds = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getOrgId())) {
            List<CascadeVO> orgTreeList = new ArrayList<>();
            CascadeVO cascadeVO = orgService.buildCascadeVOByOrgId(dto.getOrgId());
            if (cascadeVO != null){
                orgTreeList.add(cascadeVO);
                allOrgIds = orgService.getAllOrgId(orgTreeList);
            }else {
                allOrgIds.add(dto.getOrgId());
            }
        }

        // 构建查询参数
        EventCriteria eventCriteria = EventCriteria.builder()
                .regionInfoList(dto.getRegionInfoList())
                .symptomTypeCollection(dto.getSymptom())
                .processingStatusCollection(dto.getStatusCollection())
                .minBeginTime(dto.getMinFullDate())
                .maxBeginTime(dto.getMaxFullDate())
                .sortByAiScreen(sortByAiScreen)
                .sortType(dto.getSortType())
                .dateType(dto.getDateType())
                .orgIds(allOrgIds)
                .responseTimeOutStatus(dto.getResponseTimeOutStatus())
                .processingTimeOutStatus(dto.getProcessingTimeOutStatus())
                .conclusions(conclusions)
                .sourceType(StringUtils.isEmpty(dto.getSourceType()) ? null : Collections.singletonList(dto.getSourceType()))
                .eventNum(dto.getEventNum())
                .eventType(dto.getEventType())
                .build();


        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        List<TbCdcewUnknownReasonWarningEvent> events = tbCdcewUnknownReasonWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());
        PageInfo<TbCdcewUnknownReasonWarningEvent> pageInfo = new PageInfo<>(events);
        PageData<EventVO> pageData = PageData.fromPageInfo(pageInfo);
        List<TbCdcewUnknownReasonWarningEvent> list = pageInfo.getList();

        final Map<String, WarningGradeVO> warningGradeMap = restService.getWarningGradeMap(Constants.CURRENT_VERSION);
        //实体类转所需字段VO
        List<EventVO> result = list.stream().map(v -> EventVO.fromEntityAndMap(v,warningGradeMap)).collect(Collectors.toList());

        boolean finalSortByAiScreen = sortByAiScreen;
        result.forEach(item -> item.setEventVOAttentionLevel(isOrdinaryUser, finalSortByAiScreen));

        List<String> ids = result.stream().map(EventVO::getId).collect(Collectors.toList());

        if(!ids.isEmpty()){

            //查询信号处理情况，设置处理人处理时间
            List<TbCdcewWarningEventProcessRecord> warningEventProcessRecords = warningEventProcessRecordService.findByEventIdAndProcessingStatusAndType(ids,
                    dto.getStatusCollection(), ProcessTypeEnum.STATUS.getCode(), WarningTypeCodeEnum.UNKNOWN_REASON.getName());
            Map<String, List<TbCdcewWarningEventProcessRecord>> processRecordsMap = warningEventProcessRecords.stream().collect(Collectors.groupingBy(TbCdcewWarningEventProcessRecord::getEventId));
            //设置信号的处理人以及处理时间
            result.forEach(eventVO -> {
                List<TbCdcewWarningEventProcessRecord> records = processRecordsMap.get(eventVO.getId());
                if (!org.apache.commons.collections.CollectionUtils.isEmpty(records)) {
                    //设置最新 处理人和时间
                    eventVO.setProcessor(records.get(0).getProcessLoginUserName());
                    eventVO.setProcessTime(records.get(0).getProcessTime());
                }
                if (Integer.valueOf(eventVO.getProcessingStatus()).equals(ProcessingStatus.REMOVED_AI)) {
                    eventVO.setProcessor(ProcessingStatus.REMOVED_AI_STR);
                    eventVO.setProcessTime(eventVO.getUpdateTime());
                }
            });
        }

        if (isDesensitization) {
            result.forEach(DesensitizeVOUtils::desensitizeEventVo);
        }
        pageData.setData(result);
        return pageData;
    }

    @Override
    public List<CascadeVO> getDiseaseNameListByLoginUserId(String loginUserId) {

        List<CascadeVO> restList = getUnknownReasonDiseaseNameList();

//        PersonDataAuthPo personDataAuthPo = businessPersonService.getUnknownReasonDataAuthByLoginUserId(loginUserId);
//        if (personDataAuthPo.getIsPlatformAdmin()) {
//            List<TbCdcewPersonDataAuth> list = personDataAuthPo.getList();
//            List<String> collect = list.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());
//            restList = restList.stream().filter(item -> collect.contains(item.getValue())).collect(Collectors.toList());
//        }
        List<String> collect = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.UNKNOWN_REASON.getType());
        restList = restList.stream().filter(item -> collect.contains(item.getValue())).collect(Collectors.toList());
        return restList;
    }

    private CascadeVO convertObject(CascadeVO restVO) {
        CascadeVO resultVO = new CascadeVO();
        resultVO.setValue(restVO.getValue());
        resultVO.setLabel(restVO.getLabel());
        if (!CollectionUtils.isEmpty(restVO.getChildren())) {
            resultVO.setChildren(new ArrayList<>());
            Collections.sort(restVO.getChildren());
            restVO.getChildren().forEach(childRestVO -> {
                CascadeVO childResultVO = convertObject(childRestVO);
                resultVO.getChildren().add(childResultVO);
            });
        }
        return resultVO;
    }

    private List<CascadeVO> getUnknownReasonDiseaseNameList() {
        List<TbCdcewUnknownReasonDisease> list = tbCdcewUnknownReasonDiseaseMapper.getAll();
        List<CascadeVO> result = new ArrayList<>();

        list.forEach(item -> {
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setValue(item.getDiseaseCode());
            cascadeVO.setLabel(item.getDiseaseName());
            result.add(cascadeVO);
        });
        Collections.sort(result);

        return result;
    }

    @Override
    public EventTypeCountVO getEventTypeCountVO(String loginUserId, String loginUserName) {
        EventTypeCountVO eventTypeCountVO = new EventTypeCountVO();
        UapOrgPo userOrg = restService.getUserOrg(loginUserName);

        List<String> symptomTypeCollection = new ArrayList<>();
//        PersonDataAuthPo personDataAuthPo = businessPersonService.getUnknownReasonDataAuthByLoginUserId(loginUserId);
//        if (personDataAuthPo.getIsPlatformAdmin()) {
//            List<TbCdcewPersonDataAuth> list = personDataAuthPo.getList();
//            List<String> collect = list.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());
//            // 避免空集合
//            collect.add("");
//            symptomTypeCollection = collect;
//        }
        symptomTypeCollection = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.UNKNOWN_REASON.getType());
        List<EventTypeCountDTO> eventTypeCountDTOList = tbCdcewUnknownReasonWarningEventMapper.getEventCountDto(userOrg.getCityCode(), userOrg.getDistrictCode(), symptomTypeCollection);

        eventTypeCountDTOList.forEach(eventTypeCountDto -> {
            String eventType = eventTypeCountDto.getEventType();
            Integer count = eventTypeCountDto.getCount();

            if (EventTypeEnum.DJYY.getName().equals(eventType)) {
                eventTypeCountVO.setHospitalEventCount(count);
            }
            if (EventTypeEnum.JCYL.getName().equals(eventType)) {
                eventTypeCountVO.setPrimaryCareEventCount(count);
            }
            if (EventTypeEnum.XXDW.getName().equals(eventType)) {
                eventTypeCountVO.setSchoolEventCount(count);
            }
            if (EventTypeEnum.JDQY.getName().equals(eventType)) {
                eventTypeCountVO.setStreetEventCount(count);
            }
        });

        return eventTypeCountVO;
    }

    @Override
    public List<AgeGroupVO> ageDistribution(String eventId, String beginDate, String endDate, AgeQueryDto ageQueryDto) {

        List<UnknownReasonRecordVO> unknownRecordVOList = hisMedicalInfoService.getUnknownReasonRecordsByEventId(eventId, null, null, null, null);

        List<Double> ageList = unknownRecordVOList.stream()
                .map(UnknownReasonRecordVO::getAge)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .map(Double::parseDouble).collect(Collectors.toList());

        List<AgeGroupVO> result = new ArrayList<>();
        Integer type = ageQueryDto.getAgeGroupType();
        if (1 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, Arrays.asList(Age.kernelAgeRange));
        }
        if (2 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, Arrays.asList(Age.secondAgeRange));
        }
        if (3 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, ageQueryDto.getAgeGroupList());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getWarningEventResourceDistribution(String eventId, String startTime, String endTime) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime;
        Date formatEndTime;
        try {
            formatStartTime = simpleDateFormat.parse(startTime);
            formatEndTime = simpleDateFormat.parse(endTime);
        } catch (ParseException e) {
            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
        }
        int wmaCount = 0;
        int ghCount = 0;
        int esCount = 0;
        int phCount = 0;
        int scCount = 0;

        List<UnknownReasonRecordVO> unknownRecordVOList = hisMedicalInfoService.getUnknownReasonRecordsByEventId(eventId, null, null, null, null);
        List<UnknownReasonRecordVO> nullSourceTypeData = unknownRecordVOList.stream().filter(unknownRecordVO -> unknownRecordVO.getSourceType() == null).collect(Collectors.toList());
        List<UnknownReasonRecordVO> notNullSourceTypeData = unknownRecordVOList.stream().filter(unknownRecordVO -> unknownRecordVO.getSourceType() != null).collect(Collectors.toList());
        Map<String, List<UnknownReasonRecordVO>> resGroupBySourceType = notNullSourceTypeData.stream().collect(Collectors.groupingBy(UnknownReasonRecordVO::getSourceType));
        List<Map<String, Object>> resultList = new ArrayList<>();
        resGroupBySourceType.forEach((s, list) -> {
            Map<String, Object> map = new HashMap<>();
            map.put("sourceType", s);
            map.put("cn", list.size());
            resultList.add(map);
        });

        for (Map<String, Object> tempMap : resultList) {
            Object type = tempMap.get("sourceType");
            if (!Objects.isNull(type)) {
                if (String.valueOf(DataSourceTypeEnum.WISDOM_MEDICAL_ASSISTANT.getValue()).equals(type.toString())) {
                    wmaCount = wmaCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (DataSourceTypeEnum.GRADE_HOSPITAL_OUTPATIENT.getStrValue().equals(type.toString())
                        || String.valueOf(DataSourceTypeEnum.GRADE_HOSPITAL_IN_HOSPITAL.getValue()).equals(type.toString())) {
                    ghCount = ghCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getValue()).equals(type.toString())) {
                    esCount = esCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.GRADE_PHARMACY.getValue()).equals(type.toString())) {
                    phCount = phCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.GRADE_SCHOOL.getValue()).equals(type.toString())) {
                    scCount = scCount + Integer.parseInt(tempMap.get("cn").toString());
                }
            }
        }

        List<Map<String, Object>> returnList = new ArrayList<>();
        Map<String, Object> result1 = new HashMap<>();
        Map<String, Object> result2 = new HashMap<>();
        Map<String, Object> result3 = new HashMap<>();
        Map<String, Object> result4 = new HashMap<>();
        Map<String, Object> result5 = new HashMap<>();
        if (wmaCount > 0) {
            result1.put("name", MedDistributionEnum.COMMUNITY.getDesc());
            result1.put("value", wmaCount);
            returnList.add(result1);
        }
        if (ghCount > 0) {
            result2.put("name", MedDistributionEnum.HOSPITAL.getDesc());
            result2.put("value", ghCount);
            returnList.add(result2);
        }
        if (esCount > 0) {
            result3.put("name", MedDistributionEnum.LAB.getDesc());
            result3.put("value", esCount);
            returnList.add(result3);
        }
        if (phCount > 0) {
            result4.put("name", MedDistributionEnum.PHARMACY.getDesc());
            result4.put("value", phCount);
            returnList.add(result4);
        }
        if (scCount > 0) {
            result5.put("name", MedDistributionEnum.SCHOOL.getDesc());
            result5.put("value", scCount);
            returnList.add(result5);
        }
        if (nullSourceTypeData.size() > 0) {
            Map<String, Object> nullDataMap = new HashMap<>();
            nullDataMap.put("name", "未知");
            nullDataMap.put("value", nullSourceTypeData.size());
            returnList.add(nullDataMap);
        }
        return returnList;
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getMedOutCallCount(String eventId, String startTime, String endTime) {
        return new ArrayList<>();
    }
    @Override
    public List<EventAnalysisResultVO> getEventAnalysisResult(String eventId) {

        //1，获取研判信息
        List<TbCdcewUnknownReasonEventAnalysis> eventAnalysisList = tbCdcewUnknownReasonEventAnalysisMapper.selectByEventId(eventId);
        if (CollectionUtils.isEmpty(eventAnalysisList)) {
            return Collections.EMPTY_LIST;
        }

        //2，获取附件信息
        List<EventAnalysisResultVO> resultList = new ArrayList<>();
        for (TbCdcewUnknownReasonEventAnalysis tbCdcewPoisonEventAnalysis : eventAnalysisList) {
            List<TbCdcAttachment> tbCdcewPoisonEventAnalysisAttachments = tbCdcAttachmentMapper.selectByUnknownReasonAnalysisId(tbCdcewPoisonEventAnalysis.getId());
            EventAnalysisResultVO eventAnalysisResultVO = EventAnalysisResultVO.fromEntity(tbCdcewPoisonEventAnalysis);
            eventAnalysisResultVO.setAttachmentList(tbCdcewPoisonEventAnalysisAttachments);
            resultList.add(eventAnalysisResultVO);
        }
        return resultList;
    }

    @Override
    public void startProcess(String loginUserId, String eventId, int status, String loginUserName) {
        log.info("不明原因事件手动改变事件状态开始,信息: loginUserId:" + loginUserId + " ,eventId:" + eventId
                + " ,status:" + status + " ,loginUserName:" + loginUserName);
        if (status >= ProcessingStatus.REMOVED_AI) {
            throw new MedicalBusinessException("11441005", "无效的状态");
        }
        TbCdcewUnknownReasonWarningEvent event = tbCdcewUnknownReasonWarningEventMapper.findById(eventId);
        //查询不到 或 状态重复 不能进行操作
        if (event == null) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }
        //操作权限检查
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        UapAccessUtil.hasAccess(uapOrg, event.getProvinceCode(), event.getCityCode(), event.getDistrictCode());
        //新增一条处理记录
        TbCdcewUnknownReasonEventRecord record = new TbCdcewUnknownReasonEventRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_unknown_reason_event_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessStatus(status);
        record.setProcessLoginUserId(loginUserId);
        record.setProcessType(ProcessTypeEnum.STATUS.getCode());
        Date endTime = null;
        switch (status) {
            case ProcessingStatus.PROCESSING:
                record.setProcessDesc(ProcessingStatus.PROCESSING_STR);
                break;
            //如果是排除和上报 则要修改事件的endTime
            case ProcessingStatus.REMOVED_MANUAL:
                record.setProcessDesc(ProcessingStatus.REMOVED_MANUAL_STR);
                endTime = new Date();
                break;
            case ProcessingStatus.POSITIVE:
                record.setProcessDesc(ProcessingStatus.POSITIVE_STR);
                endTime = new Date();
                break;
            default:
                break;
        }
        tbCdcewUnKnownReasonEventRecordMapper.insert(record);
        //将原始事件表里的processing_status改为正在处理
        tbCdcewUnknownReasonWarningEventMapper.updateProcessingStatusAndEndTimeById(status, endTime, eventId,null);
        //2020-11-26 需要将所关联的detail中的状态也同步修改
        tbCdcewUnknownReasonWarningDetailMapper.updateProcessingStatusByEventId(status, eventId);
    }

    @Override
    public void updateAiScreen(Integer isAiScreen, String eventId) {
        //1,更新状态
        log.info("事件AI初筛置顶：事件ID-{}: 置顶状态-{}", eventId, isAiScreen);
        tbCdcewUnknownReasonWarningEventMapper.updateIsAiScreenById(isAiScreen, eventId);
    }

    @Override
    public void addProcessRecord(String eventId, String loginUserId, String loginUserName, String processType, String processDesc) {
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        //新增一条处理记录
        TbCdcewUnknownReasonEventRecord record = new TbCdcewUnknownReasonEventRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_unknown_reason_event_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);
        record.setProcessDesc(processDesc);
        record.setProcessType(processType);

        tbCdcewUnKnownReasonEventRecordMapper.insert(record);
    }

    @Override
    public TbCdcewUnknownReasonWarningEvent getCdcWaringEventByEventId(String eventId) {

        return tbCdcewUnknownReasonWarningEventMapper.findById(eventId);
    }

    @Override
    public void updateCheckedLevel(Integer checkedLevel, String eventId) {
        //1,更新状态
        log.info("更新事件审查状态：事件ID-{}: 审查状态-{}", eventId, checkedLevel);
        tbCdcewUnknownReasonWarningEventMapper.updateCheckedLevelById(checkedLevel, eventId);
    }

    @Override
    public List<CascadeVO> getDiseaseNameList() {
        return getUnknownReasonDiseaseNameList();
    }

    @Override
    public List<String> getUnknownReasonRoleIdsByPositionAndType(String positionCode, String positionTypeCode) {

        List<TbCdcewPosition> positions = tbCdcewPositionMapper.getPositionsByPositionCodeAndType(positionCode, positionTypeCode);

        Map<String, List<TbCdcewPosition>> appCodeMap = positions.stream().collect(Collectors.groupingBy(TbCdcewPosition::getUapAppCode));
        List<String> result = new ArrayList<>();
        appCodeMap.forEach((k, v) -> {
            List<UapRole> uapRoleList = restService.getUapRoleListByAppCode(k);

            v.forEach(tbCdcewPosition -> {
                String id = tbCdcewPosition.getId();
                String uapRoleName = tbCdcewPosition.getUapRoleName();

                List<UapRole> subUapRoleList = uapRoleList.stream().filter(uapRole -> uapRoleName.equals(uapRole.getName())).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(subUapRoleList)) {
                    UapRole uapRole = subUapRoleList.get(0);

                    positions.forEach(item -> {
                        if (item.getId().equals(id)) {
                            result.add(uapRole.getId());
                        }
                    });
                }
            });
        });

        return result;
    }
}

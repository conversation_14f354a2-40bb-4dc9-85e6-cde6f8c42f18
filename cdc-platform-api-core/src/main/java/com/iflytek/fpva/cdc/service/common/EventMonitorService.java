package com.iflytek.fpva.cdc.service.common;

import com.alibaba.fastjson.JSONObject;
import com.iflytek.fpva.cdc.model.dto.syndrome.AppSyndromeMedicalDetailDTO;
import com.iflytek.fpva.cdc.model.dto.syndrome.SyndromeMonitorListQueryDTO;
import com.iflytek.fpva.cdc.model.dto.syndrome.SyndromeMonitorMedicalListQueryDTO;
import com.iflytek.fpva.cdc.model.vo.MedicalInfoVO;
import com.iflytek.fpva.cdc.model.vo.monitor.*;
import com.iflytek.fpva.cdc.model.vo.PageData;

import java.util.List;

public interface EventMonitorService {
    PageData<EventMonitorVO> getEventMonitorList(SyndromeMonitorListQueryDTO dto);

    EventMonitorDetailVO getMonitorDetail(String monitorSetId,String detailId);

    List<MonitorDistributionVO> monitorDistribution(String startTime, String endTime);

    List<SignalRecallRateVO> signalRecallRate(String startTime, String endTime, String symptom, String symptomName);

    List<EventSourceAnalysisVO> eventSourceAnalysis(String startTime, String endTime, String symptom, String symptomName);

    TotalDistributionVO totalDistribution(String startTime, String endTime);


    PageData<AppSyndromeMedicalDetailDTO> medDistributionList(SyndromeMonitorMedicalListQueryDTO queryDTO,String loginUserId);


    byte[] exportPeriodDMedicalList(SyndromeMonitorMedicalListQueryDTO queryDTO,String loginUserId);

    List<MedicalInfoVO> getMedicalInfoList(String detailId,String loginUserId);

    String getSuspectTrendDay(String monitorSetId, String detailId, String lastYearFlag);
}

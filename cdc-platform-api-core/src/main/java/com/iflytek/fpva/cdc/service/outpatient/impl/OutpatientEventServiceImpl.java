package com.iflytek.fpva.cdc.service.outpatient.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.constant.*;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewPositionMapper;
import com.iflytek.fpva.cdc.mapper.outpatient.*;
import com.iflytek.fpva.cdc.model.dto.AgeQueryDto;
import com.iflytek.fpva.cdc.model.warningEvent.dto.EventTypeCountDTO;
import com.iflytek.fpva.cdc.model.dto.outpatient.OutpatientEventListQueryDTO;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.po.UapRole;
import com.iflytek.fpva.cdc.model.po.UapUserPo;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.outpatient.OutpatientEventTypeCountVO;
import com.iflytek.fpva.cdc.model.vo.outpatient.OutpatientRecordVO;
import com.iflytek.fpva.cdc.service.common.BusinessPersonService;
import com.iflytek.fpva.cdc.service.common.HisMedicalInfoService;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.outpatient.OutpatientEventService;
import com.iflytek.fpva.cdc.util.AgeGroupUtils;
import com.iflytek.fpva.cdc.util.CollectionUtil;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import com.iflytek.fpva.cdc.util.UapAccessUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OutpatientEventServiceImpl implements OutpatientEventService {
    @Resource
    BusinessPersonService businessPersonService;
    @Resource
    RestService restService;
    @Resource
    TbCdcewOutpatientWarningEventMapper tbCdcewOutpatientWarningEventMapper;
    @Resource
    TbCdcConfigService tbCdcConfigService;
    @Resource
    OrgService orgService;
    @Resource
    TbCdcewOutpatientEventRecordMapper tbCdcewOutpatientEventRecordMapper;
    @Resource
    BatchUidService batchUidService;
    @Resource
    TbCdcewOutpatientWarningDetailMapper tbCdcewOutpatientWarningDetailMapper;
    @Resource
    HisMedicalInfoService hisMedicalInfoService;
    @Resource
    TbCdcewOutpatientEventAnalysisMapper tbCdcewOutpatientEventAnalysisMapper;

    @Resource
    TbCdcewOutpatientEventAnalysisAttachmentMapper tbCdcewOutpatientEventAnalysisAttachmentMapper;

    @Resource
    TbCdcewPositionMapper tbCdcewPositionMapper;

    @Override
    public ScStatisticsVO statistics(String loginUserName, String loginUserId, Integer processingStatus, String outpatientType) {
        Set<Integer> statusSet = new HashSet<>();
        if (processingStatus == null) {
            statusSet.add(ProcessingStatus.PENDING);
            statusSet.add(ProcessingStatus.PROCESSING);
            statusSet.add(ProcessingStatus.FINISHED);
        } else {
            statusSet.add(processingStatus);
        }

        List<String> symptomList = new ArrayList<>();


        List<String> collect = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.OUTPATIENT.getType());
        if (!org.springframework.util.StringUtils.isEmpty(outpatientType)){
            if(collect.contains(outpatientType)){
                symptomList.add(outpatientType);
            } else {
                symptomList.add(CommonConstants.NOT_DATA_AUTH);
            }
        }else {
            symptomList.addAll(collect);
        }



        ScStatisticsVO vo = new ScStatisticsVO();
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        if (statusSet.contains(ProcessingStatus.PENDING)) {
            EventCriteria pendingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria responseTimeOutCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .responseTimeOutStatus(1)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria processingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PENDING))
                    .processingTimeOutStatus(1)
                    .symptomTypeCollection(symptomList)
                    .build();
            int pending = tbCdcewOutpatientWarningEventMapper.countByParameterMap(pendingCriteria.getParameterMap());
            int responseTimeout = tbCdcewOutpatientWarningEventMapper.countByParameterMap(responseTimeOutCriteria.getParameterMap());
            int processTimeout = tbCdcewOutpatientWarningEventMapper.countByParameterMap(processingCriteria.getParameterMap());
            vo.setPending(pending);
            vo.setResponseTimeout(responseTimeout);
            vo.setProcessTimeout(processTimeout);
        }
        if (statusSet.contains(ProcessingStatus.PROCESSING)) {
            EventCriteria pendingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria responseTimeOutCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .responseTimeOutStatus(1)
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria processingCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.PROCESSING))
                    .processingTimeOutStatus(1)
                    .symptomTypeCollection(symptomList)
                    .build();
            int processing = tbCdcewOutpatientWarningEventMapper.countByParameterMap(pendingCriteria.getParameterMap());
            int responseTimeout = tbCdcewOutpatientWarningEventMapper.countByParameterMap(responseTimeOutCriteria.getParameterMap());
            int processTimeout = tbCdcewOutpatientWarningEventMapper.countByParameterMap(processingCriteria.getParameterMap());
            vo.setProcessing(processing);
            vo.setResponseTimeout(responseTimeout);
            vo.setProcessTimeout(processTimeout);
        }
        if (statusSet.contains(ProcessingStatus.FINISHED)) {
            EventCriteria removedManualCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.REMOVED_MANUAL))
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria removedAiCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.REMOVED_AI))
                    .symptomTypeCollection(symptomList)
                    .build();
            EventCriteria positiveCriteria = EventCriteria.builder()
                    .districtCode(StringUtils.isBlank(uapOrg.getDistrictCode()) ? null : Arrays.asList(uapOrg.getDistrictCode()))
                    .cityCode(StringUtils.isBlank(uapOrg.getCityCode()) ? null : Arrays.asList(uapOrg.getCityCode()))
                    .provinceCode(StringUtils.isBlank(uapOrg.getProvinceCode()) ? null : Arrays.asList(uapOrg.getProvinceCode()))
                    .processingStatusCollection(Collections.singleton(ProcessingStatus.POSITIVE))
                    .symptomTypeCollection(symptomList)
                    .build();
            int removedManual = tbCdcewOutpatientWarningEventMapper.countByParameterMap(removedManualCriteria.getParameterMap());
            int removedAi = tbCdcewOutpatientWarningEventMapper.countByParameterMap(removedAiCriteria.getParameterMap());
            int positive = tbCdcewOutpatientWarningEventMapper.countByParameterMap(positiveCriteria.getParameterMap());
            int finished = removedManual + removedAi + positive;
            vo.setRemovedManual(removedManual);
            vo.setRemovedAi(removedAi);
            vo.setPositive(positive);
            vo.setFinished(finished);
        }
        vo.setTotal(vo.getPending() + vo.getProcessing() + vo.getFinished());
        return vo;
    }

    @Override
    public PageData<EventVO> getOutpatientEventList(OutpatientEventListQueryDTO dto, String loginUserName, String loginUserId) {
        //crud权限控制
        UapAccessUtil.hasAccess(restService.getUserOrg(loginUserName), dto.getProvinceCode(), dto.getCityCode(), dto.getDistrictCode());

        //是否是普通用户
        boolean isOrdinaryUser = tbCdcConfigService.isOrdinaryUser(loginUserId);
        //是否脱敏显示
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);


        boolean sortByAiScreen = tbCdcConfigService.isFeverOutpatientEnableAiScreen();
        if (dto.getSymptom().contains(OutpatientTypeEnum.BOWEL.getCode())) {
            sortByAiScreen = tbCdcConfigService.isBowelOutpatientEnableAiScreen();
        }

        List<String> symptomList = new ArrayList<>();

        List<String> dataAuthCode = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.OUTPATIENT.getType());
        if (!dto.getSymptom().isEmpty()){
            dataAuthCode.retainAll(dto.getSymptom());
            if(!CollectionUtils.isEmpty(dataAuthCode)){
                symptomList.addAll(dataAuthCode);
            }else {
                symptomList.add(CommonConstants.NOT_DATA_AUTH);
            }
        }else {
            symptomList.addAll(dataAuthCode);
        }

        // 已完成状态包含人工排除、AI排除、阳性事件三种状态
        if (dto.getStatusCollection() != null && dto.getStatusCollection().contains(ProcessingStatus.FINISHED)) {
            dto.getStatusCollection().remove(ProcessingStatus.FINISHED);
            dto.getStatusCollection().add(ProcessingStatus.REMOVED_MANUAL);
            dto.getStatusCollection().add(ProcessingStatus.REMOVED_AI);
            dto.getStatusCollection().add(ProcessingStatus.POSITIVE);
        }

        //处理研判结论
        List<String> inputConclusions = CollectionUtil.getSplitList(dto.getConclusions());
        List<Integer> conclusions = inputConclusions.stream().map(Integer::parseInt).collect(Collectors.toList());

        // 机构查询条件
        List<String> allOrgIds = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getOrgId())) {
            List<CascadeVO> orgTreeList = new ArrayList<>();
            CascadeVO cascadeVO = orgService.buildCascadeVOByOrgId(dto.getOrgId());
            if (cascadeVO != null){
                orgTreeList.add(cascadeVO);
                allOrgIds = orgService.getAllOrgId(orgTreeList);
            }else {
                allOrgIds.add(dto.getOrgId());
            }
        }

        // 构建查询参数
        EventCriteria eventCriteria = EventCriteria.builder().cityCode(CollectionUtil.getSplitList(dto.getCityCode()))
                .districtCode(CollectionUtil.getSplitList(dto.getDistrictCode()))
                .symptomTypeCollection(symptomList)
                .processingStatusCollection(dto.getStatusCollection())
                .minBeginTime(dto.getMinFullDate())
                .maxBeginTime(dto.getMaxFullDate())
                .sortByAiScreen(sortByAiScreen)
                .sortType(dto.getSortType())
                .dateType(dto.getDateType())
                .orgIds(allOrgIds)
                .responseTimeOutStatus(dto.getResponseTimeOutStatus())
                .processingTimeOutStatus(dto.getProcessingTimeOutStatus())
                .conclusions(conclusions)
                .sourceType(StringUtils.isEmpty(dto.getSourceType()) ? null : Collections.singletonList(dto.getSourceType()))
                .eventNum(dto.getEventNum())
                .build();


        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        List<TbCdcewOutpatientWarningEvent> events = tbCdcewOutpatientWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());
        PageInfo<TbCdcewOutpatientWarningEvent> pageInfo = new PageInfo<>(events);
        PageData<EventVO> pageData = PageData.fromPageInfo(pageInfo);
        List<TbCdcewOutpatientWarningEvent> list = pageInfo.getList();

        final Map<String, WarningGradeVO> warningGradeMap = restService.getWarningGradeMap(Constants.CURRENT_VERSION);
        //实体类转所需字段VO
        List<EventVO> result = list.stream().map(v -> EventVO.fromEntityAndMap(v,warningGradeMap)).collect(Collectors.toList());

        boolean finalSortByAiScreen = sortByAiScreen;
        result.forEach(item -> item.setEventVOAttentionLevel(isOrdinaryUser, finalSortByAiScreen));

        List<String> ids = result.stream().map(EventVO::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {

            // 获取loginUserIds
            List<TbCdcewOutpatientEventRecord> eventProcessRecords = tbCdcewOutpatientEventRecordMapper.findByEventIdAndProcessingStatusAndType(ids, dto.getStatusCollection(), ProcessTypeEnum.STATUS.getCode());
            List<String> loginUserIds = eventProcessRecords.stream()
                    .filter(cdcEventProcessRecord -> StringUtils.isNotEmpty(cdcEventProcessRecord.getProcessLoginUserId()))
                    .map(TbCdcewOutpatientEventRecord::getProcessLoginUserId).collect(Collectors.toList());
            List<UapUserPo> userList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(loginUserIds)) {
                userList = restService.getUserList(loginUserIds);
            }
            List<UapUserPo> finalUserList = userList;
            result.forEach(eventVO -> {
                List<TbCdcewOutpatientEventRecord> processRecords = eventProcessRecords.stream().filter(cdcEventProcessRecord ->
                        eventVO.getProcessingStatus().equals(cdcEventProcessRecord.getProcessStatus().toString()) &&
                                eventVO.getId().equals(cdcEventProcessRecord.getEventId())
                ).sorted(Comparator.comparing(TbCdcewOutpatientEventRecord::getProcessTime).reversed()).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(processRecords)) {
                    TbCdcewOutpatientEventRecord cdcEventProcessRecord = processRecords.get(0);

                    List<UapUserPo> collect = finalUserList.stream().filter(uapUserPo -> uapUserPo.getId().equals(cdcEventProcessRecord.getProcessLoginUserId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        UapUserPo uapUserPo = collect.get(0);
                        eventVO.setProcessor(uapUserPo.getName());
                        eventVO.setProcessTime(cdcEventProcessRecord.getProcessTime());
                    }
                }

                if (eventVO.getProcessingStatus().equals(String.valueOf(ProcessingStatus.REMOVED_AI))) {
                    eventVO.setProcessor(ProcessingStatus.REMOVED_AI_STR);
                    eventVO.setProcessTime(eventVO.getUpdateTime());
                }
            });
        }

        if (isDesensitization) {
            result.forEach(DesensitizeVOUtils::desensitizeEventVo);
        }
        pageData.setData(result);
        return pageData;
    }

    @Override
    public void startProcess(String loginUserId, String eventId, int status, String loginUserName) {
        log.info("门诊事件手动改变事件状态开始,信息: loginUserId:" + loginUserId + " ,eventId:" + eventId
                + " ,status:" + status + " ,loginUserName:" + loginUserName);
        if (status >= ProcessingStatus.REMOVED_AI) {
            throw new MedicalBusinessException("11441005", "无效的状态");
        }
        TbCdcewOutpatientWarningEvent event = tbCdcewOutpatientWarningEventMapper.findById(eventId);
        //查询不到 或 状态重复 不能进行操作
        if (event == null) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }
        //操作权限检查
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        UapAccessUtil.hasAccess(uapOrg, event.getProvinceCode(), event.getCityCode(), event.getDistrictCode());
        //新增一条处理记录
        TbCdcewOutpatientEventRecord record = new TbCdcewOutpatientEventRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outpatient_event_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessStatus(status);
        record.setProcessLoginUserId(loginUserId);
        record.setProcessType(ProcessTypeEnum.STATUS.getCode());
        Date endTime = null;
        switch (status) {
            case ProcessingStatus.PROCESSING:
                record.setProcessDesc(ProcessingStatus.PROCESSING_STR);
                break;
            //如果是排除和上报 则要修改事件的endTime
            case ProcessingStatus.REMOVED_MANUAL:
                record.setProcessDesc(ProcessingStatus.REMOVED_MANUAL_STR);
                endTime = new Date();
                break;
            case ProcessingStatus.POSITIVE:
                record.setProcessDesc(ProcessingStatus.POSITIVE_STR);
                endTime = new Date();
                break;
            default:
                break;
        }
        tbCdcewOutpatientEventRecordMapper.insert(record);
        //将原始事件表里的processing_status改为正在处理
        tbCdcewOutpatientWarningEventMapper.updateProcessingStatusAndEndTimeById(status, endTime, eventId,null);
        //2020-11-26 需要将所关联的detail中的状态也同步修改
        tbCdcewOutpatientWarningDetailMapper.updateProcessingStatusByEventId(status, eventId);
    }

    @Override
    public void updateAiScreen(int isAiScreen, String eventId) {
        //1,更新状态
        log.info("门诊事件AI初筛置顶：事件ID-{}: 置顶状态-{}", eventId, isAiScreen);
        tbCdcewOutpatientWarningEventMapper.updateIsAiScreenById(isAiScreen, eventId);
    }

    @Override
    public void addProcessRecord(String eventId, String loginUserId, String loginUserName, String processType, String processDesc) {
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        //新增一条处理记录
        TbCdcewOutpatientEventRecord record = new TbCdcewOutpatientEventRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outpatient_event_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);
        record.setProcessDesc(processDesc);
        record.setProcessType(processType);

        tbCdcewOutpatientEventRecordMapper.insert(record);
    }

    @Override
    public void updateCheckedLevel(int checkedLevel, String eventId) {
        //1,更新状态
        log.info("门诊事件更新事件审查状态：事件ID-{}: 审查状态-{}", eventId, checkedLevel);
        tbCdcewOutpatientWarningEventMapper.updateCheckedLevelById(checkedLevel, eventId);
    }

    @Override
    public TbCdcewOutpatientWarningEvent getCdcWaringEventByEventId(String eventId) {
        return tbCdcewOutpatientWarningEventMapper.findById(eventId);
    }

    @Override
    public List<AgeGroupVO> ageDistribution(String eventId, String beginDate, String endDate, AgeQueryDto ageQueryDto) {

        List<OutpatientRecordVO> outpatientRecordVOList = hisMedicalInfoService.getOutpatientRecordByEventId(eventId, TimeConstant.formatDate(beginDate), TimeConstant.formatDate(endDate));

        List<Double> ageList = outpatientRecordVOList.stream()
                .map(OutpatientRecordVO::getDiagnoseAge)
                .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                .map(Double::parseDouble).collect(Collectors.toList());

        List<AgeGroupVO> result = new ArrayList<>();
        Integer type = ageQueryDto.getAgeGroupType();
        if (1 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, Arrays.asList(Age.kernelAgeRange));
        }
        if (2 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, Arrays.asList(Age.secondAgeRange));
        }
        if (3 == type) {
            result = AgeGroupUtils.getAgeGroupResult(ageList, ageQueryDto.getAgeGroupList());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getWarningEventResourceDistribution(String eventId, String startTime, String endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime;
        Date formatEndTime;
        try {
            formatStartTime = simpleDateFormat.parse(startTime);
            formatEndTime = simpleDateFormat.parse(endTime);
        } catch (ParseException e) {
            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
        }
        int wmaCount = 0;
        int ghCount = 0;
        int esCount = 0;
        int phCount = 0;
        int scCount = 0;

        List<OutpatientRecordVO> outpatientRecordVOList = hisMedicalInfoService.getOutpatientRecordByEventId(eventId, formatStartTime, formatEndTime);
        List<OutpatientRecordVO> nullSourceTypeData = outpatientRecordVOList.stream().filter(outpatientRecordVO -> outpatientRecordVO.getSourceType() == null).collect(Collectors.toList());
        List<OutpatientRecordVO> notNullSourceTypeData = outpatientRecordVOList.stream().filter(outpatientRecordVO -> outpatientRecordVO.getSourceType() != null).collect(Collectors.toList());
        Map<String, List<OutpatientRecordVO>> resGroupBySourceType = notNullSourceTypeData.stream().collect(Collectors.groupingBy(OutpatientRecordVO::getSourceType));
        List<Map<String, Object>> resultList = new ArrayList<>();
        resGroupBySourceType.forEach((s, list) -> {
            Map<String, Object> map = new HashMap<>();
            map.put("sourceType", s);
            map.put("cn", list.size());
            resultList.add(map);
        });

        for (Map<String, Object> tempMap : resultList) {
            Object type = tempMap.get("sourceType");
            if (!Objects.isNull(type)) {
                if (String.valueOf(DataSourceTypeEnum.WISDOM_MEDICAL_ASSISTANT.getValue()).equals(type.toString())) {
                    wmaCount = wmaCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (DataSourceTypeEnum.GRADE_HOSPITAL_OUTPATIENT.getStrValue().equals(type.toString())
                        || String.valueOf(DataSourceTypeEnum.GRADE_HOSPITAL_IN_HOSPITAL.getValue()).equals(type.toString())) {
                    ghCount = ghCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.EPIDEMIOLOGICAL_SURVEY.getValue()).equals(type.toString())) {
                    esCount = esCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.GRADE_PHARMACY.getValue()).equals(type.toString())) {
                    phCount = phCount + Integer.parseInt(tempMap.get("cn").toString());
                } else if (String.valueOf(DataSourceTypeEnum.GRADE_SCHOOL.getValue()).equals(type.toString())) {
                    scCount = scCount + Integer.parseInt(tempMap.get("cn").toString());
                }
            }
        }

        List<Map<String, Object>> returnList = new ArrayList<>();
        Map<String, Object> result1 = new HashMap<>();
        Map<String, Object> result2 = new HashMap<>();
        Map<String, Object> result3 = new HashMap<>();
        Map<String, Object> result4 = new HashMap<>();
        Map<String, Object> result5 = new HashMap<>();
        if (wmaCount > 0) {
            result1.put("name", MedDistributionEnum.COMMUNITY.getDesc());
            result1.put("value", wmaCount);
            returnList.add(result1);
        }
        if (ghCount > 0) {
            result2.put("name", MedDistributionEnum.HOSPITAL.getDesc());
            result2.put("value", ghCount);
            returnList.add(result2);
        }
        if (esCount > 0) {
            result3.put("name", MedDistributionEnum.LAB.getDesc());
            result3.put("value", esCount);
            returnList.add(result3);
        }
        if (phCount > 0) {
            result4.put("name", MedDistributionEnum.PHARMACY.getDesc());
            result4.put("value", phCount);
            returnList.add(result4);
        }
        if (scCount > 0) {
            result5.put("name", MedDistributionEnum.SCHOOL.getDesc());
            result5.put("value", scCount);
            returnList.add(result5);
        }
        if (nullSourceTypeData.size() > 0) {
            Map<String, Object> nullDataMap = new HashMap<>();
            nullDataMap.put("name", "未知");
            nullDataMap.put("value", nullSourceTypeData.size());
            returnList.add(nullDataMap);
        }
        return returnList;
    }

    @Override
    public TbCdcewOutpatientWarningEvent getOutpatientWaringEventByEventId(String eventId) {
        return tbCdcewOutpatientWarningEventMapper.findByEventId(eventId);
    }

    @Override
    public void addProcessRecord(String eventId, String loginUserId, String loginUserName, String recordType) {
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);

        //新增一条处理记录
        TbCdcewOutpatientEventRecord record = new TbCdcewOutpatientEventRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_poison_event_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);
        record.setProcessType(recordType);
        record.setProcessDesc(RecordTypeEnum.getNameByCode(recordType));

        //保存事件处理记录
        tbCdcewOutpatientEventRecordMapper.insertSelective(record);
    }

    @Override
    @Deprecated
    public List<Map<String, Object>> getMedOutCallCount(String eventId, String startTime, String endTime) {
        return new ArrayList<>();
    }

    @Override
    public List<EventAnalysisResultVO> getEventAnalysisResult(String eventId) {
        //1，获取研判信息
        List<TbCdcewOutpatientEventAnalysis> tbCdcewOutpatientEventAnalyses = tbCdcewOutpatientEventAnalysisMapper.selectByEventId(eventId);
        if (CollectionUtils.isEmpty(tbCdcewOutpatientEventAnalyses)) {
            return Collections.EMPTY_LIST;
        }

        //2，获取附件信息
        List<EventAnalysisResultVO> resultList = new ArrayList<>();
        for (TbCdcewOutpatientEventAnalysis tbCdcewOutpatientEventAnalysis : tbCdcewOutpatientEventAnalyses) {
            List<TbCdcAttachment> tbCdcewOutpatientEventAnalysisAttachments = tbCdcewOutpatientEventAnalysisAttachmentMapper.selectByOutpatientAnalysisId(tbCdcewOutpatientEventAnalysis.getId());
            EventAnalysisResultVO eventAnalysisResultVO = EventAnalysisResultVO.fromEntity(tbCdcewOutpatientEventAnalysis);
            eventAnalysisResultVO.setAttachmentList(tbCdcewOutpatientEventAnalysisAttachments);
            resultList.add(eventAnalysisResultVO);
        }
        return resultList;
    }

    @Override
    public List<CascadeVO> getOutpatientNameListByLoginUserId(String loginUserId) {
        List<CascadeVO> resultList;
        List<CascadeVO> restList = getOutpatientNameList();

//        PersonDataAuthPo personDataAuthPo = businessPersonService.getOutpatientDataAuthByLoginUserId(loginUserId);
//        if (personDataAuthPo.getIsPlatformAdmin()) {
//            // 传染病数据权限过滤  过滤第二层级的数据
//            List<TbCdcewPersonDataAuth> infectedDataAuthByLoginUserId = personDataAuthPo.getList();
//            List<String> collect = infectedDataAuthByLoginUserId.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());
//            resultList = restList.stream().filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
//            return resultList;
//        }
        List<String> collect = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.OUTPATIENT.getType());
        resultList = restList.stream().filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<CascadeVO> getOutpatientNameList() {
        List<CascadeVO> outpatientTypeList = restService.getOutpatientType();
        return outpatientTypeList;
    }

    @Override
    public OutpatientEventTypeCountVO getEventTypeCountVO(String loginUserId, String loginUserName) {
        OutpatientEventTypeCountVO eventTypeCountVO = new OutpatientEventTypeCountVO();
        UapOrgPo userOrg = restService.getUserOrg(loginUserName);

        List<String> symptomTypeCollection = new ArrayList<>();
        //数据权限过滤数据
//        PersonDataAuthPo personDataAuthPo = businessPersonService.getOutpatientDataAuthByLoginUserId(loginUserId);
//        // 预警管理员需要做数据权限
//        if (personDataAuthPo.getIsPlatformAdmin()) {
//            List<TbCdcewPersonDataAuth> syndromeDataAuthByLoginUserId = personDataAuthPo.getList();
//            symptomTypeCollection = syndromeDataAuthByLoginUserId.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());
//            symptomTypeCollection.add("");
//        }
        symptomTypeCollection = businessPersonService.getConfiguredDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.OUTPATIENT.getType());

        List<EventTypeCountDTO> eventTypeCountDTOList = tbCdcewOutpatientWarningEventMapper.getOutpatientEventCountDto(userOrg.getCityCode(), userOrg.getDistrictCode(), symptomTypeCollection);

        eventTypeCountDTOList.forEach(eventTypeCountDto -> {
            String eventType = eventTypeCountDto.getEventType();
            Integer count = eventTypeCountDto.getCount();

            if (OutpatientTypeEnum.FEVER.getCode().equals(eventType)) {
                eventTypeCountVO.setFeverEventCount(count);
            }
            if (OutpatientTypeEnum.BOWEL.getCode().equals(eventType)) {
                eventTypeCountVO.setBowelEventCount(count);
            }
        });
        return eventTypeCountVO;
    }

    private CascadeVO convertObject(CascadeVO restVO) {
        CascadeVO resultVO = new CascadeVO();
        resultVO.setValue(restVO.getValue());
        resultVO.setLabel(restVO.getLabel());
        if (!CollectionUtils.isEmpty(restVO.getChildren())) {
            resultVO.setChildren(new ArrayList<>());
            Collections.sort(restVO.getChildren());
            restVO.getChildren().forEach(childRestVO -> {
                CascadeVO childResultVO = convertObject(childRestVO);
                resultVO.getChildren().add(childResultVO);
            });
        }
        return resultVO;
    }

    @Override
    public List<String> getOutpatientRoleIdsByPositionAndType(String positionCode, String positionTypeCode) {

        List<TbCdcewPosition> positions = tbCdcewPositionMapper.getPositionsByPositionCodeAndType(positionCode, positionTypeCode);

        Map<String, List<TbCdcewPosition>> appCodeMap = positions.stream().collect(Collectors.groupingBy(TbCdcewPosition::getUapAppCode));
        List<String> result = new ArrayList<>();
        appCodeMap.forEach((k, v) -> {
            List<UapRole> uapRoleList = restService.getUapRoleListByAppCode(k);

            v.forEach(tbCdcewPosition -> {
                String id = tbCdcewPosition.getId();
                String uapRoleName = tbCdcewPosition.getUapRoleName();

                List<UapRole> subUapRoleList = uapRoleList.stream().filter(uapRole -> uapRoleName.equals(uapRole.getName())).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(subUapRoleList)) {
                    UapRole uapRole = subUapRoleList.get(0);

                    positions.forEach(item -> {
                        if (item.getId().equals(id)) {
                            result.add(uapRole.getId());
                        }
                    });
                }
            });
        });

        return result;
    }
}

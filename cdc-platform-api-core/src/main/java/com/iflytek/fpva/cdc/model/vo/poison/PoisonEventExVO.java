package com.iflytek.fpva.cdc.model.vo.poison;

import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.patient.DocMedDistribution;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PoisonEventExVO {

    @ApiModelProperty("事件详情")
    private EventVO eventVO;

    @ApiModelProperty("年龄/性别统计")
    private HumanDistributionCountVO countVO;

    @ApiModelProperty("通知结果统计")
    private List<MatrixVO> noteList;

    @ApiModelProperty("病例列表")
    private List<RegionVO<PoisonRecordVO>> patientList;

    @ApiModelProperty("学生列表")
    private List<RegionVO<StudentVO>> studentList;

    @ApiModelProperty("医生分布统计")
    private List<DocMedDistribution> docMedDistributionList;

    @ApiModelProperty("职业分布")
    private List<DistributionVO> careerDistributionVO;
}

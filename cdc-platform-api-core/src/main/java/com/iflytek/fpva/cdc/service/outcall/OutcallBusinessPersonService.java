package com.iflytek.fpva.cdc.service.outcall;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallBusinessPerson;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.fpva.cdc.model.vo.outcall.OutCallBusinessPersonVO;

import java.util.List;

/**
 * <p>
 * 外呼通知 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface OutcallBusinessPersonService extends IService<TbCdcewOutcallBusinessPerson> {

    List<TbCdcewOutcallBusinessPerson> findByEventIdAndConfigType(String eventId, String warningType);

    List<TbCdcewOutcallBusinessPerson> findByOutCallStatusAndOutCallBatchIdIsNotNull(Integer code, Integer personType, String warningType);

    List<OutCallBusinessPersonVO> findOutCallResult(String eventId, String warningType, Integer type);
}

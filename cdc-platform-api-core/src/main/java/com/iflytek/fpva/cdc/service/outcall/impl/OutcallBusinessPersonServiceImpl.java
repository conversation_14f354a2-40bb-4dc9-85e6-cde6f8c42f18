package com.iflytek.fpva.cdc.service.outcall.impl;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallBusinessPerson;
import com.iflytek.fpva.cdc.mapper.outcall.TbCdcewOutcallBusinessPersonMapper;
import com.iflytek.fpva.cdc.model.vo.outcall.OutCallBusinessPersonVO;
import com.iflytek.fpva.cdc.service.outcall.OutcallBusinessPersonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 外呼通知 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Service
public class OutcallBusinessPersonServiceImpl extends ServiceImpl<TbCdcewOutcallBusinessPersonMapper, TbCdcewOutcallBusinessPerson> implements OutcallBusinessPersonService {
    @Resource
    private TbCdcewOutcallBusinessPersonMapper outcallBusinessPersonMapper;

    @Override
    public List<TbCdcewOutcallBusinessPerson> findByEventIdAndConfigType(String eventId, String warningType) {
        return outcallBusinessPersonMapper.findByEventIdAndConfigType(eventId,warningType);
    }

    @Override
    public List<TbCdcewOutcallBusinessPerson> findByOutCallStatusAndOutCallBatchIdIsNotNull(Integer code, Integer personType, String warningType) {
        return outcallBusinessPersonMapper.findByOutCallStatusAndOutCallBatchIdIsNotNull(code,personType,warningType);
    }

    @Override
    public List<OutCallBusinessPersonVO> findOutCallResult(String eventId, String warningType, Integer personType) {
        return outcallBusinessPersonMapper.findOutCallResult(eventId,warningType,personType);
    }
}

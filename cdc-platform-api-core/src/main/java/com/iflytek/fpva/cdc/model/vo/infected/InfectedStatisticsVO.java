package com.iflytek.fpva.cdc.model.vo.infected;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InfectedStatisticsVO {

    //待处理事件数
    @ApiModelProperty("待处理事件数")
    private int pending;

    //处理中事件数
    @ApiModelProperty("处理中事件数")
    private int processing;

    //已完成事件数
    @ApiModelProperty("已完成事件数")
    private int finished;

    // 响应超时事件数
    @ApiModelProperty("响应超时事件数")
    private int responseTimeout;

    // 处置超时事件数
    @ApiModelProperty("处置超时事件数")
    private int processTimeout;

    // 人工排除数
    @ApiModelProperty("人工排除数")
    private int removedManual;

    // AI排除数
    @ApiModelProperty("AI排除数")
    private int removedAi;

    // 阳性事件数
    @ApiModelProperty("阳性事件数")
    private int positive;

    // 全部事件数
    @ApiModelProperty("全部事件数")
    private int total;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * 2020-12-25 该字段表示登录后监管大屏显示的地区
     */
    private String areaName;

}

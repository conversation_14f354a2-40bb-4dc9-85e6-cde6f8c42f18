package com.iflytek.fpva.cdc.model.vo.monitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2023/10/19 15:41
 * @description:SignalRecallRateVO
 */
@Data
public class SignalRecallRateVO {
    @ApiModelProperty("日期字符串")
    private String dateStr;

    @ApiModelProperty("数据生成时间")
    private Date updateTime;

    @ApiModelProperty("症候群类型")
    private String symptomType;

    @ApiModelProperty("症候群名称")
    private String syndromeName;


    @ApiModelProperty("监测信号数")
    private Long monitorNum;

    @ApiModelProperty("预警信号数")
    private Long warningNum;

    @ApiModelProperty("合理信号数")
    private Long reasonableNum;



    @ApiModelProperty("监测信号召回率")
    private String monitorRecallRate;

    @ApiModelProperty("监测信号准确率")
    private String monitorRightRate;

    @ApiModelProperty("预警信号召回率")
    private String warningRecallRate;

    @ApiModelProperty("预警信号准确率")
    private String warningRightRate;



}

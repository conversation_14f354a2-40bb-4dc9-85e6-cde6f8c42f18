package com.iflytek.fpva.cdc.model.vo.symptom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SymptomEventTypeTrendVO {

    @ApiModelProperty(value = "症状编码")
    private String symptomCode;

    @ApiModelProperty(value = "症状名称")
    private String symptomName;

    @ApiModelProperty(value = "信号趋势列表")
    private List<SymptomEventTrendVO> eventTrendList;
}

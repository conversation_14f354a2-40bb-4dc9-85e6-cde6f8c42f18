package com.iflytek.fpva.cdc.controller.common;

import com.iflytek.fpva.cdc.annotation.LogExportAnnotation;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.model.vo.PersonVO;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件处理控制类
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "文件处理")
public class FileController {

    @Autowired
    private FileService fileService;
    @Resource
    private RestService restService;

    @PostMapping(value = "/pt/v1/file/analysis/upload")
    @ApiOperation("上传专家研判附件")
    public List<String> analysisUpload(@RequestParam(value = "files") MultipartFile[] files) {
        if (null == files || files.length == 0) {
            throw new MedicalBusinessException("11441003", "上传文件为空！");
        }
        return fileService.uploads(files);
    }

    @PostMapping(value = "/pt/v1/file/outCall/upload")
    @ApiOperation("上传外呼对象")
    public List<String> outCallUpload(@RequestParam(value = "file") MultipartFile file) {
        return fileService.upload(file);
    }

    @GetMapping("/pt/v1/file/outCallDownloadTemplate")
    @ApiOperation("下载外呼对象模板")
    @LogExportAnnotation
    public ResponseEntity<byte[]> outCallDownloadTemplate(@RequestParam String loginUserId) {
        return FileUtils.exportExcel(new ArrayList<>(), PersonVO.class, true, CommonConstants.OUTCALL_TEMPLATE_FILE_NAME+".xlsx");
    }

    @GetMapping("/pt/v1/file/download")
    @ApiOperation("下载文件")
    @LogExportAnnotation
    public ResponseEntity<byte[]> outCallDownload(@RequestParam(value = "id") String id, @RequestParam String loginUserId) {
        return fileService.download(id);
    }

    @GetMapping("/pt/v1/file/downloadFromAdmin")
    @ApiOperation("下载文件")
    @LogExportAnnotation
    public ResponseEntity<byte[]> downloadFromAdmin(@RequestParam(value = "id") String attachmentId, @RequestParam String loginUserId) {
        return restService.downloadAttachmentFromAdmin(attachmentId, loginUserId);
    }


}

package com.iflytek.fpva.cdc.model.vo.outcall;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
@Data
public class OutCallBusinessPersonVO {


    @ApiModelProperty("拨出人员")
    private String creatorName;
    @ApiModelProperty("拨出机构名称")
    private String creatorOrgName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("外呼时间")
    private Date createTime;



    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty(value = "外呼类型 1-信号病历患者 2-关联人员 3-疾控 4-防保科")
    private Integer personType;

    @ApiModelProperty("通知对象机构")
    private String statDimName;

    @ApiModelProperty("姓名")
    private String personName;

    @ApiModelProperty("职务")
    private String positionName;

    @ApiModelProperty("接通状态 1-已接通; 2-未接通; 3-失败")
    private Integer connectStatus;

    @ApiModelProperty("音频URL")
    private String audioUrl;
    @ApiModelProperty("外呼结果")
    private String callResult;
}
package com.iflytek.fpva.cdc.entity.preventionControl;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcew_prevention_control_source
 * <AUTHOR>
@Data
public class TbCdcewPreventionControlSource implements Serializable {
    private String id;

    private Date fullDate;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    private String statDimId;

    private String statDimName;

    private String preventionControlCode;

    private String preventionControlName;

    private Integer recordCaseCnt;

    private Date createTime;

    private String eventType;

    private static final long serialVersionUID = 1L;
}

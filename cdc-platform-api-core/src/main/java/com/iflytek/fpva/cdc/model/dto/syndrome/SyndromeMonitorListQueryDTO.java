package com.iflytek.fpva.cdc.model.dto.syndrome;

import com.iflytek.fpva.cdc.entity.RegionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class SyndromeMonitorListQueryDTO {

    private int pageIndex;

    @Range(max = 40)
    private int pageSize;

    @ApiModelProperty("DJYY-等级医院；JCYL-基层医疗；XXDW-学校单位；JDQY-街道区域")
    private String eventType;

    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date minFullDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date maxFullDate;

    @ApiModelProperty("症状")
    private Collection<String> symptom;

    @ApiModelProperty("1.数据编号;2:预警信号编号")
    private String numType;

    @ApiModelProperty("编号")
    private String num;



    @ApiModelProperty("监测信号编号")
    private String monitorSetId;

    @ApiModelProperty("预警信号编号")
    private String eventNum;

    @ApiModelProperty("信号标注")
    private String markLevel;

    @ApiModelProperty("信号标注审核")
    private String auditLevel;

    @ApiModelProperty("信号审核结果")
    private String auditResult;

    @ApiModelProperty("算法是否预警，1：是；0：否")
    private String preResult;

    @ApiModelProperty("算法是否监测，1：是；0：否")
    private String monitorResult;


    @ApiModelProperty("id|数据编号")
    private String id;


    @ApiModelProperty("区域列表")
    List<RegionInfo> regionInfoList;

    @ApiModelProperty(hidden = true)
    private String loginUserId;

    @ApiModelProperty("只看预警信号，1：是")
    private String onlyEventFlag;

    @ApiModelProperty("是否具有审核人权限，1：是")
    private String auditFlag;

}

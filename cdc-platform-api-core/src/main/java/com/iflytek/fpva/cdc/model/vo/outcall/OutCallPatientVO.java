package com.iflytek.fpva.cdc.model.vo.outcall;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Date;
@Data
public class OutCallPatientVO {

    @ApiModelProperty("拨出人员")
    private String creatorName;
    @ApiModelProperty("拨出机构名称")
    private String creatorOrgName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("外呼时间")
    private Date createTime;

    @ApiModelProperty("id")
    private String id;


    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("接通状态 1-已接通; 2-未接通; 3-失败")
    private Integer connectStatus;

    @ApiModelProperty("音频URL")
    private String audioUrl;

    @ApiModelProperty("外呼结果")
    private String callResult;

    @ApiModelProperty("外呼关联人群结果列表")
    private List<OutCallPatientRelationVO> patientRelationList;

}

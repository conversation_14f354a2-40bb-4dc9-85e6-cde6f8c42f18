package com.iflytek.fpva.cdc.service.outcall.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.fpva.cdc.constant.enums.CallConnectStatusEnum;
import com.iflytek.fpva.cdc.constant.enums.MedicalCallStatusEnum;
import com.iflytek.fpva.cdc.constant.enums.MonitorCategoryEnum;
import com.iflytek.fpva.cdc.entity.TbCdcHisMedicalExtend;
import com.iflytek.fpva.cdc.entity.TbCdcewOutcallPatient;
import com.iflytek.fpva.cdc.entity.TbCdcewSympRecordExtend;
import com.iflytek.fpva.cdc.mapper.common.TbCdcHisMedicalExtendMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewHisMedicalInfoMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympRecordExtendMapper;
import com.iflytek.fpva.cdc.model.vo.MedicalInfoVO;
import com.iflytek.fpva.cdc.model.vo.school.SchoolPersonSymptomRecordVO;
import com.iflytek.fpva.cdc.outbound.constant.OutboundConfig;
import com.iflytek.fpva.cdc.outbound.constant.enums.ConnectStatusEnum;
import com.iflytek.fpva.cdc.outbound.constant.enums.ExecStatusEnum;
import com.iflytek.fpva.cdc.outbound.model.dto.input.BatchList;
import com.iflytek.fpva.cdc.outbound.model.dto.output.BatchDetailRs;
import com.iflytek.fpva.cdc.outbound.model.dto.output.BatchListRs;
import com.iflytek.fpva.cdc.outbound.model.dto.output.Record;
import com.iflytek.fpva.cdc.outbound.model.dto.output.RecordDetailRs;
import com.iflytek.fpva.cdc.outbound.service.QueryOutBoundService;
import com.iflytek.fpva.cdc.outbound.util.Response;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.service.outcall.OutboundResultSyncService;
import com.iflytek.fpva.cdc.service.outcall.OutcallOrginalResultService;
import com.iflytek.fpva.cdc.service.outcall.OutcallPatientService;
import com.iflytek.fpva.cdc.service.symptom.SympOutCallService;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PatientOutBoundResultSyncImpl implements OutboundResultSyncService {

    @Resource
    private QueryOutBoundService queryOutBoundService;

    @Resource
    private TbCdcewHisMedicalInfoMapper hisMedicalInfoMapper;

    @Resource
    private TbCdcHisMedicalExtendMapper cdcHisMedicalExtendMapper;

    @Resource
    private TbCdcewSympRecordExtendMapper tbCdcewSympRecordExtendMapper;

    @Resource
    private OutboundConfig outboundConfig;

    @Resource
    SympOutCallService sympOutCallService;

    @Resource
    private FileService fileService;

    @Resource
    private OutcallPatientService outcallPatientService;


    @Resource
    private OutcallOrginalResultService outcallOrginalResultService;

    @Override
    public int sync(Integer personType, Integer monitorType) {
        if (Integer.valueOf(MonitorCategoryEnum.SCHOOL.getId()).equals(monitorType)){
            return syncSchoolCallResult(personType);
        }
        return syncSympCallResult(personType);
    }

    private int syncSchoolCallResult(Integer personType) {
        AtomicInteger atomicInteger = new AtomicInteger(0);

        List<TbCdcewSympRecordExtend> hisMedicalExtends = tbCdcewSympRecordExtendMapper.findByOutCallStatusAndOutCallBatchIdIsNotNull(MedicalCallStatusEnum.CALLING.getCode());
        if (CollectionUtils.isEmpty(hisMedicalExtends)) {
            return 0;
        }
        log.info("查询到学校症状-信号病例外呼正在执行中数量：{}", hisMedicalExtends.size());
        hisMedicalExtends.forEach(medicalExtend -> {
            Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(medicalExtend.getCallBatchId());
            BatchDetailRs batchDetailRs = batchDetailRsResponse.getData();
            if (batchDetailRsResponse.isSuccess() && null != batchDetailRs && batchDetailRs.getExecStatus().equals(ExecStatusEnum.COMPLETED.getCode())) {
                Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(BatchList.builder().batch(medicalExtend.getCallBatchId()).build());
                Record record = batchListRsResponse.getData().getData().get(0);
                TbCdcewSympRecordExtend extend = new TbCdcewSympRecordExtend();
                boolean success = ConnectStatusEnum.NORMAL_ANSWER.getCode().equals(record.getEndNode());
                LinkedHashMap<String, String> callResult = new LinkedHashMap<>(8);
                if (success) {
                    List<SchoolPersonSymptomRecordVO> cdcHisMedicalInfos = sympOutCallService.getResident(medicalExtend.getEventId());
                    Map<String, List<SchoolPersonSymptomRecordVO>> medicalInfoMap = cdcHisMedicalInfos.stream().collect(Collectors.groupingBy(SchoolPersonSymptomRecordVO::getId));
                    List<SchoolPersonSymptomRecordVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(record.getRelationId(), v -> new ArrayList<>());
                    callResult.put("外呼结果", record.getCallResult());
                    if (CollectionUtils.isNotEmpty(medicalInfoVOS)) {
                        SchoolPersonSymptomRecordVO medicalInfo = medicalInfoVOS.get(0);
                        callResult.put("症状问询", record.getWetherDiarrhea()+medicalInfo.getSymptomContent()+"症状");
                    }
                    extend.setConnectStatus(CallConnectStatusEnum.CONNECTED.getCode());
                    extend.setAudioUrl(getAudioUrl(record));

                    //获取音频地址需要重试操作(外呼返回音频地址有延迟)，如果超过30分钟仍然没有结果就终止
                    Long taskTime = batchDetailRs.getTaskTime();
                    long gapMinute = (System.currentTimeMillis() - Optional.ofNullable(taskTime).orElse(0L)) / 1000 / 60;
                    if (StringUtils.isNotBlank(extend.getAudioUrl()) || gapMinute >= 30 ) {
                        extend.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                    }
                } else {
                    callResult.put("外呼结果", record.getCallResult());
                    extend.setConnectStatus(CallConnectStatusEnum.UNCONNECTED.getCode());
                    extend.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                }
                extend.setId(medicalExtend.getId());
                extend.setUpdateTime(new Date());
                String callResultStr = JSON.toJSONString(callResult);
                extend.setCallResult(callResultStr);

                tbCdcewSympRecordExtendMapper.updateById(extend);
                atomicInteger.incrementAndGet();
            }
        });
        log.info("更新学校症状-信号病例外呼结果数量：{}", atomicInteger.get());
        return atomicInteger.get();
    }

    private int syncSympCallResult(Integer personType) {
        AtomicInteger atomicInteger = new AtomicInteger(0);

        List<TbCdcHisMedicalExtend> hisMedicalExtends = cdcHisMedicalExtendMapper.findByOutCallStatusAndOutCallBatchIdIsNotNull(MedicalCallStatusEnum.CALLING.getCode());
        if (CollectionUtils.isEmpty(hisMedicalExtends)) {
            return 0;
        }
        log.info("查询到症候群-信号病例外呼正在执行中数量：{}", hisMedicalExtends.size());
        hisMedicalExtends.forEach(medicalExtend -> {
            Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(medicalExtend.getCallBatchId());
            BatchDetailRs batchDetailRs = batchDetailRsResponse.getData();
            if (batchDetailRsResponse.isSuccess() && null != batchDetailRs && batchDetailRs.getExecStatus().equals(ExecStatusEnum.COMPLETED.getCode())) {
                Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(BatchList.builder().batch(medicalExtend.getCallBatchId()).build());
                Record record = batchListRsResponse.getData().getData().get(0);
                TbCdcHisMedicalExtend extend = new TbCdcHisMedicalExtend();
                boolean success = ConnectStatusEnum.NORMAL_ANSWER.getCode().equals(record.getEndNode());
                LinkedHashMap<String, String> callResult = new LinkedHashMap<>(8);
                if (success) {
                    List<MedicalInfoVO> cdcHisMedicalInfos = hisMedicalInfoMapper.findByWarnEventIdAndWarnDetailIdAndSourceKey(medicalExtend.getEventId(), medicalExtend.getDetailId(), medicalExtend.getCallBatchId(), Arrays.asList(medicalExtend.getSourceKey()));
                    Map<String, List<MedicalInfoVO>> medicalInfoMap = cdcHisMedicalInfos.stream().collect(Collectors.groupingBy(MedicalInfoVO::getSourceKey));
                    List<MedicalInfoVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(record.getRelationId(), v -> new ArrayList<>());
                    callResult.put("外呼结果", record.getCallResult());
                    if (CollectionUtils.isNotEmpty(medicalInfoVOS)) {
                        MedicalInfoVO medicalInfo = medicalInfoVOS.get(0);
                        callResult.put("症状问询", record.getWetherDiarrhea()+medicalInfo.getSymptomType()+"症状");
                    }
                    extend.setConnectStatus(CallConnectStatusEnum.CONNECTED.getCode());
                    extend.setAudioUrl(getAudioUrl(record));

                    //获取音频地址需要重试操作(外呼返回音频地址有延迟)，如果超过30分钟仍然没有结果就终止
                    Long taskTime = batchDetailRs.getTaskTime();
                    long gapMinute = (System.currentTimeMillis() - Optional.ofNullable(taskTime).orElse(0L)) / 1000 / 60;
                    if (StringUtils.isNotBlank(extend.getAudioUrl()) || gapMinute >= 30 ) {
                        extend.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                    }
                } else {
                    callResult.put("外呼结果", record.getCallResult());
                    extend.setConnectStatus(CallConnectStatusEnum.UNCONNECTED.getCode());
                    extend.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                }
                extend.setId(medicalExtend.getId());
                extend.setUpdateTime(new Date());
                String callResultStr = JSON.toJSONString(callResult);
                extend.setCallResult(callResultStr);

                cdcHisMedicalExtendMapper.updateById(extend);
                atomicInteger.incrementAndGet();
            }
        });
        log.info("更新症候群-信号病例外呼结果数量：{}", atomicInteger.get());
        return atomicInteger.get();
    }

    public String getAudioUrl(Record record) {
        String audioUrl = null;
        Response<RecordDetailRs> recordDetailRsResponse = queryOutBoundService.queryRecordDetail(record.getRecordId());
        if (recordDetailRsResponse.isSuccess() && com.iflytek.fpva.cdc.util.StringUtils.isNotBlank(recordDetailRsResponse.getData().getLocalUrl())) {
            String recordUrl = outboundConfig.getRecordUrl();
            String path = URLUtil.getPath(recordDetailRsResponse.getData().getLocalUrl());
            String fileUrl = recordUrl + path;
            if (com.iflytek.fpva.cdc.util.StringUtils.isBlank(recordUrl)) {
                fileUrl = recordDetailRsResponse.getData().getLocalUrl();
            }
            audioUrl = fileService.storeMediaToFileServer(fileUrl);
        }
        return audioUrl;
    }


    @Override
    public int syncNew(Integer personType, String configType) {
        AtomicInteger atomicInteger = new AtomicInteger(0);

        List<TbCdcewOutcallPatient> patientList = outcallPatientService.findByOutCallStatusAndOutCallBatchIdIsNotNull(MedicalCallStatusEnum.CALLING.getCode(),configType);
        if (CollectionUtils.isEmpty(patientList)) {
            return 0;
        }
        log.info("查询到{}-信号病例外呼正在执行中数量：{}", configType, patientList.size());
        patientList.forEach(patient -> {
            Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(patient.getCallBatchId());
            outcallOrginalResultService.saveData(patient.getCallBatchId(), JSONObject.toJSONString(batchDetailRsResponse),"queryBatchDetail");
            BatchDetailRs batchDetailRs = batchDetailRsResponse.getData();
            if (batchDetailRsResponse.isSuccess() && null != batchDetailRs && batchDetailRs.getExecStatus().equals(ExecStatusEnum.COMPLETED.getCode())) {
                Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(BatchList.builder().batch(patient.getCallBatchId()).build());
                outcallOrginalResultService.saveData(patient.getCallBatchId(), JSONObject.toJSONString(batchListRsResponse),"queryBatchList");

                Record record = batchListRsResponse.getData().getData().get(0);
                boolean success = ConnectStatusEnum.NORMAL_ANSWER.getCode().equals(record.getEndNode());
                LinkedHashMap<String, String> callResult = new LinkedHashMap<>(8);
                if (success) {
                    callResult.put("外呼结果", record.getCallResult());

                    callResult.put("症状问询", record.getWetherDiarrhea()+patient.getSymptomTagList()+"症状");
                    patient.setConnectStatus(CallConnectStatusEnum.CONNECTED.getCode());
                    patient.setAudioUrl(getAudioUrl(record));

                    //获取音频地址需要重试操作(外呼返回音频地址有延迟)，如果超过30分钟仍然没有结果就终止
                    Long taskTime = batchDetailRs.getTaskTime();
                    long gapMinute = (System.currentTimeMillis() - Optional.ofNullable(taskTime).orElse(0L)) / 1000 / 60;
                    if (StringUtils.isNotBlank(patient.getAudioUrl()) || gapMinute >= 30 ) {
                        patient.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                    }
                } else {
                    callResult.put("外呼结果", record.getCallResult());
                    patient.setConnectStatus(CallConnectStatusEnum.UNCONNECTED.getCode());
                    patient.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                }
                patient.setUpdateTime(LocalDateTime.now());
                String callResultStr = JSON.toJSONString(callResult);
                patient.setCallResult(callResultStr);

                outcallPatientService.updateById(patient);
                atomicInteger.incrementAndGet();
            }
        });
        log.info("更新{}-信号病例外呼结果数量：{}",configType, atomicInteger.get());
        return atomicInteger.get();
    }
}

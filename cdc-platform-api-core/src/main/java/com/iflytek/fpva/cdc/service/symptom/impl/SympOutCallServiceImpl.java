package com.iflytek.fpva.cdc.service.symptom.impl;

import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.common.TbCdcCallTemplateMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewBusinessPersonMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewHisMedicalInfoMapper;
import com.iflytek.fpva.cdc.mapper.symptom.*;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.model.dto.BusinessPersonOutCallDTO;
import com.iflytek.fpva.cdc.model.dto.CreateCallTaskDto;
import com.iflytek.fpva.cdc.model.dto.Resident;
import com.iflytek.fpva.cdc.model.dto.school.RecordCountDTO;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.po.UapUserPo;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.school.SchoolPersonSymptomRecordVO;
import com.iflytek.fpva.cdc.outbound.model.dto.input.CallTask;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Dweller;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Recall;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Sms;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Var;
import com.iflytek.fpva.cdc.outbound.model.dto.output.CreateCallRs;
import com.iflytek.fpva.cdc.outbound.service.CreateOutboundService;
import com.iflytek.fpva.cdc.outbound.util.Response;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.symptom.SympEventService;
import com.iflytek.fpva.cdc.service.symptom.SympOutCallService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.StorageClientUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SympOutCallServiceImpl implements SympOutCallService {
    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    private StorageClientUtil storageClientUtil;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    TbCdcewSympMedRelationMapper tbCdcewSympMedRelationMapper;

    @Resource
    TbCdcewSympWarningEventMapper tbCdcewSympWarningEventMapper;

    @Resource
    TbCdcewSympWarningDetailMapper tbCdcewSympWarningDetailMapper;

    @Resource
    private CreateOutboundService createOutboundService;

    @Resource
    TbCdcewSympOutcallBatchMapper outcallBatchMapper;

    @Resource
    TbCdcewSympRecordExtendMapper extendMapper;

    @Resource
    private FileService fileService;

    @Resource
    private TbCdcewHisMedicalInfoMapper hisMedicalInfoMapper;

    @Resource
    private TbCdcewScSymptomRecordMapper tbCdcewScSymptomRecordMapper;

    @Resource
    private TbCdcewSympEventPersonRelationMapper tbCdcewSympEventPersonRelationMapper;

    @Resource
    private TbCdcCallTemplateMapper tbCdcCallTemplateMapper;
    @Resource
    TbCdcewBusinessPersonMapper businessPersonMapper;

    @Resource
    SympEventService sympEventService;

    @Resource
    TbCdcConfigService tbCdcConfigService;

    @Resource
    TbCdcewSympOutcallNoticeMapper tbCdcewSympOutcallNoticeMapper;

    @Resource
    RestService restService;

    @Value("${fpva.outCall.appName}")
    private String appName;

    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;
    @Value("${outcall.smsId:12107}")
    private String smsId;



    @Override
    public List<Response<CreateCallRs>> createCallSpeechTaskForPatient(CreateCallTaskDto createCallTaskDto) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();
        //2，从文件读取数据
        List<PatientSourceTypeVO> personVOList = getPatientVOS(createCallTaskDto);

        TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(createCallTaskDto.getEventId());
        TbCdcewSympWarningDetail detail = tbCdcewSympWarningDetailMapper.findFirstByEventId(createCallTaskDto.getEventId());
        List<SchoolPersonSymptomRecordVO> list = getResident(createCallTaskDto.getEventId());
        Map<String, List<SchoolPersonSymptomRecordVO>> medicalInfoMap = list.stream().collect(Collectors.groupingBy(SchoolPersonSymptomRecordVO::getId));

        List<TbCdcewSympRecordExtend> extendList = new ArrayList<>();
        residents.stream().forEach(resident -> {
            List<SchoolPersonSymptomRecordVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(resident.getSourceKey(), v -> null);
            if (CollectionUtils.isNotEmpty(medicalInfoVOS)
                    && CallConnectStatusEnum.UNCONNECTED.getCode().equals(medicalInfoVOS.get(0).getConnectStatus())) {
                SchoolPersonSymptomRecordVO medicalInfoVO = medicalInfoVOS.get(0);
                TbCdcewSympRecordExtend medicalExtend = new TbCdcewSympRecordExtend();
                medicalExtend.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_record_extend")));
                medicalExtend.setEventId(createCallTaskDto.getEventId());
                medicalExtend.setDetailId(detail.getId());
                medicalExtend.setPatientName(resident.getDwellerName());
                medicalExtend.setPatientPhone(resident.getTelephone());
                medicalExtend.setSourceKey(resident.getSourceKey());
                medicalExtend.setSourceType(resident.getSourceType());
                medicalExtend.setKeySymptomTagList(medicalInfoVO.getSymptomContent());
                medicalExtend.setOutPatientTime(medicalInfoVO.getOutPatientTime());
                extendList.add(medicalExtend);
            }
        });
        personVOList.stream().forEach(item -> {
            List<SchoolPersonSymptomRecordVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(item.getSourceKey(), v -> null);
            if (CollectionUtils.isNotEmpty(medicalInfoVOS)
                    && CallConnectStatusEnum.UNCONNECTED.getCode().equals(medicalInfoVOS.get(0).getConnectStatus())) {
                SchoolPersonSymptomRecordVO medicalInfoVO = medicalInfoVOS.get(0);
                TbCdcewSympRecordExtend medicalExtend = new TbCdcewSympRecordExtend();
                medicalExtend.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_record_extend")));
                medicalExtend.setEventId(createCallTaskDto.getEventId());
                medicalExtend.setDetailId(detail.getId());
                medicalExtend.setPatientName(item.getPatientName());
                medicalExtend.setPatientPhone(item.getPhone());
                medicalExtend.setSourceKey(medicalInfoVO.getId());
                medicalExtend.setSourceType(medicalInfoVO.getSourceType());
                medicalExtend.setKeySymptomTagList(medicalInfoVO.getSymptomContent());
                medicalExtend.setOutPatientTime(medicalInfoVO.getOutPatientTime());
                extendList.add(medicalExtend);
            }
        });

        if (CollectionUtils.isEmpty(extendList)) {
            return Arrays.asList(Response.error("没有外呼对象！"));
        }

        TbCdcewSympOutcallBatch TbCdcewSympOutcallBatch = new TbCdcewSympOutcallBatch();
        TbCdcewSympOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_outcall_batch")));
        TbCdcewSympOutcallBatch.setEventId(createCallTaskDto.getEventId());
        TbCdcewSympOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
        TbCdcewSympOutcallBatch.setCreateTime(new Date());
        TbCdcewSympOutcallBatch.setUpdateTime(new Date());

        List<Response<CreateCallRs>> responseList = new ArrayList<>();
        SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
        extendList.forEach(extend -> {
            String date = sp.format(Optional.ofNullable(extend.getOutPatientTime()).orElse(new Date()));
            // 症状长度过长时 外呼会报错  此处只取前两种症状
            String symptom;
            String[] split = extend.getKeySymptomTagList().split(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER);
            if (split.length > 2) {
                symptom = org.apache.commons.lang3.StringUtils.joinWith(CommonConstants.VERTICAL_COMMA, split[0], split[1]).concat("等");
            } else {
                symptom = extend.getKeySymptomTagList().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
            }

            CallTask callTask = getCallTask(createCallTaskDto, event.getStatDimName(), symptom, date, false);
            callTask.setDwellers(Arrays.asList(Dweller.builder().dwellerName(extend.getPatientName()).telephone(extend.getPatientPhone()).relationId(extend.getSourceKey()).build()));
            Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
            if (response.isSuccess()) {
                String batchId = response.getData().getBatch().get(0);
                extend.setCallBatchId(batchId);
                extend.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
                log.info("执行外呼成功: {}", response);
            } else {
                extend.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                log.error("执行外呼失败：{}", response);
            }
            extend.setCreateTime(new Date());
            extend.setBatchId(TbCdcewSympOutcallBatch.getId());
            responseList.add(response);
        });

        if (CollectionUtils.isNotEmpty(extendList)) {
            outcallBatchMapper.insert(TbCdcewSympOutcallBatch);
            saveDataToExtend(event, detail, extendList);
        }
        return responseList;
    }

    /**
     * 已经存在的更新，不存在的插入
     *
     * @param cdcWarningEvent
     * @param tbCdcWarningDetail
     * @param extendList
     */
    private void saveDataToExtend(TbCdcewSympWarningEvent cdcWarningEvent,
                                  TbCdcewSympWarningDetail tbCdcWarningDetail,
                                  List<TbCdcewSympRecordExtend> extendList) {
        List<TbCdcewSympRecordExtend> updateList = new ArrayList<>();
        List<TbCdcewSympRecordExtend> insertList = new ArrayList<>();
        List<TbCdcewSympRecordExtend> currentList = extendMapper.findByEventIdAndDetailId(cdcWarningEvent.getId(), tbCdcWarningDetail.getId());
        Map<String, List<TbCdcewSympRecordExtend>> hisMedicalExtendMap = currentList.stream().collect(Collectors.groupingBy(TbCdcewSympRecordExtend::getSourceKey));
        extendList.forEach(extendDTO -> {
            List<TbCdcewSympRecordExtend> existingExtendList = Optional.ofNullable(hisMedicalExtendMap).orElse(new HashMap<>())
                    .computeIfAbsent(extendDTO.getSourceKey(), v -> null);
            if (null != existingExtendList) {
                extendDTO.setId(existingExtendList.get(0).getId());
                updateList.add(extendDTO);
            } else {
                insertList.add(extendDTO);
            }
        });
        if (CollectionUtils.isNotEmpty(updateList)) {
            extendMapper.batchUpdate(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            extendMapper.insertList(insertList);
        }
    }

    // todo  根据外呼模板 选择对应的speechID
    private CallTask getCallTask(CreateCallTaskDto createCallTaskDto,
                                 String orgName,
                                 String symptom,
                                 String date,
                                 boolean isChild) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.CALL_PERSON_2.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.CALL_PERSON_2.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("机构名称").content(createCallTaskDto.getOutCallOrgName()).build());
        vars.add(Var.builder().remarks("症状名称").content(symptom).build());
        vars.add(Var.builder().remarks("日期").content(date).build());
        vars.add(Var.builder().remarks("具体称谓").content(isChild ? "您的小孩" : "您").build());
        vars.add(Var.builder().remarks("卫生院名称").content(orgName).build());
        callTask.setVars(vars);
        return callTask;
    }

    @Override
    public List<SchoolPersonSymptomRecordVO> getResident(String eventId) {
        //查询学校打卡记录以及电子病历记录
        List<SchoolPersonSymptomRecordVO> list = tbCdcewSympMedRelationMapper.getAllByEventId(eventId, null,null,null);
        List<SchoolPersonSymptomRecordVO> result = new ArrayList<>();

        Map<String, List<SchoolPersonSymptomRecordVO>> map = list.stream().collect(Collectors.groupingBy(SchoolPersonSymptomRecordVO::getPersonTag));
        map.forEach((key, personList) -> {
            List<SchoolPersonSymptomRecordVO> subList = personList.stream()
                    .sorted(Comparator.comparing(SchoolPersonSymptomRecordVO::getPhoneSourceTypeIntValue).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subList)) {
                SchoolPersonSymptomRecordVO recordVO = subList.get(0);

                result.add(recordVO);
            }
        });

        return result;
    }

    private List<PatientSourceTypeVO> getPatientVOS(CreateCallTaskDto createCallTaskDto) {
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(createCallTaskDto.getAttachmentId());
        if (null == tbCdcAttachment) {
            log.info("附件没有没有外呼对象！");
            return new ArrayList<>();
        }
        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);
        List<PatientSourceTypeVO> personVOList = ExcelUtils.readExcel(PatientSourceTypeVO.class, tbCdcAttachment.getAttachmentName(), storageObject.getInputStream());
        return personVOList;
    }

    @Override
    public byte[] downloadMedicalTemplate(String eventId) {

        List<SchoolPersonSymptomRecordVO> list = getResident(eventId);
        //校验是否超出文件导出最大值
        restService.checkExportMax(list);
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        sheet.setColumnHidden(0, true);
        sheet.setColumnHidden(3, true);

        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);

        Row firstRow = sheet.createRow(0);

        createCellAndSetValue(firstRow, "病历ID", 0);
        createCellAndSetValue(firstRow, "居民姓名", 1);
        createCellAndSetValue(firstRow, "电话号码", 2);
        createCellAndSetValue(firstRow, "病历sourceType", 3);

        for (int i = 0; i < list.size(); i++) {

            SchoolPersonSymptomRecordVO vo = list.get(i);
            Row row = sheet.createRow(i + 1);
            createCellAndSetValue(row, vo.getId(), 0);
            createCellAndSetValue(row, vo.getPatientName(), 1);
            createCellAndSetValue(row, vo.getSourceType(), 3);
        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);

        return outputStream.toByteArray();
    }

    @Override
    public UploadResultVO uploadMedicalFile(MultipartFile file) {
        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (IOException e) {
            log.error("读取上传文件失败", e);
            throw new MedicalBusinessException("11451107", "读取上传文件失败");
        }
        XSSFSheet sheet = workbook.getSheet("Sheet1");
        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {

            Row row = sheet.getRow(index);
            String sourceKey = getStringValue(row.getCell(0));
            String patientName = getStringValue(row.getCell(1));
            String phone = getStringValue(row.getCell(2));

            List<Integer> errorList = new ArrayList<>();

            StringBuilder sb = new StringBuilder("");

            // 患者与电话号码同时为空，则视为删除该行
            if (org.apache.commons.lang.StringUtils.isBlank(patientName) && org.apache.commons.lang.StringUtils.isBlank(phone)) {
                continue;
            }

            TbCdcewHisMedicalInfo medical = null;
            if (org.apache.commons.lang.StringUtils.isBlank(sourceKey)) {
                sb.append("病历ID必须填写;");
                errorList.add(0);
            } else {
                medical = hisMedicalInfoMapper.findBySourceKey(sourceKey);
                TbCdcewScSymptomRecord tbCdcewScSymptomRecord = tbCdcewScSymptomRecordMapper.selectByPrimaryKey(sourceKey);
                if (medical == null && tbCdcewScSymptomRecord == null) {
                    sb.append("病历信息不存在;");
                    errorList.add(0);
                }
            }

            if (org.apache.commons.lang.StringUtils.isBlank(patientName)) {
                sb.append("患者姓名必须填写;");
                errorList.add(1);
            } else if (medical != null && !patientName.equals(medical.getPatientName())) {
                sb.append("患者姓名错误;");
                errorList.add(1);
            }

            if (org.apache.commons.lang.StringUtils.isNotBlank(phone)) {
                if (!org.apache.commons.lang.StringUtils.isNumeric(phone)) {
                    sb.append("电话号码必须是数字;");
                    errorList.add(2);
                } else if (phone.length() != 11) {
                    sb.append("电话号码必须是11位;");
                    errorList.add(2);
                }
            }

            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {

                failedCount++;
                createCellAndSetValue(row, errorMsg, 4);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        String resultAttachmentId = fileService.upload(outputStream.toByteArray(), file.getOriginalFilename());
        String attachmentId = fileService.upload(file).get(0);

        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);
        resultVO.setAttachmentId(attachmentId);
        resultVO.setResultAttachmentId(resultAttachmentId);

        return resultVO;
    }

    @Override
    public List<Response<CreateCallRs>> createCallSpeechTaskForAdditionPerson(CreateCallTaskDto createCallTaskDto) {
        List<PersonVO> personVOList = getPersonVOS(createCallTaskDto);
        TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(createCallTaskDto.getEventId());
        String orgName = event.getStatDimName();

        List<SchoolPersonSymptomRecordVO> list = getResident(createCallTaskDto.getEventId());
        Map<String, List<SchoolPersonSymptomRecordVO>> medicalInfoMap = list.stream().collect(Collectors.groupingBy(SchoolPersonSymptomRecordVO::getPatientName));

        List<TbCdcewSympEventPersonRelation> personRelationList = new ArrayList<>();
        personVOList.stream().forEach(item -> {
            List<SchoolPersonSymptomRecordVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(item.getPatientName(), v -> null);
            if (CollectionUtils.isNotEmpty(medicalInfoVOS)) {
                SchoolPersonSymptomRecordVO medicalInfoVO = medicalInfoVOS.get(0);
                String uId = String.valueOf(batchUidService.getUid("tb_cdcew_event_person_relation"));
                TbCdcewSympEventPersonRelation tbCdcEventPersonRelation = new TbCdcewSympEventPersonRelation();
                tbCdcEventPersonRelation.setEventId(createCallTaskDto.getEventId());
                tbCdcEventPersonRelation.setAttachmentId(createCallTaskDto.getAttachmentId());
                tbCdcEventPersonRelation.setCreateTime(new Date());
                tbCdcEventPersonRelation.setId(uId);
                tbCdcEventPersonRelation.setPersonName(item.getName());
                tbCdcEventPersonRelation.setPersonPhone(item.getPhone());
                tbCdcEventPersonRelation.setRelationType(item.getRelationType());
                tbCdcEventPersonRelation.setPatientName(item.getPatientName());
                tbCdcEventPersonRelation.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
                tbCdcEventPersonRelation.setKeySymptomTagList(medicalInfoVO.getSymptomContent());
                tbCdcEventPersonRelation.setOutPatientTime(medicalInfoVO.getOutPatientTime());
                personRelationList.add(tbCdcEventPersonRelation);
            } else {
                log.info("未找到关联患者信息：{}", item);
            }
        });

        if (CollectionUtils.isEmpty(personRelationList)) {
            log.error("关联人群-没有获取到关联外呼对象！");
            return new ArrayList<>();
        }

        TbCdcewSympOutcallBatch TbCdcewSympOutcallBatch = new TbCdcewSympOutcallBatch();
        TbCdcewSympOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_outcall_batch")));
        TbCdcewSympOutcallBatch.setEventId(createCallTaskDto.getEventId());
        TbCdcewSympOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
        TbCdcewSympOutcallBatch.setCreateTime(new Date());
        TbCdcewSympOutcallBatch.setUpdateTime(new Date());

        List<Response<CreateCallRs>> responseList = new ArrayList<>();
        SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
        personRelationList.forEach(person -> {
            String date = sp.format(Optional.ofNullable(person.getOutPatientTime()).orElse(new Date()));
            // 症状长度过长时 外呼会报错  此处只取前两种症状
            String symptom;
            String[] split = person.getKeySymptomTagList().split(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER);
            if (split.length > 2) {
                symptom = org.apache.commons.lang3.StringUtils.joinWith(CommonConstants.VERTICAL_COMMA, split[0], split[1]).concat("等");
            } else {
                symptom = person.getKeySymptomTagList().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
            }
            CallTask callTask = getCallTask(createCallTaskDto, orgName, symptom, date, true);
            callTask.setDwellers(Arrays.asList(Dweller.builder().dwellerName(person.getPersonName()).telephone(person.getPersonPhone()).build()));
            Response<CreateCallRs> createCallRsResponse = createOutboundService.execCallTask(callTask);
            if (createCallRsResponse.isSuccess()) {
                String batchId = createCallRsResponse.getData().getBatch().get(0);
                person.setCallBatchId(batchId);
                person.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
                log.info("执行外呼成功: {}", createCallRsResponse);
            } else {
                person.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                log.error("执行外呼失败：{}", createCallRsResponse);
            }
            person.setCreateTime(new Date());
            person.setBatchId(TbCdcewSympOutcallBatch.getId());
            responseList.add(createCallRsResponse);
        });

        if (CollectionUtils.isNotEmpty(personRelationList)) {
            outcallBatchMapper.insert(TbCdcewSympOutcallBatch);
            tbCdcewSympEventPersonRelationMapper.batchInsert(personRelationList);
        }
        return responseList;
    }

    @Override
    public UploadResultVO uploadRelationFile(MultipartFile file, String eventId) {
        List<SchoolPersonSymptomRecordVO> list = getResident(eventId);
        Set<String> patientNameSet = list.stream().map(e -> e.getPatientName())
                .collect(Collectors.toSet());

        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (IOException e) {
            log.error("读取上传文件失败", e);
            throw new MedicalBusinessException("11451107", "读取上传文件失败");
        }
        XSSFSheet sheet = workbook.getSheet("Sheet1");

        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {
            Row row = sheet.getRow(index);
            String patientName = getStringValue(row.getCell(0));
            String name = getStringValue(row.getCell(1));
            String phone = getStringValue(row.getCell(2));
            String relationType = getStringValue(row.getCell(3));

            StringBuilder sb = new StringBuilder("");

            List<Integer> errorList = new ArrayList<>();

            if (StringUtils.isBlank(patientName)) {
                sb.append("患者姓名必须填写;");
                errorList.add(0);
            } else if (StringUtils.length(patientName) > 25) {
                sb.append("患者姓名不能超过25位;");
                errorList.add(0);
            } else if (!patientNameSet.contains(patientName)) {
                sb.append("患者姓名不存在;");
                errorList.add(0);
            }

            if (StringUtils.isBlank(name)) {
                sb.append("相关人群姓名必须填写;");
                errorList.add(1);
            } else if (StringUtils.length(name) > 25) {
                sb.append("相关人群姓名不能超过25;");
                errorList.add(1);
            }

            if (StringUtils.isBlank(relationType)) {
                sb.append("与患者关系必须填写;");
                errorList.add(3);
            }

            if (StringUtils.isBlank(phone)) {
                sb.append("关联人群号码必须填写;");
                errorList.add(2);
            } else if (!StringUtils.isNumeric(phone)) {
                sb.append("关联人群号码必须是数字;");
                errorList.add(2);
            } else if (phone.length() != 11) {
                sb.append("关联人群号码必须是11位;");
                errorList.add(2);
            }

            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {
                failedCount++;
                createCellAndSetValue(row, errorMsg, 4);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        String resultAttachmentId = fileService.upload(outputStream.toByteArray(), file.getOriginalFilename());
        String attachmentId = fileService.upload(file).get(0);

        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);
        resultVO.setAttachmentId(attachmentId);
        resultVO.setResultAttachmentId(resultAttachmentId);

        return resultVO;
    }

    @Override
    public CallTemplateVO getSchoolCallTemplate(String eventId, Integer personType, String outCallOrgName) {
        TbCdcCallTemplate tbCdcCallTemplate = new TbCdcCallTemplate();
        tbCdcCallTemplate.setPersonType(personType);
        tbCdcCallTemplate.setMonitorType(Integer.valueOf(MonitorCategoryEnum.SCHOOL.getId()));
        List<TbCdcCallTemplate> allCallTemplate = tbCdcCallTemplateMapper.selectByPersonTypeMonitorType(tbCdcCallTemplate);
        Map<String, Object> mapping = getSchoolTemplateMapping(eventId, outCallOrgName);
        return getCallTemplate(allCallTemplate, mapping);
    }

    @Override
    public OutCallResultVO getCheckResult(String loginUserId, String eventId) {
        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        OutCallResultVO resultVO = new OutCallResultVO();

        List<OutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<OutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbCdcewSympOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            OutCallResultVO.BusinessPersonBatchVO batchVO = new OutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(OutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            OutCallResultVO.BusinessPersonBatchVO batchVO = new OutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(OutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);


        // 信号病历排查结果
        List<OutCallResultVO.MedicalBatchVO> medicalBatchVOList = getMedicalBatchList(eventId);
        if (CollectionUtils.isNotEmpty(medicalBatchVOList)) {
            medicalBatchVOList.sort(
                    Comparator.comparing(OutCallResultVO.MedicalBatchVO::getOutCallTime).reversed());
        }
        resultVO.setMedicalBatchVOList(medicalBatchVOList);

        // 关联人员结果
        List<OutCallResultVO.PatientRelationBatchVO> patientRelationBatchList = getPatientRelationList(eventId);
        if (CollectionUtils.isNotEmpty(patientRelationBatchList)) {
            patientRelationBatchList.sort(
                    Comparator.comparing(OutCallResultVO.PatientRelationBatchVO::getOutCallTime).reversed());
        }
        resultVO.setPatientRelationBatchList(patientRelationBatchList);

        if (desensitization) {
            DesensitizeVOUtils.desensitizeOutCallResultVO(resultVO);
        }

        return resultVO;
    }

    private List<OutCallResultVO.MedicalBatchVO> getMedicalBatchList(String eventId) {
        List<TbCdcewSympRecordExtend> tbCdcewSympRecordExtends = extendMapper.listByEventId(eventId);
        Map<String, List<TbCdcewSympRecordExtend>> personRelationMap = tbCdcewSympRecordExtends.stream()
                .collect(Collectors.groupingBy(e -> e.getBatchId()));

        List<TbCdcewSympOutcallBatch> batchList = outcallBatchMapper.listByEventId(eventId);
        Map<String, TbCdcewSympOutcallBatch> batchIdMap = batchList.stream()
                .collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k1));

        List<OutCallResultVO.MedicalBatchVO> batchVOList = new ArrayList<>();
        personRelationMap.forEach((batchId, tempList) -> {
            TbCdcewSympOutcallBatch batch = batchIdMap.computeIfAbsent(batchId, e -> new TbCdcewSympOutcallBatch());
            OutCallResultVO.MedicalBatchVO batchVO = new OutCallResultVO.MedicalBatchVO();
            List<OutCallResultVO.MedicalBatchResultVO> tempResultList = new ArrayList<>();
            tempList.forEach(personRelation -> {
                OutCallResultVO.MedicalBatchResultVO tempResultVO = new OutCallResultVO.MedicalBatchResultVO();
                tempResultVO.setName(personRelation.getPatientName());
                tempResultVO.setConnectStatus(personRelation.getConnectStatus());
                tempResultVO.setAudioUrl(personRelation.getAudioUrl());
                tempResultList.add(tempResultVO);
            });
            batchVO.setOutCallTime(batch.getCreateTime());
            batchVO.setResultList(tempResultList);
            batchVOList.add(batchVO);
        });
        return batchVOList;
    }

    @Override
    public Response<CreateCallRs> createCallSpeechTaskForBusiness(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();
        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        List<TbCdcewSympOutcallNotice> tbCdcewSympOutcallNotices = new ArrayList<>();
        List<Dweller> dwellerList = new ArrayList<>();
        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewSympOutcallNotice tbCdcewSympOutcallNotice = new TbCdcewSympOutcallNotice();
                tbCdcewSympOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_outcall_notice")));
                tbCdcewSympOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewSympOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewSympOutcallNotice.setPersonType(createCallTaskDto.getPersonType().shortValue());
                tbCdcewSympOutcallNotice.setCreateTime(new Date());
                tbCdcewSympOutcallNotice.setUpdateTime(new Date());
                tbCdcewSympOutcallNotice.setStatDimId(userOrg.getId());
                tbCdcewSympOutcallNotice.setStatDimName(userOrg.getName());
                tbCdcewSympOutcallNotice.setName(uapUserPo.getName());
                tbCdcewSympOutcallNotice.setPhone(resident.getTelephone());
                tbCdcewSympOutcallNotices.add(tbCdcewSympOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewSympOutcallNotice tbCdcewSympOutcallNotice = new TbCdcewSympOutcallNotice();
                tbCdcewSympOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_outcall_notice")));
                tbCdcewSympOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewSympOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewSympOutcallNotice.setPersonType(createCallTaskDto.getPersonType().shortValue());
                tbCdcewSympOutcallNotice.setCreateTime(new Date());
                tbCdcewSympOutcallNotice.setUpdateTime(new Date());
                tbCdcewSympOutcallNotice.setStatDimId(person.getStatDimId());
                tbCdcewSympOutcallNotice.setStatDimName(person.getStatDimName());
                tbCdcewSympOutcallNotice.setName(person.getName());
                tbCdcewSympOutcallNotice.setPhone(person.getPhone());
                tbCdcewSympOutcallNotice.setPositionName(person.getPositionName());
                tbCdcewSympOutcallNotices.add(tbCdcewSympOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).build());
            });
        }

        CallTask callTask = getNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewSympOutcallBatch tbCdcewSympOutcallBatch = new TbCdcewSympOutcallBatch();
            tbCdcewSympOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_outcall_batch")));
            tbCdcewSympOutcallBatch.setEventId(createCallTaskDto.getEventId());
            tbCdcewSympOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
            tbCdcewSympOutcallBatch.setCreateTime(new Date());
            tbCdcewSympOutcallBatch.setUpdateTime(new Date());
            outcallBatchMapper.insert(tbCdcewSympOutcallBatch);

            tbCdcewSympOutcallNotices.forEach(TbCdcewSympOutcallNotice -> {
                TbCdcewSympOutcallNotice.setBatchId(tbCdcewSympOutcallBatch.getId());
                TbCdcewSympOutcallNotice.setCallBatchId(batchId);
                TbCdcewSympOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcewSympOutcallNotices)) {
            saveDataToOutCallNotice(createCallTaskDto, tbCdcewSympOutcallNotices);
        }
        return response;
    }

    private void saveDataToOutCallNotice(CreateCallTaskDto createCallTaskDto, List<TbCdcewSympOutcallNotice> tbCdcewOutcallNotices) {
        List<TbCdcewSympOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewSympOutcallNotice> insertList = new ArrayList<>();
        List<BusinessPersonOutCallDTO> businessPersonOutCallDTOS = tbCdcewSympOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType().shortValue());
        Map<String, List<BusinessPersonOutCallDTO>> valueMap = Optional.ofNullable(businessPersonOutCallDTOS).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(BusinessPersonOutCallDTO::getPersonId));
        tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<BusinessPersonOutCallDTO> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                BusinessPersonOutCallDTO businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewSympOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewSympOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private List<OutCallResultVO.PatientRelationBatchVO> getPatientRelationList(String eventId) {
        List<TbCdcewSympEventPersonRelation> personRelationList = tbCdcewSympEventPersonRelationMapper.listByEventId(eventId);
        Map<String, List<TbCdcewSympEventPersonRelation>> personRelationMap = personRelationList.stream()
                .collect(Collectors.groupingBy(e -> e.getBatchId()));

        List<TbCdcewSympOutcallBatch> batchList = outcallBatchMapper.listByEventId(eventId);
        Map<String, TbCdcewSympOutcallBatch> batchIdMap = batchList.stream()
                .collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k1));

        List<OutCallResultVO.PatientRelationBatchVO> patientRelationBatchList = new ArrayList<>();
        personRelationMap.forEach((batchId, tempList) -> {
            TbCdcewSympOutcallBatch batch = batchIdMap.computeIfAbsent(batchId, e -> new TbCdcewSympOutcallBatch());
            OutCallResultVO.PatientRelationBatchVO batchVO = new OutCallResultVO.PatientRelationBatchVO();
            List<OutCallResultVO.PatientRelationResultVO> tempResultList = new ArrayList<>();
            tempList.forEach(personRelation -> {
                OutCallResultVO.PatientRelationResultVO tempResultVO = new OutCallResultVO.PatientRelationResultVO();
                tempResultVO.setPatientName(personRelation.getPatientName());
                tempResultVO.setRelationName(personRelation.getPersonName());
                tempResultVO.setRelationType(personRelation.getRelationType());
                tempResultVO.setIsSick(personRelation.getIsSick());
                tempResultVO.setRelationPhone(personRelation.getPersonPhone());
                tempResultVO.setAudioUrl(personRelation.getAudioUrl());
                tempResultList.add(tempResultVO);
            });
            batchVO.setOutCallTime(batch.getCreateTime());
            batchVO.setResultList(tempResultList);
            patientRelationBatchList.add(batchVO);
        });
        return patientRelationBatchList;
    }

    public CallTemplateVO getCallTemplate(List<TbCdcCallTemplate> allCallTemplate, Map<String, Object> mapping) {
        //模板替换
        allCallTemplate.forEach(tbCdcCallTemplate -> {
            tbCdcCallTemplate.setTemplateContent(com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, tbCdcCallTemplate.getTemplateContent()));
            tbCdcCallTemplate.setTemplateName(com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, tbCdcCallTemplate.getTemplateName()));
        });

        Map<Integer, List<TbCdcCallTemplate>> callTemplateMap = allCallTemplate.stream().collect(Collectors.groupingBy(TbCdcCallTemplate::getNoteType));
        List<TbCdcCallTemplate> outCallTemplates = callTemplateMap.computeIfAbsent(NoteTypeEnum.OUTCALL.getType(), v -> new ArrayList<>());
        List<TbCdcCallTemplate> smsTemplates = callTemplateMap.computeIfAbsent(NoteTypeEnum.SMS.getType(), v -> new ArrayList<>());
        CallTemplateVO callTemplateVO = new CallTemplateVO();
        callTemplateVO.setOutCallTemplate(outCallTemplates);
        callTemplateVO.setSmsTemplate(smsTemplates);
        return callTemplateVO;
    }

    private CallTask getNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.SYMPTOM.getId()));
        String callContent = getCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.SCHOOL.getId()));
            String appContent = getCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    private TbCdcCallTemplate getDefaultTbCdcCallTemplate(Integer personType, Integer noteType, Integer monitorType) {
        TbCdcCallTemplate tbCdcCallTemplate = new TbCdcCallTemplate();
        tbCdcCallTemplate.setMonitorType(monitorType);
        tbCdcCallTemplate.setPersonType(personType);
        tbCdcCallTemplate.setNoteType(noteType);
        List<TbCdcCallTemplate> tbCdcCallTemplates = tbCdcCallTemplateMapper.selectBySelective(tbCdcCallTemplate);
        if (CollectionUtils.isEmpty(tbCdcCallTemplates)) {
            throw new MedicalBusinessException("11451101", "执行外呼失败，外呼模板为空！");
        }
        return tbCdcCallTemplates.get(0);
    }

    /**
     * 获取外呼内容：如果前端没有传入内容，从模板里获取默认内容
     *
     * @param eventId
     * @param content
     * @return
     */
    @Override
    public String getCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {

                TbCdcewSympWarningEvent cdcWarningEvent = sympEventService.getCdcWaringEventByEventId(eventId);
                List<MedicalInfoVO> medsByEventIds = hisMedicalInfoMapper.findRecordsByEventIds(Arrays.asList(eventId));
                if (Objects.nonNull(cdcWarningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), cdcWarningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), cdcWarningEvent.getSymptomName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(cdcWarningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), cdcWarningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), medsByEventIds.size());
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    private Map<String, Object> getSchoolTemplateMapping(String eventId, String outCallOrgName) {
        Map<String, Object> mapping = new HashMap<>(10);
        mapping.putAll(TemplateParaEnum.MAPPING);
        if (null != eventId && eventId.length() > 0) {
            TbCdcewSympWarningEvent tbCdcewSympWarningEvent = sympEventService.getScWaringEventByEventId(eventId);
            List<RecordCountDTO> recordCountDTO = tbCdcewSympMedRelationMapper.getScCountByEventId(eventId);
            int count = 0;
            if (recordCountDTO.size() > 0) {
                count = recordCountDTO.stream().mapToInt(RecordCountDTO::getRecordCount).sum();
            }
            if (Objects.nonNull(tbCdcewSympWarningEvent)) {
                mapping.put(TemplateParaEnum.ORG_NAME.getCode(), tbCdcewSympWarningEvent.getStatDimName());
                mapping.put(TemplateParaEnum.SYMPTOM.getCode(), tbCdcewSympWarningEvent.getSymptomName());
                SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                String date = sp.format(Optional.ofNullable(tbCdcewSympWarningEvent.getBeginDate()).orElse(new Date()));
                mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                mapping.put(TemplateParaEnum.EVENT_NUM.getCode(), tbCdcewSympWarningEvent.getEventNum());
                mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), tbCdcewSympWarningEvent.getDistrictName());
                mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                if (com.iflytek.fpva.cdc.util.StringUtils.isNotBlank(outCallOrgName)) {
                    mapping.put(TemplateParaEnum.CALL_ORG_NAME.getCode(), outCallOrgName);
                }
            }
        }
        return mapping;
    }


    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        cell.setCellValue(value);
    }

    private ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11451108", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    private String getStringValue(Cell cell) {

        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:  //字符串类型
                return cell.getStringCellValue();
            case BOOLEAN:   //布尔类型
                return String.valueOf(cell.getBooleanCellValue());
            case NUMERIC:  //数值类型
                return String.format("%.0f", cell.getNumericCellValue());
            default:
                return null;
        }

    }

    private List<BusinessPersonOutCallDTO> getResultList(List<BusinessPersonOutCallDTO> sourceList) {

        List<BusinessPersonOutCallDTO> retList = new ArrayList<>();
        Map<String, List<BusinessPersonOutCallDTO>> sourceMap = sourceList.stream()
                .collect(Collectors.groupingBy(BusinessPersonOutCallDTO::getPersonId));

        sourceMap.values().forEach(dtoList -> {
            MutableBoolean isFind = new MutableBoolean(false);
            for (BusinessPersonOutCallDTO dto : dtoList) {
                if (CallConnectStatusEnum.CONNECTED.getCode().equals(dto.getConnectStatus())) {
                    isFind.setTrue();
                    retList.add(dto);
                    break;
                }
            }

            // 没有已接通的记录
            if (!isFind.booleanValue()) {
                retList.add(dtoList.get(0));
            }
        });

        return retList;
    }

    /**
     * 从文件服务器获取人员对象列表
     *
     * @param createCallTaskDto
     * @return
     */
    private List<PersonVO> getPersonVOS(CreateCallTaskDto createCallTaskDto) {
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(createCallTaskDto.getAttachmentId());
        if (null == tbCdcAttachment) {
            log.info("附件没有没有外呼对象！");
            return new ArrayList<>();
        }
        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);
        List<PersonVO> personVOList = ExcelUtils.readExcel(PersonVO.class, tbCdcAttachment.getAttachmentName(), storageObject.getInputStream());
        return personVOList;
    }
}

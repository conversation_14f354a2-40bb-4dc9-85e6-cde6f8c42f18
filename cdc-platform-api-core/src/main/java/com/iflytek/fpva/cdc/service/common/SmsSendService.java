package com.iflytek.fpva.cdc.service.common;

public interface SmsSendService {
    /**
     * 立即发送 传染病
     */
    void sendATypeInfected();

    /**
     * 定时发送 传染病
     */
    void sendOtherInfected();

    /**
     * 症候群
     */
    void sendSymptom();

    /**
     * 症状立即发送
     */
    void sendATypeSchoolSymptom();

    /**
     * 症状
     */
    void sendOtherSchoolSymptom();

    /**
     * 中毒 立即发送
     */
    void sendATypePoison();

    /**
     * 中毒
     */
    void sendOtherPoison();

    /**
     * 门诊 立即发送
     */
    void sendATypeOutpatient();
    /**
     * 门诊 ????
     */
    void sendOtherOutpatient();
    /**
     * 不明原因 立即发送
     */
    void sendATypeUnknownReason();
    /**
     * 不明原因
     */
    void sendOtherUnknownReason();

    /**
     * 联防联控 立即发送
     */
    void sendATypePreventionControl();
    /**
     * 联防联控
     */
    void sendOtherPreventionControl();

    /**
     * 自定义 立即发送
     */
    void sendATypeCustomized();
    /**
     * 自定义
     */
    void sendOtherCustomized();
}

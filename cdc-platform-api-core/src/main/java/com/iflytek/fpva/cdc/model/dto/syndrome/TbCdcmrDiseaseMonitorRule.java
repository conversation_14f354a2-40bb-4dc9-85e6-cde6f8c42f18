package com.iflytek.fpva.cdc.model.dto.syndrome;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TbCdcmrDiseaseMonitorRule implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(name = "主键", notes = "")
    private String id;

    /**
     * 类型值为:syndrome,infected,poison,symptom,outpatient,unknownReason,preventionControl
     */
    @ApiModelProperty(name = "类型值为:syndrome,infected,poison,symptom,outpatient,unknownReason,preventionControl", notes = "")
    private String configType;

    /**
     * 疾病名称
     */
    @ApiModelProperty(name = "疾病名称", notes = "")
    private String diseaseName;

    /**
     * 疾病编码
     */
    @ApiModelProperty(name = "疾病编码", notes = "")
    private String diseaseCode;

    /**
     * 监测规则描述
     */
    @ApiModelProperty(name = "监测规则描述", notes = "")
    private String description;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注", notes = "")
    private String remark;

    /**
     * 统计前m天病例数
     */
    @ApiModelProperty(name = "统计前m天病例数", notes = "")
    private Integer abnormalDays;

    /**
     * n倍
     */
    @ApiModelProperty(name = "n倍", notes = "")
    private BigDecimal abnormalMulriple;

    /**
     * 类型，1:abnormal_num；
     */
    @ApiModelProperty(name = "类型，1:abnormal_num；", notes = "")
    private Integer type;

    /**
     * 异常数量
     */
    @ApiModelProperty(name = "异常数量", notes = "")
    private Integer abnormalNum;

    /**
     * 启用状态 1是  ;0否
     */
    @ApiModelProperty(name = "启用状态 1是  ", notes = "0否")
    private Integer status;

    /**
     * 删除状态 0正常，1删除
     */
    @ApiModelProperty(name = "删除状态 0正常，1删除", notes = "")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间", notes = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createDatetime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(name = "更新时间", notes = "")
    private LocalDateTime updateDatetime;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人", notes = "")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "更新人", notes = "")
    private String updator;

    private static final long serialVersionUID = 1L;
}

package com.iflytek.fpva.cdc.model.dto.syndrome;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 症候群分析可视化病例详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AppSyndromeMedicalDetail对象", description="症候群分析可视化病例详情表")
public class AppSyndromeMedicalDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "症候群病历id（病例id-症候群子类id）")
    private String syndromeMedicalId;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "症候群id")
    private String syndromeSubCode;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日期")
    private LocalDate visitDay;

    @ApiModelProperty(value = "病例id")
    private String medicalId;

    @ApiModelProperty(value = "姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSexName;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @ApiModelProperty(value = "年龄段")
    private String ageGroup;

    @ApiModelProperty(value = "现住址")
    private String livingAddrDetail;

    @ApiModelProperty(value = "主诉")
    private String mainSuit;

    @ApiModelProperty(value = "关键症状")
    private String symptomList;

    @ApiModelProperty(value = "诊断")
    private String diagnoseList;

    @ApiModelProperty(value = "工作单位/学校")
    private String company;

    @ApiModelProperty(value = "病例来源机构名称")
    private String orgName;

    @ApiModelProperty(value = "就诊时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime visitTime;

    @ApiModelProperty(value = "增强条件")
    private String enhanceConditionList;

    @ApiModelProperty(value = "接诊医生")
    private String doctorName;

    @ApiModelProperty(value = "现病史")
    private String medicalHistoryNow;

    @ApiModelProperty(value = "既往史")
    private String medicalHistoryBefore;

    @ApiModelProperty(value = "体格检查")
    private String checkup;

    @ApiModelProperty(value = "辅助检查")
    private String assistedExam;

    @ApiModelProperty(value = "病例入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime inTableTime;

    @ApiModelProperty(value = "症状识别")
    private String mainSuitSymptomList;

    @ApiModelProperty(value = "症候群识别")
    private String syndromeName;

    @ApiModelProperty(value = "生冷刺激饮食")
    private String eatColdFlag;

    @ApiModelProperty(value = "不洁饮食")
    private String eatUncleanFlag;

    @ApiModelProperty(value = "其他饮食")
    private String eatOrtherFoodFlag;

    @ApiModelProperty(value = "饮酒")
    private String drinkFlag;

    @ApiModelProperty(value = "吃药")
    private String drugFlag;

    @ApiModelProperty(value = "职业")
    private String job;

    @ApiModelProperty(value = "是否模板病例（基于模板未做明显更改）")
    private String justModelFlag;

    @ApiModelProperty(value = "就诊类型名称")
    private String visitTypeName;

    @ApiModelProperty(value = "科室代号")
    private String deptCode;

    @ApiModelProperty(value = "科室")
    private String deptName;

    @ApiModelProperty(value = "联系方式")
    private String phone;

    @ApiModelProperty(value = "身份证号")
    private String identityNo;

    @ApiModelProperty(value = "是否为异常时间就诊病例")
    private String abnormalTimeFlag;

    @ApiModelProperty(value = "是否增强条件病例")
    private String enhanceConditionFlag;

    @ApiModelProperty(value = "etl创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime etlCreateDatetime;

    @ApiModelProperty(value = "etl更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime etlUpdateDatetime;

    @ApiModelProperty(value = "街道编码")
    private String streetCode;

    @ApiModelProperty(value = "街道名称")
    private String streetName;

    @ApiModelProperty("医生ID")
    private String doctorCode;

    @ApiModelProperty("病例来源")
    private String sourceType;

    private String orgIdAttent;
    private String orgNameAttent;

    private Double orgLongitude;
    private Double orgLatitude;

    private Double livingAddrLongitude;
    private Double livingAddrLatitude;

    private Double streetLongitude;
    private Double streetLatitude;

    private Double companyLongitude;
    private Double companyLatitude;

}

package com.iflytek.fpva.cdc.model.vo;

import com.iflytek.fpva.cdc.entity.HisMedicalInfo;
import com.iflytek.fpva.cdc.entity.TbCdcewHisMedicalInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/29 14:12
 */
@Data
public class HisMedicalInfoVo{

    /**
     * 病例数据
     */
    private TbCdcewHisMedicalInfo hisMedicalInfo;

    /**
     * 外呼状态-用于前端展示，从病例扩展表中获取
     */
    private Integer outCallStatus;
    /**
     * 外呼批次号
     */
    private String outCallBatchId;
    /**
     * 信号id
     */
    private String detailId;

}

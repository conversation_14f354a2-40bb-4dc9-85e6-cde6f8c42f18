package com.iflytek.fpva.cdc.model.vo.infected;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InfectedStatEventTopVO {

    @ApiModelProperty(value = "传染病名称代码")
    private String infectedCode;

    @ApiModelProperty(value = "传染病名称")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String infectedName;

    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "百分百")
    private double percent;

}

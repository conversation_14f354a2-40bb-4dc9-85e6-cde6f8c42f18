package com.iflytek.fpva.cdc.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DesensitizedVO {
    @ApiModelProperty("通用脱敏标识")
    private Boolean normalFlag;


    @ApiModelProperty("诊断脱敏角色")
    private DiagnoseVO diagnoseVO;

    @Data
    public static class DiagnoseVO {
        @ApiModelProperty("诊断脱敏标识")
        private Boolean diagnoseFlag;

        @ApiModelProperty("诊断脱敏角色关联的字段")
        private List<String> diagnoseNames;

    }

    public static DesensitizedVO getDefault(){
        DesensitizedVO  vo = new DesensitizedVO();
        vo.setNormalFlag(true);

        DiagnoseVO diagnoseVO1 = new DiagnoseVO();
        diagnoseVO1.setDiagnoseFlag(true);
        diagnoseVO1.setDiagnoseNames(new ArrayList<>());

        vo.setDiagnoseVO(diagnoseVO1);
        return vo;
    }
}

package com.iflytek.fpva.cdc.model.vo.poison;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class PoisonEventQueryVO {

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    private Date minFullDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    private Date maxFullDate;

    @NotNull
    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("区县编码")
    private String districtCode;

    @ApiModelProperty("食源性疾病集合")
    private List<String> poisonList;

    private int pageIndex;

    private int pageSize;
}

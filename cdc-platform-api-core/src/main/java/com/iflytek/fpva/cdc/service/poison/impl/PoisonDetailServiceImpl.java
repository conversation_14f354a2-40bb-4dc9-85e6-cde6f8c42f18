package com.iflytek.fpva.cdc.service.poison.impl;

import com.google.common.collect.Lists;
import com.iflytek.fpva.cdc.common.CountStrategy;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.constant.*;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.common.TbCdcConfigMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewHisMedicalInfoMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewOrganizationInfoMapper;
import com.iflytek.fpva.cdc.mapper.poison.*;
import com.iflytek.fpva.cdc.model.StatDimKey;
import com.iflytek.fpva.cdc.model.common.CommonOrganizationVO;
import com.iflytek.fpva.cdc.model.dto.Company;
import com.iflytek.fpva.cdc.model.dto.Street;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMappingResult;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMedicalInfoDto;
import com.iflytek.fpva.cdc.model.resultmap.MedCountResult;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.distribution.OrgMedicalDistributionVO;
import com.iflytek.fpva.cdc.model.vo.map.MapPoint;
import com.iflytek.fpva.cdc.model.vo.patient.DocMedDistribution;
import com.iflytek.fpva.cdc.model.vo.poison.PoisonEventExVO;
import com.iflytek.fpva.cdc.model.vo.poison.excel.*;
import com.iflytek.fpva.cdc.model.vo.time.PoisonTimeVO;
import com.iflytek.fpva.cdc.service.common.*;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.poison.PoisonDetailService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.service.warningEvent.WarningEventProcessRecordService;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import com.iflytek.fpva.cdc.util.PercentUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.commons.math3.util.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
@Service
@Slf4j
public class PoisonDetailServiceImpl implements PoisonDetailService {

    @Resource
    TbCdcConfigService tbCdcConfigService;

    @Resource
    PoisonDetailService poisonDetailService;

    @Resource
    TbCdcewPoisonWarningEventMapper tbCdcewPoisonWarningEventMapper;

    @Resource
    TbCdcewPoisonWarningEventDetailMapper tbCdcewPoisonWarningEventDetailMapper;

    @Resource
    private OrgService orgService;

    @Resource
    TbCdcewHisMedicalInfoMapper tbCdcewHisMedicalInfoMapper;

    @Resource
    TbCdcewOrganizationInfoMapper tbCdcewOrganizationInfoMapper;

    @Resource
    HisMedicalInfoService hisMedicalInfoService;

    @Resource
    AreaService areaService;


    @Resource
    BatchUidService batchUidService;

    @Resource
    TbCdcewPoisonEventAnalysisMapper tbCdcewPoisonEventAnalysisMapper;

    @Resource
    TbCdcewPoisonEventAnalysisAttachmentMapper tbCdcewPoisonEventAnalysisAttachmentMapper;

    @Resource
    PoisonEventService poisonEventService;

    @Resource
    RestService restService;

    @Resource
    private TbCdcConfigMapper tbCdcConfigMapper;

    @Resource
    private TbCdcewOrganizationInfoMapper tbCdcOrganizationInfoMapper;

    @Resource
    private TbCdcewPoisonEventRecordMapper tbCdcewPoisonEventRecordMapper;

    @Resource
    private TbCdcewSaPoisonSourceMapper tbCdcewSaPoisonSourceMapper;

    @Resource
    private TbCdcewPoisonMedRelationMapper tbCdcewPoisonMedRelationMapper;

    @Resource
    private FormConfigService formConfigService;

    @Resource
    private WarningEventProcessRecordService warningEventProcessRecordService;

    @Override
    public EventVO buildEventVo(String eventId) {
        TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(eventId);
        if (Objects.isNull(event)) {
            log.error("事件ID：{}不存在！", eventId);
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }

        EventVO result = EventVO.fromEntity(event);
        return EventVO.fromEntity(event);
    }

    @Override
    public EventVO eventDetail(String eventId, String loginUserId) {
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);
        //是否是普通用户
        boolean isOrdinaryUser = tbCdcConfigService.isOrdinaryUser(loginUserId);
        // ai置顶开关是否打开
        boolean isAiScreen = tbCdcConfigService.isSyndromeEnableAiScreen();

        EventVO eventVO = this.buildEventVo(eventId).setEventVOAttentionLevel(isOrdinaryUser, isAiScreen).setOrgInfo();

        //查询信号处理情况，设置处理人处理时间
        List<TbCdcewWarningEventProcessRecord> warningEventProcessRecords = warningEventProcessRecordService.findByEventIdAndProcessingStatusAndType(
                Collections.singletonList(eventId),
                Collections.singletonList(Integer.valueOf(eventVO.getProcessingStatus())),
                ProcessTypeEnum.STATUS.getCode(),
                WarningTypeCodeEnum.POISON.getName());

        if (!CollectionUtils.isEmpty(warningEventProcessRecords)) {
            if(warningEventProcessRecords.get(0).getEventId().equals(eventVO.getId())){
                //设置最新 处理人和时间
                eventVO.setProcessor(warningEventProcessRecords.get(0).getProcessLoginUserName());
                eventVO.setProcessTime(warningEventProcessRecords.get(0).getProcessTime());
            }
        }
        if (Integer.valueOf(eventVO.getProcessingStatus()).equals(ProcessingStatus.REMOVED_AI)) {
            eventVO.setProcessor(ProcessingStatus.REMOVED_AI_STR);
            eventVO.setProcessTime(eventVO.getUpdateTime());
        }
        setMedicalCountInfo(eventVO);
        if (isDesensitization) {
            DesensitizeVOUtils.desensitizeEventVo(eventVO);
        }
        return eventVO;
    }

    public void setMedicalCountInfo(EventVO eventVO) {
        Date date = getMaxEndTime(eventVO.getEventEndDate());

        String yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
        String today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));

        Date yesterdayDate = DateFormatUtils.formatDate(yesterday);
        Date todayDate = DateFormatUtils.formatDate(today);

        TbCdcewSaPoisonSource yesterdaySource = tbCdcewSaPoisonSourceMapper.findBySymptomCodeAndStatDimIdAndFullDate(yesterdayDate, eventVO.getPoisonCode(), eventVO.getStatDimId());
        TbCdcewSaPoisonSource todaySource = tbCdcewSaPoisonSourceMapper.findBySymptomCodeAndStatDimIdAndFullDate(todayDate, eventVO.getPoisonCode(), eventVO.getStatDimId());

        eventVO.setTodayMedicalCaseCn(Optional.ofNullable(todaySource).map(TbCdcewSaPoisonSource::getMedicalCaseCn).orElse(0));
        eventVO.setPreMedicalCaseCn(Optional.ofNullable(yesterdaySource).map(TbCdcewSaPoisonSource::getMedicalCaseCn).orElse(0));

        DistributionVO countByEventId = tbCdcewPoisonMedRelationMapper.getCountByEventId(eventVO.getId());
        eventVO.setMedCount(countByEventId.getCount());


        setMedicalTrend(eventVO);
    }

    private Date getMaxEndTime(Date endTime) {
        if (endTime == null) {
            return new Date();
        }
        return endTime;
    }

    private void setMedicalTrend(EventVO eventVO) {
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        double rate = (eventVO.getTodayMedicalCaseCn() - eventVO.getPreMedicalCaseCn()) / (eventVO.getPreMedicalCaseCn() == 0 ? 1.0 : eventVO.getPreMedicalCaseCn());
        eventVO.setGrowthRate(numberFormat.format(rate));
        if (rate >= 0) {
            eventVO.setMedicalCaseCnTrend(ChangeTrendEnum.RISING.getType());
        } else {
            eventVO.setMedicalCaseCnTrend(ChangeTrendEnum.FALLING.getType());
        }
    }

    @Override
    public PoisonEventExVO humanDistribution(String eventId, String beginDate, String endDate, String loginUserId) {

        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);

        PoisonEventExVO eventExVO = new PoisonEventExVO();

        List<PoisonMedicalInfoDto> PoisonRecordVOList = hisMedicalInfoService.getPoisonRecordByEventId(eventId, null, null, null, null);

        HumanDistributionCountVO humanDistributionCountVO = new HumanDistributionCountVO();
        //年龄统计
        CountStrategy<PoisonMedicalInfoDto> countStrategy = new CountStrategy<>();
        countStrategy.groupByAge(humanDistributionCountVO, PoisonRecordVOList, PoisonMedicalInfoDto::getAge);

        //性别统计
        countStrategy.groupByGender(humanDistributionCountVO, PoisonRecordVOList, PoisonMedicalInfoDto::getSexDesc);
        eventExVO.setCountVO(humanDistributionCountVO);

        List<DocMedDistribution> docMedDistributions = doctorData(PoisonRecordVOList);
        if (isDesensitization) {
            docMedDistributions.forEach(DesensitizeVOUtils::desensitizeDocMedDistribution);
        }
        eventExVO.setDocMedDistributionList(docMedDistributions);

        List<DistributionVO> distributionVOList = new ArrayList<>();
        countStrategy.groupByCareer(distributionVOList, PoisonRecordVOList, PoisonMedicalInfoDto::getCareerName);
        distributionVOList.sort(Comparator.comparing(DistributionVO::getCount).reversed());
        eventExVO.setCareerDistributionVO(distributionVOList);
        return eventExVO;
    }

    @Override
    public List<Date> getEventTimeLine(String eventId) {
//        List<PoisonMedicalInfoDto> poisonRecordVOList = hisMedicalInfoService.getPoisonRecordByEventId(eventId,null, null, null, null);
        List<PoisonMedicalInfoDto> poisonRecordVOList = hisMedicalInfoService.getDetailsByEventId(eventId,null, null, null, null);
        poisonRecordVOList = poisonRecordVOList.stream().filter(medicalInfo->medicalInfo.getFullDate() != null).collect(Collectors.toList());
        List<Date> dateList = poisonRecordVOList.stream().sorted(Comparator.comparing(PoisonMedicalInfoDto::getFullDate,
                        Comparator.nullsFirst(Comparator.naturalOrder())))
                .map(PoisonMedicalInfoDto::getFullDate).collect(Collectors.toList());

        Date endTime = dateList.get(dateList.size() - 1);
        Date startTime = dateList.get(0);
        return tbCdcewPoisonWarningEventMapper.getTimeLine(startTime, endTime);
    }


    @Override
    public List<Map<String, Object>> getEventMedCountByDate(String eventId, String startTime, String endTime) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
        Date formatStartTime;
        Date formatEndTime;
        try {
            formatStartTime = simpleDateFormat.parse(startTime);
            formatEndTime = simpleDateFormat.parse(endTime);
        } catch (ParseException e) {
            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
        }
        //获取中毒信号属于哪一个中毒大类
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(eventId);
        //获取中毒数据源配置
        List<DataSourceConfig> dataSourceConfigs = restService.getDataSource();
        List<DataSourceConfig> source = dataSourceConfigs.stream()
                                                                   .filter(e -> Constants.POISON_TYPE.equals(e.getBusinessType()))
                                                                   .filter(e -> poisonTypeCode.equals(e.getSignalType()))
                                                                   .collect(Collectors.toList());
        //默认数据来源病例
        String dataSource = source.isEmpty() ? DataSourceConfig.DATA_SOURCE_CONFIG_MED : source.get(0).getDataSource();
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<MedCountResult> poisonRecordVOList;
        if(DataSourceConfig.DATA_SOURCE_CONFIG_RPT.equals(dataSource)){
            poisonRecordVOList = tbCdcewPoisonWarningEventMapper.selectEventMedCountByDateAndEventId(eventId, formatStartTime, formatEndTime, poisonTypeCode);
        }else{
            poisonRecordVOList = tbCdcewPoisonWarningEventMapper.selectHisEventMedCountByDateAndEventId(eventId, formatStartTime, formatEndTime);
        }

        //取出身份证号或者名字为空的情况的报卡，只要有一个为空则保持为不同的人
        List<MedCountResult> keepDifPerson = poisonRecordVOList.stream().filter(medCountResult -> medCountResult.getPatientName() == null || "".equals(medCountResult.getPatientName().trim()) || medCountResult.getIdentityNo() == null || "".equals(medCountResult.getIdentityNo().trim())).collect(Collectors.toList());
        //取出身份证号和名字都不为空的情况的报卡
        List<MedCountResult> poisonPatientMsgNotNull = poisonRecordVOList.stream().filter(medCountResult -> medCountResult.getPatientName() != null && !"".equals(medCountResult.getPatientName().trim()) && medCountResult.getIdentityNo() != null && !"".equals(medCountResult.getIdentityNo().trim())).collect(Collectors.toList());
        if (poisonPatientMsgNotNull.size() > 0) {
            Map<String, Map<String, List<MedCountResult>>> map = poisonPatientMsgNotNull.stream().collect(Collectors.groupingBy(MedCountResult::getPatientName, Collectors.groupingBy(MedCountResult::getIdentityNo)));
            List<MedCountResult> result = new ArrayList<>();
            map.forEach((k, v) -> {
                v.forEach((k1, v1) -> {
                    result.add(v1.stream().sorted(Comparator.comparing(MedCountResult::getDateTime)).collect(Collectors.toList()).get(0));
                });
            });
            keepDifPerson.addAll(result);
        }
        Map<String, List<MedCountResult>> map = keepDifPerson.stream().collect(Collectors.groupingBy(MedCountResult::getDateTime));
        for (Map.Entry<String, List<MedCountResult>> entry : map.entrySet()) {
            Map<String, Object> countOneDay = new HashMap<>();
            countOneDay.put("dateTime", entry.getKey());
            countOneDay.put("count", entry.getValue().size());
            resultList.add(countOneDay);
        }
        int count = 0;
        //单独循环，统计从之前到现在的总数
        Collections.sort(resultList, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                return ((String) o1.get("dateTime")).compareTo((String) o2.get("dateTime"));
            }
        });
        for (Map<String, Object> resultMap : resultList) {
            count = count + Integer.parseInt(resultMap.get("count").toString());
            resultMap.put("count", count);
        }
        return resultList;
    }

    @Override
    public PageData<PoisonMedicalInfoDto> getPoisonRecordList(String eventId, String loginUserId, String patientName, Integer pageIndex, Integer pageSize, Integer sortType, String doctorName, String companyName) {

        List<PoisonMedicalInfoDto> poisonRecordVOList = hisMedicalInfoService.getPoisonRecordByEventId(eventId, patientName, sortType, doctorName, companyName);
        //是否脱敏
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);
        List<PoisonMedicalInfoDto> subList;
        int size = poisonRecordVOList.size();
        if (pageIndex == null) {
            pageIndex = 1;
            pageSize = size;
        }
        int pageNum = size / pageSize;
        //是不是整除
        int surplus = size % pageSize;
        if (surplus > 0) {
            pageNum = pageNum + 1;
        }
        int pageStart = pageIndex == 1 ? 0 : (pageIndex - 1) * pageSize;
        int pageEnd = Math.min(pageIndex * pageSize, size);

        if (pageStart > pageEnd) {
            subList = Collections.EMPTY_LIST;
        } else {
            subList = poisonRecordVOList.subList(pageStart, pageEnd);
        }

        if(isDesensitization){
            subList.forEach(DesensitizeVOUtils::desensitizePoisonMedicalInfoDto);
        }
        PageData<PoisonMedicalInfoDto> pageData = new PageData<>();
        pageData.setPages(pageNum);
        pageData.setPageIndex(pageIndex);
        pageData.setPageSize(pageSize);
        pageData.setTotal(size);
        pageData.setData(subList);
        return pageData;
    }

    @Override
    public EventListVO mapRelation(String eventId, String loginUserId) {
        //是否脱敏
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);
        EventListVO eventListVO = new EventListVO();

        EventVO eventVO = buildEventVo(eventId).setOrgInfo();
        MapPoint mapPoint = new MapPoint();
        mapPoint.setLatitude(eventVO.getLatitude());
        mapPoint.setLongitude(eventVO.getLongitude());
        mapPoint.setName(eventVO.getStatDimName());
        mapPoint.setKey(eventVO.getStatDimId());
        if (isDesensitization) {
            DesensitizeVOUtils.desensitizeMapPoint(mapPoint);
        }
        eventListVO.setEventLocation(mapPoint);
        // 返回行政区划
        if (!restService.getIsNeededMapping()) {
            eventListVO.setDistrictCode(eventVO.getMapDistrictCode());
            eventListVO.setDistrictName(eventVO.getMapDistrictName());
        } else {
            eventListVO.setDistrictCode(eventVO.getDistrictCode());
            eventListVO.setDistrictName(eventVO.getDistrictName());
        }

        PageData<PoisonMedicalInfoDto> data = getPoisonRecordList(eventId, loginUserId, null, 1, Integer.MAX_VALUE, null, null, null);
        List<PoisonMedicalInfoDto> symptomRecordVOList = data.getData();

        List<String> orgSourceKeyList = symptomRecordVOList.stream().map(PoisonMedicalInfoDto::getStatDimId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgSourceKeyList)) {
            orgSourceKeyList = Collections.singletonList(eventVO.getStatDimId());
        }

        List<CommonOrganizationVO> orgList = tbCdcewOrganizationInfoMapper.findAllByOrgIds(orgSourceKeyList);
        List<Street> allStreetList = orgList.stream().map(Street::fromCommonOrgnazitionVO).collect(Collectors.toList());

        // 获取单位list
        List<Company> allCompanyList = symptomRecordVOList.stream().map(Company::fromPoisonRecordVO).collect(Collectors.toList());
        eventListVO.setStreetList(getStreetList(symptomRecordVOList, allStreetList, allCompanyList, false, isDesensitization));
        eventListVO.setCompanyList(getCompanyList(symptomRecordVOList, allStreetList, allCompanyList, false, isDesensitization));
        eventListVO.setAddressList(getAddressList(symptomRecordVOList, isDesensitization));

        // 设置周边机构病历数
        setSurroundOrgVOList(eventListVO, isDesensitization, eventVO);
        return eventListVO;
    }

    /**
     * 获取 周边机构病历数 以及 周边病历的位置信息
     */
    private void setSurroundOrgVOList(EventListVO eventListVO, boolean isDesensitization, EventVO eventVO) {
        String poisonCode = eventVO.getPoisonCode();

        // 获取配置表中配置的周边机构的距离
        Double distance;
        CdcConfig cdcConfig = tbCdcConfigMapper.findByConfigKey(CommonConstants.SURROUND_ORG_DISTANCE);
        if (cdcConfig != null) {
            distance = cdcConfig.getConfigValue();
        } else {
            throw new MedicalBusinessException("11457001", "周边机构距离未配置");
        }
        eventListVO.setDistance(distance);

        // 获取统计日期
        String today;
        Date date = eventVO.getEventEndDate();
        if (date == null) {
            today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.now().minus(0, ChronoUnit.DAYS));
        } else {
            today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));
        }
        // 获取信号机构的原始坐标
        String statDimId = eventVO.getStatDimId();
        List<TbCdcewOrganizationInfo> eventOrgList = tbCdcewOrganizationInfoMapper.getAllOrgListByStatDimId(Arrays.asList(statDimId), SourceTypeEnum.getSourceTypeList());
        Double latitude;
        Double longitude;
        String sourceType;

        if (!CollectionUtils.isEmpty(eventOrgList)) {
            TbCdcewOrganizationInfo eventOrg = eventOrgList.get(0);
            latitude = eventOrg.getOrgLatitude();
            longitude = eventOrg.getOrgLongitude();
            sourceType = eventOrg.getSourceType();
        } else {
            throw new MedicalBusinessException("11457003", "机构表缺失机构数据");
        }
        //当sourceType为空时，代表街道或者单位信号，此时自定义预警信号取周边的等级医院以及基层医疗作为周边机构
        List<String> sourceList = new ArrayList<>();
        if(StringUtils.isBlank(sourceType)){
            sourceList.add(SourceTypeEnum.HOSPITAL.getCode());
            sourceList.add(SourceTypeEnum.PRIMARY_CARE.getCode());
        }else {
            sourceList.add(sourceType);
        }

        // 生成周边机构病历数结果
        List<SurroundOrgVO> surroundOrgVOList = new ArrayList<>();

        log.info("获取周边机构开始");
        List<TbCdcewOrganizationInfo> surroundStatOrgList = orgService.findStatOrgByDistanceAndSourceList(latitude, longitude, distance, sourceList);
        log.info("获取周边机构结束");

        log.info("开始查询target表");
        List<String> surroundStatOrgIds = surroundStatOrgList.stream().map(TbCdcewOrganizationInfo::getStatOrgId).collect(Collectors.toList());
        List<OrgMedCount> tmpTodayList = tbCdcewHisMedicalInfoMapper.orgMedCountByIds(DateFormatUtils.formatDate(today), poisonCode, surroundStatOrgIds);
        Map<String, OrgMedCount> todayOrgCountMap = tmpTodayList.stream().collect(Collectors.toMap(OrgMedCount::getHospitalSourceKey, Function.identity(), (t1, t2) -> t1));
        log.info("结束查询target表,size:" + tmpTodayList.size());

        for (TbCdcewOrganizationInfo surroundStatOrg : surroundStatOrgList) {
            String statOrgId = surroundStatOrg.getStatOrgId();
            if (!Objects.equals(statDimId, statOrgId)) {
                int medCount = Optional.ofNullable(todayOrgCountMap.get(statOrgId)).map(OrgMedCount::getCount).orElse(0);
                SurroundOrgVO surroundOrgVO = new SurroundOrgVO();
                surroundOrgVO.setStatDimId(statOrgId);
                surroundOrgVO.setStatDimName(surroundStatOrg.getStatOrgName());
                surroundOrgVO.setLatitude(surroundStatOrg.getOrgLatitude());
                surroundOrgVO.setLongitude(surroundStatOrg.getOrgLongitude());
                surroundOrgVO.setCount(medCount);
                surroundOrgVOList.add(surroundOrgVO);
            }
        }
        log.info("结束处理周边机构");

        if (isDesensitization) {
            surroundOrgVOList.forEach(DesensitizeVOUtils::desensitizeSurroundOrgVO);
        }
        eventListVO.setSurroundOrgVOList(surroundOrgVOList);


        List<SurroundMedAddressVO> surroundMedAddressVOList = new ArrayList<>();
        // 周边机构病历打点
        log.info("开始处理周边机构病历");
        if (!CollectionUtils.isEmpty(surroundOrgVOList)) {
            List<String> orgIdList = surroundOrgVOList.stream().map(SurroundOrgVO::getStatDimId).collect(Collectors.toList());

            log.info("开始调analysis接口,获取病历结果");
            List<PoisonMappingResult> mappingResultList = restService.getPoisonMappingResult(today, poisonCode, orgIdList);
            log.info("结束调analysis接口,获取病历结果完成，size:" + mappingResultList.size());

            if (!CollectionUtils.isEmpty(mappingResultList)) {

                mappingResultList.forEach(mappingResult -> {
                    SurroundMedAddressVO surroundMedAddressVO = new SurroundMedAddressVO();
                    surroundMedAddressVO.setSourceKey(mappingResult.getMedicalId());
                    surroundMedAddressVO.setAddressName(mappingResult.getLivingAddress());
                    surroundMedAddressVO.setLatitude(mappingResult.getLivingAddressLatitude());
                    surroundMedAddressVO.setLongitude(mappingResult.getLivingAddressLongitude());
                    surroundMedAddressVO.setPatientId(mappingResult.getGlobalPersonId());
                    surroundMedAddressVOList.add(surroundMedAddressVO);
                });
            }
        }
        log.info("结束处理周边机构病历");

        eventListVO.setSurroundMedAddressVOList(surroundMedAddressVOList);
    }

    @Override
    public OrgMedicalVO regionMedDistribution(String eventId, boolean total) {
        OrgMedicalVO orgMedicalVO = new OrgMedicalVO();
        TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(eventId);
        String yesterday;
        String today;
        Date date;
        if (total) {
            //总计的时间 如果事件没结束 则取当前时间 如果事件结束了 则取事件结束时间
            date = event.getEndDate();
            if (date == null) {
                yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.now().minus(1, ChronoUnit.DAYS));
                today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.now().minus(0, ChronoUnit.DAYS));
            } else {
                yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
                today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                        .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));
            }
        } else {
            //如果取预警发生第一天 则日期取事件开始时间
            date = event.getBeginDate();
            yesterday = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
            today = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()));
        }

        String sourceType = event.getSourceType();
        String districtCode = event.getDistrictCode();
        List<OrgMedCount> yesterdayList;
        List<OrgMedCount> todayList;
        if (com.iflytek.fpva.cdc.util.StringUtils.isNotEmpty(sourceType)) {
            List<String> sourceTypeList = Lists.newArrayList(sourceType);
            yesterdayList = tbCdcewPoisonWarningEventDetailMapper.orgMedDistribution(sourceTypeList, districtCode, DateFormatUtils.formatDate(yesterday), event.getPoisonCode());
            todayList = tbCdcewPoisonWarningEventDetailMapper.orgMedDistribution(sourceTypeList, districtCode, DateFormatUtils.formatDate(today), event.getPoisonCode());
        } else {
            if (EventTypeEnum.XXDW.getName().equals(event.getEventType())) {
                yesterdayList = tbCdcewPoisonWarningEventDetailMapper.companyMedDistribution(districtCode, DateFormatUtils.formatDate(yesterday), event.getPoisonCode());
                todayList = tbCdcewPoisonWarningEventDetailMapper.companyMedDistribution(districtCode, DateFormatUtils.formatDate(today), event.getPoisonCode());
            } else {
                yesterdayList = tbCdcewPoisonWarningEventDetailMapper.regionMedDistribution(districtCode, DateFormatUtils.formatDate(yesterday), event.getPoisonCode());
                todayList = tbCdcewPoisonWarningEventDetailMapper.regionMedDistribution(districtCode, DateFormatUtils.formatDate(today), event.getPoisonCode());
            }
        }

        // 当poison_source表没有那一天的数据时，将该区县下的机构填充加入，病历数置为0
//        List<TbCdcewOrganizationInfo> statOrgList = orgService.getStatOrgByDistrictSourceTypes(districtCode, sourceType);
        List<StatDimKey> statDimKeyList = orgService.getStatDimKeysBy(districtCode, event.getEventType());

        List<OrgMedicalDistributionVO> result = OrgMedicalDistributionVO.calcStatDimDistribution(statDimKeyList, yesterdayList, todayList);

        orgMedicalVO.setList(result);
        orgMedicalVO.setYesterday(yesterday);
        orgMedicalVO.setToday(today);

        return orgMedicalVO;
    }

    @Override
    public TbCdcewPoisonEventAnalysis createEventAnalysisResult(EventAnalysisResultVO eventAnalysisResultVO, String loginUserId, String loginUserName) {
        //传入字段校验
        if (eventAnalysisResultVO.getOtherDiseaseName() != null && eventAnalysisResultVO.getOtherDiseaseName().length() > 15) {
            throw new MedicalBusinessException("11441027", "疾病名称超出15字限制");
        }
        if ((eventAnalysisResultVO.getDeadCaseNum() < 0) || (eventAnalysisResultVO.getDeadCaseNum() > 999)) {
            throw new MedicalBusinessException("11441028", "死亡病例数不合规");
        }
        if ((eventAnalysisResultVO.getTotalCaseNum() < 0) || (eventAnalysisResultVO.getTotalCaseNum() > 999)) {
            throw new MedicalBusinessException("11441029", "总病例数不合规");
        }
        //1, 增加研判信息
        String analysisId = String.valueOf(batchUidService.getUid("tb_cdcew_poison_event_analysis"));
        TbCdcewPoisonEventAnalysis tbCdcEventAnalysis = TbCdcewPoisonEventAnalysis.builder()
                .id(analysisId)
                .conclusions(eventAnalysisResultVO.getConclusions())
                .educationInvolved(eventAnalysisResultVO.getEducationInvolved())
                .comments(eventAnalysisResultVO.getComments())
                .eventId(eventAnalysisResultVO.getEventId())
                .createTime(new Date())
                .orgName(eventAnalysisResultVO.getOrgName())
                .orgId(eventAnalysisResultVO.getOrgId())
                .investigationTime(eventAnalysisResultVO.getInvestigationTime())
                .investigationMethod(eventAnalysisResultVO.getInvestigationMethod())
                .epiHistory(eventAnalysisResultVO.getEpiHistory())
                .pathogenDetection(eventAnalysisResultVO.getPathogenDetection())
                .totalCaseNum(eventAnalysisResultVO.getTotalCaseNum())
                .deadCaseNum(eventAnalysisResultVO.getDeadCaseNum())
                .creatorName(eventAnalysisResultVO.getCreatorName())
                .creatorId(loginUserId)
                .fillingDate(eventAnalysisResultVO.getFillingDate())
                .diseaseCode(eventAnalysisResultVO.getDiseaseCode())
                .diseaseName(eventAnalysisResultVO.getDiseaseName())
                .otherDiseaseName(eventAnalysisResultVO.getOtherDiseaseName())
                .positiveEventType(eventAnalysisResultVO.getPositiveEventType())
                .build();

        //2, 增加附件信息
        List<TbCdcAttachment> attachmentList = eventAnalysisResultVO.getAttachmentList();
        List<TbCdcewPoisonEventAnalysisAttachment> TbCdcewPoisonEventAnalysisAttachmentList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(attachmentList)) {
            attachmentList.forEach(item -> {
                String uid = String.valueOf(batchUidService.getUid("tb_cdcew_infected_event_analysis_attachment"));
                TbCdcewPoisonEventAnalysisAttachment TbCdcewPoisonEventAnalysisAttachment = new TbCdcewPoisonEventAnalysisAttachment();
                TbCdcewPoisonEventAnalysisAttachment.setAnalysisId(analysisId);
                TbCdcewPoisonEventAnalysisAttachment.setAttachmentId(item.getId());
                TbCdcewPoisonEventAnalysisAttachment.setId(uid);
                TbCdcewPoisonEventAnalysisAttachmentList.add(TbCdcewPoisonEventAnalysisAttachment);
            });
        }

        //3, 存储数据库
        tbCdcewPoisonEventAnalysisMapper.insert(tbCdcEventAnalysis);
        if (!CollectionUtils.isEmpty(TbCdcewPoisonEventAnalysisAttachmentList)) {
            tbCdcewPoisonEventAnalysisAttachmentMapper.batchInsert(TbCdcewPoisonEventAnalysisAttachmentList);
        }
        // 根据研判结论判断
        if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_SUSPECT || eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_CONFIRM) {

            poisonEventService.startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.POSITIVE, loginUserName);

        } else if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_REMOVED) {

            poisonEventService.startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.REMOVED_MANUAL, loginUserName);

        } else if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_FOCUS) {

            // 持续关注，延长处置超时时间
            TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(eventAnalysisResultVO.getEventId());
            if (event == null) {
                throw new MedicalBusinessException("11450001", "不存在对应ID的事件：" + eventAnalysisResultVO.getEventId());
            }

            // 处置超时状态，不延长处置超时时间
            if (OvertimeStatus.PROCESS_OVERTIME_OUT !=
                    Optional.ofNullable(event.getProcessingTimeOutStatus()).orElse(OvertimeStatus.PROCESS_OVERTIME_NORMAL)) {
                long processTimeout = tbCdcConfigService.getPoisonProcessingTimeoutConfig();
                Date processLatestTime = DateUtils.addHours(new Date(), (int) processTimeout);
                tbCdcewPoisonWarningEventMapper.updateProcessingLatestTimeByEventId(eventAnalysisResultVO.getEventId(), processLatestTime);
            }

            poisonEventService.startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.PROCESSING, loginUserName);

        }
        return tbCdcEventAnalysis;
    }

    public List<DocMedDistribution> doctorData(List<PoisonMedicalInfoDto> medList) {

        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setMinimumFractionDigits(2);
        //直接根据医生id分组
        Map<String, List<PoisonMedicalInfoDto>> map = medList.stream()
                .filter(item -> StringUtils.isNoneBlank(item.getDoctorId()))
                .collect(Collectors.groupingBy(PoisonMedicalInfoDto::getDoctorId));

        MutableInt count = new MutableInt(0);
        for (List<PoisonMedicalInfoDto> list : map.values()) {
            count.add(list.size());
        }

        return map.values().stream().map(value -> {
            DocMedDistribution dmd = new DocMedDistribution();
            dmd.setDoctorName(value.get(0).getDoctorName());
            dmd.setHospitalName(value.get(0).getOrgName());
            dmd.setMedicalNum(value.size());
            dmd.setRate(numberFormat.format(dmd.getMedicalNum().doubleValue() / count.intValue()));
            return dmd;
        }).sorted(Comparator.comparing(DocMedDistribution::getMedicalNum).reversed()).collect(Collectors.toList());
    }

    /**
     * 构建学校/单位列表
     *
     * @param
     * @return
     */
    private List<CompanyVO> getCompanyList(List<PoisonMedicalInfoDto> list, List<Street> allStreetList, List<Company> allCompanyList, boolean returnRootOnly, boolean isDesensitization) {
        List<CompanyVO> companyList = new ArrayList<>();
        Map<String, List<PoisonMedicalInfoDto>> companyPatientMap = new HashMap<>();
        try {
            companyPatientMap = list.stream().filter(x -> StringUtils.isNoneBlank(x.getCompanyName())).collect(Collectors.groupingBy(PoisonMedicalInfoDto::getCompanyName));
        } catch (Exception e) {
            log.error("构建患者单位信息出错！", e);
        }
        Map<String, Company> companyMap = allCompanyList.stream().filter(company -> org.apache.commons.lang.StringUtils.isNotEmpty(company.getCompanyName())).collect(Collectors.toMap(Company::getCompanyName, a -> a, (k1, k2) -> k1));
        companyPatientMap.forEach(
                (companyName, personList) -> {
                    Company company = areaService.getCompanyByName(companyName, companyMap);
                    CompanyVO companyVO = CompanyVO.fromEntity(company);
                    companyVO.setCount(personList.size());
                    companyVO.setRatio(PercentUtil.getRatio(personList.size()));

                    if (!returnRootOnly) {
                        Map<Pair<Double, Double>, List<PoisonMedicalInfoDto>> regionMap = personList.stream().collect(Collectors.groupingBy(PoisonMedicalInfoDto::getLongitudeAndLatitude));
                        List<RegionVO> regionList = new ArrayList<>();
                        regionMap.forEach((region, persons) -> {
                            List<String> addrList = persons.stream().map(PoisonMedicalInfoDto::getLivingAddress).distinct().collect(Collectors.toList());
                            String regionName = StringUtils.join(addrList, ",");
                            RegionVO<StreetVO> regionVO = new RegionVO<>();
                            regionVO.setRegionName(regionName);
                            regionVO.setLongitude(region.getKey());
                            regionVO.setLatitude(region.getValue());
                            regionVO.setCount(persons.size());

                            List<StreetVO> streetList = getStreetList(personList, allStreetList, allCompanyList, true, isDesensitization);
                            regionVO.setSubList(streetList);
                            regionVO.setRatio(PercentUtil.getRatio(streetList.size()));
                            regionVO.setPicType(PicTypeEnum.HOSPITAL.getType());
                            if (isDesensitization) {
                                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
                            }
                            regionList.add(regionVO);
                        });
                        companyVO.setRegionList(regionList);
                    }

                    if (isDesensitization) {
                        DesensitizeVOUtils.desensitizeCompanyVo(companyVO);
                    }
                    companyList.add(companyVO);
                }
        );
        return companyList;
    }

    /**
     * 构建街道列表
     *
     * @return
     */
    private List<StreetVO> getStreetList(List<PoisonMedicalInfoDto> list, List<Street> allStreetList, List<Company> allCompanyList, boolean returnRootOnly, boolean isDesensitization) {
        //组装街道信息
        List<StreetVO> streetList = new ArrayList<>();
        Map<String, List<PoisonMedicalInfoDto>> streetPatientMap = new HashMap<>();
        try {
            streetPatientMap = list.stream().filter(x -> StringUtils.isNoneBlank(x.getStatDimName())).collect(Collectors.groupingBy(PoisonMedicalInfoDto::getStatDimName));
        } catch (Exception e) {
            log.error("构建患者街道信息出错！", e);
        }
        Map<String, Street> streetMap = allStreetList.stream().filter(street -> org.apache.commons.lang.StringUtils.isNotEmpty(street.getStreetName())).collect(Collectors.toMap(Street::getStreetName, a -> a, (k1, k2) -> k1));

        streetPatientMap.forEach(
                (streetName, patientVOList) -> {
                    Street street = areaService.getStreetByName(streetName, streetMap);
                    StreetVO streetVO = StreetVO.fromEntity(street);
                    streetVO.setCount(patientVOList.size());
                    streetVO.setRatio(PercentUtil.getRatio(patientVOList.size()));

                    if (!returnRootOnly) {
                        Map<Pair<Double, Double>, List<PoisonMedicalInfoDto>> regionMap = patientVOList.stream().collect(Collectors.groupingBy(PoisonMedicalInfoDto::getLongitudeAndLatitude));
                        List<RegionVO> regionList = new ArrayList<>();
                        regionMap.forEach((region, medList) -> {
                            List<String> addrList = medList.stream().map(PoisonMedicalInfoDto::getLivingAddress).distinct().collect(Collectors.toList());
                            String regionName = StringUtils.join(addrList, ",");
                            RegionVO<CompanyVO> regionVO = new RegionVO<>();
                            regionVO.setRegionName(regionName);
                            regionVO.setLongitude(region.getKey());
                            regionVO.setLatitude(region.getValue());

                            List<CompanyVO> companyList = getCompanyList(medList, allStreetList, allCompanyList, true, isDesensitization);
                            regionVO.setCount(companyList.size());
                            regionVO.setSubList(companyList);
                            regionVO.setRatio(PercentUtil.getRatio(companyList.size()));
                            regionVO.setPicType(PicTypeEnum.COMPANY.getType());
                            if (isDesensitization) {
                                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
                            }
                            regionList.add(regionVO);
                        });
                        streetVO.setRegionList(regionList);
                    }
                    if (isDesensitization) {
                        DesensitizeVOUtils.desensitizeStreetVo(streetVO);
                    }
                    streetList.add(streetVO);
                }
        );
        return streetList;
    }

    /**
     * 构建住址列表-从医院数据源提取
     *
     * @param medicalInfoVOList
     * @return
     */
    private List<RegionVO> getAddressList(List<PoisonMedicalInfoDto> medicalInfoVOList, boolean isDesensitization) {
        List<RegionVO> regionList = new ArrayList<>();
        Map<Pair<Double, Double>, List<PoisonMedicalInfoDto>> regionMap = medicalInfoVOList.stream().collect(Collectors.groupingBy(PoisonMedicalInfoDto::getLongitudeAndLatitude));
        regionMap.forEach((region, medList) -> {
            List<String> addrList = medList.stream().map(PoisonMedicalInfoDto::getLivingAddress).distinct().collect(Collectors.toList());
            String regionName = StringUtils.join(addrList, ",");
            RegionVO<MedicalInfoVO> regionVO = new RegionVO<>();
            regionVO.setRegionName(regionName);
            regionVO.setLongitude(region.getKey());
            regionVO.setLatitude(region.getValue());
            regionVO.setCount(medList.size());
            regionVO.setPicType(PicTypeEnum.ADDRESS.getType());
            regionVO.setRatio(PercentUtil.getRatio(medList.size()));
            if (isDesensitization) {
                DesensitizeVOUtils.desensitizeRegionVo(regionVO);
            }
            regionList.add(regionVO);
        });
        return regionList;
    }

    @Override
    public List<PoisonTimeVO> queryTimeTrend(String eventId, Integer timeType) {

        TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(eventId);

        String poisonName = event.getPoisonName();
        DateFormat dateFormat = DateFormat.getDateTimeInstance();

        Date endDate = event.getEndDate() == null ? new Date() : event.getEndDate();
        Date beginDate = DateUtils.addDays(endDate, -30);
        if (TimeType.YEAR == timeType) {
            endDate = DateUtils.addYears(endDate, -1);
            beginDate = DateUtils.addYears(beginDate, -1);
        }

        List<PoisonTimeVO> result = new ArrayList<>();

        try {
            List<TimeTrendVO> poisonTimeTrends = tbCdcewPoisonWarningEventDetailMapper.getPoisonTimeTrendByOrg(event.getStatDimId(), poisonName,
                    dateFormat.parse(dateFormat.format(beginDate)),
                    dateFormat.parse(dateFormat.format(endDate)));

            SimpleDateFormat sdf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
            for (int i = 30; i >= 0; i--) {
                Date day = DateUtils.addDays(endDate, -i);
                PoisonTimeVO poisonTimeVO = new PoisonTimeVO();
                poisonTimeVO.setPoisonName(poisonName);
                poisonTimeVO.setPoisonDate(day);
                poisonTimeVO.setMedicalNum(0);

                poisonTimeTrends.forEach(symptomTimeVO1 -> {
                    Date dd;
                    try {
                        dd = sdf.parse(sdf.format(day));
                    } catch (ParseException e) {
                        log.error("日期格式解析错误");
                        throw new MedicalBusinessException("11452001", "日期转换失败");
                    }

                    if (dd.equals(symptomTimeVO1.getDate())) {
                        poisonTimeVO.setMedicalNum(symptomTimeVO1.getMedicalNum());
                    }
                });

                String fullDate = sdf.format(day);
                poisonTimeVO.setMedicalNum(poisonTimeVO.getMedicalNum());

                result.add(poisonTimeVO);
            }
        } catch (ParseException e) {
            log.error("日期格式解析错误");
            throw new MedicalBusinessException("11452001", "日期转换失败");
        }

        return result;
    }

    @Override
    public byte[] exportPoisonRecordList(String eventId, String loginUserId, Integer sortType, String doctorName, String companyName, String formType) {
        PageData<PoisonMedicalInfoDto> poisonRecordVOPageData = getPoisonRecordList(eventId, loginUserId, null, 1, Integer.MAX_VALUE, sortType, doctorName, companyName);
        List<PoisonMedicalInfoDto> result = poisonRecordVOPageData.getData();
        //校验是否超出文件导出最大值
        restService.checkExportMax(result);
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");


        makeExcelColumn(loginUserId, result, sheet, formType);
        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        return outputStream.toByteArray();
    }

    @Override
    public ResponseEntity<byte[]> exportPoisonRecordList(String eventId, String loginUserId, Integer sortType) {
        PageData<PoisonMedicalInfoDto> poisonRecordVOPageData = getPoisonRecordList(eventId, loginUserId, null, 1, Integer.MAX_VALUE, sortType, null, null);
        List<PoisonMedicalInfoDto> poisonRecordList = poisonRecordVOPageData.getData();
        //校验是否超出文件导出最大值
        restService.checkExportMax(poisonRecordList);

        //获取当前的信号属于哪一个中毒大类
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(eventId);
        if (PoisonTypeEnum.FOOD.getCode().equals(poisonTypeCode)) {
            List<PoisonFoodExcelVO> resultList = poisonRecordList.stream().map(PoisonFoodExcelVO::fromPoisonMedicalInfoDto).collect(Collectors.toList());
            return FileUtils.exportExcel(resultList, PoisonFoodExcelVO.class, true, eventId + ".xlsx");
        }
        if (PoisonTypeEnum.PESTICIDE.getCode().equals(poisonTypeCode)) {
            List<PoisonPesticideExcelVO> resultList = poisonRecordList.stream().map(PoisonPesticideExcelVO::fromPoisonMedicalInfoDto).collect(Collectors.toList());
            return FileUtils.exportExcel(resultList, PoisonPesticideExcelVO.class, true, eventId + ".xlsx");
        }
        if (PoisonTypeEnum.CAREER.getCode().equals(poisonTypeCode)) {
            List<PoisonCareerExcelVO> resultList = poisonRecordList.stream().map(PoisonCareerExcelVO::fromPoisonMedicalInfoDto).collect(Collectors.toList());
            return FileUtils.exportExcel(resultList, PoisonCareerExcelVO.class, true, eventId + ".xlsx");
        }
        if (PoisonTypeEnum.CARBON_MONOXIDE.getCode().equals(poisonTypeCode)) {
            List<PoisonCarbonMonoxideExcelVO> resultList = poisonRecordList.stream().map(PoisonCarbonMonoxideExcelVO::fromPoisonMedicalInfoDto).collect(Collectors.toList());
            return FileUtils.exportExcel(resultList, PoisonCarbonMonoxideExcelVO.class, true, eventId + ".xlsx");
        }
        if (PoisonTypeEnum.HEATSTROKE.getCode().equals(poisonTypeCode)) {
            List<PoisonHeatstrokeExcelVO> resultList = poisonRecordList.stream().map(PoisonHeatstrokeExcelVO::fromPoisonMedicalInfoDto).collect(Collectors.toList());
            return FileUtils.exportExcel(resultList, PoisonHeatstrokeExcelVO.class, true, eventId + ".xlsx");
        }
        if (PoisonTypeEnum.RADIATE.getCode().equals(poisonTypeCode)) {
            List<PoisonRadiateExcelVO> resultList = poisonRecordList.stream().map(PoisonRadiateExcelVO::fromPoisonMedicalInfoDto).collect(Collectors.toList());
            return FileUtils.exportExcel(resultList, PoisonRadiateExcelVO.class, true, eventId + ".xlsx");
        }
        return null;
    }

    private void makeExcelColumn(String loginUserId, List<PoisonMedicalInfoDto> result, XSSFSheet sheet, String formType) {
        TbCdcewFormConfig formConfig = formConfigService.getConfigByLoginUserId(loginUserId, formType);
        Row firstRow = sheet.createRow(0);

        if (formConfig != null) {
            int index = 0;
            sheet.setColumnWidth(index, 4000);
            createCellAndSetValue(firstRow, "姓名", index);
            String queryString = formConfig.getConfigDesc();
            List<String> queryStringList = Arrays.asList(queryString.split(","));

            index = getIndex(queryStringList, "sexDesc");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "性别", index);
            }

            index = getIndex(queryStringList, "age");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "年龄", index);
            }

            index = getIndex(queryStringList, "happenPlace");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "发生场所", index);
            }

            index = getIndex(queryStringList, "happenAddress");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "发生地址", index);
            }

            index = getIndex(queryStringList, "livingAddress");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "现住址", index);
            }

            index = getIndex(queryStringList, "mainSuit");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "主诉", index);
            }

            index = getIndex(queryStringList, "diagnoseName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "诊断", index);
            }

            index = getIndex(queryStringList, "companyName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "工作单位/学校", index);
            }

            index = getIndex(queryStringList, "careerName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "职业", index);
            }

            index = getIndex(queryStringList, "orgName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "病例来源", index);
            }

            index = getIndex(queryStringList, "pesticideType");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "中毒农药种类", index);
            }

            index = getIndex(queryStringList, "pesticideName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "中毒农药名称", index);
            }

            index = getIndex(queryStringList, "poisonReason");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "中毒原因", index);
            }

            index = getIndex(queryStringList, "outPatientTime");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "诊断时间", index);
            }

            index = getIndex(queryStringList, "foodName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "食品名称", index);
            }

            index = getIndex(queryStringList, "foodType");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "食品分类", index);
            }

            index = getIndex(queryStringList, "processingType");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "加工或包装方式", index);
            }

            index = getIndex(queryStringList, "brand");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "食品品牌", index);
            }

            index = getIndex(queryStringList, "manufacturer");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "生产厂家", index);
            }

            index = getIndex(queryStringList, "eatingTime");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "进食时间", index);
            }

            index = getIndex(queryStringList, "eatingAddress");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "进食地点", index);
            }

            index = getIndex(queryStringList, "purchaseLocation");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "购买地点", index);
            }

            index = getIndex(queryStringList, "symptomOnsetTime");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "出现症状时间", index);
            }

            index = getIndex(queryStringList, "illnessHistory");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "现病史", index);
            }

            index = getIndex(queryStringList, "previousHistory");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "既往史", index);
            }

            index = getIndex(queryStringList, "checkupOther");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "体格检查", index);
            }

            index = getIndex(queryStringList, "auxExam");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "辅助检查", index);
            }

            index = getIndex(queryStringList, "inStorageTime");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "病历入库时间", index);
            }

            index = getIndex(queryStringList, "doctorName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "接诊医生", index);
            }

            index = getIndex(queryStringList, "dataSource");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "数据来源", index);
            }
            index = getIndex(queryStringList, "visitOrgName");
            if (index > 0) {
                sheet.setColumnWidth(index, 4000);
                createCellAndSetValue(firstRow, "就诊机构", index);
            }
            for (int i = 0; i < result.size(); i++) {
                index = 0;
                PoisonMedicalInfoDto vo = result.get(i);
                Row row = sheet.createRow(i + 1);
                createCellAndSetValue(row, vo.getPatientName(), index);

                index = getIndex(queryStringList, "sexDesc");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getSexDesc(), index);
                }

                index = getIndex(queryStringList, "age");
                if (index > 0) {
                    if(StringUtils.isNotEmpty(vo.getAge())){
                        createCellAndSetValue(row, vo.getAge() + vo.getAgeUnit(), index);
                    } else {
                        createCellAndSetValue(row, null, index);
                    }

                }

                index = getIndex(queryStringList, "happenPlace");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getHappenPlace(), index);
                }

                index = getIndex(queryStringList, "happenAddress");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getHappenAddress(), index);
                }

                index = getIndex(queryStringList, "livingAddress");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getLivingAddress(), index);
                }

                index = getIndex(queryStringList, "mainSuit");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getMainSuit(), index);
                }

                index = getIndex(queryStringList, "diagnoseName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getDiagnoseName(), index);
                }

                index = getIndex(queryStringList, "companyName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getCompanyName(), index);
                }

                index = getIndex(queryStringList, "careerName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getCareerName(), index);
                }

                index = getIndex(queryStringList, "orgName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getOrgName(), index);
                }

                index = getIndex(queryStringList, "pesticideType");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getPesticideType(), index);
                }

                index = getIndex(queryStringList, "pesticideName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getPesticideName(), index);
                }

                index = getIndex(queryStringList, "poisonReason");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getPoisonReason(), index);
                }

                index = getIndex(queryStringList, "outPatientTime");
                if (index > 0) {
                    createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getOutPatientTime(), "MM-dd HH:mm"), index);
                }

                index = getIndex(queryStringList, "foodName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getFoodName(), index);
                }

                index = getIndex(queryStringList, "foodType");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getFoodType(), index);
                }

                index = getIndex(queryStringList, "processingType");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getProcessingType(), index);
                }

                index = getIndex(queryStringList, "brand");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getBrand(), index);
                }

                index = getIndex(queryStringList, "manufacturer");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getManufacturer(), index);
                }

                index = getIndex(queryStringList, "eatingTime");
                if (index > 0) {
                    createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getEatingTime(), "MM-dd HH:mm"), index);
                }

                index = getIndex(queryStringList, "eatingAddress");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getEatingAddress(), index);
                }

                index = getIndex(queryStringList, "purchaseLocation");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getPurchaseLocation(), index);
                }

                index = getIndex(queryStringList, "symptomOnsetTime");
                if (index > 0) {
                    createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getSymptomOnsetTime(), "MM-dd HH:mm"), index);
                }

                index = getIndex(queryStringList, "identityNo");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getIdentityNo(), index);
                }

                index = getIndex(queryStringList, "illnessHistory");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getIllnessHistory(), index);
                }

                index = getIndex(queryStringList, "previousHistory");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getPreviousHistory(), index);
                }

                index = getIndex(queryStringList, "checkupOther");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getCheckupOther(), index);
                }

                index = getIndex(queryStringList, "auxExam");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getAuxExam(), index);
                }

                index = getIndex(queryStringList, "inStorageTime");
                if (index > 0) {
                    createCellAndSetValue(row, com.iflytek.fpva.cdc.util.DateUtils.parseDate(vo.getInStorageTime(), "MM-dd HH:mm"), index);
                }

                index = getIndex(queryStringList, "doctorName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getDoctorName(), index);
                }

                index = getIndex(queryStringList, "dataSource");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getDataSource(), index);
                }

                index = getIndex(queryStringList, "visitOrgName");
                if (index > 0) {
                    createCellAndSetValue(row, vo.getVisitOrgName(), index);
                }
            }
        }
    }

    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        if (StringUtils.isBlank(value)) {
            cell.setCellValue("--");
        } else {
            cell.setCellValue(value);
        }
    }

    private int getIndex(List<String> stringList, String queryString) {
        return stringList.indexOf(queryString) + 1;
    }

    private ByteArrayOutputStream generateOutputStream(XSSFWorkbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11451108", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }
}

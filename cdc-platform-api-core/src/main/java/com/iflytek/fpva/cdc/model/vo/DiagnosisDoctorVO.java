package com.iflytek.fpva.cdc.model.vo;

import com.iflytek.fpva.cdc.entity.TbCdcewDiagnosisDoctor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DiagnosisDoctorVO {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;

    /**
     * 区县编码
     */
    @ApiModelProperty("区县编码")
    private String districtCode;

    /**
     * 区县名称
     */
    @ApiModelProperty("区县名称")
    private String districtName;

    /**
     * 机构编码
     */
    @ApiModelProperty("机构编码")
    private String statDimId;

    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String statDimName;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 状态 0：禁用 1：启用 2：删除
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 职务名称
     */
    private String postName;


    /**
     * 性别名称
     */
    private String sexName;

    public static DiagnosisDoctorVO fromEntity(TbCdcewDiagnosisDoctor entity) {
        DiagnosisDoctorVO vo = new DiagnosisDoctorVO();
        vo.setId(entity.getId());
        vo.setProvinceCode(entity.getProvinceCode());
        vo.setProvinceName(entity.getProvinceName());
        vo.setCityCode(entity.getCityCode());
        vo.setCityName(entity.getCityName());
        vo.setDistrictCode(entity.getDistrictCode());
        vo.setDistrictName(entity.getDistrictName());
        vo.setStatDimId(entity.getStatDimId());
        vo.setStatDimName(entity.getStatDimName());
        vo.setName(entity.getName());
        vo.setPhone(entity.getPhone());
        vo.setStatus(entity.getStatus());
        vo.setPostName(entity.getPostName());
        vo.setSexName(entity.getSexName());
        return vo;
    }
}

package com.iflytek.fpva.cdc.service.poison.impl;

import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.constant.enums.AttentionLevelEnum;
import com.iflytek.fpva.cdc.constant.enums.CheckedLevelEnum;
import com.iflytek.fpva.cdc.constant.enums.ProcessTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewPoisonWarningEvent;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonWarningDetailMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonWarningEventMapper;
import com.iflytek.fpva.cdc.model.vo.call.SmsVo;
import com.iflytek.fpva.cdc.model.warningEvent.dto.EventProcessDTO;
import com.iflytek.fpva.cdc.service.outcall.OutCallService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.warningEvent.WarningEventProcessRecordService;
import com.iflytek.fpva.cdc.service.WarningEventProcessService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.util.UapAccessUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class PoisonWarningProcessEventImpl implements WarningEventProcessService {
    @Resource
    private WarningEventProcessRecordService processRecordService;
    @Resource
    TbCdcewPoisonWarningEventMapper tbCdcewPoisonWarningEventMapper;
    @Resource
    TbCdcewPoisonWarningDetailMapper tbCdcewPoisonWarningDetailMapper;
    @Resource
    OutCallService outCallService;
    @Resource
    PoisonEventService poisonEventService;
    @Resource
    private TbCdcConfigService tbCdcConfigService;

    @Override
    public String getWarningEventType() {
        return WarningTypeCodeEnum.POISON.getName();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startProcess(EventProcessDTO dto, String loginUserId, String loginUserName) {
        String eventId = dto.getEventId();
        Integer status = dto.getStatus();
        TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.findById(eventId);
        //查询不到 或 状态重复 不能进行操作
        if (event == null) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }
        UapAccessUtil.hasAccess(dto.getUapOrg(), event.getProvinceCode(), event.getCityCode(), event.getDistrictCode());
        Date processLatestTime = null;
        if (dto.isNeedUpdateProcessingLatestTime()){
            long processTimeout = tbCdcConfigService.getPoisonProcessingTimeoutConfig();
            processLatestTime = org.apache.commons.lang.time.DateUtils.addHours(new Date(), (int) processTimeout);
        }

        processRecordService.insertRecord(dto,loginUserId,loginUserName);
        //将原始事件表里的processing_status改为正在处理
        tbCdcewPoisonWarningEventMapper.updateProcessingStatusAndEndTimeById(status, dto.getEndTime(), eventId,processLatestTime);
        //2020-11-26 需要将所关联的detail中的状态也同步修改
        tbCdcewPoisonWarningDetailMapper.updateProcessingStatusByEventId(status, eventId);


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableTopEvent(SmsVo smsVo, String loginUserId, String loginUserName) {
        log.info("中毒手动置顶开始，信息：loginUserId:" + loginUserId + " ,loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        //1, 更新AI筛选状态
        poisonEventService.updateAiScreen(AttentionLevelEnum.TOP.getType(), smsVo.getEventId());

        //2, 发送短信提醒
        outCallService.sendPoisonSmsForOnTop(loginUserId, loginUserName, smsVo);

        processRecordService.addProcessRecord(getWarningEventType(), smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.TOP.getCode(), "置顶");

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTopEvent(SmsVo smsVo, String loginUserId, String loginUserName) {
        log.info("中毒手动事件取消置顶开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        //更新AI筛选状态
        poisonEventService.updateAiScreen(AttentionLevelEnum.ORDINARY.getType(), smsVo.getEventId());

        processRecordService.addProcessRecord(getWarningEventType(), smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.TOP.getCode(), Constants.CANCEL_TOP);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableAttentionEvent(SmsVo smsVo, String loginUserId, String loginUserName) {
        log.info("中毒手动事件标记开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewPoisonWarningEvent event = poisonEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //限定标记级别
        if (!(AttentionLevelEnum.ATTENTION_LEVEL1.getType().equals(smsVo.getAttentionLevel()) || AttentionLevelEnum.ATTENTION_LEVEL2.getType().equals(smsVo.getAttentionLevel()))) {
            throw new MedicalBusinessException("11450003", "事件标记级别有误");
        }

        //1, 更新AI筛选状态
        poisonEventService.updateAiScreen(smsVo.getAttentionLevel(), smsVo.getEventId());

        //更新为已审核状态    事件为标记时，默认就是已审核的
        poisonEventService.updateCheckedLevel(CheckedLevelEnum.CHECKED.getType(), smsVo.getEventId());

        processRecordService.addProcessRecord(getWarningEventType(), smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.ATTENTION.getCode(), ProcessTypeEnum.ATTENTION.getName());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAttentionEvent(SmsVo smsVo, String loginUserId, String loginUserName) {
        log.info("中毒手动事件取消标记开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewPoisonWarningEvent event = poisonEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //更新AI筛选状态
        poisonEventService.updateAiScreen(AttentionLevelEnum.ORDINARY.getType(), smsVo.getEventId());

        processRecordService.addProcessRecord(getWarningEventType(), smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.ATTENTION.getCode(), Constants.CANCEL_ATTENTION);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableCheckedEvent(SmsVo smsVo, String loginUserId, String loginUserName) {
        log.info("中毒手动事件已审核开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewPoisonWarningEvent event = poisonEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //更新为已审核状态
        poisonEventService.updateCheckedLevel(CheckedLevelEnum.CHECKED.getType(), smsVo.getEventId());

        processRecordService.addProcessRecord(getWarningEventType(), smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.CHECKED.getCode(), ProcessTypeEnum.CHECKED.getName());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCheckedEvent(SmsVo smsVo, String loginUserId, String loginUserName) {
        log.info("中毒手动事件取消审核开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewPoisonWarningEvent event = poisonEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //更新为未审核状态
        poisonEventService.updateCheckedLevel(CheckedLevelEnum.NOT_CHECKED.getType(), smsVo.getEventId());

        processRecordService.addProcessRecord(getWarningEventType(), smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.CHECKED.getCode(), Constants.CANCEL_CHECKED);

    }
}

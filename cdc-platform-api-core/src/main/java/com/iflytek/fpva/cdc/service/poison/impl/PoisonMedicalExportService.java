package com.iflytek.fpva.cdc.service.poison.impl;

import com.iflytek.fpva.cdc.model.vo.poison.excel.*;
import com.iflytek.fpva.cdc.model.warningEvent.vo.PoisonMedicalInfoVO;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PoisonMedicalExportService {

    /**
     * 食源性
     */
    public ResponseEntity<byte[]> exportFood(List<PoisonMedicalInfoVO> poisonRecordList,
                                             String configDesc,
                                             String eventId){
        List<PoisonFoodExcelVO> resultList = poisonRecordList.stream().map(PoisonFoodExcelVO::from).collect(Collectors.toList());
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonFoodExcelVO.class, configDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PoisonFoodExcelVO.class, true, eventId + ".xlsx", propertyMetaList);

    }

    /**
     * 农药中毒
     */
    public ResponseEntity<byte[]> exportPesticide(List<PoisonMedicalInfoVO> poisonRecordList,
                                             String configDesc,
                                             String eventId){
        List<PoisonPesticideExcelVO> resultList = poisonRecordList.stream().map(PoisonPesticideExcelVO::from).collect(Collectors.toList());
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonPesticideExcelVO.class, configDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PoisonPesticideExcelVO.class, true, eventId + ".xlsx", propertyMetaList);

    }

    /**
     * 职业中毒
     */
    public ResponseEntity<byte[]> exportCareer(List<PoisonMedicalInfoVO> poisonRecordList,
                                                  String configDesc,
                                                  String eventId){
        List<PoisonCareerExcelVO> resultList = poisonRecordList.stream().map(PoisonCareerExcelVO::from).collect(Collectors.toList());
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonCareerExcelVO.class, configDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PoisonCareerExcelVO.class, true, eventId + ".xlsx", propertyMetaList);

    }

    /**
     * 非职业一氧化碳中毒
     */
    public ResponseEntity<byte[]> exportCarbonMonoxide(List<PoisonMedicalInfoVO> poisonRecordList,
                                               String configDesc,
                                               String eventId){
        List<PoisonCarbonMonoxideExcelVO> resultList = poisonRecordList.stream().map(PoisonCarbonMonoxideExcelVO::from).collect(Collectors.toList());
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonCarbonMonoxideExcelVO.class, configDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PoisonCarbonMonoxideExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
    }

    /**
     * 高温中暑
     */
    public ResponseEntity<byte[]> exportHeatstroke(List<PoisonMedicalInfoVO> poisonRecordList,
                                                   String configDesc,
                                                   String eventId){
        List<PoisonHeatstrokeExcelVO> resultList = poisonRecordList.stream().map(PoisonHeatstrokeExcelVO::from).collect(Collectors.toList());
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonHeatstrokeExcelVO.class, configDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PoisonHeatstrokeExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
    }

    /**
     * 过量受照
     */
    public ResponseEntity<byte[]> exportRadiate(List<PoisonMedicalInfoVO> poisonRecordList,
                                                   String configDesc,
                                                   String eventId){
        List<PoisonRadiateExcelVO> resultList = poisonRecordList.stream().map(PoisonRadiateExcelVO::from).collect(Collectors.toList());
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonRadiateExcelVO.class, configDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PoisonRadiateExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
    }



}

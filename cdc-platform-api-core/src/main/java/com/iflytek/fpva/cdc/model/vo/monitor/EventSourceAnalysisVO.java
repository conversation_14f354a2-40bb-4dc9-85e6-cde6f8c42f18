package com.iflytek.fpva.cdc.model.vo.monitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date :2023/10/19 15:41
 * @description:EventSourceAnalysisVO
 */
@Data
public class EventSourceAnalysisVO {


    @ApiModelProperty("事件类型 DJYY-等级医院；JCYL-基层医疗；XXDW-学校单位；JDQY-街道区域")
    private String eventType;

    @ApiModelProperty("症候群类型")
    private String symptomType;

    @ApiModelProperty("症候群名称")
    private String syndromeName;



    @ApiModelProperty("预警事件数")
    private Long warningEventNum;

    @ApiModelProperty("阳性事件数")
    private Long positiveEventNum;

}

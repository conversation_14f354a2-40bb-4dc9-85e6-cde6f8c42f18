package com.iflytek.fpva.cdc.model.vo.spectrum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Comparator;
import java.util.Objects;

@Data
public class EventSpectrumVO {

    public static final String TITLE_MED_HIS_ONSET_TIME = "发病时间";
    public static final String TITLE_MED_HIS_PHYSICAL_SIGN = "体征";
    public static final String TITLE_MED_HIS_EXAMINE = "检验检测";
    public static final String TITLE_MED_HIS_EPI_HIS = "流行病学史";

    @ApiModelProperty("主要症状")
    private String sortedKeySymptomTags;

    @ApiModelProperty("相关症状")
    private String otherSymptoms;

//    @ApiModelProperty("其他病历信息标题")
    private String otherMedHisTitle;

    @ApiModelProperty("其他病历信息")
    private String otherMedHisInfo;

    @ApiModelProperty("数量")
    private int medHisCnt;

    @ApiModelProperty("比例")
    private double ratio;

    public EventSpectrumVO(String sortedKeySymptomTags,
                           String otherSymptoms,
                           String otherMedHisTitle,
                           String otherMedHisInfo) {
        this.sortedKeySymptomTags = sortedKeySymptomTags;
        this.otherSymptoms = otherSymptoms;
        this.otherMedHisTitle = otherMedHisTitle;
        if (TITLE_MED_HIS_ONSET_TIME.equals(this.otherMedHisTitle)) {
            this.otherMedHisInfo = "发病" + otherMedHisInfo;
        } else {
            this.otherMedHisInfo = otherMedHisInfo;
        }

    }

    public static Comparator<EventSpectrumVO> comparator() {
        return Comparator.comparing(EventSpectrumVO::getSortedKeySymptomTags)
                         .thenComparing(EventSpectrumVO::getOtherSymptoms)
                         .thenComparing(EventSpectrumVO::getOtherMedHisTitle)
                         .thenComparing(Comparator.comparing(EventSpectrumVO::getRatio).reversed());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EventSpectrumVO that = (EventSpectrumVO) o;
        return Objects.equals(sortedKeySymptomTags, that.sortedKeySymptomTags) &&
               Objects.equals(otherSymptoms, that.otherSymptoms) &&
               Objects.equals(otherMedHisTitle, that.otherMedHisTitle) &&
               Objects.equals(otherMedHisInfo, that.otherMedHisInfo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sortedKeySymptomTags, otherSymptoms, otherMedHisTitle, otherMedHisInfo);
    }
}

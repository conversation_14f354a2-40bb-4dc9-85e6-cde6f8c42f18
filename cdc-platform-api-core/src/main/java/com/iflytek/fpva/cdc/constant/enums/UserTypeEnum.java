package com.iflytek.fpva.cdc.constant.enums;

/**
 * 用户类型
 * */
public enum UserTypeEnum {

    ORDINARY_USER("0", "普通用户"),
    INTERN("1", "实习生"),      // 标记、已审权限
    DOCTOR("2", "家庭医生");    // 置顶权限

    private String type;
    private String desc;

    UserTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}

package com.iflytek.fpva.cdc.model.vo.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 购药人信息
 *
 * <AUTHOR>
 * @date 2022-05-20 16:41
 */
@Data
@ApiModel("购药人信息")
public class Purchaser {

    /**
     * 患者主索引ID
     */
    @ApiModelProperty("患者主索引ID")
    private String globalPersonId;
    /**
     * 居民身份证号
     */
    @ApiModelProperty("居民身份证号")
    private String patientIdNumber;
    /**
     * 证件类型名称
     */
    @ApiModelProperty("证件类型名称")
    private String identityTypeName;
    /**
     * 患者姓名
     */
    @ApiModelProperty("患者姓名")
    private String patientName;
    /**
     * 性别名称
     */
    @ApiModelProperty("性别名称")
    private String sexName;
    /**
     * 年龄
     */
    @ApiModelProperty("年龄")
    private String age;
    /**
     * 本人电话号码
     */
    @ApiModelProperty("本人电话号码")
    private String telephone;
    /**
     * 现工作单位名称
     */
    @ApiModelProperty("现工作单位/学校")
    private String companyName;
    /**
     * 证件地址
     */
    @ApiModelProperty("证件地址")
    private String householdAddrDetail;
    /**
     * 现住地详细地址
     */
    @ApiModelProperty("现住地详细地址")
    private String livingAddress;
    /**
     * 所属区域
     */
    @ApiModelProperty("所属区域")
    private String stdLivingPlaceName;

    /**
     * 购药时间
     */
    @ApiModelProperty("购药时间")
    private String purchaseTime;
}

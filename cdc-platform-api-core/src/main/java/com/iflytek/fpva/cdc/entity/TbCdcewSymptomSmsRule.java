package com.iflytek.fpva.cdc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcew_symptom_sms_rule
 *
 * <AUTHOR>
@Data
public class TbCdcewSymptomSmsRule implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;
    /**
     * 人员类型
     */
    @ApiModelProperty("人员类型:1.疾控，2.测试")
    private String userType;
    /**
     * 手机
     */
    @ApiModelProperty("手机")
    private String phone;
    /**
     * 规则类型
     */
    @ApiModelProperty("规则类型：1.全部，2.关注度，3.病例数")
    private String ruleType;
    /**
     * 规则参数
     */
    @ApiModelProperty("规则参数")
    private String ruleNum;
    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;
    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;
    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;
    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;
    /**
     * 区县名称
     */
    @ApiModelProperty("区县名称")
    private String districtName;
    /**
     * 区县编码
     */
    @ApiModelProperty("区县编码")
    private String districtCode;
    /**
     * 症候群名称
     */
    @ApiModelProperty("症候群名称")
    private String symptomName;
    /**
     * 症候群编码
     */
    @ApiModelProperty("症候群编码")
    private String symptomCode;
    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updater;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 删除标识
     */
    @ApiModelProperty("删除标识")
    private Integer deleted;
    /**
     * 业务人员ID
     */
    @ApiModelProperty("业务人员ID")
    private String businessPersonId;
}
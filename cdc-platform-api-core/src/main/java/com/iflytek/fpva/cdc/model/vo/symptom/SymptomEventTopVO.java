package com.iflytek.fpva.cdc.model.vo.symptom;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SymptomEventTopVO {

    @ApiModelProperty(value = "疾病编码")
    private String symptomCode;

    @ApiModelProperty(value = "疾病名称")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String symptomName;

    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "百分百")
    private double percent;
}

package com.iflytek.fpva.cdc.service.poison.impl;

import com.iflytek.fpva.cdc.apiService.AdminServiceApi;
import com.iflytek.fpva.cdc.apiService.UapServiceApi;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.constant.enums.PoisonTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.CdcConfig;
import com.iflytek.fpva.cdc.entity.TbCdcewFormConfig;
import com.iflytek.fpva.cdc.mapper.common.TbCdcConfigMapper;
import com.iflytek.fpva.cdc.mapper.poison.PoisonWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonWarningEventMapper;
import com.iflytek.fpva.cdc.model.common.CommonEventPointVO;
import com.iflytek.fpva.cdc.model.common.CommonWarningEventVO;
import com.iflytek.fpva.cdc.model.dto.RelatedEventDto;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.model.vo.poison.excel.*;
import com.iflytek.fpva.cdc.model.warningEvent.dto.MedicalListQueryDTO;
import com.iflytek.fpva.cdc.model.warningEvent.vo.*;
import com.iflytek.fpva.cdc.service.common.BusinessPersonService;
import com.iflytek.fpva.cdc.service.common.FormConfigService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.WarningEventService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.service.EventCommonUtilsService;
import com.iflytek.fpva.cdc.common.utils.DesensitizedUtils;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.SensitiveInterceptor.threadLocal;

@Service
public class PoisonWarningEventImpl implements WarningEventService {

    @Resource
    private TbCdcConfigService tbCdcConfigService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private BusinessPersonService businessPersonService;

    @Resource
    private PoisonEventService poisonEventService;

    @Resource
    private EventCommonUtilsService eventCommonUtilsService;

    @Resource
    private PoisonWarningEventMapper poisonWarningEventMapper;

    @Resource
    private TbCdcewPoisonMedRelationMapper tbCdcewPoisonMedRelationMapper;

    @Resource
    private TbCdcewPoisonWarningEventMapper tbCdcewPoisonWarningEventMapper;

    @Resource
    private TbCdcConfigMapper tbCdcConfigMapper;

    @Resource
    private AdminServiceApi adminServiceApi;

    @Resource
    private FormConfigService formConfigService;

    @Override
    public String getWarningEventType() {
        return WarningTypeCodeEnum.POISON.getName();
    }

    @Override
    public boolean isEnableAiScreen() {
        //是否能根据AI初筛进行排序  --  中毒
        return eventCommonUtilsService.isEnableAiScreen(CommonConstants.CONFIG_KEY_POISON_IS_AI_SCREEN_TOP);
    }

    @Override
    public List<WarningEventListVO> getWarningEventListBy(EventCriteria eventCriteria) {
        return tbCdcewPoisonWarningEventMapper.getWarningEventListBy(eventCriteria);
    }

    @Override
    public List<RelatedEventDto> getRelatedEvents(List<String> eventIds) {
        return Collections.emptyList();
    }

    @Override
    public List<PoisonMedicalInfoVO> getMedicalInfoListBy(MedicalListQueryDTO dto) {

        List<PoisonMedicalInfoVO> medicalInfos = tbCdcewPoisonMedRelationMapper.getPoisonMedicalInfoBy(dto);
        List<PoisonMedicalInfoVO> retList = new ArrayList<>(medicalInfos);
        //获取当前的信号属于哪一个中毒大类
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(dto.getEventId());
        switch (poisonTypeCode) {
            case Constants.FOOD:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getFoodborneReportBy(dto));
                break;
            case Constants.PESTICIDE:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getPesticideReportBy(dto));
                break;
            case Constants.CARBON_MONOXIDE:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getCarbonMonoxideReportBy(dto));
                break;
            case Constants.HEATSTROKE:
                //报卡
                retList.addAll(tbCdcewPoisonMedRelationMapper.getHeatstrokeReportBy(dto));
                retList.addAll(tbCdcewPoisonMedRelationMapper.getHeatstrokeReportFromDeathBy(dto));
                break;
            default:
        }
        return retList;
    }

    @Override
    public ResponseEntity<byte[]> exportEventMedicalList(List<Object> medicalList, MedicalListQueryDTO dto, String loginUserId) {

        List<PoisonMedicalInfoVO> medicalInfoList = medicalList.stream().map(o -> (PoisonMedicalInfoVO)o).collect(Collectors.toList());
        //校验是否超出文件导出最大值
        adminServiceApi.checkExportMax(medicalInfoList);
        //判断导出是否需要脱敏
//        boolean isDesensitized = tbCdcConfigService.isDesensitization(loginUserId);
//        if(isDesensitized){
//            medicalInfoList.forEach(DesensitizedUtils::desensitizedObject);
//        }
        boolean isDesensitized = threadLocal.get().getNormalFlag();
        boolean isDiagnoseRole = threadLocal.get().getDiagnoseVO().getDiagnoseFlag();

        for (PoisonMedicalInfoVO r : medicalInfoList) {
            if (isDesensitized){
                DesensitizedUtils.desensitizedObject(r);
            }
            if (isDiagnoseRole){
                DesensitizedUtils.desensitizedDiagnoseForVO(r,threadLocal.get().getDiagnoseVO().getDiagnoseNames());
            }
        }

        TbCdcewFormConfig formConfig = formConfigService.getConfigByLoginUserId(loginUserId, dto.getFormType());
        //没有列表表头配置时，直接置为null
        String formConfigDesc = formConfig == null ? null : formConfig.getConfigDesc();
        return this.exportPoisonRecordList(dto.getEventId(), medicalInfoList, formConfigDesc);
    }

    /**
     * 中毒不同类型的导出
     * */
    private ResponseEntity<byte[]> exportPoisonRecordList(String eventId, List<PoisonMedicalInfoVO> poisonRecordList, String configDesc){

        //获取当前的信号属于哪一个中毒大类
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(eventId);
        if (PoisonTypeEnum.FOOD.getCode().equals(poisonTypeCode)) {
            List<PoisonFoodExcelVO> resultList = poisonRecordList.stream().map(PoisonFoodExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonFoodExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonFoodExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        if (PoisonTypeEnum.PESTICIDE.getCode().equals(poisonTypeCode)) {
            List<PoisonPesticideExcelVO> resultList = poisonRecordList.stream().map(PoisonPesticideExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonPesticideExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonPesticideExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        if (PoisonTypeEnum.CAREER.getCode().equals(poisonTypeCode)) {
            List<PoisonCareerExcelVO> resultList = poisonRecordList.stream().map(PoisonCareerExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonCareerExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonCareerExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        if (PoisonTypeEnum.CARBON_MONOXIDE.getCode().equals(poisonTypeCode)) {
            List<PoisonCarbonMonoxideExcelVO> resultList = poisonRecordList.stream().map(PoisonCarbonMonoxideExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonCarbonMonoxideExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonCarbonMonoxideExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        if (PoisonTypeEnum.HEATSTROKE.getCode().equals(poisonTypeCode)) {
            List<PoisonHeatstrokeExcelVO> resultList = poisonRecordList.stream().map(PoisonHeatstrokeExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonHeatstrokeExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonHeatstrokeExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        if (PoisonTypeEnum.RADIATE.getCode().equals(poisonTypeCode)) {
            List<PoisonRadiateExcelVO> resultList = poisonRecordList.stream().map(PoisonRadiateExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonRadiateExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonRadiateExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        if (PoisonTypeEnum.OTHER.getCode().equals(poisonTypeCode)) {
            List<PoisonOtherExcelVO> resultList = poisonRecordList.stream().map(PoisonOtherExcelVO::from).collect(Collectors.toList());
            //获取导出列表表头
            List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PoisonOtherExcelVO.class, configDesc);
            return FileUtils.exportExcelByFormConfig(resultList, PoisonOtherExcelVO.class, true, eventId + ".xlsx", propertyMetaList);
        }
        return null;
    }

    @Override
    public EventTypeCountVO getEventTypeCountVO(String loginUserId, String loginUserName) {

        UapOrgPo userOrg = uapServiceApi.getUserOrg(loginUserName);
        List<String> diseaseCodes = businessPersonService.getCompletedDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.POISON.getType());

        return poisonWarningEventMapper.getEventTypeCount(userOrg.getProvinceCode(), userOrg.getCityCode(), userOrg.getDistrictCode(), diseaseCodes);
    }

    @Override
    public List<String> getCompanyByEventId(String eventId) {
        //中毒病例
        final List<String> companyList = tbCdcewPoisonMedRelationMapper.getMedicalCompanyByPoisonEventId(eventId);
        List<String> temp;
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(eventId);
        switch (poisonTypeCode) {
            case Constants.FOOD:
                temp = tbCdcewPoisonMedRelationMapper.getFoodborneCompanyByPoisonEventId(eventId);
                temp.addAll(companyList);
                break;
            case Constants.PESTICIDE:
                temp = tbCdcewPoisonMedRelationMapper.getPesticideCompanyByPoisonEventId(eventId);
                temp.addAll(companyList);
                break;
            case Constants.CARBON_MONOXIDE:
                temp = tbCdcewPoisonMedRelationMapper.getCOCompanyByPoisonEventId(eventId);
                temp.addAll(companyList);
                break;
            case Constants.HEATSTROKE:
                temp = tbCdcewPoisonMedRelationMapper.getHeatstrokeCompanyByPoisonEventId(eventId);
                temp.addAll(companyList);
                break;
            default:
                temp = companyList;
        }
        return temp;
    }

    @Override
    public <T extends CommonWarningEventVO> T getWarningEventMsgByEventId(String eventId) {

        return (T) tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(eventId);
    }

    @Override
    public Double findByConfigKey(String diseaseCode) {

        CdcConfig config = tbCdcConfigMapper.findByConfigKey(CommonConstants.POISON_SURROUND_ORG_DISTANCE);
        return config == null ? 0.0 : config.getConfigValue();
    }

    @Override
    public List<OrgMedCount> getOrgMedCountByIds(Date statDate, String diseaseCode, List<String> orgIds) {
        return poisonWarningEventMapper.getOrgMedCountByIds(statDate, diseaseCode, orgIds);
    }

    @Override
    public List<CommonEventPointVO> getAllEventPoints(EventCriteria eventCriteria) {
        return tbCdcewPoisonWarningEventMapper.getAllEventPointsBy(eventCriteria);
    }
}

package com.iflytek.fpva.cdc.service.symptom.impl;

import com.iflytek.fpva.cdc.entity.TbCdcewScSymptomRecord;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewScSymptomRecordMapper;
import com.iflytek.fpva.cdc.service.symptom.CdcScSymptomRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CdcScSymptomRecordServiceImpl implements CdcScSymptomRecordService {

    @Resource
    private TbCdcewScSymptomRecordMapper tbCdcewScSymptomRecordMapper;


    @Override
    public TbCdcewScSymptomRecord selectByPrimaryKey(String id) {
        return tbCdcewScSymptomRecordMapper.selectByPrimaryKey(id);
    }
}

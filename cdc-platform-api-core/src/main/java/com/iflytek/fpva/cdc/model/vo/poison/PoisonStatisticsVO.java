package com.iflytek.fpva.cdc.model.vo.poison;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PoisonStatisticsVO {

    // 响应超时事件数
    @ApiModelProperty("响应超时事件数")
    private int responseTimeout;

    // 处置超时事件数
    @ApiModelProperty("处置超时事件数")
    private int processTimeout;

    //待处理事件数
    @ApiModelProperty("待处理事件数")
    private int pending;

    //处理中事件数
    @ApiModelProperty("处理中事件数")
    private int processing;

    //已完成事件数
    @ApiModelProperty("已完成事件数")
    private int finished;

    // 人工排除数
    @ApiModelProperty("人工排除数")
    private int removedManual;

    // AI排除数
    @ApiModelProperty("AI排除数")
    private int removedAi;

    // 阳性事件数
    @ApiModelProperty("阳性事件数")
    private int positive;

    // 全部事件数
    @ApiModelProperty("全部事件数")
    private int total;

}

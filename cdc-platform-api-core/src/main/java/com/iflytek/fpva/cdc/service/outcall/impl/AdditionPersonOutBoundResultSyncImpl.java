package com.iflytek.fpva.cdc.service.outcall.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.TbCdcEventPersonRelation;
import com.iflytek.fpva.cdc.entity.TbCdcewOutcallPatientRelation;
import com.iflytek.fpva.cdc.entity.TbCdcewSympEventPersonRelation;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcEventPersonRelationMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewHisMedicalInfoMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympEventPersonRelationMapper;
import com.iflytek.fpva.cdc.model.vo.MedicalInfoVO;
import com.iflytek.fpva.cdc.model.vo.school.SchoolPersonSymptomRecordVO;
import com.iflytek.fpva.cdc.outbound.constant.OutboundConfig;
import com.iflytek.fpva.cdc.outbound.constant.enums.ConnectStatusEnum;
import com.iflytek.fpva.cdc.outbound.constant.enums.ExecStatusEnum;
import com.iflytek.fpva.cdc.outbound.model.dto.input.BatchList;
import com.iflytek.fpva.cdc.outbound.model.dto.output.BatchDetailRs;
import com.iflytek.fpva.cdc.outbound.model.dto.output.BatchListRs;
import com.iflytek.fpva.cdc.outbound.model.dto.output.Record;
import com.iflytek.fpva.cdc.outbound.model.dto.output.RecordDetailRs;
import com.iflytek.fpva.cdc.outbound.service.QueryOutBoundService;
import com.iflytek.fpva.cdc.outbound.util.Response;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.service.outcall.OutboundResultSyncService;
import com.iflytek.fpva.cdc.service.outcall.OutcallOrginalResultService;
import com.iflytek.fpva.cdc.service.outcall.OutcallPatientRelationService;
import com.iflytek.fpva.cdc.service.symptom.SympOutCallService;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdditionPersonOutBoundResultSyncImpl implements OutboundResultSyncService {

    @Resource
    private QueryOutBoundService queryOutBoundService;

    @Resource
    private TbCdcEventPersonRelationMapper tbCdcEventPersonRelationMapper;

    @Resource
    private TbCdcewHisMedicalInfoMapper hisMedicalInfoMapper;
    
    @Resource
    TbCdcewSympEventPersonRelationMapper tbCdcewSympEventPersonRelationMapper;

    @Resource
    private OutboundConfig outboundConfig;

    @Resource
    private FileService fileService;
    
    @Resource
    SympOutCallService sympOutCallService;

    @Resource
    private OutcallPatientRelationService outcallPatientRelationService;


    @Resource
    private OutcallOrginalResultService outcallOrginalResultService;

    @Override
    public int sync(Integer personType, Integer monitorType) {
        if (Integer.valueOf(MonitorCategoryEnum.SCHOOL.getId()).equals(monitorType)){
            return syncSchoolCallResult(personType);
        }
        return syncSympCallResult(personType);
    }

    private int syncSympCallResult(Integer personType) {
        AtomicInteger atomicInteger = new AtomicInteger(0);

        List<TbCdcEventPersonRelation> tbCdcEventPersonRelations = tbCdcEventPersonRelationMapper.findByOutCallStatusAndOutCallBatchIdIsNotNull(MedicalCallStatusEnum.CALLING.getCode());
        if (CollectionUtils.isEmpty(tbCdcEventPersonRelations)) {
            return 0;
        }

        Map<String, List<TbCdcEventPersonRelation>> eventPersonRelationMap = tbCdcEventPersonRelations.stream().collect(Collectors.groupingBy(TbCdcEventPersonRelation::getEventId));
        log.info("查询到症候群-关联人群外呼正在执行中数量：{}", tbCdcEventPersonRelations.size());

        List<MedicalInfoVO> medicalInfoVOList = hisMedicalInfoMapper.findRecordsByEventIds(new ArrayList<>(eventPersonRelationMap.keySet()));
        Map<String, List<MedicalInfoVO>> medicalInfoEventMap = medicalInfoVOList.stream().collect(Collectors.groupingBy(MedicalInfoVO::getEventId));
        tbCdcEventPersonRelations.forEach(tbCdcEventPersonRelation -> {
            Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(tbCdcEventPersonRelation.getCallBatchId());
            BatchDetailRs batchDetailRs = batchDetailRsResponse.getData();
            if (batchDetailRsResponse.isSuccess() && null != batchDetailRs && batchDetailRs.getExecStatus().equals(ExecStatusEnum.COMPLETED.getCode())) {
                Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(BatchList.builder().batch(tbCdcEventPersonRelation.getCallBatchId()).build());
                Record record = batchListRsResponse.getData().getData().get(0);
                LinkedHashMap<String, String> callResult = new LinkedHashMap<>(8);
                callResult.put("外呼状态", record.getCallResult());
                if (ConnectStatusEnum.NORMAL_ANSWER.getCode().equals(record.getEndNode())) {
                    List<MedicalInfoVO> cdcHisMedicalInfos = medicalInfoEventMap.computeIfAbsent(tbCdcEventPersonRelation.getPersonName(), v -> new ArrayList<>());
                    Map<String, List<MedicalInfoVO>> medicalInfoMap = cdcHisMedicalInfos.stream().collect(Collectors.groupingBy(MedicalInfoVO::getSourceKey));
                    List<MedicalInfoVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(record.getRelationId(), v -> new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(medicalInfoVOS)) {
                        MedicalInfoVO medicalInfo = medicalInfoVOS.get(0);
                        String symptom = medicalInfo.getKeySymptomTagList().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
                        callResult.put("症状问询", record.getWetherDiarrhea()+symptom+"症状");
                    }
                    tbCdcEventPersonRelation.setIsSick(Integer.valueOf(getBusinessWetherDiarrheaEnum(record.getWetherDiarrhea()).getCode()));
                    tbCdcEventPersonRelation.setConnectStatus(CallConnectStatusEnum.CONNECTED.getCode());
                    tbCdcEventPersonRelation.setAudioUrl(getAudioUrl(record));

                    //获取音频地址需要重试操作(外呼返回音频地址有延迟)，如果超过30分钟仍然没有结果就终止
                    Long taskTime = batchDetailRs.getTaskTime();
                    long gapMinute = (System.currentTimeMillis() - Optional.ofNullable(taskTime).orElse(0L)) / 1000 / 60;
                    if (StringUtils.isNotBlank(tbCdcEventPersonRelation.getAudioUrl()) || gapMinute >= 30 ) {
                        tbCdcEventPersonRelation.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                    }
                } else {
                    tbCdcEventPersonRelation.setConnectStatus(CallConnectStatusEnum.UNCONNECTED.getCode());
                    tbCdcEventPersonRelation.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                }
                String result = JSON.toJSONString(callResult);
                tbCdcEventPersonRelation.setCallResult(result);
                tbCdcEventPersonRelation.setUpdateTime(new Date());
                atomicInteger.incrementAndGet();
                tbCdcEventPersonRelationMapper.updateByPrimaryKeySelective(tbCdcEventPersonRelation);
            }
        });
        log.info("更新症候群-关联人群外呼结果数量：{}", atomicInteger.get());
        return atomicInteger.get();
    }

    private int syncSchoolCallResult(Integer personType) {
        AtomicInteger atomicInteger = new AtomicInteger(0);

        List<TbCdcewSympEventPersonRelation> tbCdcEventPersonRelations = tbCdcewSympEventPersonRelationMapper.findByOutCallStatusAndOutCallBatchIdIsNotNull(MedicalCallStatusEnum.CALLING.getCode());
        if (CollectionUtils.isEmpty(tbCdcEventPersonRelations)) {
            return 0;
        }

        log.info("查询到学校症状-关联人群外呼正在执行中数量：{}", tbCdcEventPersonRelations.size());
        tbCdcEventPersonRelations.forEach(tbCdcEventPersonRelation -> {
            Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(tbCdcEventPersonRelation.getCallBatchId());
            BatchDetailRs batchDetailRs = batchDetailRsResponse.getData();
            if (batchDetailRsResponse.isSuccess() && null != batchDetailRs && batchDetailRs.getExecStatus().equals(ExecStatusEnum.COMPLETED.getCode())) {
                Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(BatchList.builder().batch(tbCdcEventPersonRelation.getCallBatchId()).build());
                Record record = batchListRsResponse.getData().getData().get(0);
                LinkedHashMap<String, String> callResult = new LinkedHashMap<>(8);
                callResult.put("外呼状态", record.getCallResult());
                if (ConnectStatusEnum.NORMAL_ANSWER.getCode().equals(record.getEndNode())) {
                    List<SchoolPersonSymptomRecordVO> list = sympOutCallService.getResident(tbCdcEventPersonRelation.getEventId());
                    Map<String, List<SchoolPersonSymptomRecordVO>> medicalInfoMap = list.stream().collect(Collectors.groupingBy(SchoolPersonSymptomRecordVO::getId));
                    List<SchoolPersonSymptomRecordVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(record.getRelationId(), v -> new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(medicalInfoVOS)) {
                        SchoolPersonSymptomRecordVO medicalInfo = medicalInfoVOS.get(0);
                        String symptom = medicalInfo.getSymptomContent().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
                        callResult.put("症状问询", record.getWetherDiarrhea()+symptom+"症状");
                    }
                    tbCdcEventPersonRelation.setIsSick(Integer.valueOf(getBusinessWetherDiarrheaEnum(record.getWetherDiarrhea()).getCode()));
                    tbCdcEventPersonRelation.setConnectStatus(CallConnectStatusEnum.CONNECTED.getCode());
                    tbCdcEventPersonRelation.setAudioUrl(getAudioUrl(record));

                    //获取音频地址需要重试操作(外呼返回音频地址有延迟)，如果超过30分钟仍然没有结果就终止
                    Long taskTime = batchDetailRs.getTaskTime();
                    long gapMinute = (System.currentTimeMillis() - Optional.ofNullable(taskTime).orElse(0L)) / 1000 / 60;
                    if (StringUtils.isNotBlank(tbCdcEventPersonRelation.getAudioUrl()) || gapMinute >= 30 ) {
                        tbCdcEventPersonRelation.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                    }
                } else {
                    tbCdcEventPersonRelation.setConnectStatus(CallConnectStatusEnum.UNCONNECTED.getCode());
                    tbCdcEventPersonRelation.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                }
                String result = JSON.toJSONString(callResult);
                tbCdcEventPersonRelation.setCallResult(result);
                tbCdcEventPersonRelation.setUpdateTime(new Date());
                atomicInteger.incrementAndGet();
                tbCdcewSympEventPersonRelationMapper.updateByPrimaryKeySelective(tbCdcEventPersonRelation);
            }
        });
        log.info("更新学校症状-关联人群外呼结果数量：{}", atomicInteger.get());
        return atomicInteger.get();
    }

    public CallResultEnum getBusinessWetherDiarrheaEnum(String wetherDiarrhea) {
        if (TrueOrFalseEnum.YES.getDesc1().equals(wetherDiarrhea)) {
            return CallResultEnum.TRUE;
        }
        return CallResultEnum.NOT_TRUE;
    }

    public String getAudioUrl(Record record) {
        String audioUrl = null;
        Response<RecordDetailRs> recordDetailRsResponse = queryOutBoundService.queryRecordDetail(record.getRecordId());
        if (recordDetailRsResponse.isSuccess() && com.iflytek.fpva.cdc.util.StringUtils.isNotBlank(recordDetailRsResponse.getData().getLocalUrl())) {
            String recordUrl = outboundConfig.getRecordUrl();
            String path = URLUtil.getPath(recordDetailRsResponse.getData().getLocalUrl());
            String fileUrl = recordUrl + path;
            if (com.iflytek.fpva.cdc.util.StringUtils.isBlank(recordUrl)) {
                fileUrl = recordDetailRsResponse.getData().getLocalUrl();
            }
            audioUrl = fileService.storeMediaToFileServer(fileUrl);
        }
        return audioUrl;
    }


    @Override
    public int syncNew(Integer personType, String configType) {
        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<TbCdcewOutcallPatientRelation> patientRelations = outcallPatientRelationService.findByOutCallStatusAndOutCallBatchIdIsNotNull(MedicalCallStatusEnum.CALLING.getCode(),configType);
        if (CollectionUtils.isEmpty(patientRelations)) {
            return 0;
        }
        log.info("查询到：{} -关联人群外呼正在执行中数量：{}", configType, patientRelations.size());

        patientRelations.forEach(patientRelation -> {
            Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(patientRelation.getCallBatchId());
            outcallOrginalResultService.saveData(patientRelation.getCallBatchId(), JSONObject.toJSONString(batchDetailRsResponse),"queryBatchDetail");
            BatchDetailRs batchDetailRs = batchDetailRsResponse.getData();
            if (batchDetailRsResponse.isSuccess() && null != batchDetailRs && batchDetailRs.getExecStatus().equals(ExecStatusEnum.COMPLETED.getCode())) {
                Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(BatchList.builder().batch(patientRelation.getCallBatchId()).build());
                outcallOrginalResultService.saveData(patientRelation.getCallBatchId(), JSONObject.toJSONString(batchListRsResponse),"queryBatchList");

                Record record = batchListRsResponse.getData().getData().get(0);
                LinkedHashMap<String, String> callResult = new LinkedHashMap<>(8);
                callResult.put("外呼状态", record.getCallResult());
                boolean isSuccess = ConnectStatusEnum.NORMAL_ANSWER.getCode().equals(record.getEndNode());
                if (isSuccess) {
                    String symptom = patientRelation.getSymptomTagList().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
                    callResult.put("症状问询", record.getWetherDiarrhea()+symptom+"症状");
                    patientRelation.setIsSick(Integer.valueOf(getBusinessWetherDiarrheaEnum(record.getWetherDiarrhea()).getCode()));
                    patientRelation.setConnectStatus(CallConnectStatusEnum.CONNECTED.getCode());
                    patientRelation.setAudioUrl(getAudioUrl(record));

                    //获取音频地址需要重试操作(外呼返回音频地址有延迟)，如果超过30分钟仍然没有结果就终止
                    Long taskTime = batchDetailRs.getTaskTime();
                    long gapMinute = (System.currentTimeMillis() - Optional.ofNullable(taskTime).orElse(0L)) / 1000 / 60;
                    if (StringUtils.isNotBlank(patientRelation.getAudioUrl()) || gapMinute >= 30 ) {
                        patientRelation.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                    }
                } else {
                    patientRelation.setConnectStatus(CallConnectStatusEnum.UNCONNECTED.getCode());
                    patientRelation.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                }
                String result = JSON.toJSONString(callResult);
                patientRelation.setCallResult(result);
                patientRelation.setUpdateTime(LocalDateTime.now());
                atomicInteger.incrementAndGet();
                outcallPatientRelationService.updateById(patientRelation);
            }
        });
        log.info("更新:{}-关联人群外呼结果数量：{}", configType, atomicInteger.get());
        return atomicInteger.get();
    }
}

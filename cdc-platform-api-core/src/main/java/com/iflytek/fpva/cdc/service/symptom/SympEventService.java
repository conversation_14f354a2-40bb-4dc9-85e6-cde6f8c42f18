package com.iflytek.fpva.cdc.service.symptom;

import com.iflytek.fpva.cdc.entity.TbCdcewSympEventAnalysis;
import com.iflytek.fpva.cdc.entity.TbCdcewSympWarningEvent;
import com.iflytek.fpva.cdc.model.dto.school.SchoolEventListQueryDTO;
import com.iflytek.fpva.cdc.model.dto.symptom.StatisticsDTO;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.org.OrgVO;
import com.iflytek.fpva.cdc.model.vo.school.QueryVO;
import com.iflytek.fpva.cdc.model.vo.school.SchoolPersonSymptomRecordVO;
import com.iflytek.fpva.cdc.model.vo.symptom.*;
import com.iflytek.fpva.cdc.model.vo.time.SymptomTimeVO;
import com.iflytek.fpva.cdc.model.warningEvent.vo.EventTypeCountVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SympEventService {

    /**
     * 开始处理事件
     *
     * @param loginUserId   网关带入的登录用户id 用来查询处置机构
     * @param loginUserName 登录用户名
     * @param eventId       事件id
     * @param status        事件状态
     */
    void startProcess(String loginUserId, String eventId, int status, String loginUserName);

    /**
     * 新增综合研判结果
     *
     * @param eventAnalysisResultVO
     * @return
     */
    TbCdcewSympEventAnalysis createEventAnalysisResult(EventAnalysisResultVO eventAnalysisResultVO, String loginUserId, String loginUserName);

    List<EventAnalysisResultVO> getEventAnalysisResult(String eventId);
    
    
    /**
     * AI初筛置顶/取消置顶操作
     *
     * @param isAiScreen 是否初筛
     * @param eventId    事件ID
     */
    void updateAiScreen(int isAiScreen, String eventId);

    /**
     * 事件审查级别
     *
     * @param checkedLevel 审查级别  0:未审 1:已审
     * @param eventId      事件ID
     */
    void updateCheckedLevel(int checkedLevel, String eventId);

    void addProcessRecord(String eventId, String loginUserId, String loginUserName, String processType, String processDesc);

    TbCdcewSympWarningEvent getCdcWaringEventByEventId(String eventId);

    ScStatisticsVO statistics(String loginUserName, String loginUserId, String eventType,Integer processingStatus);

    List<CascadeVO> getSymptoms(String loginUserId);

    List<CascadeVO> getSymptoms();

    PageData<EventVO> getSchoolEventList(SchoolEventListQueryDTO dto, String loginUserName, String loginUserId);

    PageData<SchoolPersonSymptomRecordVO> getSymptomRecordList(String eventId, String loginUserId, String patientName, String schoolName, String diagnoseName, Integer pageIndex, Integer pageSize);

    List<SchoolPersonSymptomRecordVO> getSymptomRecordList(String eventId,String patientName);

    EventListVO mapRelation(String eventId, String loginUserId);

    /**
     * 构建事件统计信息
     *
     * @param eventId
     * @return
     */
    EventVO buildEventVo(String eventId);

    EventExVO humanDistribution(String eventId, String loginUserId, QueryVO queryVO);

    List<Map<String,Object>> getEventMedCountByDate(String eventId,String startTime,String endTime);

    List<Map<String, String>> getWarningEventSymptomDistribution(String eventId);

    EventVO eventDetail(String eventId, String loginUserId);

    void setMedicalCountInfo(EventVO eventVO);

    OrgMedicalVO orgMedDistribution(String eventId);

    List<SymptomTimeVO> queryTimeTrend(QueryVO queryVO);

    EventVO getAppEventDetail(String eventId, String loginUserId);

    TbCdcewSympWarningEvent getScWaringEventByEventId(String eventId);

    List<Map<String, Object>> getMedOutCallCount(String eventId, String startTime, String endTime);

    List<Map<String, Object>> getWarningEventResourceDistribution(String eventId);

    List<Date> getEventTimeLine(String eventId);

    EventTypeCountVO getEventTypeCountVO(String loginUserId, String loginUserName);

    List<OrgVO> getMedOrgList(String eventId);

    List<AgeGroupVO> ageDistribution(String eventId, QueryVO queryVO);

    byte[] exportMedicalListNew(List<SchoolPersonSymptomRecordVO> data, String loginUserId, String formType);

    List<String> getSymptomCompanySchoolByEventId(String eventId);

    SymptomEventStatisticsVO getEventStatisticsInfo(StatisticsDTO dto);

    PageData<SymptomEventVO> queryEventStatisticsList(StatisticsDTO dto);

    List<SymptomEventTypeTrendVO> queryEventTrendList(StatisticsDTO dto);

    List<SymptomEventTopVO> queryEventTopList(StatisticsDTO dto);

    byte[] exportEventStatisticsInfo(StatisticsDTO dto);

    byte[] exportEventStatisticsList(StatisticsDTO dto);
}

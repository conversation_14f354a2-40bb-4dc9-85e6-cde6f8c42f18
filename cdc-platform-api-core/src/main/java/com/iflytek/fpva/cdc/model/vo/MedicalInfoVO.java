package com.iflytek.fpva.cdc.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.constant.enums.CallConnectStatusEnum;
import com.iflytek.fpva.cdc.constant.enums.ClinicTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.MedicalCallStatusEnum;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import com.iflytek.fpva.cdc.model.dto.EventInfoDto;
import com.iflytek.fpva.cdc.model.dto.syndrome.AppSyndromeMedicalDetailDTO;
import com.iflytek.fpva.cdc.util.BeanUtils;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.math3.util.Pair;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Getter
@Setter
@ApiModel(value = "病历信息VO")
public class MedicalInfoVO {

    @ApiModelProperty("病历ID")
    private String sourceKey;

    @ApiModelProperty("数据来源")
    private String dataSourceCode;

    @ApiModelProperty("数据类型")
    private String dataSrc;

    @ApiModelProperty("患者姓名")
    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    private String patientName;

    @ApiModelProperty("患者ID")
    private String patientId;

    @ApiModelProperty("医生姓名")
    @Sensitive(type = SensitiveTypeEnum.DOCTOR)
    private String doctorName;

    @ApiModelProperty("医生ID")
    private String doctorId;

    @ApiModelProperty("患者电话")
    private String patientPhone;

    @ApiModelProperty("患者年龄")
    private String diagnoseAge;

    @ApiModelProperty("患者年龄单位")
    private String diagnoseAgeUnit;

    @ApiModelProperty("诊断")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String diagnoseName;

    @ApiModelProperty("性别")
    private String sexDesc;

    @ApiModelProperty("症候群名称")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String symptomType;

    @ApiModelProperty("信号维度名称")
    @Sensitive(type = SensitiveTypeEnum.ADDRESS)
    private String statDimName;

    @ApiModelProperty("信号维度ID")
    private String statDimId;

    @ApiModelProperty("机构ID")
    private String orgId;

    @ApiModelProperty("机构名称")
    @Sensitive(type = SensitiveTypeEnum.ORG)
    private String orgName;

    @ApiModelProperty("单位名称")
    @Sensitive(type = SensitiveTypeEnum.COMPANY)
    private String companyName;

    @ApiModelProperty("身份证号")
    @Sensitive(type = SensitiveTypeEnum.ID_NUM)
    private String identityNo;

    @ApiModelProperty("职业")
    private String careerName;

    @ApiModelProperty("病例来源")
    private String sourceType;

    @ApiModelProperty("病历入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date fullDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date outPatientTime;

    @ApiModelProperty("信号详情ID")
    private String detailId;

    @ApiModelProperty("事件ID")
    private String eventId;

    @ApiModelProperty("城市编码")
    private String cityCode;

    @ApiModelProperty("区县编码")
    private String districtCode;

    @ApiModelProperty("城市名称")
    @Sensitive(type = SensitiveTypeEnum.REGION)
    private String cityName;

    @ApiModelProperty("区县名称")
    @Sensitive(type = SensitiveTypeEnum.REGION)
    private String districtName;

    //  ---  外呼相关 --
    @ApiModelProperty("外呼批次ID")
    private String outCallBatchId;

    @ApiModelProperty("外呼状态")
    private Integer outCallStatus;

    @ApiModelProperty("病例确认结果")
    private Integer confirmedResult;

    @ApiModelProperty("外呼接通状态")
    private Integer connectStatus;

    @ApiModelProperty("短信是否发送")
    private Integer smsSent;

    @ApiModelProperty("智医助理调用状态")
    private Integer zyzlStatus;

    @ApiModelProperty("智医助理调用结果")
    private Integer zyzlResult;


    @ApiModelProperty("是否为聚集性病例")
    private Integer isCluster;

    //  ---  外呼相关 --

    @ApiModelProperty("家庭住址(标准)")
    @Sensitive(type = SensitiveTypeEnum.ADDRESS)
    private String livingAddress;

    @ApiModelProperty("家庭住址经度")
    private Double longitude;

    @ApiModelProperty("家庭住址纬度")
    private Double latitude;

    @ApiModelProperty("单位名称")
    @Sensitive(type = SensitiveTypeEnum.COMPANY)
    private String company;

    @ApiModelProperty("单位代码")
    private String companyCode;

    @ApiModelProperty("单位经度")
    private Double companyLongitude;

    @ApiModelProperty("单位纬度")
    private Double companyLatitude;


    @ApiModelProperty("病历是否真实")
    private Integer medAuthenticity;

    @ApiModelProperty("增强条件")
    private String enhanceConditionList;

    /**
     * 用于UI界面学生/患者关联
     */
    @ApiModelProperty("用于UI界面学生/患者关联")
    private String personId;

    /**
     * 医生电话
     */
    @ApiModelProperty("医生电话")
    @Sensitive(type = SensitiveTypeEnum.PHONE)
    private String doctorPhone;

    /**
     * 来源集合（流调）
     */
    @ApiModelProperty("来源集合（流调）")
    private String dataSourceList;

    /**
     * 症状（流调）
     */
    @ApiModelProperty("症状（流调）")
    private String symptomTypeNew;

    /**
     * 年龄单位（流调）
     */
    @ApiModelProperty("年龄单位（流调）")
    private String ageName;

    /**
     * 状态（流调）
     */
    @ApiModelProperty("状态（流调）")
    private Integer status;

    /**
     * id（流调）
     */
    @ApiModelProperty("id（流调）")
    private String caseId;

    /**
     * 主诉发病时间
     */
    @ApiModelProperty("主诉发病时间")
    private String onsetDate;

    /**
     * 是否是当天的新病例 0:否 1:是
     */
    @ApiModelProperty("是否是当天的新病例 0:否 1:是")
    private Integer isNewMedRecord;

    /**
     * 个案调查回答  0-无  1-有
     * 流调使用
     */
    @ApiModelProperty("个案调查回答  0-无  1-有")
    private String questionnaireAnswer;

    @ApiModelProperty("主诉")
    private String mainSuit;

    @ApiModelProperty("现病史")
    private String illnessHistory;

    @ApiModelProperty("既往史")
    private String previousHistory;

    @ApiModelProperty("体格检查")
    private String checkupOther;

    @ApiModelProperty("辅助检查")
    private String auxExam;

    @ApiModelProperty("全局唯一id")
    private String globalPersonId;

    /**
     * 主诉症状list, |分割
     */
    @ApiModelProperty("主诉症状list, |分割")
    private String mainSuitSymptomContent;

    /**
     * 主诉症状发病时间list, |分割, 与主诉症状一一对应
     */
    @ApiModelProperty("主诉症状发病时间list, |分割, 与主诉症状一一对应")
    private String onsettimecontent;

    @ApiModelProperty("关键症状(判别症状)标签list，|分割")
    private String keySymptomTagList;

    @ApiModelProperty("病历创建时间")
    private Date medCreateTime;

    @ApiModelProperty("症状详情")
    private String symptomContent;

    @ApiModelProperty("是否为报卡登记上报1:是,0:否")
    private String isRegister;

    @ApiModelProperty("机构地址")
    private String orgAddress;

    @ApiModelProperty("就诊机构")
    @Sensitive(type = SensitiveTypeEnum.ORG)
    private String visitOrgName;

    private String originalEventId;

    private List<MedicalInfoVO> allData;
    /**
     * 获取经纬度
     *
     * @return
     */
    @JsonIgnore
    public Pair<Double, Double> getLongitudeAndLatitude() {
        return new Pair<>(longitude, latitude);
    }


    /**
     * 病历事件ID
     */
    @ApiModelProperty("病历事件ID")
    private String medicalEventId;

    public void setCityName(String cityName) {
        this.cityName = cityName == null ? "" : cityName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName == null ? "" : districtName;
    }

    public void setOutCallStatus(Integer outCallStatus) {
        if (outCallStatus == null) {
            this.outCallStatus = MedicalCallStatusEnum.NOT_CALL.getCode();
        } else {
            this.outCallStatus = outCallStatus;
        }
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId == null ? "" : patientId;
    }

    public String getPatientId() {
        return this.patientId == null ? "" : this.patientId;
    }

    public String getStringOutCallStatus() {
        return String.valueOf(this.outCallStatus);
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName == null ? "" : patientName;
    }

//    public void setConfirmedResult(Integer confirmedResult) {
//        this.confirmedResult = (confirmedResult == null ? CallResultEnum.UNKNOWN.getCode() : confirmedResult);
//    }

    public void setConnectStatus(Integer connectStatus) {
        this.connectStatus = (connectStatus == null ? CallConnectStatusEnum.UNCONNECTED.getCode() : connectStatus);
    }

//    public void setMedAuthenticity(Integer medAuthenticity) {
//        this.medAuthenticity = (medAuthenticity == null ? MedAuthenticityEnum.UNCONNECTED.getCode() : medAuthenticity);
//    }


    public void setSexDesc(String sexDesc) {
        this.sexDesc = sexDesc != null ? sexDesc.trim() : "";
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MedicalInfoVO that = (MedicalInfoVO) o;
        return Objects.equals(sourceKey, that.sourceKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(sourceKey);
    }

    public Date getSortOutPatientTime() {
        return Optional.ofNullable(outPatientTime).orElse(new Date());
    }

    public static EventInfoDto fromEntity(MedicalInfoVO medicalInfoVO){

        EventInfoDto eventInfoDto = new EventInfoDto();
        eventInfoDto.setDataSrcCode(medicalInfoVO.getDataSrc());
        if(ClinicTypeEnum.OUTPATIENT.getCode().equals(medicalInfoVO.getDataSrc())){
            eventInfoDto.setDataSrcName("门诊");
        }
        if(ClinicTypeEnum.IN_HOSPITAL.getCode().equals(medicalInfoVO.getDataSrc())){
            eventInfoDto.setDataSrcName("住院");
        }
        eventInfoDto.setEventDatetime(medicalInfoVO.getMedCreateTime());
        eventInfoDto.setOrgId(medicalInfoVO.getOrgId());
        eventInfoDto.setOrgName(medicalInfoVO.getOrgName());
        eventInfoDto.setEventId(medicalInfoVO.getSourceKey());
        eventInfoDto.setOrgAddress(medicalInfoVO.getOrgAddress());
        return eventInfoDto;
    }

    public static MedicalInfoVO fromAppSyndromeMedical(AppSyndromeMedicalDetailDTO dto){
        final MedicalInfoVO medicalInfoVO = new MedicalInfoVO();
        if (StringUtils.isEmpty(dto.getPatientAge())){
            medicalInfoVO.setDiagnoseAge("");
        }else {
            medicalInfoVO.setDiagnoseAge(dto.getPatientAge().replace("岁",""));
        }
        if (StringUtils.isEmpty(dto.getPatientSexName())){
            medicalInfoVO.setSexDesc("");
        }else {
            medicalInfoVO.setSexDesc(dto.getPatientSexName().replace("性",""));
        }

        medicalInfoVO.setDoctorId(dto.getDoctorCode());
        medicalInfoVO.setDoctorName(dto.getDoctorName());
        medicalInfoVO.setOrgName(dto.getOrgName());
        medicalInfoVO.setFullDate(DateFormatUtils.localDateTimeConvertToDate(dto.getVisitTime()));
        medicalInfoVO.setKeySymptomTagList(dto.getSymptomList());

        medicalInfoVO.setStatDimId(dto.getOrgIdAttent());
        medicalInfoVO.setStatDimName(dto.getOrgNameAttent());

        medicalInfoVO.setLatitude(dto.getLivingAddrLatitude());
        medicalInfoVO.setLongitude(dto.getLivingAddrLongitude());
        medicalInfoVO.setLivingAddress(dto.getLivingAddrDetail());

        medicalInfoVO.setCompanyLatitude(dto.getCompanyLatitude());
        medicalInfoVO.setCompanyLongitude(dto.getCompanyLongitude());
        medicalInfoVO.setCompanyName(dto.getCompany());
        medicalInfoVO.setCompany(dto.getCompany());

        medicalInfoVO.setSourceType(dto.getSourceType());



        return medicalInfoVO;
    }

    public static  MedicalInfoVO copy(MedicalInfoVO input){
        MedicalInfoVO medicalInfoVO = new MedicalInfoVO();
        BeanUtils.copyProperties(input, medicalInfoVO);
        return medicalInfoVO;
    }
    public static void main(String[] args) {
        String a = "";
        System.out.println(a.replace("岁",""));
    }
}

package com.iflytek.fpva.cdc.model.vo.compare;

import com.iflytek.fpva.cdc.constant.enums.ReportCardCompareTaskStatusEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewReportCardCompareTask;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Data
public class ReportCardCompareTaskVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 任务名称
     */
    private String taskName;


    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 附件id
     */
    private String attachmentId;

    /**
     * 比对基准编码
     */
    private String compareBenchmarkCode;

    /**
     * 比对基准名称
     */
    private String compareBenchmarkName;

    /**
     * 开始时间
     */
    private Date beginDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 关注的省级区域编码列表，以英文逗号分隔
     */
    private String provinceCodeList;

    /**
     * 关注的省级区域名称列表，以英文逗号分隔
     */
    private String provinceNameList;

    /**
     * 关注的市级区域编码列表，以英文逗号分隔
     */
    private String cityCodeList;

    /**
     * 关注的市级区域名称列表，以英文逗号分隔
     */
    private String cityNameList;

    /**
     * 关注的区级区域编码列表，以英文逗号分隔
     */
    private String districtCodeList;

    /**
     * 关注的区级区域名称列表，以英文逗号分隔
     */
    private String districtNameList;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 是否删除 0：否 1：是
     */
    private String isDeleted;

    /**
     * 任务结束时间
     */
    private Date endTime;

    /**
     * 漏报病历数
     */
    private Integer missingMedicalRecordNum;

    private String statusName;

    private String areaName;

    public static ReportCardCompareTaskVO fromEntity(TbCdcewReportCardCompareTask task) {
        ReportCardCompareTaskVO result = new ReportCardCompareTaskVO();
        BeanUtils.copyProperties(task, result);
        result.setStatusName(ReportCardCompareTaskStatusEnum.getNameByCode(task.getStatus()));

        String provinceNameList = task.getProvinceNameList();
        String cityNameList = task.getCityNameList();
        String districtNameList = task.getDistrictNameList();

        if (StringUtils.isNotEmpty(districtNameList)) {
            result.setAreaName(districtNameList);
        } else if (StringUtils.isNotEmpty(cityNameList)) {
            result.setAreaName(cityNameList);
        } else if (StringUtils.isNotEmpty(provinceNameList)) {
            result.setAreaName(provinceNameList);
        }

        return result;
    }
}

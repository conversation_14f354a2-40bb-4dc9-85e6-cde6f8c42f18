package com.iflytek.fpva.cdc.service.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.Gender;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.entity.TbCdcewDiagnosisDoctor;
import com.iflytek.fpva.cdc.entity.TbCdcewOrganizationInfo;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.mapper.multichannel.TbCdcewDiagnosisDoctorMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewOrganizationInfoMapper;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.resultmap.MatchedResult;
import com.iflytek.fpva.cdc.model.vo.DiagnosisDoctorQueryVO;
import com.iflytek.fpva.cdc.model.vo.DiagnosisDoctorVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import com.iflytek.fpva.cdc.model.vo.org.OrgVO;
import com.iflytek.fpva.cdc.service.common.DiagnosisDoctorService;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.common.utils.StorageClientUtil;
import com.iflytek.fpva.cdc.util.StringUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DiagnosisDoctorServiceImpl implements DiagnosisDoctorService {

    @Resource
    private RestService restService;

    @Resource
    private TbCdcewOrganizationInfoMapper tbCdcOrganizationInfoMapper;

    @Resource
    private FileService fileService;

    @Resource
    TbCdcewDiagnosisDoctorMapper tbCdcewDiagnosisDoctorMapper;

    @Resource
    OrgService orgService;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    private StorageClientUtil storageClientUtil;

    @Override
    public byte[] downloadTemplate(String loginUserName) {

        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (Objects.isNull(org)) {
            throw new MedicalBusinessException("11463001", "用户账号异常,机构不存在,loginUserName:" + loginUserName);
        }

        if (StringUtils.isEmpty(org.getDistrictCode())) {
            throw new MedicalBusinessException("11441038", "市级账号无法新增诊疗医生");
        }

        Workbook workbook = new XSSFWorkbook();

        // 得到机构的下级机构列表
        List<TbCdcewOrganizationInfo> subOrgList = tbCdcOrganizationInfoMapper.findByHigherOrg(org.getId());
        List<String> orgNameList = subOrgList.stream().map(TbCdcewOrganizationInfo::getStatOrgName).collect(Collectors.toList());

        //校验是否超出文件导出最大值
        restService.checkExportMax(orgNameList);
        // 性别列表
        String[] sexNames = {Gender.MALE, Gender.FEMALE};

        createTemplateSheet(orgNameList, sexNames, workbook);

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);

        return outputStream.toByteArray();
    }

    @Override
    public UploadResultVO uploadFile(MultipartFile file) {
        UploadResultVO uploadResultVO = new UploadResultVO();
        uploadResultVO.setAttachmentId(fileService.upload(file).get(0));
        return uploadResultVO;
    }

    @Override
    public UploadResultVO batchAdd(String attachmentId, String loginUserName) {
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(attachmentId);
        if (null == tbCdcAttachment) {
            log.info("附件不存在");
            throw new MedicalBusinessException("11441034", "附件不存在");
        }

        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);

        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(storageObject.getInputStream());
        } catch (IOException e) {
            log.error("读取上传文件失败", e);
            throw new MedicalBusinessException("11441035", "读取上传文件失败");
        }

        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (Objects.isNull(org)) {
            throw new MedicalBusinessException("11463001", "用户账号异常,机构不存在,loginUserName:" + loginUserName);
        } else if (StringUtils.isEmpty(org.getDistrictCode())) {
            throw new MedicalBusinessException("11441038", "市级账号无法新增诊疗医生");
        }
        List<TbCdcewOrganizationInfo> subOrgList = tbCdcOrganizationInfoMapper.findByHigherOrg(org.getId());
        List<String> orgNameList = subOrgList.stream().map(TbCdcewOrganizationInfo::getOrgName).collect(Collectors.toList());

        Date date = new Date();
        List<TbCdcewDiagnosisDoctor> diagnosisDoctorList = new ArrayList<>();
        XSSFSheet sheet = workbook.getSheet("Sheet1");
        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {
            boolean isSatisfactoryData = true;

            Row row = sheet.getRow(index);
            String statDimName = getStringValue(row.getCell(0));
            String name = getStringValue(row.getCell(1));
            String sexName = getStringValue(row.getCell(2));
            String phone = getStringValue(row.getCell(3));
            String postName = getStringValue(row.getCell(4));

            List<Integer> errorList = new ArrayList<>();

            StringBuilder sb = new StringBuilder("");

            // 校验机构
            if (StringUtils.isBlank(statDimName)) {
                sb.append("机构必须填写;");
                errorList.add(0);
                isSatisfactoryData = false;
            } else if (!orgNameList.contains(statDimName)) {
                sb.append("机构名称不存在;");
                errorList.add(0);
                isSatisfactoryData = false;
            }

            // 校验姓名
            if (StringUtils.isBlank(name)) {
                sb.append("姓名必须填写;");
                errorList.add(1);
                isSatisfactoryData = false;
            } else if (!com.iflytek.fpva.cdc.util.StringUtils.isAllChineseCharacter(name)) {
                sb.append("姓名必须是全中文;");
                errorList.add(1);
                isSatisfactoryData = false;
            } else if (name.length() > 25) {
                sb.append("姓名超长,限制25字;");
                errorList.add(1);
                isSatisfactoryData = false;
            }

            // 校验性别
            if (StringUtils.isBlank(sexName)) {
                sb.append("性别必须填写;");
                errorList.add(2);
                isSatisfactoryData = false;
            } else if (!sexName.equals(Gender.MALE) && !sexName.equals(Gender.FEMALE)) {
                sb.append("性别只能是男或女;");
                errorList.add(2);
                isSatisfactoryData = false;
            }

            // 校验手机号
            if (StringUtils.isBlank(phone)) {
                sb.append("手机号必须填写;");
                errorList.add(3);
                isSatisfactoryData = false;
            }
            if (!StringUtils.isNumeric(phone)) {
                sb.append("手机号必须是数字");
                errorList.add(3);
                isSatisfactoryData = false;
            } else if (phone.length() != 11) {
                sb.append("手机号必须是11位");
                errorList.add(3);
                isSatisfactoryData = false;
            }

            // 校验职务
            if (StringUtils.isEmpty(postName)) {
                sb.append("职务不能为空");
                errorList.add(4);
                isSatisfactoryData = false;
            } else if (postName.length() > 15) {
                sb.append("职务名称超长,限制15字;");
                errorList.add(4);
                isSatisfactoryData = false;
            }

            // 添入报错信息
            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {
                failedCount++;
                createCellAndSetValue(row, errorMsg, 5);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

            if (isSatisfactoryData) {
                TbCdcewDiagnosisDoctor person = new TbCdcewDiagnosisDoctor();
                person.setId(String.valueOf(batchUidService.getUid("tb_cdcew_diagnose_doctor")));
                person.setProvinceCode(org.getProvinceCode());
                person.setProvinceName(org.getProvince());
                person.setCityCode(org.getCityCode());
                person.setCityName(org.getCity());
                person.setDistrictCode(org.getDistrictCode());
                person.setDistrictName(org.getDistrict());
                person.setStatDimId(getOrgIdByOrgName(subOrgList, statDimName));
                person.setStatDimName(statDimName);
                person.setName(name);
                person.setSexName(sexName);
                person.setPhone(phone);
                person.setPostName(postName);
                person.setStatus(CommonConstants.STATUS_VALID);
                person.setCreator(loginUserName);
                person.setCreateTime(date);
                person.setUpdater(loginUserName);
                person.setUpdateTime(date);

                diagnosisDoctorList.add(person);
            }
        }

        // 导入监测人员数据
        List<List<TbCdcewDiagnosisDoctor>> batchList = CollectionUtil.split(diagnosisDoctorList, CommonConstants.MAX_BATCH_SIZE);
        for (List<TbCdcewDiagnosisDoctor> list : batchList) {
            tbCdcewDiagnosisDoctorMapper.insertBatch(list);
        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        String resultAttachmentId = fileService.upload(outputStream.toByteArray(), tbCdcAttachment.getAttachmentName());

        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);
        resultVO.setAttachmentId(attachmentId);
        resultVO.setResultAttachmentId(resultAttachmentId);

        return resultVO;

    }

    @Override
    public void add(DiagnosisDoctorVO vo, String loginUserName) {
        MatchedResult matchedResult = isMatchedDiagnoseDoctor(vo);
        if (!matchedResult.getFlag()) {
            throw new MedicalBusinessException(matchedResult.getCode(), matchedResult.getMsg());
        }
        Date date = new Date();
        TbCdcewDiagnosisDoctor doctor = new TbCdcewDiagnosisDoctor();
        doctor.setId(String.valueOf(batchUidService.getUid("tb_cdcew_diagnose_doctor")));
        doctor.setProvinceCode(vo.getProvinceCode());
        doctor.setProvinceName(vo.getProvinceName());
        doctor.setCityCode(vo.getCityCode());
        doctor.setCityName(vo.getCityName());
        doctor.setDistrictCode(vo.getDistrictCode());
        doctor.setDistrictName(vo.getDistrictName());
        doctor.setStatDimId(vo.getStatDimId());
        doctor.setStatDimName(vo.getStatDimName());
        doctor.setName(vo.getName());
        doctor.setPhone(vo.getPhone());
        doctor.setStatus(CommonConstants.STATUS_VALID);
        doctor.setCreator(loginUserName);
        doctor.setCreateTime(date);
        doctor.setUpdater(loginUserName);
        doctor.setUpdateTime(date);
        doctor.setPostName(vo.getPostName());
        doctor.setSexName(vo.getSexName());
        tbCdcewDiagnosisDoctorMapper.insert(doctor);
    }

    @Override
    public void update(DiagnosisDoctorVO vo, String loginUserName) {

        Date date = new Date();

        TbCdcewDiagnosisDoctor doctor = tbCdcewDiagnosisDoctorMapper.selectByPrimaryKey(vo.getId());
        if (doctor == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + vo.getId());
        }

        if (doctor.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除");
        }


        MatchedResult matchedResult = isMatchedDiagnoseDoctor(vo);
        if (!matchedResult.getFlag()) {
            throw new MedicalBusinessException(matchedResult.getCode(), matchedResult.getMsg());
        }

        doctor.setId(vo.getId());
        doctor.setProvinceCode(vo.getProvinceCode());
        doctor.setProvinceName(vo.getProvinceName());
        doctor.setCityCode(vo.getCityCode());
        doctor.setCityName(vo.getCityName());
        doctor.setDistrictCode(vo.getDistrictCode());
        doctor.setDistrictName(vo.getDistrictName());
        doctor.setStatDimId(vo.getStatDimId());
        doctor.setStatDimName(vo.getStatDimName());
        doctor.setName(vo.getName());
        doctor.setPhone(vo.getPhone());
        doctor.setStatus(vo.getStatus());
        doctor.setUpdater(loginUserName);
        doctor.setUpdateTime(date);
        doctor.setSexName(vo.getSexName());
        doctor.setPostName(vo.getPostName());
        tbCdcewDiagnosisDoctorMapper.updateByPrimaryKeySelective(doctor);

    }

    @Override
    public void delete(String id, String loginUserId, String loginUserName) {
        TbCdcewDiagnosisDoctor doctor = tbCdcewDiagnosisDoctorMapper.selectByPrimaryKey(id);
        if (doctor == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + id);
        }

        if (doctor.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除，不允许再次删除");
        }
        tbCdcewDiagnosisDoctorMapper.updateStatusById(id, CommonConstants.STATUS_DELETE, loginUserName);
    }

    @Override
    public void updateStatus(String id, Integer status, String loginUserId, String loginUserName) {

        if (status != CommonConstants.STATUS_INVALID && status != CommonConstants.STATUS_VALID) {
            throw new MedicalBusinessException("11441005", "无效的状态：" + status);
        }

        TbCdcewDiagnosisDoctor doctor = tbCdcewDiagnosisDoctorMapper.selectByPrimaryKey(id);
        if (doctor == null) {
            throw new MedicalBusinessException("11441006", "人员信息不存在：" + id);
        }

        if (status == CommonConstants.STATUS_INVALID && status.equals(doctor.getStatus())) {
            throw new MedicalBusinessException("11441024", "人员已处于禁用状态，不允许再次禁用");
        }

        if (status == CommonConstants.STATUS_VALID && status.equals(doctor.getStatus())) {
            throw new MedicalBusinessException("11441025", "人员已处于启用状态，不允许再次启用");
        }

        if (doctor.getStatus() == CommonConstants.STATUS_DELETE) {
            throw new MedicalBusinessException("11441007", "人员已被删除");
        }

        tbCdcewDiagnosisDoctorMapper.updateStatusById(id, status, loginUserName);
    }

    @Override
    public PageData<DiagnosisDoctorVO> list(String loginUserName, DiagnosisDoctorQueryVO queryVO) {

        // 筛选条件未填写机构时  默认按当前用户进行机构筛选
        List<String> statDimIdList = new ArrayList<>();
        if (StringUtils.isEmpty(queryVO.getStatDimId())) {
            List<OrgVO> orgListForBusinessPerson = orgService.getOrgListForBusinessPerson(loginUserName, null);
            statDimIdList = orgListForBusinessPerson.stream().map(OrgVO::getOrgId).collect(Collectors.toList());
        } else {
            statDimIdList.add(queryVO.getStatDimId());
        }
        queryVO.setStatDimIdList(statDimIdList);

        PageMethod.startPage(queryVO.getPageIndex(), queryVO.getPageSize());
        List<TbCdcewDiagnosisDoctor> list = tbCdcewDiagnosisDoctorMapper.listByQueryVO(queryVO);
        PageInfo<TbCdcewDiagnosisDoctor> pageInfo = new PageInfo<>(list);
        PageData<DiagnosisDoctorVO> pageData = PageData.fromPageInfo(pageInfo);
        List<DiagnosisDoctorVO> retList = list.stream().map(DiagnosisDoctorVO::fromEntity).collect(Collectors.toList());
        pageData.setData(retList);
        return pageData;
    }

    private void createTemplateSheet(List<String> orgNameList, String[] sexNames, Workbook workbook) {

        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        Row firstRow = sheet.createRow(0);
        createCellAndSetValue(firstRow, "机构名称", 0);
        createCellAndSetValue(firstRow, "姓名", 1);
        createCellAndSetValue(firstRow, "性别", 2);
        createCellAndSetValue(firstRow, "手机号", 3);
        createCellAndSetValue(firstRow, "职务", 4);

        sheet.setColumnWidth(0, 8000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);


        // 设置机构验证规则
        XSSFDataValidationHelper helper = new XSSFDataValidationHelper(sheet);
        DataValidationConstraint districtConstraint = helper.createExplicitListConstraint(orgNameList.toArray(new String[0]));
        CellRangeAddressList districtRangeAddressList = new CellRangeAddressList(1, 1000, 0, 0);
        DataValidation districtDataValidation = helper.createValidation(districtConstraint, districtRangeAddressList);

        //验证
        districtDataValidation.createErrorBox("error", "请选择正确机构");
        districtDataValidation.setShowErrorBox(true);
        districtDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(districtDataValidation);

        // 设置性别校验规则
        DataValidationConstraint sexConstraint = helper.createExplicitListConstraint(sexNames);
        CellRangeAddressList sexRangeAddressList = new CellRangeAddressList(1, 1000, 2, 2);
        DataValidation sexDataValidation = helper.createValidation(sexConstraint, sexRangeAddressList);
        sexDataValidation.createErrorBox("error", "请选择正确性别");
        sexDataValidation.setShowErrorBox(true);
        sexDataValidation.setSuppressDropDownArrow(true);
        sheet.addValidationData(sexDataValidation);
    }


    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        cell.setCellValue(value);
    }

    private ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11441036", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    private String getStringValue(Cell cell) {

        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:  //字符串类型
                return cell.getStringCellValue();
            case BOOLEAN:   //布尔类型
                return String.valueOf(cell.getBooleanCellValue());
            case NUMERIC:  //数值类型
                return String.format("%.0f", cell.getNumericCellValue());
            default:
                return null;
        }
    }

    private String getOrgIdByOrgName(List<TbCdcewOrganizationInfo> list, String orgName) {
        List<TbCdcewOrganizationInfo> collect = list.stream().filter(tbCdcewOrganizationInfo -> orgName.equals(tbCdcewOrganizationInfo.getOrgName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            return collect.get(0).getOrgId();
        }
        return "";
    }

    /**
     * 校验是否是符合要求的诊疗医生
     */
    private MatchedResult isMatchedDiagnoseDoctor(DiagnosisDoctorVO diagnosisDoctorVO) {

        MatchedResult matchedResult = new MatchedResult();
        matchedResult.setFlag(true);

        String name = diagnosisDoctorVO.getName();
        String sexName = diagnosisDoctorVO.getSexName();
        String phone = diagnosisDoctorVO.getPhone();
        String statDimId = diagnosisDoctorVO.getStatDimId();
        String statDimName = diagnosisDoctorVO.getStatDimName();
        String postName = diagnosisDoctorVO.getPostName();

        // 校验姓名
        if (StringUtils.isBlank(name)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("姓名必须填写");
            matchedResult.setCode("11441011");
            return matchedResult;
        } else if (!com.iflytek.fpva.cdc.util.StringUtils.isAllChineseCharacter(name)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("姓名必须是全中文");
            matchedResult.setCode("11441012");
            return matchedResult;
        } else if (name.length() > 25) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("姓名超长,限制25字");
            matchedResult.setCode("11441013");
            return matchedResult;
        }

        // 校验性别
        if (StringUtils.isEmpty(sexName)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("性别必须填写");
            matchedResult.setCode("11441014");
            return matchedResult;
        } else if (!sexName.equals(Gender.MALE) && !sexName.equals(Gender.FEMALE)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("性别必须是男或女");
            matchedResult.setCode("11441015");
            return matchedResult;
        }

        // 校验机构
        if (StringUtils.isBlank(statDimId) || StringUtils.isBlank(statDimName)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("机构不能为空");
            matchedResult.setCode("11441016");
            return matchedResult;
        }

        // 校验手机号
        if (StringUtils.isBlank(phone)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("手机号必须填写");
            matchedResult.setCode("11441017");
            return matchedResult;
        }
        if (!StringUtils.isNumeric(phone)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("手机号必须是数字");
            matchedResult.setCode("11441018");
            return matchedResult;
        } else if (phone.length() != 11) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("手机号必须是11位");
            matchedResult.setCode("11441019");
            return matchedResult;
        }

        // 校验职务
        if (StringUtils.isEmpty(postName)) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("职务必须填写");
            matchedResult.setCode("11441022");
            return matchedResult;
        } else if (postName.length() > 15) {
            matchedResult.setFlag(false);
            matchedResult.setMsg("职务名称超长,限制15字");
            matchedResult.setCode("11441023");
            return matchedResult;
        }

        return matchedResult;
    }

}

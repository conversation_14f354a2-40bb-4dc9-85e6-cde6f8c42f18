package com.iflytek.fpva.cdc.model.vo.monitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date :2023/10/19 15:41
 * @description:EventMonitorVO
 */
@Data
public class EventMonitorVO {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("监测信号编号")
    private String monitorSetId;

    @ApiModelProperty("数据编号")
    private String dataNum;

    @ApiModelProperty("预警信号编号")
    private String eventNum;

    @ApiModelProperty("预警信号id")
    private String eventId;


    @ApiModelProperty("信号来源")
    private String eventType;


    @ApiModelProperty("街道编码")
    private String statDimId;

    @ApiModelProperty("信号地址")
    private String statDimName;

    @ApiModelProperty("症候群类型")
    private String symptomType;


    @ApiModelProperty("症候群名称")
    private String syndromeName;

    @ApiModelProperty("数据创建时间")
    private Date createTime;

    @ApiModelProperty("数据生成时间")
    private Date updateTime;

    private String updateTimeDay;
    private String updateTimeMonth;
    private String updateTimeYear;

    @ApiModelProperty("信号标注")
    private String markLevel;

    @ApiModelProperty("信号标注审核")
    private String auditLevel;

    @ApiModelProperty("信号审核")
    private String auditResult;

    @ApiModelProperty("算法是否预警，1：是；0：否")
    private String preResult;

    @ApiModelProperty("算法是否监测，1：是；0：否")
    private String monitorResult;

    @ApiModelProperty("算法预警依据")
    private String preNote;


}

package com.iflytek.fpva.cdc.model.vo.spectrum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Comparator;

@Data
public class SymptomDistributionVO {


    @ApiModelProperty("症状组合")
    private String keySymptomTagList;

    @ApiModelProperty("病例数量")
    private Integer medHisCnt;

    @ApiModelProperty("比例")
    private double medHisCntRatio;

    public SymptomDistributionVO(String keySymptomTagList, Integer medHisCnt, double medHisCntRatio) {
        this.keySymptomTagList = keySymptomTagList;
        this.medHisCnt = medHisCnt;
        this.medHisCntRatio = medHisCntRatio;
    }

    public static Comparator<SymptomDistributionVO> comparator() {
        return Comparator.comparing(SymptomDistributionVO::getMedHisCntRatio).reversed();
    }
}

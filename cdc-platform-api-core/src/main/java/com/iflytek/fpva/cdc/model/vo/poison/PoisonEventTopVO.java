package com.iflytek.fpva.cdc.model.vo.poison;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PoisonEventTopVO {

    @ApiModelProperty("食源性疾病类型编码")
    private String poisonCode;

    @ApiModelProperty("食源性疾病类型名称")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String poisonName;

    @ApiModelProperty(value = "总数")
    private int total;

    @ApiModelProperty(value = "百分百")
    private double percent;
}

package com.iflytek.fpva.cdc.model.vo.monitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date :2023/10/19 15:41
 * @description:TotalDistribution
 */
@Data
public class TotalDistributionVO {

    @ApiModelProperty("监测信号数")
    private Long monitorNum;
    @ApiModelProperty("监测信号数 相比同期")
    private String monitorNumPeriod;


    @ApiModelProperty("预警信号数")
    private Long warningNum;
    @ApiModelProperty("预警信号数 相比同期")
    private String warningNumPeriod;


    @ApiModelProperty("合理信号数")
    private Long reasonableNum;
    @ApiModelProperty("合理信号数 相比同期")
    private String reasonableNumPeriod;


    @ApiModelProperty("不合理信号数")
    private Long unReasonableNum;
    @ApiModelProperty("不合理信号数 相比同期")
    private String unReasonableNumPeriod;


    @ApiModelProperty("待审核信号数")
    private Long pendingNum;
    @ApiModelProperty("待审核信号数 相比同期")
    private String pendingNumPeriod;


    @ApiModelProperty("预警事件数")
    private Long warningEventNum;
    @ApiModelProperty("阳性事件数 相比同期")
    private String positiveEventNumPeriod;
    @ApiModelProperty("阳性事件数")
    private Long positiveEventNum;

    @ApiModelProperty("监测信号召回率")
    private String monitorRecallRate;
    @ApiModelProperty("监测信号召回率 相比同期")
    private String monitorRecallRatePeriod;


    @ApiModelProperty("监测信号准确率")
    private String monitorRightRate;
    @ApiModelProperty("监测信号准确率 相比同期")
    private String monitorRightRatePeriod;


    @ApiModelProperty("预警信号召回率")
    private String warningRecallRate;
    @ApiModelProperty("预警信号召回率 相比同期")
    private String warningRecallRatePeriod;

    @ApiModelProperty("预警信号准确率")
    private String warningRightRate;
    @ApiModelProperty("预警信号准确率 相比同期")
    private String warningRightRatePeriod;


    @ApiModelProperty("预警事件召回率")
    private String warningEventRecallRate;
    @ApiModelProperty("预警事件召回率 相比同期")
    private String warningEventRecallRatePeriod;

    @ApiModelProperty("预警事件准确率")
    private String warningEventRightRate;
    @ApiModelProperty("预警事件准确率 相比同期")
    private String warningEventRightRatePeriod;


    @ApiModelProperty("正确事件数")
    private Long rightEventNum;
}

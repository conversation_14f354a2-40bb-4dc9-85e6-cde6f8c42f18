package com.iflytek.fpva.cdc.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcew_infected_info
 * <AUTHOR>
@Data
public class TbCdcewInfectedInfo implements Serializable {
    /**
     * 业务主键
     */
    private String id;
    /**
     * 传染病病种二级编码
     */
    private String infectedCode;

    /**
     * 传染病病种二级名称
     */
    private String infectedName;

    /**
     * 传染病病种一级编码
     */
    private String infectedPrimaryCode;

    /**
     * 传染病病种一级名称
     */
    private String infectedPrimaryName;

    /**
     * 传染病类别编码
     */
    private String infectedTypeCode;

    /**
     * 传染病类别名称
     */
    private String infectedTypeName;

    /**
     * 传染病管理类别编码
     */
    private String managementTypeCode;

    /**
     * 传染病管理类别名称
     */
    private String managementTypeName;

    /**
     * 来源
     */
    private String infectedSource;

    /**
     * 备注
     */
    private String memo;

    /**
     * 创建时间
     */
    private Date createDatetime;

    /**
     * 更新时间
     */
    private Date updateDatetime;

    private static final long serialVersionUID = 1L;
}
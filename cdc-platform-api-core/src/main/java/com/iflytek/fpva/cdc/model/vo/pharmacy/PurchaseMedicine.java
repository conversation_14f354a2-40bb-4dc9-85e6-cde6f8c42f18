package com.iflytek.fpva.cdc.model.vo.pharmacy;

import lombok.Data;

/**
 * 购药汇总信息
 *
 * <AUTHOR>
 * @date 2022-05-24 9:47
 */
@Data
public class PurchaseMedicine {
    /**
     * 机构ID
     */
    private String orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 机构地址
     */
    private String orgAddress;
    /**
     * 统一社会信用代码
     */
    private String checkCode;
    /**
     * 药店负责人姓名
     */
    private String directorName;
    /**
     * 药店负责人手机号
     */
    private String directorTelephone;
    /**
     * 登记人身份证号
     */
    private String registerIdNumber;
    /**
     * 登记人姓名
     */
    private String registerName;
    /**
     * 登记人手机号
     */
    private String registerPhone;
    /**
     * 登记时间
     */
    private String registerTime;
    /**
     * 所属药店
     */
    private String pharmacyName;
    /**
     * 居民身份证号
     */
    private String patientIdNumber;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 性别名称
     */
    private String sexName;
    /**
     * 年龄
     */
    private String age;
    /**
     * 本人电话号码
     */
    private String telephone;
    /**
     * 现住地详细地址
     */
    private String livingAddress;
    /**
     * 购药时间
     */
    private String purchaseTime;
    /**
     * 处方文件
     */
    private String prescriptionUrl;
}

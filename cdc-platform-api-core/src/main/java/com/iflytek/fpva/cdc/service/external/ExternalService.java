package com.iflytek.fpva.cdc.service.external;

import com.iflytek.fpva.cdc.entity.TbCdcewReportCardRecord;
import com.iflytek.fpva.cdc.model.dto.external.ReportCardRequestDto;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.external.InfectedCaseWarningVO;
import com.iflytek.fpva.cdc.model.vo.external.InfectedSymptomWarningVO;

import java.util.List;

public interface ExternalService {
    List<InfectedSymptomWarningVO> infectedSymptomWarningLibrary(String beginDate, String endDate);

    List<InfectedCaseWarningVO> infectedCaseWarningLibrary(String beginDate, String endDate);

    PageData<TbCdcewReportCardRecord> infectedCaseLibrary(ReportCardRequestDto reportCardRequestDto);
}

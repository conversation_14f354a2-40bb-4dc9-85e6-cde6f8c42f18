package com.iflytek.fpva.cdc.model.dto.compare;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;


/**
 * 大疫情网上报报卡Dto -- 报卡漏报比对
 */
@Data
public class ReportCardCompareUploadResultDto {

    @ApiModelProperty("卡片id")
    @ExcelColumn(column = 1,name = "卡片ID")
    private String reportCardId;

    @ApiModelProperty("卡片状态")
    @ExcelColumn(column = 2,name = "卡片状态")
    private String status;

    @ApiModelProperty("患者姓名")
    @ExcelColumn(column = 3,name = "*患者姓名")
    private String name;

    @ApiModelProperty("患儿家长姓名")
    @ExcelColumn(column = 4,name = "患儿家长姓名")
    private String parentName;

    @ApiModelProperty("有效证件号")
    @ExcelColumn(column = 5,name = "有效证件号")
    private String validCertNumber;

    @ApiModelProperty("患者工作单位")
    @ExcelColumn(column = 6,name = "患者工作单位")
    private String company;

    @ApiModelProperty("联系电话")
    @ExcelColumn(column = 7,name = "*联系电话")
    private String phone;

    @ApiModelProperty("病人属于")
    @ExcelColumn(column = 8,name = "病人属于")
    private String attribution;

    @ApiModelProperty("现住地址国标")
    @ExcelColumn(column = 9,name = "现住地址国标")
    private String addressCode;

    @ApiModelProperty("现住详细地址")
    @ExcelColumn(column = 10,name = "现住详细地址")
    private String addressName;

    @ApiModelProperty("人群分类")
    @ExcelColumn(column = 11,name = "人群分类")
    private String humanCategory;

    @ApiModelProperty("病例分类")
    @ExcelColumn(column = 12,name = "病例分类")
    private String casesCategory;

    @ApiModelProperty("病例分类2")
    @ExcelColumn(column = 13,name = "病例分类2")
    private String casesCategory2;

    @ApiModelProperty("发病日期")
    @ExcelColumn(column = 14,name = "发病日期")
    private String onsetDate;

    @ApiModelProperty("诊断时间")
    @ExcelColumn(column = 15,name = "诊断时间")
    private String diagnoseTime;

    @ApiModelProperty("死亡日期")
    @ExcelColumn(column = 16,name = "死亡日期")
    private String deathDate;

    @ApiModelProperty("疾病名称")
    @ExcelColumn(column = 17,name = "*疾病名称")
    private String diseaseName;

    @ApiModelProperty("订正前病种")
    @ExcelColumn(column = 18,name = "订正前病种")
    private String revisedPreviousDisease;

    @ApiModelProperty("订正前诊断时间")
    @ExcelColumn(column = 19,name = "订正前诊断时间")
    private String revisedPreviousDiagnoseTime;

    @ApiModelProperty("订正前终审时间")
    @ExcelColumn(column = 20,name = "订正前终审时间")
    private String revisedPreviousCheckTime;

    @ApiModelProperty("填卡医生")
    @ExcelColumn(column = 21,name = "填卡医生")
    private String fillDoctor;

    @ApiModelProperty("医生填卡日期")
    @ExcelColumn(column = 22,name = "医生填卡日期")
    private String fillDate;

    @ApiModelProperty("报告单位地区编码")
    @ExcelColumn(column = 23,name = "报告单位地区编码")
    private String unitCode;

    @ApiModelProperty("报告单位")
    @ExcelColumn(column = 24,name = "报告单位")
    private String unitName;

    @ApiModelProperty("单位类型")
    @ExcelColumn(column = 25,name = "单位类型")
    private String unitType;

    @ApiModelProperty("报告卡录入时间")
    @ExcelColumn(column = 26,name = "报告卡录入时间")
    private String recordTime;

    @ApiModelProperty("录卡用户")
    @ExcelColumn(column = 27,name = "录卡用户")
    private String recordUser;

    @ApiModelProperty("录卡用户所属单位")
    @ExcelColumn(column = 28,name = "录卡用户所属单位")
    private String recordUserCompany;

    @ApiModelProperty("县区审核时间")
    @ExcelColumn(column = 29,name = "县区审核时间")
    private String districtCheckTime;

    @ApiModelProperty("地市审核时间")
    @ExcelColumn(column = 30,name = "地市审核时间")
    private String cityCheckTime;

    @ApiModelProperty("省市审核时间")
    @ExcelColumn(column = 31,name = "省市审核时间")
    private String provinceCheckTime;

    @ApiModelProperty("审核状态") //必填
    @ExcelColumn(column = 32,name = "*审核状态")
    private String checkStatus;

    @ApiModelProperty("订正报告时间")
    @ExcelColumn(column = 33,name = "订正报告时间")
    private String revisedReportTime;

    @ApiModelProperty("订正终审时间")
    @ExcelColumn(column = 34,name = "订正终审时间")
    private String revisedFinalCheckTime;

    @ApiModelProperty("终审死亡时间")
    @ExcelColumn(column = 35,name = "终审死亡时间")
    private String finalCheckDeathTime;

    @ApiModelProperty("订正用户")
    @ExcelColumn(column = 36,name = "订正用户")
    private String revisedUser;

    @ApiModelProperty("订正用户所属单位")
    @ExcelColumn(column = 37,name = "订正用户所属单位")
    private String revisedUserCompany;

    @ApiModelProperty("删除时间")
    @ExcelColumn(column = 38,name = "删除时间")
    private String deleteTime;

    @ApiModelProperty("删除用户")
    @ExcelColumn(column = 39,name = "删除用户")
    private String deleteUser;

    @ApiModelProperty("删除用户所属单位")
    @ExcelColumn(column = 40,name = "删除用户所属单位")
    private String deleteUserCompany;

    @ApiModelProperty("删除原因")
    @ExcelColumn(column = 41,name = "删除原因")
    private String deleteReason;

    @ApiModelProperty("备注")
    @ExcelColumn(column = 42,name = "备注")
    private String remark;

    @ApiModelProperty("备注")
    @ExcelColumn(column = 43,name = "失败原因")
    private String reason;

    public static ReportCardCompareUploadResultDto fromDto(ReportCardCompareUploadDto dto){
        ReportCardCompareUploadResultDto result = new ReportCardCompareUploadResultDto();
        BeanUtils.copyProperties(dto,result);
        return result;
    }
}

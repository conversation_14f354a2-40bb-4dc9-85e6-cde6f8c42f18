package com.iflytek.fpva.cdc.mapper.customized;

import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedWarningEvent;
import com.iflytek.fpva.cdc.model.common.CommonEventPointVO;
import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.model.warningEvent.vo.WarningEventListVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
public interface TbCdcewCustomizedWarningEventMapper {

    List<WarningEventListVO> getWarningEventListBy(EventCriteria eventCriteria);

    //查询所有信号点信息
    List<CommonEventPointVO> getAllEventPointsBy(EventCriteria eventCriteria);

    int countByParameterMap(Map<String, Object> parameterMap);

    List<TbCdcewCustomizedWarningEvent> findByParameterMap(Map<String, Object> parameterMap);

    TbCdcewCustomizedWarningEvent selectByPrimaryKey(String id);

    List<Date> getTimeLine(Date startTime, Date endTime);

    void updateProcessingLatestTimeByEventId(@Param("id")String eventId, @Param("processingLatestTime")Date processLatestTime);

    void updateProcessingStatusAndEndTimeById(@Param("updatedProcessingStatus")int status, @Param("updatedEndTime") Date endTime,
                                              @Param("id") String eventId , @Param("processingLatestTime")Date processLatestTime);

    void updateIsAiScreenById(@Param("isAiScreen")int isAiScreen,@Param("id") String eventId);

    void updateCheckedLevelById(@Param("checkedLevel")int checkedLevel,@Param("id") String eventId);

    int updateIsEsById(@Param("isEs") Integer isEs, @Param("id") String id);

    String getWarningRuleByEventId(String eventId);

    List<String> findSourceTypeById(@Param("id") String id);

    void updateEventGradeById(@Param("eventId")String eventId, @Param("updateTime")Date updateTime,@Param("eventGrade")String eventGrade);

}

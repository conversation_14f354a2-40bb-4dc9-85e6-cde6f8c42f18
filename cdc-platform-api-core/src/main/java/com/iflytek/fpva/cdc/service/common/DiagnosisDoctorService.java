package com.iflytek.fpva.cdc.service.common;


import com.iflytek.fpva.cdc.model.vo.DiagnosisDoctorQueryVO;
import com.iflytek.fpva.cdc.model.vo.DiagnosisDoctorVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import org.springframework.web.multipart.MultipartFile;

public interface DiagnosisDoctorService {
    byte[] downloadTemplate(String loginUserName);

    UploadResultVO uploadFile(MultipartFile file);

    UploadResultVO batchAdd(String attachmentId, String loginUserName);

    void add(DiagnosisDoctorVO vo, String loginUserName);

    void update(DiagnosisDoctorVO vo, String loginUserName);

    void delete(String id, String loginUserId, String loginUserName);

    void updateStatus(String id, Integer status, String loginUserId, String loginUserName);

    PageData<DiagnosisDoctorVO> list(String loginUserName, DiagnosisDoctorQueryVO queryVO);
}

package com.iflytek.fpva.cdc.model.po;

import com.iflytek.fpva.cdc.model.vo.BusinessPersonVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
@Getter
@Setter
public class UapUserPo {
    private String id;
    private String name;
    private String loginName;
    private Integer userType;
    private Integer userSource;
    private String phone;
    private String address;
    private String email;
    private Integer status;
    private String orgId;
    private String orgName;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private Date pwdUpdateTime;
    private String orgProvinceCode;
    private String orgProvince;
    private String orgCityCode;
    private String orgCity;
    private String orgDistrict;
    private String orgDistrictCode;


    public static UapUserPo fromBusinessPersonVO(BusinessPersonVO businessPersonVO){
        UapUserPo uapUserPo = new UapUserPo();
        uapUserPo.setId(businessPersonVO.getUapUserId());
        uapUserPo.setName(businessPersonVO.getName());
        uapUserPo.setLoginName(businessPersonVO.getPhone());
        uapUserPo.setUserType(-1);
        uapUserPo.setStatus(1);
        uapUserPo.setOrgId(businessPersonVO.getStatDimId());
        uapUserPo.setOrgName(businessPersonVO.getStatDimName());
        return uapUserPo;
    }
}

package com.iflytek.fpva.cdc.model.dto.infected;

import com.iflytek.fpva.cdc.entity.RegionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 传染病监测
 */
@Data
public class InfectionMonitorReqDTO implements Serializable {

    private static final long serialVersionUID = -5394935245927863140L;

    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty("传染病编码")
    @NotBlank(message = "请传传染病编码")
    private String infectedCode;

    @ApiModelProperty("传染病名称")
    private String infectedName;

    @ApiModelProperty("区域列表")
    private List<RegionInfo> regionInfoList;

    @ApiModelProperty(value = "传染病编码集合",hidden = true)
    private List<String> infectedCodeList;

}

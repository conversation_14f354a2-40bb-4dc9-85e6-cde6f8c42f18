package com.iflytek.fpva.cdc.model.vo.school;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.Data;

@Data
public class StreetPersonSymptomRecordExcelVO {
    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;

    @ExcelColumn(name = "现住址", column = 2)
    private String livingAddress;

    @ExcelColumn(name = "性别", column = 3)
    private String sexDesc;

    @ExcelColumn(name = "年龄", column = 4)
    private String diagnoseAge;

    @ExcelColumn(name = "主要症状", column = 5)
    private String symptomType;

    @ExcelColumn(name = "症状日期", column = 6)
    private String fullDate;

    /**
     * 单位名称
     */
    @ExcelColumn(name = "工作单位/学校", column = 7)
    private String schoolName;

    @ExcelColumn(name = "数据来源", column = 8)
    private String sourceTypeName;

    @ExcelColumn(name = "就诊机构", column = 9)
    private String visitOrgName;

    public static StreetPersonSymptomRecordExcelVO fromEntity(SchoolPersonSymptomRecordVO schoolPersonSymptomRecordVO) {
        StreetPersonSymptomRecordExcelVO excelVO = new StreetPersonSymptomRecordExcelVO();

        excelVO.setPatientName(schoolPersonSymptomRecordVO.getPatientName());
        excelVO.setLivingAddress(schoolPersonSymptomRecordVO.getLivingAddress());
        excelVO.setSexDesc(schoolPersonSymptomRecordVO.getSexDesc());
        excelVO.setSymptomType(schoolPersonSymptomRecordVO.getSymptomType());
        excelVO.setFullDate(DateFormatUtils.parseDate(schoolPersonSymptomRecordVO.getFullDate(), TimeConstant.NORM_DATE_PATTERN));
        excelVO.setSchoolName(StringUtils.isNotEmpty(schoolPersonSymptomRecordVO.getSchoolName())? schoolPersonSymptomRecordVO.getSchoolName() : "--");
        excelVO.setSourceTypeName(schoolPersonSymptomRecordVO.getSourceTypeName());

        excelVO.setDiagnoseAge(schoolPersonSymptomRecordVO.getDiagnoseAge());
        if (!StringUtils.isEmpty(schoolPersonSymptomRecordVO.getDiagnoseAge())) {
            excelVO.setDiagnoseAge(schoolPersonSymptomRecordVO.getDiagnoseAge().concat(schoolPersonSymptomRecordVO.getDiagnoseAgeUnit()));
        }
        excelVO.setVisitOrgName(schoolPersonSymptomRecordVO.getVisitOrgName());

        return excelVO;
    }
}

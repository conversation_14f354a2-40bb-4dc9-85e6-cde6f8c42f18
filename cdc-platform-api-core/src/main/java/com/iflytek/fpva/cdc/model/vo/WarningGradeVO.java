package com.iflytek.fpva.cdc.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date :2023/6/28 18:40
 * @description:WarningGradeVO
 */
@Data
public class WarningGradeVO implements Serializable {

    /**
     * 分级名称
     */

    @ApiModelProperty("分级名称")
    private String gradeName;

    /**
     * 分级编码
     */
    @ApiModelProperty("分级编码")
    private String gradeCode;

    /**
     * 启用标识
     */
    @ApiModelProperty("启用标识")
    private Integer status;

    /**
     * 颜色
     */
    @ApiModelProperty("颜色")
    private String color;

    /**
     * 删除标识
     */
    @ApiModelProperty("删除标识")
    private Integer isDeleted;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    private static final long serialVersionUID = 1L;
}
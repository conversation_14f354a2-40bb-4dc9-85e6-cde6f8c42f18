package com.iflytek.fpva.cdc.entity.customized;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcew_customized_outcall_notice
 * <AUTHOR>
@Data
public class TbCdcewCustomizedOutcallNotice implements Serializable {
    private String id;

    private String eventId;

    private String personId;

    private Integer personType;

    private String batchId;

    private String callResult;

    private Integer connectStatus;

    private Integer smsSent;

    private Date createTime;

    private Date updateTime;

    private Integer callStatus;

    private String callBatchId;

    private String statDimId;

    private String statDimName;

    private String name;

    private String positionName;

    private String phone;

    private String processRecordId;

    private static final long serialVersionUID = 1L;
}

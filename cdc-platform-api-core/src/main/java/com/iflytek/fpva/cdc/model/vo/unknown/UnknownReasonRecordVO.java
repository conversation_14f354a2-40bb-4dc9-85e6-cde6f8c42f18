package com.iflytek.fpva.cdc.model.vo.unknown;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.iflytek.fpva.cdc.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.math3.util.Pair;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
@Data
public class UnknownReasonRecordVO {

    private String sourceKey;
    private List<String> sourceKeyList;

    private String patientId;

    private String patientName;

    private String identityNo;

    private String sexDesc;

    private String age;

    private String ageUnit;

    @ApiModelProperty("诊断")
    private String diagnoseName;

    private String personTypeName;

    private String doctorId;

    private String doctorName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date fullDate;

    @ApiModelProperty("就诊时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date outPatientTime;

    @ApiModelProperty("工作单位/学校")
    private String companyName;

    private String companyCode;

    private String statDimId;

    private String statDimName;

    private String orgName;

    private Double longitude;

    private Double latitude;

    @ApiModelProperty("现住址")
    private String livingAddress;

    private Integer isNewMedRecord;

    @ApiModelProperty("病例来源")
    private String sourceType;

    @ApiModelProperty("数据来源")
    private String dataSource;

    @ApiModelProperty("出现症状时间")
    private Date symptomOnsetTime;

    @ApiModelProperty("主诉")
    private String mainSuit;

    @ApiModelProperty("现病史")
    private String illnessHistory;

    @ApiModelProperty("既往史")
    private String previousHistory;

    @ApiModelProperty("辅助检查")
    private String auxExam;

    @ApiModelProperty("体格检查")
    private String checkupOther;

    @ApiModelProperty("病历入库时间")
    private Date inStorageTime;

    @ApiModelProperty("职业")
    private String careerName;

    @ApiModelProperty("关键症状")
    private String symptomContent;

    @ApiModelProperty("就诊机构")
    private String visitOrgName;

    @ApiModelProperty("是否已读 0：未读 1：已读")
    private Integer isProcessed;

    @ApiModelProperty("患者联系电话")
    private String phone;

    /**
     * 获取经纬度
     *
     * @return
     */
    @JsonIgnore
    public Pair<Double, Double> getLongitudeAndLatitude() {
        return new Pair<>(longitude, latitude);
    }

    private String companyAddressLongitude;

    private String companyAddressLatitude;

    public String getAgeUnit() {
        return StringUtils.isEmpty(this.ageUnit) ? "岁" : this.ageUnit;
    }
}

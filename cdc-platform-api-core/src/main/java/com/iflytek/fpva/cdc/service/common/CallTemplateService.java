package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedWarningEvent;
import com.iflytek.fpva.cdc.entity.preventionControl.TbCdcewPreventionControlWarningEvent;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.Map;

public interface CallTemplateService {
    /**
     * 核实病例文本
     * @param date
     * @param size
     * @param callSystem
     * @return
     */
    String buildMedicalConfirmContent(String date, int size, String callSystem);

    /**
     * 置顶短信  通用的   SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML
     * @param until
     * @param hospitalName
     * @param syndromeName
     * @param eventNum
     * @param size
     * @return
     */
    String buildCommonTopContext(String until, String hospitalName, String syndromeName, String eventNum, String size,String waringType);

    /**
     * 传染病立即发送
     * @param e
     * @return
     */
    String getSmsContent(TbCdcInfectedWarningEvent e);

    /**
     * 传染病立即发送
     * @param e
     * @return
     */
    String getExcludeEventSmsContent(TbCdcInfectedWarningEvent e);

    /**
     * 症状立即发送
     * @param e
     * @return
     */
    String getSmsContent(TbCdcewSympWarningEvent e);
    /**
     * 症状立即发送
     * @param e
     * @return
     */
    String getExcludeEventSmsContent(TbCdcewSympWarningEvent e);

    /**
     * 中毒 立即发送
     * @param e
     * @return
     */
    String getSmsContent(TbCdcewPoisonWarningEvent e);

    String getExcludeEventSmsContent(TbCdcewPoisonWarningEvent e);

    String getSmsContent(TbCdcewOutpatientWarningEvent e, int finalGrowthRate);

    String getExcludeEventSmsContent(TbCdcewOutpatientWarningEvent e);

    String getSmsContent(TbCdcewUnknownReasonWarningEvent e);

    String getExcludeEventSmsContent(TbCdcewUnknownReasonWarningEvent e);

    String getSmsContent(TbCdcewPreventionControlWarningEvent e);

    String getExcludeEventSmsContent(TbCdcewPreventionControlWarningEvent e);

    String getSmsContent(TbCdcewCustomizedWarningEvent e);

    String getExcludeEventSmsContent(TbCdcewCustomizedWarningEvent e);

    <T> String getTimeSmsShowEventNum(String waringType, Map<String, List<T>> infectedMap,Class<T> tClass);

    <T> String getTimeSmsHideEventNum(String waringType, Map<String, List<T>> infectedMap, Class<T> tClass);
}

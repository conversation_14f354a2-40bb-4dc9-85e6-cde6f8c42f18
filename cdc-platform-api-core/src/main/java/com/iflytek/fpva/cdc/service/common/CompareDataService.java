package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.entity.TbCdcewFileUpload;
import com.iflytek.fpva.cdc.entity.TbCdcewReportCardCompareTask;

public interface CompareDataService {
    void compareData(TbCdcAttachment tbCdcAttachment, TbCdcewReportCardCompareTask task);

    void analysisFile(TbCdcewFileUpload tbCdcewFileUpload, String originalFilename);
}

package com.iflytek.fpva.cdc.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class RisInfoVO {
    @ApiModelProperty("机构名称")
    String orgName;
    @ApiModelProperty("就诊类型")
    String visitTypeName;
    @ApiModelProperty("就诊类型编码")
    String visitTypeCode;
    @ApiModelProperty("就诊/住院日期")
    Date visitTime;
    @ApiModelProperty("门诊/住院号")
    String serialNo;
    @ApiModelProperty("患者姓名")
    String patientName;
    @ApiModelProperty("性别")
    String patientSexDesc;
    @ApiModelProperty("年龄")
    String patientAge;
    @ApiModelProperty("检查日期")
    Date examDateTime;
    @ApiModelProperty("检查报告单号")
    String examineReportNo;
    @ApiModelProperty("检查项目名称")
    String itemName;
    @ApiModelProperty("检查结论")
    String examConcluse;
    @ApiModelProperty("关联传染病")
    String diseaseName;
    @ApiModelProperty("是否存在报卡")
    Integer isExistReportCard;
    @ApiModelProperty("最近既往报卡日期")
    Date reportCardDatetime;
    @ApiModelProperty("病例id")
    String medicalId;

    @ApiModelProperty("报卡类型")
    String reportCardType;

    @ApiModelProperty("报卡类型名称")
    String reportCardTypeName;
}

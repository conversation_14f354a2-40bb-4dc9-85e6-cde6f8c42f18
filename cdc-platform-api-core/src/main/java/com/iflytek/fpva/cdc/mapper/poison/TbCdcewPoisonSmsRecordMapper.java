package com.iflytek.fpva.cdc.mapper.poison;

import com.iflytek.fpva.cdc.entity.TbCdcewPoisonSmsRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewPoisonSmsRecordMapper {

    List<String> getEventIdInPersonRecord(@Param("eventIds") List<String> eventIds, @Param("personId") String personId);

    void batchInsert(List<TbCdcewPoisonSmsRecord> recordList);
}

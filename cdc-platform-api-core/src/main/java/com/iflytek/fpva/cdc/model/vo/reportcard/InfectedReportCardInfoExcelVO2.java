package com.iflytek.fpva.cdc.model.vo.reportcard;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.constant.enums.AgeUnitEnum;
import com.iflytek.fpva.cdc.model.vo.InfectedReportCardInfo;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class InfectedReportCardInfoExcelVO2 {

    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;

    @ExcelColumn(name = "性别", column = 2)
    private String sexName;

    @ExcelColumn(name = "年龄", column = 3)
    private String displayAge;

    @ExcelColumn(name = "发病日期", column = 4)
    private String firstOnsetDate;

    @ExcelColumn(name = "病种名称", column = 5)
    private String infectedSubName;

    @ExcelColumn(name = "传染病等级", column = 6)
    private String infectedTypeName;

    @ExcelColumn(name = "诊断时间", column = 7)
    private String diagnoseDatetime;

    @ExcelColumn(name = "生成时间", column = 8)
    private String createTime;

    @ExcelColumn(name = "防保科审核时间", column = 9)
    private String checkDateTime;

    @ExcelColumn(name = "身份证号", column = 10)
    private String identityNo;

    @ExcelColumn(name = "人群分类", column = 11)
    private String personTypeName;

    @ExcelColumn(name = "上报机构", column = 12)
    private String reportOrgName;

    @ExcelColumn(name = "状态", column = 13)
    private String status;

    @ExcelColumn(name = "现住址", column = 14)
    private String displayAddr;

    public static InfectedReportCardInfoExcelVO2 fromInfectedReportCardInfo(InfectedReportCardInfo infectedReportCardInfo) {
        InfectedReportCardInfoExcelVO2 infectedReportCardInfoExcelVO = new InfectedReportCardInfoExcelVO2();

        String ageUnit = StringUtils.isEmpty(infectedReportCardInfo.getAgeUnit()) ? "" : infectedReportCardInfo.getAgeUnit();
        String detail = StringUtils.isEmpty(infectedReportCardInfo.getLivingAddrDetail()) ? "" : infectedReportCardInfo.getLivingAddrDetail();

        String displayAge = StringUtils.isEmpty(infectedReportCardInfo.getExactAge()) ? "" : infectedReportCardInfo.getExactAge().concat(AgeUnitEnum.getNameByCode(ageUnit));

        infectedReportCardInfoExcelVO.setPatientName(infectedReportCardInfo.getPatientName());
        infectedReportCardInfoExcelVO.setSexName(infectedReportCardInfo.getSexName());
        infectedReportCardInfoExcelVO.setDisplayAge(displayAge);
        infectedReportCardInfoExcelVO.setFirstOnsetDate(DateFormatUtils.parseDate(infectedReportCardInfo.getFirstOnsetDate(), TimeConstant.NORM_DATE_PATTERN));
        infectedReportCardInfoExcelVO.setInfectedSubName(infectedReportCardInfo.getInfectedSubName());
        infectedReportCardInfoExcelVO.setInfectedTypeName(infectedReportCardInfo.getInfectedTypeName());
        infectedReportCardInfoExcelVO.setDiagnoseDatetime(DateFormatUtils.parseDate(infectedReportCardInfo.getDiagnoseDatetime(), TimeConstant.NORM_DATEHOUR_PATTERN));
        infectedReportCardInfoExcelVO.setCheckDateTime(DateFormatUtils.parseDate(infectedReportCardInfo.getCheckDateTime(), TimeConstant.NORM_DATEHOUR_PATTERN));
        infectedReportCardInfoExcelVO.setCreateTime(DateFormatUtils.parseDate(infectedReportCardInfo.getCreateTime(), TimeConstant.NORM_DATEHOUR_PATTERN));
        infectedReportCardInfoExcelVO.setIdentityNo(infectedReportCardInfo.getIdentityNo());
        infectedReportCardInfoExcelVO.setPersonTypeName(infectedReportCardInfo.getPersonTypeName());
        infectedReportCardInfoExcelVO.setReportOrgName(infectedReportCardInfo.getReportOrgName());
        infectedReportCardInfoExcelVO.setDisplayAddr(detail);

        return infectedReportCardInfoExcelVO;
    }

}

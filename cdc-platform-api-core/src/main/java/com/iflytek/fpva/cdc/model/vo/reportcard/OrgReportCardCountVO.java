package com.iflytek.fpva.cdc.model.vo.reportcard;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgReportCardCountVO {

    private String orgId;

    private String orgName;

    /**
     * 临床医生-待上报数
     */
    @ApiModelProperty(value = "临床医生-待上报数")
    private IndicatorStatusVO clinicalDoctorToBeReportedCount;

    /**
     * 临床医生-已上报数
     */
    @ApiModelProperty(value = "临床医生-已上报数")
    private IndicatorStatusVO clinicalDoctorReportedCount;

    /**
     * 临床医生-已排除数
     */
    @ApiModelProperty(value = "临床医生-已排除数")
    private IndicatorStatusVO clinicalDoctorExcludedCount;

    /**
     * 防保科-待审核-医生排除数
     */
    @ApiModelProperty(value = "防保科-待审核-医生排除数")
    private IndicatorStatusVO ppsPendingExcludedCount;

    /**
     * 防保科-待审核-医生排除数
     */
    @ApiModelProperty(value = "防保科-待审核-医生上报数")
    private IndicatorStatusVO ppsPendingReportedCount;

    /**
     * 防保科-已审核-医生上报数
     */
    @ApiModelProperty(value = "防保科-已审核-医生上报数")
    private IndicatorStatusVO ppsCheckedReportedCount;

    /**
     * 防保科-已审核-防保科排除数
     */
    @ApiModelProperty(value = "防保科-已审核-防保科排除数")
    private IndicatorStatusVO ppsCheckedExcludedCount;

    /**
     * 已驳回数
     */
    @ApiModelProperty(value = "已驳回数")
    private IndicatorStatusVO rejectedCount;
}

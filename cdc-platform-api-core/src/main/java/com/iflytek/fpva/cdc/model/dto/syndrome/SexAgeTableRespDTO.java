package com.iflytek.fpva.cdc.model.dto.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SexAgeTableRespDTO {
    @ApiModelProperty("男性数量")
    private Integer maleCount;
    @ApiModelProperty("女性数量")
    private Integer femaleCount;
    @ApiModelProperty("其他数量")
    private Integer unknownCount;

    @ApiModelProperty("区县名称")
    private String districtCode;

    @ApiModelProperty("区县名称编码")
    private String districtName;

    @ApiModelProperty("市名称")
    private String cityCode;

    @ApiModelProperty("市编码")
    private String cityName;

    @ApiModelProperty("分组描述")
    private String ageGroupDesc;

    @ApiModelProperty("年龄分组数据")
    private List<SexAgeTableRespDTO> dataList;

}

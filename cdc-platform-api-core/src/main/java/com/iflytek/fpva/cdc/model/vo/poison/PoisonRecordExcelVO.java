package com.iflytek.fpva.cdc.model.vo.poison;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonMedicalInfoDto;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.Data;

@Data
public class PoisonRecordExcelVO {

    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;

    @ExcelColumn(name = "性别", column = 2)
    private String sexDesc;

    @ExcelColumn(name = "年龄", column = 3)
    private String diagnoseAge;

    @ExcelColumn(name = "诊断", column = 4)
    private String diagnoseName;

    @ExcelColumn(name = "诊断时间", column = 5)
    private String outPatientTime;

    @ExcelColumn(name = "工作单位/学校", column = 6)
    private String companyName;

    @ExcelColumn(name = "来源", column = 7)
    private String orgName;

    public PoisonRecordExcelVO(PoisonMedicalInfoDto poisonRecordVO) {
        this.patientName = poisonRecordVO.getPatientName();
        this.sexDesc = poisonRecordVO.getSexDesc();
        if (!StringUtils.isBlank(poisonRecordVO.getAge())) {
            if (!StringUtils.isBlank(poisonRecordVO.getAgeUnit())) {
                this.diagnoseAge = poisonRecordVO.getAge() + poisonRecordVO.getAgeUnit();
            }
        } else {
            this.diagnoseAge = "-";
        }
        this.diagnoseName = poisonRecordVO.getDiagnoseName();
        this.outPatientTime = DateUtils.parseDate(poisonRecordVO.getOutPatientTime());

        this.companyName = poisonRecordVO.getCompanyName();

        this.orgName = poisonRecordVO.getOrgName();
    }
}

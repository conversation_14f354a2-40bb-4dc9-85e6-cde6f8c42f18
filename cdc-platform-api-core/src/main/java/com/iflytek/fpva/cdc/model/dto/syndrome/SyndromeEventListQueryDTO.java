package com.iflytek.fpva.cdc.model.dto.syndrome;

import com.iflytek.fpva.cdc.entity.RegionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class SyndromeEventListQueryDTO {

    private int pageIndex;

    @Range(max = 40)
    private int pageSize;

    @ApiModelProperty("区域列表")
    List<RegionInfo> regionInfoList;

    @ApiModelProperty("症状")
    private Collection<String> symptom;

    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date minFullDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date maxFullDate;

    @ApiModelProperty("具体的处理状态 0-未处理 1-处理中 2-人工排除 3-阳性事件 4-AI排除 5-已完成 不传-全部")
    private Collection<Integer> statusCollection;

    @ApiModelProperty("1-事件发生事件由近到远 2-事件发生时间由远到近 3-关注度大到小 4-关注度小到大 " +
            "6-更新时间由近到远 7-更新时间由远到近 8-病历数量由多到少 9-病历数量由少到多 10-置顶时间由近到远 11-置顶时间由远到近")
    @Range(min = 1, max = 11)
    private int sortType;

    @ApiModelProperty("时间筛选类型：1-发生时间；2-更新时间；3-置顶时间")
    private Integer dateType;

    @ApiModelProperty("响应超时状态 0.未超时 1.响应超时")
    private Integer responseTimeOutStatus;

    @ApiModelProperty("处置超时状态 0.未超时 1.响应超时")
    private Integer processingTimeOutStatus;

    @ApiModelProperty("DJYY-等级医院；JCYL-基层医疗；XXDW-学校单位；JDQY-街道区域")
    private String eventType;

    @ApiModelProperty("调查结论：0: 疑似  1：排除 2：继续关注 3：确认事件")
    private String conclusions;

    @ApiModelProperty("信号编号")
    private String eventNum;

    @ApiModelProperty("信号标注")
    private String markLevel;

    @ApiModelProperty("信号标注审核")
    private String auditLevel;

    @ApiModelProperty("信号审核结果")
    private String auditResult;

    @ApiModelProperty("是否需要标注或审核:0或者不传不返回内容,1标注,2审核")
    private Integer needMarkAndAudit = 0;

    private String loginUserId;
}

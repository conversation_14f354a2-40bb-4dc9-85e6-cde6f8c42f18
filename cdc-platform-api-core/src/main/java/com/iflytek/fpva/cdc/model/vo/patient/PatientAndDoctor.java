package com.iflytek.fpva.cdc.model.vo.patient;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
/**
 * 事件页患者和医生人群分布中，患者性别，医生排名分布
 */
public class PatientAndDoctor {

    /**
     * 男性数量
     **/
    private int maleNum;

    /**
     * 女性数量
     **/
    private int femaleNum;

    /**
     * 未知性别数量
     **/
    private int unknownSexyNum;

    /**
     * 0-3岁患者数量
     **/
    private int underThereAge;

    /**
     * 3-6岁患者数量
     **/
    private int underSixAge;

    /**
     * 6-18岁患者数量
     **/
    private int underEighteenAge;

    /**
     * 18-30岁患者数量
     **/
    private int underThirtyAge;

    /**
     * 30-60岁患者数量
     **/
    private int underSixtyAge;

    /**
     * 60岁以上患者数量
     **/
    private int beyondSixtyAge;


    /**
     * 医生就诊排名
     **/
    private List<DocMedDistribution> docMedDistributionList;

}

package com.iflytek.fpva.cdc.service.common;

import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.entity.TbCdcewServiceTicket;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.fpva.cdc.model.vo.serviceTicket.ServiceTicketAddReqVO;
import com.iflytek.fpva.cdc.model.vo.serviceTicket.ServiceTicketAuditReqVO;
import com.iflytek.fpva.cdc.model.vo.serviceTicket.ServiceTicketReqVO;
import com.iflytek.fpva.cdc.model.vo.serviceTicket.ServiceTicketRespVO;

import java.util.List;

/**
 * <p>
 * 工单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
public interface ServiceTicketService extends IService<TbCdcewServiceTicket> {

    String create(ServiceTicketAddReqVO reqVO, String loginUserId, String loginUserName);

    String audit(ServiceTicketAuditReqVO reqVO, String loginUserId, String loginUserName);

    ServiceTicketRespVO findById(String id, String loginUserId, String loginUserName);

    PageInfo<ServiceTicketRespVO> findList(ServiceTicketReqVO reqVO, String loginUserId, String loginUserName);

    boolean isAuditRole(String loginUserId);

    String updateServiceTicket(ServiceTicketAddReqVO reqVO, String loginUserId, String loginUserName);
}

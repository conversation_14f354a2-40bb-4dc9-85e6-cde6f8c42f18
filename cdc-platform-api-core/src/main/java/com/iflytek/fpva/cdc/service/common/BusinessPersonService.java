package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.model.vo.BusinessPersonQueryVO;
import com.iflytek.fpva.cdc.model.vo.BusinessPersonVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BusinessPersonService {

    void add(BusinessPersonVO vo, String loginUserName);

    void batchAdd(List<BusinessPersonVO> list, String loginUserName);

    void update(BusinessPersonVO vo, String loginUserId, String loginUserName);

    void delete(String id, String loginUserId, String loginUserName);

    void updateStatus(String id, Integer status, String loginUserId, String loginUserName);

    void updateDesensitize(String id, Integer desensitize, String loginUserId, String loginUserName);

    PageData<BusinessPersonVO> list(String loginUserName, BusinessPersonQueryVO queryVO);

    List<BusinessPersonVO> listByOutCall(String loginUserName, String orgId, String statDimId, String symptomId, Integer dataType);

    byte[] downloadTemplate(String loginUserName);

    UploadResultVO uploadFile(MultipartFile file);

    UploadResultVO batchAdd(String attachmentId, String loginUserName);

    /**
     * 获取权限配置表中有权限的病种，用于数据权限配置
     * @param loginUserId 用户ID
     * @param warnTypeCode 预警类别
     * @return 返回病种代码集合
     */
    List<String> getConfiguredDiseaseCodesByAuth(String loginUserId, String warnTypeCode);

    /**
     * 获取权限配置表中有权限的病种，包含当前病种所属的子集病种
     * @param loginUserId 用户ID
     * @param warnTypeCode 预警类别
     * @return 返回病种代码集合
     */
    List<String> getCompletedDiseaseCodesByAuth(String loginUserId, String warnTypeCode);

    /**
     * 根据传参以及用户权限 得到最终查询的疾病code
     * */
    List<String> getQueryDiseaseCodeBy(List<String> diseaseType, List<String> symptom, String loginUserId, String warnTypeCode);
}

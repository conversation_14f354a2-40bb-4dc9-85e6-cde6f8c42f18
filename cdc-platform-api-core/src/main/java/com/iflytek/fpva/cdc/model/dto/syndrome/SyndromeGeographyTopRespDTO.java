package com.iflytek.fpva.cdc.model.dto.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2023/11/7 10:05
 * @description:SyndromeGeographyTopRespDTO
 */
@Data
public class SyndromeGeographyTopRespDTO {

    @ApiModelProperty("覆盖区域数")
    private Long regionCnt;

    @ApiModelProperty("覆盖机构数")
    private Long orgCnt;

    @ApiModelProperty("病例数")
    private Long medCnt;

    @ApiModelProperty("区域病例人数排名")
    private List<SyndromeGeographyRespDTO> dataTop;


}

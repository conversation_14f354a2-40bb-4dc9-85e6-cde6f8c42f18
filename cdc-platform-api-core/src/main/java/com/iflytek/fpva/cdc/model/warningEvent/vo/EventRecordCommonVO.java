package com.iflytek.fpva.cdc.model.warningEvent.vo;

import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date :2024/1/26 11:34
 * @description:EventRecordCommonVO
 */
@Data
public class EventRecordCommonVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 事件id
     */
    private String eventId;
    /**
     * 处理机构编码
     */
    private String processOrgCode;
    /**
     * 处理机构名称
     */
    private String processOrgName;
    /**
     * 处理描述
     */
    private String processDesc;
    /**
     * 处理时间
     */
    private Date processTime;
    /**
     * 处理状态：0-待处理;1-处理中;2-已排除;3-已上报;4-响应超时;5-处置超时
     */
    private Integer processStatus;
    /**
     * 处理人loginUserId
     */
    private String processLoginUserId;
    /**
     * 处理类型：0-状态处理;1-置顶;2-标记;3-已审
     */
    private String processType;

    /**
     * 记录类型：0-操作记录;1-病例节点;2-风险预警;3-外呼排查;4-远程排查;5-现场排查;6-信号评估
     */
    private String recordType;




    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * uap机构
     */
    private UapOrgPo uapOrg ;
}

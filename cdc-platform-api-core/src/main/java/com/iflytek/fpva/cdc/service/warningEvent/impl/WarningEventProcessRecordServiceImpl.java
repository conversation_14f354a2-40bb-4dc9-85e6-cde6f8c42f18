package com.iflytek.fpva.cdc.service.warningEvent.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.fpva.cdc.apiService.UapServiceApi;
import com.iflytek.fpva.cdc.constant.ProcessingStatus;
import com.iflytek.fpva.cdc.constant.enums.ProcessTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.RecordTypeEnum;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.outcall.TbCdcewOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.common.TbCdcewWarningEventProcessRecordMapper;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.po.UapUserPo;
import com.iflytek.fpva.cdc.model.warningEvent.dto.EventProcessDTO;
import com.iflytek.fpva.cdc.service.outcall.OutcallBusinessPersonService;
import com.iflytek.fpva.cdc.service.outcall.OutcallPatientRelationService;
import com.iflytek.fpva.cdc.service.outcall.OutcallPatientService;
import com.iflytek.fpva.cdc.service.warningEvent.WarningEventProcessRecordService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 事件处理记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Slf4j
@Service
public class WarningEventProcessRecordServiceImpl extends ServiceImpl<TbCdcewWarningEventProcessRecordMapper, TbCdcewWarningEventProcessRecord> implements WarningEventProcessRecordService {
    @Resource
    BatchUidService batchUidService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private TbCdcewOutcallBatchMapper outcallBatchMapper;
    @Resource
    private OutcallPatientService outcallPatientService;

    @Resource
    private OutcallPatientRelationService outcallPatientRelationService;

    @Resource
    private OutcallBusinessPersonService outcallBusinessPersonService;

    private static final String TB_CDCEW_WARNING_EVENT_PROCESS_RECORD = "tb_cdcew_warning_event_process_record";
    @Override
    public void insertRecord(EventProcessDTO dto, String loginUserId, String loginUserName) {
        final UapUserPo user = uapServiceApi.getUser(loginUserId);
        final Integer status = dto.getStatus();
        //新增一条处理记录
        TbCdcewWarningEventProcessRecord record = new TbCdcewWarningEventProcessRecord();
        record.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_WARNING_EVENT_PROCESS_RECORD)));
        record.setConfigType(dto.getWarningType());
        record.setEventId(dto.getEventId());
        record.setProcessOrgCode(dto.getUapOrg().getCode());
        record.setProcessOrgName(dto.getUapOrg().getName());
        record.setProcessTime(new Date());
        record.setProcessStatus(status);
        record.setProcessLoginUserId(loginUserId);
        record.setProcessLoginUserName(user.getName());
        record.setProcessType(ProcessTypeEnum.STATUS.getCode());
        record.setRecordType(RecordTypeEnum.OPERATION_RECORD.getCode());
        dto.setEndTime(null);
        switch (status) {
            case ProcessingStatus.PROCESSING:
                record.setProcessDesc(ProcessingStatus.PROCESSING_STR);
                break;
            //如果是排除和上报 则要修改事件的endTime
            case ProcessingStatus.REMOVED_MANUAL:
                record.setProcessDesc(ProcessingStatus.REMOVED_MANUAL_STR);
                dto.setEndTime(new Date());
                break;
            case ProcessingStatus.POSITIVE:
                record.setProcessDesc(ProcessingStatus.POSITIVE_STR);
                dto.setEndTime(new Date());
                break;
            default:
                break;
        }
        baseMapper.insert(record);
    }

    @Override
    public List<TbCdcewWarningEventProcessRecord> findByEventIdAndProcessingStatusAndType(List<String> eventIds,
                                                                                          Collection<Integer> processingStatusCollection,
                                                                                          String processType,
                                                                                          String warningType) {
        return baseMapper.findByEventIdAndProcessingStatusAndType(eventIds, processingStatusCollection, processType, warningType);
    }

    @Override
    public String addProcessRecord(String warningEventType, String eventId, String loginUserId, String loginUserName, String code, String desc) {
        return addProcessRecord(warningEventType, eventId, loginUserId, loginUserName, code, desc, RecordTypeEnum.OPERATION_RECORD.getCode());
    }

    @Override
    public String addProcessRecord(String warningEventType, String eventId, String loginUserId, String loginUserName, String processType, String processDesc, String recordType) {
        UapOrgPo uapOrg = uapServiceApi.getUserOrg(loginUserName);
        final UapUserPo user = uapServiceApi.getUser(loginUserId);
        //新增一条处理记录
        TbCdcewWarningEventProcessRecord record = new TbCdcewWarningEventProcessRecord();
        record.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_WARNING_EVENT_PROCESS_RECORD)));
        record.setConfigType(warningEventType);
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);
        record.setProcessLoginUserName(user.getName());
        record.setProcessDesc(processDesc);
        record.setProcessType(processType);
        record.setRecordType(recordType);
        baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public TbCdcewWarningEventProcessRecord buildEntity(String warningEventType, String eventId, String loginUserId, String loginUserName, String processType,  String recordType) {
        UapOrgPo uapOrg = uapServiceApi.getUserOrg(loginUserName);
        final UapUserPo user = uapServiceApi.getUser(loginUserId);
        //新增一条处理记录
        TbCdcewWarningEventProcessRecord record = new TbCdcewWarningEventProcessRecord();
        record.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_WARNING_EVENT_PROCESS_RECORD)));
        record.setConfigType(warningEventType);
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessLoginUserId(loginUserId);
        record.setProcessLoginUserName(user.getName());
        record.setProcessDesc(RecordTypeEnum.getNameByCode(recordType));
        record.setProcessType(processType);
        record.setRecordType(recordType);
        return  record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEventOutcallRecord(TbCdcewWarningEventProcessRecord processRecord, TbCdcewOutcallBatch tbCdcewOutcallBatch, List<TbCdcewOutcallBusinessPerson> outcallBusinessList,
                                       List<TbCdcewOutcallPatient> outcallPatientList, List<TbCdcewOutcallPatientRelation> outcallPatientRelationList) {
        outcallBatchMapper.insert(tbCdcewOutcallBatch);
        baseMapper.insert(processRecord);
        if (!CollectionUtils.isEmpty(outcallBusinessList)){
            outcallBusinessPersonService.saveOrUpdateBatch(outcallBusinessList);
        }
        if (!CollectionUtils.isEmpty(outcallPatientList)){
            outcallPatientService.saveOrUpdateBatch(outcallPatientList);
        }
        if (!CollectionUtils.isEmpty(outcallPatientRelationList)){
            outcallPatientRelationService.saveOrUpdateBatch(outcallPatientRelationList);
        }

    }

    @Override
    public List<TbCdcewWarningEventProcessRecord> findNoneLoginName() {
        return baseMapper.findNoneLoginName();
    }

    @Override
    public void updateLoginNameById(String loginName, String id) {
         baseMapper.updateLoginNameById(loginName,id);
    }

    @Override
    public void updateLoginName() {
        List<TbCdcewWarningEventProcessRecord> list = this.findNoneLoginName();
        for (TbCdcewWarningEventProcessRecord record : list) {
            if (StringUtils.isNotBlank(record.getProcessLoginUserId())){
                try {
                    final UapUserPo user = uapServiceApi.getUser(record.getProcessLoginUserId());
                    if (user != null){
                        final String userName = user.getName();
                        if (StringUtils.isNotBlank(userName)){
                            this.updateLoginNameById(userName,record.getId());
                        }
                    }
                } catch (Exception e){
                    e.printStackTrace();
                    log.error("用户id在uap中未找到#{}",record.getProcessLoginUserId());
                }
            }
        }

    }
}

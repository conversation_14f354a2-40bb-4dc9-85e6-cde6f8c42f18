package com.iflytek.fpva.cdc.service.symptom.impl;

import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.constant.TimeType;
import com.iflytek.fpva.cdc.constant.enums.EventTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.SchoolUnitEnum;
import com.iflytek.fpva.cdc.constant.enums.SourceTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcWarningDetail;
import com.iflytek.fpva.cdc.entity.TbCdcewScSchoolUnitInfo;
import com.iflytek.fpva.cdc.entity.TbCdcewSympWarningEvent;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewScSchoolUnitInfoMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympSourceMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcWarningDetailMapper;
import com.iflytek.fpva.cdc.model.StatDimKey;
import com.iflytek.fpva.cdc.model.dto.EventDetailQueryDTO;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.OrgMedicalVO;
import com.iflytek.fpva.cdc.model.vo.TimeTrendVO;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.distribution.OrgMedicalDistributionVO;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.WarningEventDetailService;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.PercentUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SymptomWarningEventDetailImpl implements WarningEventDetailService {

    @Resource
    private TbCdcewSympSourceMapper tbCdcewSympSourceMapper;
    @Resource
    private TbCdcewSympWarningEventMapper tbCdcewSympWarningEventMapper;
    @Resource
    private TbCdcewScSchoolUnitInfoMapper tbCdcewScSchoolUnitInfoMapper;
    @Resource
    private OrgService orgService;
    @Resource
    private TbCdcWarningDetailMapper tbCdcWarningDetailMapper;

    @Override
    public String getWarningEventType() {
        return WarningTypeCodeEnum.SC_SYMPTOM.getName();
    }

    @Override
    public List<TimeTrendVO> timeTrend(EventDetailQueryDTO queryDto) {
        TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(queryDto.getEventId());

        // 获取当前时间
        Date endTime = event.getEndDate() == null ? new Date() : event.getEndDate();
        Date beginTime = DateUtils.addDays(endTime, -30);

        if (TimeType.YEAR == queryDto.getTimeType()) {
            endTime = DateUtils.addYears(endTime, -1);
            beginTime = DateUtils.addYears(beginTime, -1);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date beginDate = TimeConstant.formatDate(sdf.format(beginTime));
        Date endDate = TimeConstant.formatDate(sdf.format(endTime));
        String symptomCode = event.getSymptomCode();
        List<TimeTrendVO> symptomTimeVOList;
        if(!CollectionUtils.isEmpty(queryDto.getOrgIds())){
            symptomTimeVOList = tbCdcewSympSourceMapper.getMedCountByOrgList(beginDate, endDate, queryDto.getOrgIds(), symptomCode);
        }else{
            symptomTimeVOList = tbCdcewSympSourceMapper.getMedCountByStatDim(beginDate, endDate, event.getStatDimId(), symptomCode);
        }
        return TimeTrendVO.build30Day(endDate, event.getSymptomName(), symptomTimeVOList);
    }

    @Override
    public OrgMedicalVO orgDistribution(EventDetailQueryDTO queryDto) {
        TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(queryDto.getEventId());
        OrgMedicalVO orgMedicalVO = new OrgMedicalVO();
        String eventId = event.getId();
        String districtCode = event.getDistrictCode();
        String symptomCode = event.getSymptomCode();
        // 处理时间
        String yesterdayStr;
        String todayStr;
        Date date;
        //统计的时间 如果事件没结束 则取当前时间 如果事件结束了 则取事件结束时间
        date = event.getEndDate();
        if (date == null) {
            yesterdayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.now().minus(1, ChronoUnit.DAYS));
            todayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.now().minus(0, ChronoUnit.DAYS));
        } else {
            yesterdayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(1, ChronoUnit.DAYS));
            todayStr = DateTimeFormatter.ofPattern(TimeConstant.NORM_DATE_PATTERN)
                    .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(date.getTime()).minus(0, ChronoUnit.DAYS));
        }
        Date yesterday = DateFormatUtils.formatDate(yesterdayStr);
        Date today = DateFormatUtils.formatDate(todayStr);
        List<OrgMedicalDistributionVO> orgMedicalDistributionVOS = new ArrayList<>();

        if (EventTypeEnum.XXDW.getName().equals(event.getEventType())) {
            TbCdcewScSchoolUnitInfo tbCdcewScSchoolUnitInfo = tbCdcewScSchoolUnitInfoMapper.findByEventId(eventId);
            String statDimType = tbCdcewScSchoolUnitInfoMapper.findByEventId(eventId).getUnitType();
            List<TbCdcewScSchoolUnitInfo> unitInfoList = new ArrayList<>();

            if (SchoolUnitEnum.SCHOOL.getCode().equals(statDimType)) {
                unitInfoList = tbCdcewScSchoolUnitInfoMapper.getStatDimIdsByUnitTypeAndDistrict(statDimType, tbCdcewScSchoolUnitInfo.getDistrictCode());
            } else if (SchoolUnitEnum.GRADE.getCode().equals(statDimType)) {
                unitInfoList = tbCdcewScSchoolUnitInfoMapper.getStatDimIdsByUnitTypeAndSchool(statDimType, tbCdcewScSchoolUnitInfo.getSchoolId());
            } else if (SchoolUnitEnum.CLASS.getCode().equals(statDimType)) {
                unitInfoList = tbCdcewScSchoolUnitInfoMapper.getStatDimIdsByUnitTypeAndGrade(statDimType, tbCdcewScSchoolUnitInfo.getGradeId());
            }

            List<String> queryIdList = unitInfoList.stream().map(TbCdcewScSchoolUnitInfo::getUnitId).collect(Collectors.toList());
            Map<Date, List<OrgMedCount>> dateOrgMedCountMap = tbCdcewSympSourceMapper.getOrgMedCountByStatDim(yesterday, today, queryIdList, symptomCode).stream().collect(Collectors.groupingBy(OrgMedCount::getDate));

            Map<String, Integer> yesterdayMap = dateOrgMedCountMap.getOrDefault(yesterday, Collections.emptyList()).stream()
                    .collect(Collectors.toMap(OrgMedCount::getHospitalSourceKey, OrgMedCount::getCount));
            Map<String, Integer> todayMap = dateOrgMedCountMap.getOrDefault(today, Collections.emptyList()).stream()
                    .collect(Collectors.toMap(OrgMedCount::getHospitalSourceKey, OrgMedCount::getCount));
            int yesterdaySum = yesterdayMap.values().stream().reduce(Integer::sum).orElse(0);
            int todaySum = todayMap.values().stream().reduce(Integer::sum).orElse(0);

            for (TbCdcewScSchoolUnitInfo info : unitInfoList) {
                String orgId = info.getUnitId();
                OrgMedicalDistributionVO vo = new OrgMedicalDistributionVO();
                vo.setOrgName(info.getUnitName());
                vo.setOrgSourceKey(info.getUnitId());
                Integer yesterdayCnt = yesterdayMap.getOrDefault(orgId, 0);
                Integer todayCnt = todayMap.getOrDefault(orgId, 0);
                vo.setYesterday(yesterdayCnt);
                vo.setYesterdayPerc(PercentUtil.getPercent(yesterdayCnt, yesterdaySum, 2));
                vo.setToday(todayCnt);
                vo.setTodayPerc(PercentUtil.getPercent(todayCnt, todaySum, 2));
                orgMedicalDistributionVOS.add(vo);
            }
        } else {
            List<StatDimKey> statDimKeyList = orgService.getStatDimKeysBy(districtCode, event.getEventType());
            List<String> statDimIds = statDimKeyList.stream().map(StatDimKey::getStatDimId).collect(Collectors.toList());
            Map<Date, List<OrgMedCount>> dateOrgMedCountMap = tbCdcewSympSourceMapper.getOrgMedCountByStatDim(yesterday,
                            today,
                            statDimIds,
                            symptomCode)
                    .stream()
                    .collect(Collectors.groupingBy(OrgMedCount::getDate));

            List<OrgMedCount> yesterdayList = dateOrgMedCountMap.getOrDefault(yesterday, new ArrayList<>());
            List<OrgMedCount> todayList = dateOrgMedCountMap.getOrDefault(today, new ArrayList<>());
            orgMedicalDistributionVOS = OrgMedicalDistributionVO.calcStatDimDistribution(statDimKeyList, yesterdayList, todayList);
        }

        orgMedicalVO.setYesterday(yesterdayStr);
        orgMedicalVO.setToday(todayStr);
        orgMedicalVO.setList(orgMedicalDistributionVOS);

        return orgMedicalVO;
    }

    @Override
    public int updateIsEsById(Integer isEs, String id) {
        return tbCdcewSympWarningEventMapper.updateIsEsById(isEs, id);
    }

    @Override
    public String sourceName(String type) {
        return SourceTypeEnum.getDescByCode(type);
    }



//    @Override
//    public List<SymptomCountVO> symptomCount(EventDetailQueryDTO queryDto) {
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
//        Date formatStartTime;
//        Date formatEndTime;
//        try {
//            formatStartTime = simpleDateFormat.parse(startTime);
//            formatEndTime = simpleDateFormat.parse(endTime);
//        } catch (ParseException e) {
//            throw new MedicalBusinessException("11452001", "时间格式化错误:/n startTime:" + startTime + "/n endTime:" + endTime);
//        }
//        List<Date> dateList = getEventTimeLine(eventId);
//        int index = dateList.indexOf(formatEndTime);
//        List<Map<String, String>> symptomResultList = new ArrayList<>();
//        Map<String, Integer> presentSymptomMap = new HashMap<>();
//        Map<String, Integer> previousSymptomMap = new HashMap<>();
//        if (!org.springframework.util.StringUtils.isEmpty(detailId)){
//            final TbCdcWarningDetail detail = tbCdcWarningDetailMapper.findById(detailId);
//            final List<MedicalInfoVO> medicalInfoList = eventMonitorService.getMedicalInfoList(detailId,loginUserId);
//            final List<String> presentArray = medicalInfoList.stream().map(MedicalInfoVO::getKeySymptomTagList).collect(Collectors.toList());
//            buildSymptomMap(presentSymptomMap, presentArray);
//
//            final List<String> previousArray = medicalInfoList.stream().filter(v -> !detail.getFullDate().equals(v.getFullDate())).map(MedicalInfoVO::getKeySymptomTagList).collect(Collectors.toList());
//            buildSymptomMap(previousSymptomMap, previousArray);
//
//        } else {
//            List<String> presentResultList = getSymptomList(eventId, formatStartTime, formatEndTime, presentSymptomMap);
//            if (presentResultList == null) return null;
//            if (index != 0) {
//                if (CollectionUtils.isEmpty(presentResultList)) {
//                    return null;
//                }
//                getSymptomList(eventId, formatStartTime, dateList.get(index - 1), previousSymptomMap);
//            }
//        }
//
//        presentSymptomMap.forEach((symptom, count) ->
//        {
//            Map<String, String> symptom1Map = new HashMap<>();
//            symptom1Map.put("code", symptom);
//            if (previousSymptomMap.containsKey(symptom)) {
//                Integer previousCount = previousSymptomMap.get(symptom);
//                symptom1Map.put("value1", String.valueOf(previousCount));
//                symptom1Map.put("value2", String.valueOf(count - previousCount));
//                symptom1Map.put("type", "0");
//            } else {
//                symptom1Map.put("value1", "0");
//                symptom1Map.put("value2", String.valueOf(count));
//                symptom1Map.put("type", "1");
//            }
//            symptomResultList.add(symptom1Map);
//            Collections.sort(symptomResultList, (o1, o2) -> -Integer.compare((Integer.parseInt(o1.get("value1")) + Integer.parseInt(o1.get("value2"))), (Integer.parseInt(o2.get("value1")) + Integer.parseInt(o2.get("value2")))));
//        });
//        return symptomResultList;
//
//    }
//
//    private List<String> getSymptomList(String eventId, Date startTime, Date endTime,  Map<String, Integer> presentSymptomMap) {
//        List<String> presentResultList;
//        tbCdcewSympWarningEventMapper
//        List<String> presentMedicalList = cdcWarningEventMapper.getMedicalIdListFromRelation(eventId, startTime, endTime);
//        if (CollectionUtils.isEmpty(presentMedicalList)) {
//            return null;
//        }
//        presentResultList = cdcWarningEventMapper.getWarningEventSymptomDistribution(presentMedicalList);
//        buildSymptomMap(presentSymptomMap, presentResultList);
//
//        return presentResultList;
//    }
//
//    private static void buildSymptomMap(Map<String, Integer> presentSymptomMap, List<String> presentResultList) {
//        for (String symptomArray : presentResultList) {
//            if (!StringUtils.isEmpty(symptomArray)) {
//                List<String> symptomList = Arrays.stream(symptomArray.split("\\|")).distinct().collect(Collectors.toList());
//                for (String symptom : symptomList) {
//                    Integer count = presentSymptomMap.computeIfAbsent(symptom, v -> 0);
//                    presentSymptomMap.put(symptom, count + 1);
//                }
//            }
//        }
//    }
}

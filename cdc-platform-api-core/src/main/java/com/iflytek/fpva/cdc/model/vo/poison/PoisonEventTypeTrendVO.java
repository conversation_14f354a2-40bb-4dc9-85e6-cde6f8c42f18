package com.iflytek.fpva.cdc.model.vo.poison;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PoisonEventTypeTrendVO {

    @ApiModelProperty(value = "食源性疾病类型编码")
    private String poisonCode;

    @ApiModelProperty(value = "食源性疾病类型名称")
    private String poisonName;

    @ApiModelProperty(value = "信号趋势列表")
    private List<PoisonEventTrendVO> eventTrendList;
}

package com.iflytek.fpva.cdc.model.vo.monitor;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date :2023/10/20 11:36
 * @description:EventMonitorDetailVO
 */

@Data
public class EventMonitorDetailVO {

    @ApiModelProperty(value = "信号ID")
    private String monitorSetId;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "症候群子类编码")
    private String syndromeSubCode;

    @ApiModelProperty(value = "症候群子类名称")
    private String syndromeSubName;

    @ApiModelProperty(value = "日期")
    private LocalDate day;

    @ApiModelProperty(value = "近30日病例趋势")
    private String suspectTrendDay;

    @ApiModelProperty(value = "年龄分布")
    private String ageDistribute;

    @ApiModelProperty(value = "窗口期诊断分布")
    private String riskDiagnoseDistribute;

    @ApiModelProperty(value = "风险诊断占比")
    private String riskDiagnoseRate;

    @ApiModelProperty(value = "就诊及异常情况分布")
    private String visitDistribute;

    @ApiModelProperty(value = "地址聚集")
    private String addrGather;

    @ApiModelProperty(value = "人群聚集")
    private String ageGroupGather;

    @ApiModelProperty(value = "etl创建时间")
    private LocalDateTime etlCreateDateTime;

    @ApiModelProperty(value = "etl更新时间")
    private LocalDateTime etlUpdateDateTime;

    @ApiModelProperty(value = "住址分布情况")
    private String livingAddrDistribute;
}

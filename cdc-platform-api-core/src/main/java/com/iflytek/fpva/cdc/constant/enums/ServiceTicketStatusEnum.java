package com.iflytek.fpva.cdc.constant.enums;
/**
 * <AUTHOR>
 * @date :2024/3/12 10:35
 * @description:ServiceTicketStatusEnum   0-已撤回; 1-待反馈; 2-待议; 3-予以采纳; 4-已拒绝
 */
public enum ServiceTicketStatusEnum {
    REVOCATION(0,"已撤回"),
    WAIT(1,"待反馈"),
    TO_BE_DISCUSSED(2,"待议"),
    ACCEPT(3,"予以采纳"),
    REJECT(4,"已拒绝"),
    ;

    private Integer code;
    private String name;

    ServiceTicketStatusEnum(Integer code,String name){
        this.code = code;
        this.name = name;
    }
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

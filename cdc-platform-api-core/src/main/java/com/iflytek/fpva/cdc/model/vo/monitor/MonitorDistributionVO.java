package com.iflytek.fpva.cdc.model.vo.monitor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date :2023/10/19 15:41
 * @description:EventMonitorVO
 */
@Data
public class MonitorDistributionVO {
    @ApiModelProperty("症候群类型")
    private String symptomType;

    @ApiModelProperty("症候群名称")
    private String syndromeName;

    @ApiModelProperty("监测信号数")
    private Long monitorNum;

    @ApiModelProperty("预警信号数")
    private Long warningNum;

    @ApiModelProperty("合理信号数")
    private Long reasonableNum;



    @ApiModelProperty("待审核信号数")
    private Long pendingNum;

    @ApiModelProperty("不合理信号数")
    private Long unReasonableNum;

    @ApiModelProperty("待审核信号占比")
    private String pendingRate;

    @ApiModelProperty("不合理信号占比")
    private String unReasonableRate;










}

package com.iflytek.fpva.cdc.entity.customized;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcew_customized_source
 * <AUTHOR>
@Data
public class TbCdcewCustomizedSource implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 日期
     */
    private Date fullDate;

    /**
     * 自定义标准规则
     */
    private String customizedWarnId;

    /**
     * 统计维度ID
     */
    private String statDimId;

    /**
     * 统计维度名称
     */
    private String statDimName;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 预警类型名称
     */
    private String warningTypeName;

    /**
     * 病种/自定义标准代码
     */
    private String diseaseCode;

    /**
     * 病种/自定义标准名称
     */
    private String diseaseName;

    /**
     * 病例数
     */
    private Integer medCaseCnt;

    /**
     * 患者人数
     */
    private Integer medPersonCnt;

    /**
     * 学校/单位，街道，基层医疗，等级医院
     */
    private String eventType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}

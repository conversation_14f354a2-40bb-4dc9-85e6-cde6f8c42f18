package com.iflytek.fpva.cdc.controller.symptom;

import com.iflytek.fpva.cdc.constant.enums.AttentionLevelEnum;
import com.iflytek.fpva.cdc.constant.enums.CheckedLevelEnum;
import com.iflytek.fpva.cdc.constant.enums.ProcessTypeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewSympWarningEvent;
import com.iflytek.fpva.cdc.model.vo.call.SmsVo;
import com.iflytek.fpva.cdc.service.outcall.OutCallService;
import com.iflytek.fpva.cdc.service.symptom.SympEventService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "学校传染病事件处理")
@Slf4j
public class SympProcessController {

    @Resource
    SympEventService sympEventService;
    
    @Resource
    OutCallService outCallService;

    @PostMapping("/pt/v1/sc/eventProcess/{eventId}")
    @ApiOperation("手动改变学校症状事件处理状态")
    public void startProcess(
            @PathVariable String eventId,
            @RequestParam String loginUserId,
            @RequestParam String loginUserName,
            @RequestParam int status) {
        sympEventService.startProcess(loginUserId, eventId, status, loginUserName);
    }

    @PostMapping("/pt/v1/sc/topEvent/enable")
    @ApiOperation("学校症状手动事件置顶")
    public void enableTopEvent(@RequestParam String loginUserId, @RequestParam String loginUserName, @Valid @RequestBody SmsVo smsVo) {
        log.info("学校症状手动置顶开始，信息：loginUserId:" + loginUserId + " ,loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        //1, 更新AI筛选状态
        sympEventService.updateAiScreen(AttentionLevelEnum.TOP.getType(), smsVo.getEventId());

        //2, 发送短信提醒
        outCallService.sendScSymptomSmsForOnTop(loginUserId, loginUserName, smsVo);

        sympEventService.addProcessRecord(smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.TOP.getCode(), "置顶");
    }

    @PostMapping("/pt/v1/sc/topEvent/cancel")
    @ApiOperation("学校症状手动事件取消置顶")
    public void cancelTopEvent(@RequestParam String loginUserId, @RequestParam String loginUserName, @Valid @RequestBody SmsVo smsVo) {
        log.info("学校症状手动事件取消置顶开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        //更新AI筛选状态
        sympEventService.updateAiScreen(AttentionLevelEnum.ORDINARY.getType(), smsVo.getEventId());

        sympEventService.addProcessRecord(smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.TOP.getCode(), "取消置顶");
    }

    @PostMapping("/pt/v1/sc/attentionEvent/enable")
    @ApiOperation("学校症状手动事件标记")
    public void enableAttentionEvent(@RequestParam String loginUserId, @RequestParam String loginUserName, @Valid @RequestBody SmsVo smsVo) {
        log.info("学校症状手动事件标记开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewSympWarningEvent event = sympEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //限定标记级别
        if (!(AttentionLevelEnum.ATTENTION_LEVEL1.getType().equals(smsVo.getAttentionLevel()) || AttentionLevelEnum.ATTENTION_LEVEL2.getType().equals(smsVo.getAttentionLevel()))) {
            throw new MedicalBusinessException("11450003", "事件标记级别有误");
        }

        //1, 更新AI筛选状态
        sympEventService.updateAiScreen(smsVo.getAttentionLevel(), smsVo.getEventId());

        //更新为已审核状态    事件为标记时，默认就是已审核的
        sympEventService.updateCheckedLevel(CheckedLevelEnum.CHECKED.getType(), smsVo.getEventId());

        sympEventService.addProcessRecord(smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.ATTENTION.getCode(), "标记");
    }

    @PostMapping("/pt/v1/sc/attentionEvent/cancel")
    @ApiOperation("学校症状手动事件取消标记")
    public void cancelAttentionEvent(@RequestParam String loginUserId, @RequestParam String loginUserName, @Valid @RequestBody SmsVo smsVo) {
        log.info("学校症状手动事件取消标记开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewSympWarningEvent event = sympEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //更新AI筛选状态
        sympEventService.updateAiScreen(AttentionLevelEnum.ORDINARY.getType(), smsVo.getEventId());

        sympEventService.addProcessRecord(smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.ATTENTION.getCode(), "取消标记");
    }

    @PostMapping("/pt/v1/sc/checkedEvent/enable")
    @ApiOperation("学校症状手动事件已审核")
    public void enableCheckedEvent(@RequestParam String loginUserId, @RequestParam String loginUserName, @Valid @RequestBody SmsVo smsVo) {
        log.info("学校症状手动事件已审核开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewSympWarningEvent event = sympEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //更新为已审核状态
        sympEventService.updateCheckedLevel(CheckedLevelEnum.CHECKED.getType(), smsVo.getEventId());

        sympEventService.addProcessRecord(smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.CHECKED.getCode(), "已审");
    }

    @PostMapping("/pt/v1/sc/checkedEvent/cancel")
    @ApiOperation("学校症状手动事件取消审核")
    public void cancelCheckedEvent(@RequestParam String loginUserId, @RequestParam String loginUserName, @Valid @RequestBody SmsVo smsVo) {
        log.info("学校症状手动事件取消审核开始，信息: loginUserName:" + loginUserName + " ,smsVo:" + smsVo.toString());
        TbCdcewSympWarningEvent event = sympEventService.getCdcWaringEventByEventId(smsVo.getEventId());
        if (Objects.isNull(event)) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        } else if (AttentionLevelEnum.TOP.getType().equals(event.getFocusLevel())) {
            throw new MedicalBusinessException("11450002", "该事件已被置顶,不可再进行其他操作");
        }

        //更新为未审核状态
        sympEventService.updateCheckedLevel(CheckedLevelEnum.NOT_CHECKED.getType(), smsVo.getEventId());

        sympEventService.addProcessRecord(smsVo.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.CHECKED.getCode(), "取消已审");

    }

}

package com.iflytek.fpva.cdc.model.vo.outcall;

import com.iflytek.fpva.cdc.constant.enums.CallConnectStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import java.util.List;


@Data
public class OutCallResultRespVO {
    @ApiModelProperty("疾控中心外呼结果")
    private OutCallBusinessPersonStatVO personType3Stat;

    @ApiModelProperty("防保科外呼结果")
    private OutCallBusinessPersonStatVO personType4Stat;

    @ApiModelProperty("信号病历及关联人群外呼结果")
    private OutCallPatientStatVO patientAndRelationStat;

    @Data
    public static class OutCallBusinessPersonStatVO{
        @ApiModelProperty("总计")
        private Long total;
        @ApiModelProperty("接通数量")
        private Long connectCount;
        @ApiModelProperty("未接通数量")
        private Long unConnectCount;
        @ApiModelProperty("业务人员集合")
        private List<OutCallBusinessPersonVO> businessPersonList;

        public static OutCallBusinessPersonStatVO fromVO(List<OutCallBusinessPersonVO> list){
            OutCallBusinessPersonStatVO statVO = new OutCallBusinessPersonStatVO();
            final long connectCount = list.stream()
                    .filter(v -> CallConnectStatusEnum.CONNECTED.getCode().equals(v.getConnectStatus()))
                    .count();
            final long unConnectCount = list.stream()
                    .filter(v -> CallConnectStatusEnum.UNCONNECTED.getCode().equals(v.getConnectStatus()))
                    .count();

            statVO.setConnectCount(connectCount);
            statVO.setTotal(connectCount+unConnectCount);
            statVO.setUnConnectCount(unConnectCount);
            statVO.setBusinessPersonList(list);
            return statVO;
        }
    }

    @Data
    public static class OutCallPatientStatVO{
        @ApiModelProperty("总计")
        private Long total;
        @ApiModelProperty("接通数量")
        private Long connectCount;
        @ApiModelProperty("未接通数量")
        private Long unConnectCount;
        @ApiModelProperty("病例与病例关联人群集合")
        private List<OutCallPatientVO> patientList;
        public static OutCallPatientStatVO fromVO(List<OutCallPatientVO> list){
            OutCallPatientStatVO statVO = new OutCallPatientStatVO();
            statVO.setPatientList(list);

            Long unConnectTotal = 0L;
            Long connectTotal = 0L;
            for (OutCallPatientVO patientVO : list) {
                final List<OutCallPatientRelationVO> patientRelationList = patientVO.getPatientRelationList();
                if (!CollectionUtils.isEmpty(patientRelationList)){
                    final Long relationConnectCount = patientRelationList.stream()
                            .filter(v -> CallConnectStatusEnum.CONNECTED.getCode().equals(v.getConnectStatus()))
                            .count();
                    connectTotal = connectTotal + relationConnectCount;

                    final Long relationUnConnectCount = patientRelationList.stream()
                            .filter(v -> CallConnectStatusEnum.UNCONNECTED.getCode().equals(v.getConnectStatus()))
                            .count();
                    unConnectTotal = unConnectTotal + relationUnConnectCount;
                }

                if (CallConnectStatusEnum.CONNECTED.getCode().equals(patientVO.getConnectStatus())){
                    connectTotal = connectTotal + 1;
                }
                if (CallConnectStatusEnum.UNCONNECTED.getCode().equals(patientVO.getConnectStatus())){
                    unConnectTotal = unConnectTotal + 1;
                }
            }

            statVO.setTotal(unConnectTotal + connectTotal);
            statVO.setConnectCount(connectTotal);
            statVO.setUnConnectCount(unConnectTotal);
            return statVO;
        }
    }


}

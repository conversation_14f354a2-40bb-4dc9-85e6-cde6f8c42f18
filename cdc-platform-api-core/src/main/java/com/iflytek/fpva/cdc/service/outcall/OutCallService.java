package com.iflytek.fpva.cdc.service.outcall;

import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import com.iflytek.fpva.cdc.constant.enums.SymptomTypeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcCallTemplate;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedOutCallResultVO;
import com.iflytek.fpva.cdc.model.dto.CreateCallTaskDto;
import com.iflytek.fpva.cdc.model.dto.CreateSmsVO;
import com.iflytek.fpva.cdc.model.preventionControl.vo.PreventionControlOutCallResultVO;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.call.SmsVo;
import com.iflytek.fpva.cdc.model.vo.chart.ChartDataVo;
import com.iflytek.fpva.cdc.outbound.model.dto.output.CreateCallRs;
import com.iflytek.fpva.cdc.outbound.util.Response;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 外呼服务
 *
 * <AUTHOR>
 * @date 2020/10/19 14:24
 */
public interface OutCallService {

    /**
     * 创建外呼任务
     *
     * @param createCallTaskDto 外呼任务创建参数
     * @return 批次号信息
     */
    List<Response<CreateCallRs>> createCallSpeechTaskForPatient(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName);

    /**
     * 创建外呼任务
     *
     * @param createCallTaskDto 外呼任务创建参数
     * @return 批次号信息
     */
    List<Response<CreateCallRs>> createCallSpeechTaskForAdditionPerson(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName);

    /**
     * 创建外呼任务
     *
     * @param createCallTaskDto 外呼任务创建参数
     * @return 批次号信息
     */
    Response<CreateCallRs> createCallSpeechTaskForBusiness(CreateCallTaskDto createCallTaskDto,String loginUserId,String loginUserName);

    /**
     * 根据事件编码获取外呼统计结果
     *
     * @param eventId 事件id
     * @return ChartDataVo
     */
    List<ChartDataVo> getOutCallResult(String eventId, String dataSourceTypeCode);

    /**
     * 传染病事件ID导出外呼结果-excel格式-返回前端字节流
     *
     * @param eventId 事件id
     * @return 文件流
     */
    ResponseEntity<byte[]> exportInfectedCallResultExcel(String eventId);

    /**
     * 根据关注事件ID导出外呼结果-excel格式-返回前端字节流
     *
     * @param loginUserId 用户ID
     * @param attentionId 事件id
     * @return 文件流
     */
    ResponseEntity<byte[]> exportCallResultExcelByAttentionId(String loginUserId, String attentionId, String dataSourceTypeCode);

    /**
     * 根据传染病关注事件ID导出外呼结果-excel格式-返回前端字节流
     *
     * @param loginUserId 用户ID
     * @param attentionId 事件id
     * @return 文件流
     */
    ResponseEntity<byte[]> exportInfectedCallResultExcelByAttentionId(String loginUserId, String attentionId);

    /**
     * 症候群区分
     *
     * @param symptomType 症状
     * @return SymptomTypeEnum
     */
    SymptomTypeEnum getSymptomTypeEnum(String symptomType);

    /**
     * 置顶发送短信提醒 --- 症候群
     * @param loginUserId 登录用户ID
     * @param smsVo 短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendSyndromeSmsForOnTop(String loginUserId,String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 传染病
     * @param loginUserId 登录用户ID
     * @param smsVo 短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendInfectedSmsForOnTop(String loginUserId,String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 学校症状
     * @param loginUserId 登录用户ID
     * @param smsVo 短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendScSymptomSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 中毒监测
     * @param loginUserId 登录用户ID
     * @param smsVo 短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendPoisonSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 中毒监测
     *
     * @param loginUserId 登录用户ID
     * @param smsVo       短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendOutpatientSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 不明原因监测
     *
     * @param loginUserId 登录用户ID
     * @param smsVo       短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendUnknownReasonSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 联防联控监测
     *
     * @param loginUserId 登录用户ID
     * @param smsVo       短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendPreventionControlSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo);

    /**
     * 置顶发送短信提醒 --- 自定义预警监测
     * @param loginUserId 登录用户ID
     * @param smsVo 短信内容参数
     * @return CallSpeechVo 短信回执信息
     */
    Response<CreateCallRs> sendCustomizedSmsForOnTop(String loginUserId,String loginUserName, SmsVo smsVo);

    /**
     * 发送短信
     * @param createSmsVO 短信配置参数
     * @return
     */
    Response<CreateCallRs> sendSms(CreateSmsVO createSmsVO);

    /**
     * 病例核实
     * @return
     */
    void medicalConfirm();

    /**
     * 传染病创建外呼任务
     *
     * @param createCallTaskDto 外呼任务创建参数
     * @return 批次号信息
     */
    Response<CreateCallRs> createCallSpeechTaskForInfectedBusiness(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName);

    /**
     * 中毒创建外呼任务
     *
     * @param createCallTaskDto 外呼任务创建参数
     * @return 批次号信息
     */
    Response<CreateCallRs> createCallSpeechTaskForPoisonBusiness(CreateCallTaskDto createCallTaskDto);


    Response<CreateCallRs> createCallSpeechTaskForOutpatientBusiness(CreateCallTaskDto createCallTaskDto);

    Response<CreateCallRs> createCallSpeechTaskForUnknownReasonBusiness(CreateCallTaskDto createCallTaskDto);

    Response<CreateCallRs> createCallSpeechTaskForPreventionControlBusiness(CreateCallTaskDto createCallTaskDto);


    OutCallResultVO getCheckResult(String loginUserId, String eventId);
    /**
     * 自定义创建外呼任务
     *
     * @param createCallTaskDto 外呼任务创建参数
     * @return 批次号信息
     */
    Response<CreateCallRs> createCallSpeechTaskForCustomizedBusiness(CreateCallTaskDto createCallTaskDto);

    InfectedOutCallResultVO getInfectedCheckResult(String loginUserId, String eventId);

    PoisonOutCallResultVO getPoisonCheckResult(String loginUserId, String eventId);


    OutpatientOutCallResultVO getOutpatientCheckResult(String loginUserId, String eventId);

    UnknownReasonOutCallResultVO getUnknownReasonCheckResult(String loginUserId, String eventId);

    PreventionControlOutCallResultVO getPreventionControlCheckResult(String loginUserId, String eventId);

    CustomizedOutCallResultVO getCustomizedCheckResult(String loginUserId, String eventId);

    /**
     * 从外呼平台同步外呼结果
     * @return
     */
    Map<String, Map<String, Integer>> syncOutboundResult();
    /**
     * 从外呼平台同步外呼结果
     * @return
     */
    Map<String, Map<String, Integer>> syncOutboundResultNew();

    byte[] downloadMedicalTemplate(String eventId);

    UploadResultVO uploadRelationFile(MultipartFile file, String eventId);

    UploadResultVO uploadMedicalFile(MultipartFile file);

    String getCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate);

}

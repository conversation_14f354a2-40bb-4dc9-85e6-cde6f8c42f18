package com.iflytek.fpva.cdc.service.outcall.impl;

import com.iflytek.fpva.cdc.entity.TbCdcewOutcallPatientRelation;
import com.iflytek.fpva.cdc.mapper.outcall.TbCdcewOutcallPatientRelationMapper;
import com.iflytek.fpva.cdc.model.vo.MedOutCallCountVO;
import com.iflytek.fpva.cdc.model.vo.outcall.OutCallPatientRelationVO;
import com.iflytek.fpva.cdc.service.outcall.OutcallPatientRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 事件其他相关人群-周边居民 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Service
public class OutcallPatientRelationServiceImpl extends ServiceImpl<TbCdcewOutcallPatientRelationMapper, TbCdcewOutcallPatientRelation> implements OutcallPatientRelationService {

    @Override
    public List<TbCdcewOutcallPatientRelation> listByEventIdAndConfigType(String eventId, String warningType) {
        return baseMapper.listByEventIdAndConfigType(eventId,warningType);
    }

    @Override
    public List<TbCdcewOutcallPatientRelation> findByOutCallStatusAndOutCallBatchIdIsNotNull(Integer code, String configType) {
        return baseMapper.findByOutCallStatusAndOutCallBatchIdIsNotNull(code,configType);
    }

    @Override
    public MedOutCallCountVO getMedOutCallCount(String eventId, String warningType) {
        return baseMapper.getMedOutCallCount(eventId, warningType);
    }

    @Override
    public List<OutCallPatientRelationVO> findOutCallResult(String eventId, String warningType) {
        return baseMapper.findOutCallResult(eventId, warningType);
    }
}

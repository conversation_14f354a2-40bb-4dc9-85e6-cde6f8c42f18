package com.iflytek.fpva.cdc.model.vo;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.constant.enums.ClinicTypeEnum;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TopIndicatorsReportCardExcelVO {
    //姓名
    @ExcelColumn(name = "姓名", column = 1)
    String name;
    //性别
    @ExcelColumn(name = "性别", column = 2)
    String sex;
    //年龄
    @ExcelColumn(name = "年龄", column = 3)
    String age;
    //来源
    @ExcelColumn(name = "就诊类型", column = 4)
    @ApiModelProperty(value = "患者来源名称(1、建档注册；2、门诊服务；3、住院服务；4、健康体检；5、计划免疫；6、健康随访；7、公卫报卡；9、其它；)")
    String patientSource;
    //现住址
    @ExcelColumn(name = "现住址", column = 5)
    String address;
    //发病日期
    @ExcelColumn(name = "发病日期", column = 6)
    String firstOnsetDate;
    //疾病名称
    @ExcelColumn(name = "疾病名称", column = 7)
    String infectedName;
    //疾病类型
    @ExcelColumn(name = "疾病类型", column = 8)
    String infectedTypeName;
    //诊断时间
    @ExcelColumn(name = "诊断时间", column = 9)
    String diagnoseDatetime;
    //生成时间
    @ExcelColumn(name = "生成时间", column = 10)
    String createTime;
    //上报时间
    @ExcelColumn(name = "防保科审核时间", column = 11)
    String checkDateTime;
    //上报机构
    @ExcelColumn(name = "上报机构", column = 12)
    String reportOrgName;


    public static TopIndicatorsReportCardExcelVO fromEntity(InfectedReportCardInfo infectedReportCardInfo) {
        TopIndicatorsReportCardExcelVO topIndicatorsReportCardExcelVO = new TopIndicatorsReportCardExcelVO();
        topIndicatorsReportCardExcelVO.setName(infectedReportCardInfo.getPatientName());
        topIndicatorsReportCardExcelVO.setSex(infectedReportCardInfo.getSexName());
        if (StringUtils.isBlank(infectedReportCardInfo.getExactAge())) {
            topIndicatorsReportCardExcelVO.setAge("-");
        } else if (!StringUtils.isBlank(infectedReportCardInfo.getAgeUnitName())) {
            topIndicatorsReportCardExcelVO.setAge(infectedReportCardInfo.getExactAge() + infectedReportCardInfo.getAgeUnitName());
        } else {
            topIndicatorsReportCardExcelVO.setAge(infectedReportCardInfo.getExactAge() + "岁");
        }
        topIndicatorsReportCardExcelVO.setAddress(infectedReportCardInfo.getLivingAddrDetail());
        topIndicatorsReportCardExcelVO.setFirstOnsetDate(DateUtils.parseDate(infectedReportCardInfo.getFirstOnsetDate()));
        topIndicatorsReportCardExcelVO.setInfectedName(infectedReportCardInfo.getInfectedSubName());
        topIndicatorsReportCardExcelVO.setPatientSource(ClinicTypeEnum.getDescByCode(infectedReportCardInfo.getPatientSourceCode()));
        topIndicatorsReportCardExcelVO.setInfectedTypeName(infectedReportCardInfo.getInfectedTypeName());
        topIndicatorsReportCardExcelVO.setDiagnoseDatetime(DateFormatUtils.parseDate(infectedReportCardInfo.getDiagnoseDatetime(), TimeConstant.NORM_DATEHOUR_PATTERN));
        topIndicatorsReportCardExcelVO.setCheckDateTime(DateFormatUtils.parseDate(infectedReportCardInfo.getCheckDateTime(), TimeConstant.NORM_DATEHOUR_PATTERN));
        topIndicatorsReportCardExcelVO.setCreateTime(DateFormatUtils.parseDate(infectedReportCardInfo.getCreateTime(), TimeConstant.NORM_DATEHOUR_PATTERN));
        topIndicatorsReportCardExcelVO.setReportOrgName(infectedReportCardInfo.getReportOrgName());
        return topIndicatorsReportCardExcelVO;
    }
}

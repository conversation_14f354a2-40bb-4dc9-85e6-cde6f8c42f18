package com.iflytek.fpva.cdc.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 事件信息
 *
 * <AUTHOR>
 * @date 2022-05-20 10:56
 */
@Data
public class EventInfoDto {
    /**
     * 事件ID
     */
    @ApiModelProperty("事件ID")
    private String eventId;
    /**
     * 事件类型编码
     */
    @ApiModelProperty("事件类型编码, 1:建档注册;2:门诊服务;3:住院服务;4:健康体检;5:计划免疫;6:健康随访;7:公卫报卡;9:其它;11:上报卡;12:购药;13:晨午晚检;14:海关;15:交通;16:重点场所;17:健康打卡")
    private String dataSrcCode;
    /**
     * 事件类型名称
     */
    @ApiModelProperty("事件类型名称")
    private String dataSrcName;
    /**
     * 查询机构类型
     */
    @ApiModelProperty("查询机构类型, 1：医疗机构；2：药店；3：学校")
    private String queryOrgType;
    /**
     * 机构类型编码
     */
    @ApiModelProperty("机构类型编码")
    private String orgTypeCode;
    /**
     * 机构类型名称
     */
    @ApiModelProperty("机构类型名称")
    private String orgTypeName;
    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private String orgId;
    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String orgName;
    /**
     * 机构地址
     */
    @ApiModelProperty("机构地址")
    private String orgAddress;
    /**
     * 药品处方标签
     */
    @ApiModelProperty("药品处方标签")
    private String drugPrescriptionTag;
    /**
     * 健康状态
     */
    @ApiModelProperty("健康状态")
    private String healthStatus;
    /**
     * 事件时间
     */
    @ApiModelProperty("事件时间")
    private Date eventDatetime;

    @ApiModelProperty("记录id")
    private String recordId;
}

package com.iflytek.fpva.cdc.mapper.unknownReason;

import com.iflytek.fpva.cdc.entity.TbCdcewUnknownReasonEventRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface TbCdcewUnKnownReasonEventRecordMapper {

    int insert(TbCdcewUnknownReasonEventRecord record);

    List<TbCdcewUnknownReasonEventRecord> findByEventIdAndProcessingStatusAndType(@Param("eventIds") List<String> eventIds,
                                                                           @Param("processingStatusCollection") Collection<Integer> processingStatusCollection, @Param("processType") String processType);

    List<TbCdcewUnknownReasonEventRecord> getSingleProcessorMsg(@Param("eventId") String eventId,
                                                         @Param("processingStatus") Integer processingStatus,
                                                         @Param("processType") String processType);

    int insertSelective(TbCdcewUnknownReasonEventRecord record);
}

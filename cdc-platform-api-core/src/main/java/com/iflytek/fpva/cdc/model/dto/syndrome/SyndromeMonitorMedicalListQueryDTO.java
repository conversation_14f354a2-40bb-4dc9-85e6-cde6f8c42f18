package com.iflytek.fpva.cdc.model.dto.syndrome;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;


import java.util.Date;


@Data
public class SyndromeMonitorMedicalListQueryDTO {

    private int pageIndex;

    @Range(max = 50)
    private int pageSize;

    @ApiModelProperty("病例分布类型 justModelFlag :是否模板病例（基于模板未做明显更改） ;abnormalTimeFlag: 是否为异常时间就诊病例 ;enhanceConditionFlag:是否增强条件病例")
    private String medicalType;


    @ApiModelProperty("id||数据编号")
    private String id;

    @ApiModelProperty("年龄分布")
    private String ageGroup;

    @ApiModelProperty("1:当日; 2:窗口期")
    private String dateType;

    @ApiModelProperty("最高风险诊断（这个病历这个症候群下的最高风险的诊断）")
    private String riskDiagnose;

    @ApiModelProperty("最高风险诊断风险等级,  中;高")
    private String riskDiagnoseRiskLevel;


    @ApiModelProperty(hidden = true)
    private String loginUserId;


    @ApiModelProperty(value = "查询开始日期 yyyy-MM-dd",hidden = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date minFullDate;

    @ApiModelProperty(value = "查询结束日期 yyyy-MM-dd",hidden = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date maxFullDate;

    @ApiModelProperty(value = "机构id",hidden = true)
    private String orgId;

    @ApiModelProperty(value = "街道code",hidden = true)
    private String streetCode;

    @ApiModelProperty(value = "查询结束日期 yyyy-MM-dd",hidden = true)
    private String syndromeSubCode;

    @ApiModelProperty(value = "monitorSetId",hidden = true)
    private String monitorSetId;
}

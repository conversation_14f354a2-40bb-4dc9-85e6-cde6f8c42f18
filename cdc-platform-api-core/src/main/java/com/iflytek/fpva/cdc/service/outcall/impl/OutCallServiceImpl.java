package com.iflytek.fpva.cdc.service.outcall.impl;

import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.common.enums.BooleanEnum;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import com.iflytek.fpva.cdc.common.utils.StorageClientUtil;
import com.iflytek.fpva.cdc.config.ParamConfig;
import com.iflytek.fpva.cdc.constant.*;
import com.iflytek.fpva.cdc.constant.enums.*;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.entity.preventionControl.TbCdcewPreventionControlOutcallBatch;
import com.iflytek.fpva.cdc.entity.preventionControl.TbCdcewPreventionControlOutcallNotice;
import com.iflytek.fpva.cdc.entity.preventionControl.TbCdcewPreventionControlWarningEvent;
import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedOutcallBatch;
import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedOutcallNotice;
import com.iflytek.fpva.cdc.entity.customized.TbCdcewCustomizedWarningEvent;
import com.iflytek.fpva.cdc.mapper.common.*;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcInfectedWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcewInfectedMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcewInfectedOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcewInfectedOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.multichannel.TbCdcewDiagnosisDoctorMapper;
import com.iflytek.fpva.cdc.mapper.outcall.CdcCallChartInfoMapper;
import com.iflytek.fpva.cdc.mapper.outcall.CdcCallResultDetailMapper;
import com.iflytek.fpva.cdc.mapper.outcall.TbCdcewOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.outcall.TbCdcewOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.outpatient.TbCdcewOutpatientMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.outpatient.TbCdcewOutpatientOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.outpatient.TbCdcewOutpatientOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.outpatient.TbCdcewOutpatientWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.poison.TbCdcewPoisonWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.preventionControl.TbCdcewPreventionControlMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.preventionControl.TbCdcewPreventionControlOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.preventionControl.TbCdcewPreventionControlOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.preventionControl.TbCdcewPreventionControlWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.customized.TbCdcewCustomizedWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.CdcWarningEventMapper;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcEventPersonRelationMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcWarningDetailMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonOutcallBatchMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonOutcallNoticeMapper;
import com.iflytek.fpva.cdc.mapper.unknownReason.TbCdcewUnknownReasonWarningEventMapper;
import com.iflytek.fpva.cdc.model.customizedWarning.vo.CustomizedOutCallResultVO;
import com.iflytek.fpva.cdc.model.dto.*;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.po.UapUserPo;
import com.iflytek.fpva.cdc.model.preventionControl.vo.PreventionControlOutCallResultVO;
import com.iflytek.fpva.cdc.model.vo.*;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.call.SmsVo;
import com.iflytek.fpva.cdc.model.vo.chart.ChartDataVo;
import com.iflytek.fpva.cdc.model.vo.chart.Serie;
import com.iflytek.fpva.cdc.outbound.model.dto.input.CallTask;
import com.iflytek.fpva.cdc.outbound.model.dto.input.SmsTask;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Dweller;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Recall;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Sms;
import com.iflytek.fpva.cdc.outbound.model.dto.input.param.Var;
import com.iflytek.fpva.cdc.outbound.model.dto.output.CreateCallRs;
import com.iflytek.fpva.cdc.outbound.service.CreateOutboundService;
import com.iflytek.fpva.cdc.outbound.util.Response;
import com.iflytek.fpva.cdc.schedule.DelayedTask;
import com.iflytek.fpva.cdc.service.common.*;
import com.iflytek.fpva.cdc.service.infected.CdcInfectedEventService;
import com.iflytek.fpva.cdc.service.infected.CdcReportCardService;
import com.iflytek.fpva.cdc.service.outcall.OutCallService;
import com.iflytek.fpva.cdc.service.outcall.OutboundResultSyncService;
import com.iflytek.fpva.cdc.service.syndrome.CdcEventService;
import com.iflytek.fpva.cdc.util.*;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.DelayQueue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/19 14:40
 */
@Slf4j
@Service
public class OutCallServiceImpl implements OutCallService {

    @Resource
    private TbCdcewDiagnosisDoctorMapper diagnosisDoctorMapper;

    @Resource
    private TbCdcewBusinessPersonMapper businessPersonMapper;

    @Resource
    private CreateOutboundService createOutboundService;

    @Resource
    private CdcWarningEventMapper cdcWarningEventMapper;

    @Resource
    private TbCdcWarningDetailMapper tbCdcWarningDetailMapper;

    @Resource
    private TbCdcHisMedicalExtendMapper tbCdcHisMedicalExtendMapper;

    @Resource
    private TbCdcHisMedicalExtendMapper cdcHisMedicalExtendMapper;

    @Resource
    private CdcCallResultDetailMapper cdcCallResultDetailMapper;

    @Resource
    private TbCdcewHisMedicalInfoMapper hisMedicalInfoMapper;

    @Resource
    private CdcCallChartInfoMapper cdcCallChartInfoMapper;

    @Resource
    private AttentionConditionMapper attentionConditionMapper;

    @Resource
    private CdcReportCardService cdcReportCardService;

    @Resource
    private RestService restService;

    @Resource
    private CdcEventService cdcEventService;

    @Resource
    private TbCdcCallTemplateMapper tbCdcCallTemplateMapper;

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    private StorageClientUtil storageClientUtil;

    @Resource
    private TbCdcEventPersonRelationMapper tbCdcEventPersonRelationMapper;

    @Resource
    private TbCdcewOutcallNoticeMapper outcallNoticeMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcInfectedWarningEventMapper tbCdcInfectedWarningEventMapper;

    @Resource
    private TbCdcewCustomizedWarningEventMapper tbCdcewCustomizedWarningEventMapper;

    @Resource
    private TbCdcewCustomizedMedRelationMapper tbCdcewCustomizedMedRelationMapper;

    @Resource
    private TbCdcewInfectedMedRelationMapper tbCdcewInfectedMedRelationMapper;

    @Resource
    private TbCdcewPoisonMedRelationMapper tbCdcewPoisonMedRelationMapper;

    @Resource
    TbCdcewOutpatientOutcallNoticeMapper tbCdcewOutpatientOutcallNoticeMapper;

    @Resource
    private TbCdcewOutcallBatchMapper outcallBatchMapper;

    @Resource
    private TbCdcewOutcallNoticeMapper tbCdcewOutcallNoticeMapper;

    @Resource
    private TbCdcewInfectedOutcallNoticeMapper tbCdcewInfectedOutcallNoticeMapper;

    @Resource
    private TbCdcewPoisonOutcallNoticeMapper tbCdcewPoisonOutcallNoticeMapper;

    @Resource
    private TbCdcewInfectedOutcallBatchMapper tbCdcewInfectedOutcallBatchMapper;

    @Resource
    private TbCdcewPoisonOutcallBatchMapper tbCdcewPoisonOutcallBatchMapper;
    @Resource
    private TbCdcewOutpatientMedRelationMapper tbCdcewOutpatientMedRelationMapper;

    @Resource
    private TbCdcewCustomizedOutcallBatchMapper tbCdcewCustomizedOutcallBatchMapper;

    @Resource
    private TbCdcewCustomizedOutcallNoticeMapper tbCdcewCustomizedOutcallNoticeMapper;

    @Resource(name = "patientOutBoundResultSyncImpl")
    private OutboundResultSyncService patientOutBoundResultSync;

    @Resource(name = "additionPersonOutBoundResultSyncImpl")
    private OutboundResultSyncService additionPersonOutBoundResultSync;

    @Resource(name = "businessPersonOutBoundResultSyncImpl")
    private OutboundResultSyncService businessPersonOutBoundResultSync;

    @Value("${fpva.outCall.appName}")
    private String appName;

    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;
    @Resource
    TbCdcConfigService tbCdcConfigService;

    @Value("${outcall.smsId:12107}")
    private String smsId;

    @Resource
    private CdcInfectedEventService cdcInfectedEventService;

    @Resource
    TbCdcewPoisonWarningEventMapper tbCdcewPoisonWarningEventMapper;

    @Resource
    TbCdcewSympWarningEventMapper tbCdcewSympWarningEventMapper;

    @Resource
    TbCdcewOutpatientWarningEventMapper tbCdcewOutpatientWarningEventMapper;

    @Resource
    TbCdcewUnknownReasonWarningEventMapper tbCdcewUnknownReasonWarningEventMapper;

    @Resource
    private TbCdcewOutpatientOutcallBatchMapper tbCdcewOutpatientOutcallBatchMapper;

    @Resource
    private TbCdcewUnknownReasonOutcallBatchMapper tbCdcewUnknownReasonOutcallBatchMapper;

    @Resource
    private TbCdcewUnknownReasonOutcallNoticeMapper tbcdcewUnknownReasonOutcallNoticeMapper;

    @Resource
    private TbCdcewUnknownReasonMedRelationMapper tbcdcewUnknownReasonMedRelationMapper;

    @Resource
    private TbCdcewPreventionControlWarningEventMapper tbCdcewPreventionControlWarningEventMapper;

    @Resource
    private TbCdcewPreventionControlMedRelationMapper tbCdcewPreventionControlMedRelationMapper;

    @Resource
    private TbCdcewPreventionControlOutcallBatchMapper tbCdcewPreventionControlOutcallBatchMapper;

    @Resource
    private TbCdcewPreventionControlOutcallNoticeMapper tbCdcewPreventionControlOutcallNoticeMapper;

    private static final SnowflakeIdWorker idWorker = SnowflakeIdWorker.getInstance();

    private DelayQueue<DelayedTask> delayedTasks = new DelayQueue<>();

    @Resource
    private FileService fileService;

    @Resource
    private TbCdcewRceMsgRecordService rceMsgRecordService;

    @Resource
    private CallTemplateService callTemplateService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public List<Response<CreateCallRs>> createCallSpeechTaskForPatient(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();
        //2，从文件读取数据
        List<PatientVO> personVOList = getPatientVOS(createCallTaskDto);

        //添加 外呼排查 操作记录
        //todo v1.0.9_ME_11版本暂时不处理区县级账号排查的节点问题
//        String processRecordId = cdcEventService.addEventRecord(createCallTaskDto.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.OUTCALL_PHONE.getCode(), RecordTypeEnum.OUTBOUND_CHECK.getCode());

        CdcWarningEvent cdcWarningEvent = cdcWarningEventMapper.selectByPrimaryKey(createCallTaskDto.getEventId());
        TbCdcWarningDetail tbCdcWarningDetail = tbCdcWarningDetailMapper.findFirstByEventId(createCallTaskDto.getEventId());
        List<MedicalInfoVO> medicalInfoVOList = hisMedicalInfoMapper.findRecordsByEventIds(Arrays.asList(createCallTaskDto.getEventId()));
        Map<String, List<MedicalInfoVO>> medicalInfoMap = medicalInfoVOList.stream().collect(Collectors.groupingBy(MedicalInfoVO::getSourceKey));

        List<HisMedicalExtendDTO> extendList = new ArrayList<>();
        residents.stream().forEach(resident -> {
            List<MedicalInfoVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(resident.getSourceKey(), v -> null);
            if (CollectionUtils.isNotEmpty(medicalInfoVOS)
                    && CallConnectStatusEnum.UNCONNECTED.getCode().equals(medicalInfoVOS.get(0).getConnectStatus())) {
                MedicalInfoVO medicalInfoVO = medicalInfoVOS.get(0);
                HisMedicalExtendDTO medicalExtend = new HisMedicalExtendDTO();
                medicalExtend.setId(String.valueOf(batchUidService.getUid("tb_cdcew_his_medical_extend")));
                medicalExtend.setEventId(createCallTaskDto.getEventId());
                medicalExtend.setDetailId(tbCdcWarningDetail.getId());
                medicalExtend.setPatientName(resident.getDwellerName());
                medicalExtend.setPatientPhone(resident.getTelephone());
                medicalExtend.setSourceKey(resident.getSourceKey());
                medicalExtend.setKeySymptomTagList(medicalInfoVO.getKeySymptomTagList());
                medicalExtend.setOutPatientTime(medicalInfoVO.getOutPatientTime());
                extendList.add(medicalExtend);
            }
        });
        personVOList.stream().forEach(item -> {
            List<MedicalInfoVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(item.getSourceKey(), v -> null);
            if (CollectionUtils.isNotEmpty(medicalInfoVOS)
                    && CallConnectStatusEnum.UNCONNECTED.getCode().equals(medicalInfoVOS.get(0).getConnectStatus())) {
                MedicalInfoVO medicalInfoVO = medicalInfoVOS.get(0);
                HisMedicalExtendDTO medicalExtend = new HisMedicalExtendDTO();
                medicalExtend.setId(String.valueOf(batchUidService.getUid("tb_cdcew_his_medical_extend")));
                medicalExtend.setEventId(createCallTaskDto.getEventId());
                medicalExtend.setDetailId(tbCdcWarningDetail.getId());
                medicalExtend.setPatientName(item.getPatientName());
                medicalExtend.setPatientPhone(item.getPhone());
                medicalExtend.setSourceKey(medicalInfoVO.getSourceKey());
                medicalExtend.setKeySymptomTagList(medicalInfoVO.getKeySymptomTagList());
                medicalExtend.setOutPatientTime(medicalInfoVO.getOutPatientTime());
                extendList.add(medicalExtend);
            }
        });

        if (CollectionUtils.isEmpty(extendList)) {
            return Arrays.asList(Response.error("没有外呼对象！"));
        }

//        List<TbCdcewOutcallNotice> tbCdcewOutcallNotices = new ArrayList<>();
//        extendList.forEach(extendDto -> {
//            TbCdcewOutcallNotice tbCdcewOutcallNotice = new TbCdcewOutcallNotice();
//            tbCdcewOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_notice")));
//            tbCdcewOutcallNotice.setEventId(createCallTaskDto.getEventId());
//            tbCdcewOutcallNotice.setPersonType(createCallTaskDto.getPersonType().shortValue());
//            tbCdcewOutcallNotice.setCreateTime(new Date());
//            tbCdcewOutcallNotice.setUpdateTime(new Date());
//            tbCdcewOutcallNotice.setStatDimName("信号病例");
//            tbCdcewOutcallNotice.setName(extendDto.getPatientName());
//            tbCdcewOutcallNotice.setPhone(extendDto.getPatientPhone());
//            tbCdcewOutcallNotice.setSourceKey(extendDto.getSourceKey());
//            tbCdcewOutcallNotice.setProcessRecordId(processRecordId);
//            tbCdcewOutcallNotices.add(tbCdcewOutcallNotice);
//        });
        TbCdcewOutcallBatch tbCdcewOutcallBatch = new TbCdcewOutcallBatch();
        tbCdcewOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_batch")));
        tbCdcewOutcallBatch.setEventId(createCallTaskDto.getEventId());
        tbCdcewOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
        tbCdcewOutcallBatch.setCreateTime(new Date());
        tbCdcewOutcallBatch.setUpdateTime(new Date());

        List<Response<CreateCallRs>> responseList = new ArrayList<>();
        SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
        extendList.forEach(extend -> {
            String date = sp.format(Optional.ofNullable(extend.getOutPatientTime()).orElse(new Date()));
            // 症状长度过长时 外呼会报错  此处只取前两种症状
            String symptom;
            String[] split = extend.getKeySymptomTagList().split(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER);
            if (split.length > 2) {
                symptom = org.apache.commons.lang3.StringUtils.joinWith(CommonConstants.VERTICAL_COMMA, split[0], split[1]).concat("等");
            } else {
                symptom = extend.getKeySymptomTagList().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
            }
            CallTask callTask = getCallTask(createCallTaskDto, cdcWarningEvent.getStatDimName(), symptom, date, false);
            callTask.setDwellers(Arrays.asList(Dweller.builder().dwellerName(extend.getPatientName()).telephone(extend.getPatientPhone()).relationId(extend.getSourceKey()).build()));
            Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
            if (response.isSuccess()) {
                String batchId = response.getData().getBatch().get(0);
                extend.setCallBatchId(batchId);
                extend.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
//                tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
//                    if(tbCdcewOutcallNotice.getSourceKey().equals(extend.getSourceKey())){
//                        tbCdcewOutcallNotice.setBatchId(tbCdcewOutcallBatch.getId());
//                        tbCdcewOutcallNotice.setCallBatchId(batchId);
//                        tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
//                    }
//                });
                log.info("执行外呼成功: {}", response);
            } else {
                extend.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
//                tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
//                    if(tbCdcewOutcallNotice.getSourceKey().equals(extend.getSourceKey())){
//                        tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
//                    }
//                });
                log.error("执行外呼失败：{}", response);
            }
            extend.setCreateTime(new Date());
            extend.setBatchId(tbCdcewOutcallBatch.getId());
//            tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
//                if(tbCdcewOutcallNotice.getSourceKey().equals(extend.getSourceKey())){
//                    tbCdcewOutcallNotice.setCreateTime(new Date());
//                    tbCdcewOutcallNotice.setBatchId(tbCdcewOutcallBatch.getId());
//                }
//            });
            responseList.add(response);
        });

        if (CollectionUtils.isNotEmpty(extendList)) {
            outcallBatchMapper.insert(tbCdcewOutcallBatch);
            saveDataToExtend(cdcWarningEvent, tbCdcWarningDetail, extendList);
        }
//        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcewOutcallNotices)) {
//            saveDataToOutCallNotice(createCallTaskDto, tbCdcewOutcallNotices);
//        }
        return responseList;
    }

    /**
     * 已经存在的更新，不存在的插入
     *
     * @param cdcWarningEvent
     * @param tbCdcWarningDetail
     * @param extendList
     */
    private void saveDataToExtend(CdcWarningEvent cdcWarningEvent,
                                  TbCdcWarningDetail tbCdcWarningDetail,
                                  List<HisMedicalExtendDTO> extendList) {
        List<HisMedicalExtendDTO> updateList = new ArrayList<>();
        List<HisMedicalExtendDTO> insertList = new ArrayList<>();
        Map<String, TbCdcHisMedicalExtend> hisMedicalExtendMap = tbCdcHisMedicalExtendMapper.findByEventIdAndDetailId(cdcWarningEvent.getId(), tbCdcWarningDetail.getId());
        extendList.forEach(extendDTO -> {
            TbCdcHisMedicalExtend existingExtend = Optional.ofNullable(hisMedicalExtendMap).orElse(new HashMap<>())
                    .computeIfAbsent(extendDTO.getSourceKey(), v -> null);
            if (null != existingExtend) {
                extendDTO.setId(existingExtend.getId());
                updateList.add(extendDTO);
            } else {
                insertList.add(extendDTO);
            }
        });
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcHisMedicalExtendMapper.batchUpdate(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcHisMedicalExtendMapper.insertList(insertList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Response<CreateCallRs>> createCallSpeechTaskForAdditionPerson(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName) {
        List<PersonVO> personVOList = getPersonVOS(createCallTaskDto);
        CdcWarningEvent cdcWarningEvent = cdcWarningEventMapper.selectByPrimaryKey(createCallTaskDto.getEventId());

        //添加 外呼排查 操作记录
        //todo v1.0.9_ME_11版本暂时不处理区县级账号主动排除的问题
//        String processRecordId = cdcEventService.addEventRecord(createCallTaskDto.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.OUTCALL_PHONE.getCode(), RecordTypeEnum.OUTBOUND_CHECK.getCode());
        String orgName = cdcWarningEvent.getStatDimName();
        List<MedicalInfoVO> medicalInfoVOList = hisMedicalInfoMapper.findRecordsByEventIds(Arrays.asList(createCallTaskDto.getEventId()));
        Map<String, List<MedicalInfoVO>> medicalInfoMap = medicalInfoVOList.stream().collect(Collectors.groupingBy(MedicalInfoVO::getPatientName));

        List<EventPersonRelationDTO> personRelationList = new ArrayList<>();
        personVOList.stream().forEach(item -> {
            List<MedicalInfoVO> medicalInfoVOS = medicalInfoMap.computeIfAbsent(item.getPatientName(), v -> null);
            if (CollectionUtils.isNotEmpty(medicalInfoVOS)) {
                MedicalInfoVO medicalInfoVO = medicalInfoVOS.get(0);
                String uId = String.valueOf(batchUidService.getUid("tb_cdcew_event_person_relation"));
                EventPersonRelationDTO tbCdcEventPersonRelation = new EventPersonRelationDTO();
                tbCdcEventPersonRelation.setEventId(createCallTaskDto.getEventId());
                tbCdcEventPersonRelation.setAttachmentId(createCallTaskDto.getAttachmentId());
                tbCdcEventPersonRelation.setCreateTime(new Date());
                tbCdcEventPersonRelation.setId(uId);
                tbCdcEventPersonRelation.setPersonName(item.getName());
                tbCdcEventPersonRelation.setPersonPhone(item.getPhone());
                tbCdcEventPersonRelation.setRelationType(item.getRelationType());
                tbCdcEventPersonRelation.setPatientName(item.getPatientName());
                tbCdcEventPersonRelation.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
                tbCdcEventPersonRelation.setKeySymptomTagList(medicalInfoVO.getKeySymptomTagList());
                tbCdcEventPersonRelation.setOutPatientTime(medicalInfoVO.getOutPatientTime());
                personRelationList.add(tbCdcEventPersonRelation);
            } else {
                log.info("未找到关联患者信息：{}", item);
            }
        });

        if (CollectionUtils.isEmpty(personRelationList)) {
            log.error("关联人群-没有获取到关联外呼对象！");
            return new ArrayList<>();
        }

//        List<TbCdcewOutcallNotice> tbCdcewOutcallNotices = new ArrayList<>();
//        personRelationList.forEach(personRelation -> {
//            TbCdcewOutcallNotice tbCdcewOutcallNotice = new TbCdcewOutcallNotice();
//            tbCdcewOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_notice")));
//            tbCdcewOutcallNotice.setEventId(createCallTaskDto.getEventId());
//            tbCdcewOutcallNotice.setPersonType(createCallTaskDto.getPersonType().shortValue());
//            tbCdcewOutcallNotice.setCreateTime(new Date());
//            tbCdcewOutcallNotice.setUpdateTime(new Date());
//            tbCdcewOutcallNotice.setStatDimName("关联人群");
//            tbCdcewOutcallNotice.setName(personRelation.getPersonName());
//            tbCdcewOutcallNotice.setPhone(personRelation.getPersonPhone());
//            tbCdcewOutcallNotice.setProcessRecordId(processRecordId);
//            tbCdcewOutcallNotices.add(tbCdcewOutcallNotice);
//        });
        TbCdcewOutcallBatch tbCdcewOutcallBatch = new TbCdcewOutcallBatch();
        tbCdcewOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_batch")));
        tbCdcewOutcallBatch.setEventId(createCallTaskDto.getEventId());
        tbCdcewOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
        tbCdcewOutcallBatch.setCreateTime(new Date());
        tbCdcewOutcallBatch.setUpdateTime(new Date());

        List<Response<CreateCallRs>> responseList = new ArrayList<>();
        SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
        personRelationList.forEach(person -> {
            String date = sp.format(Optional.ofNullable(person.getOutPatientTime()).orElse(new Date()));
            // 症状长度过长时 外呼会报错  此处只取前两种症状
            String symptom;
            String[] split = person.getKeySymptomTagList().split(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER);
            if (split.length > 2) {
                symptom = org.apache.commons.lang3.StringUtils.joinWith(CommonConstants.VERTICAL_COMMA, split[0], split[1]).concat("等");
            } else {
                symptom = person.getKeySymptomTagList().replaceAll(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER, CommonConstants.VERTICAL_COMMA);
            }
            CallTask callTask = getCallTask(createCallTaskDto, orgName, symptom, date, true);
            callTask.setDwellers(Arrays.asList(Dweller.builder().dwellerName(person.getPersonName()).telephone(person.getPersonPhone()).build()));
            Response<CreateCallRs> createCallRsResponse = createOutboundService.execCallTask(callTask);
            if (createCallRsResponse.isSuccess()) {
                String batchId = createCallRsResponse.getData().getBatch().get(0);
                person.setCallBatchId(batchId);
                person.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
//                tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
//                    if(tbCdcewOutcallNotice.getName().equals(person.getPersonName()) && tbCdcewOutcallNotice.getPhone().equals(person.getPersonPhone())){
//                        tbCdcewOutcallNotice.setBatchId(tbCdcewOutcallBatch.getId());
//                        tbCdcewOutcallNotice.setCallBatchId(batchId);
//                        tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
//                    }
//                });
                log.info("执行外呼成功: {}", createCallRsResponse);
            } else {
                person.setCallStatus(MedicalCallStatusEnum.CALL_FINISH.getCode());
                log.error("执行外呼失败：{}", createCallRsResponse);
            }
            person.setCreateTime(new Date());
            person.setBatchId(tbCdcewOutcallBatch.getId());
            responseList.add(createCallRsResponse);
        });

        if (CollectionUtils.isNotEmpty(personRelationList)) {
            outcallBatchMapper.insert(tbCdcewOutcallBatch);
            tbCdcEventPersonRelationMapper.batchInsert(personRelationList);
        }
        return responseList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<CreateCallRs> createCallSpeechTaskForBusiness(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();
        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        // 添加 外呼排查 操作记录
        String processRecordId = cdcEventService.addEventRecord(createCallTaskDto.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.OUTCALL_PHONE.getCode(), RecordTypeEnum.OUTBOUND_CHECK.getCode());


        List<TbCdcewOutcallNotice> tbCdcewOutcallNotices = new ArrayList<>();
        List<Dweller> dwellerList = new ArrayList<>();


        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewOutcallNotice tbCdcewOutcallNotice = new TbCdcewOutcallNotice();
                tbCdcewOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_notice")));
                tbCdcewOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewOutcallNotice.setPersonType(createCallTaskDto.getPersonType().shortValue());
                tbCdcewOutcallNotice.setCreateTime(new Date());
                tbCdcewOutcallNotice.setUpdateTime(new Date());
                tbCdcewOutcallNotice.setStatDimId(userOrg.getId());
                tbCdcewOutcallNotice.setStatDimName(userOrg.getName());
                tbCdcewOutcallNotice.setName(uapUserPo.getName());
                tbCdcewOutcallNotice.setPhone(resident.getTelephone());
                tbCdcewOutcallNotice.setProcessRecordId(processRecordId);
                tbCdcewOutcallNotices.add(tbCdcewOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewOutcallNotice tbCdcewOutcallNotice = new TbCdcewOutcallNotice();
                tbCdcewOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_notice")));
                tbCdcewOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewOutcallNotice.setPersonType(createCallTaskDto.getPersonType().shortValue());
                tbCdcewOutcallNotice.setCreateTime(new Date());
                tbCdcewOutcallNotice.setUpdateTime(new Date());
                tbCdcewOutcallNotice.setStatDimId(person.getStatDimId());
                tbCdcewOutcallNotice.setStatDimName(person.getStatDimName());
                tbCdcewOutcallNotice.setName(person.getName());
                tbCdcewOutcallNotice.setPhone(person.getPhone());
                tbCdcewOutcallNotice.setPositionName(person.getPositionName());
                tbCdcewOutcallNotice.setProcessRecordId(processRecordId);
                tbCdcewOutcallNotices.add(tbCdcewOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).build());
            });
        }
        CallTask callTask = getNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewOutcallBatch tbCdcewOutcallBatch = new TbCdcewOutcallBatch();
            tbCdcewOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_batch")));
            tbCdcewOutcallBatch.setEventId(createCallTaskDto.getEventId());
            tbCdcewOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
            tbCdcewOutcallBatch.setCreateTime(new Date());
            tbCdcewOutcallBatch.setUpdateTime(new Date());
            outcallBatchMapper.insert(tbCdcewOutcallBatch);

            tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
                tbCdcewOutcallNotice.setBatchId(tbCdcewOutcallBatch.getId());
                tbCdcewOutcallNotice.setCallBatchId(batchId);
                tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcewOutcallNotices)) {
            saveDataToOutCallNotice(createCallTaskDto, tbCdcewOutcallNotices);
        }
        return response;
    }

    private void saveDataToOutCallNotice(CreateCallTaskDto createCallTaskDto, List<TbCdcewOutcallNotice> tbCdcewOutcallNotices) {
        List<TbCdcewOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewOutcallNotice> insertList = new ArrayList<>();
        List<BusinessPersonOutCallDTO> businessPersonOutCallDTOS = tbCdcewOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType().shortValue());
        Map<String, List<BusinessPersonOutCallDTO>> valueMap = Optional.ofNullable(businessPersonOutCallDTOS).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(BusinessPersonOutCallDTO::getPersonId));
        tbCdcewOutcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<BusinessPersonOutCallDTO> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                BusinessPersonOutCallDTO businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private CallTask getInfectedNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
        String callContent = getInfectedCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
            String appContent = getInfectedCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    private CallTask getPoisonNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
        String callContent = getPoisonCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
            String appContent = getPoisonCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    private CallTask getNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.SYMPTOM.getId()));
        String callContent = getCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.SYMPTOM.getId()));
            String appContent = getCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    private TbCdcCallTemplate getDefaultTbCdcCallTemplate(Integer personType, Integer noteType, Integer monitorType) {
        TbCdcCallTemplate tbCdcCallTemplate = new TbCdcCallTemplate();
        tbCdcCallTemplate.setMonitorType(monitorType);
        tbCdcCallTemplate.setPersonType(personType);
        tbCdcCallTemplate.setNoteType(noteType);
        List<TbCdcCallTemplate> tbCdcCallTemplates = tbCdcCallTemplateMapper.selectBySelective(tbCdcCallTemplate);
        if (CollectionUtils.isEmpty(tbCdcCallTemplates)) {
            throw new MedicalBusinessException("11451101", "执行外呼失败，外呼模板为空！");
        }
        return tbCdcCallTemplates.get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<CreateCallRs> createCallSpeechTaskForInfectedBusiness(CreateCallTaskDto createCallTaskDto, String loginUserId, String loginUserName) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();

        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        // 添加 外呼排查 操作记录
        String processRecordId = cdcInfectedEventService.addEventRecord(createCallTaskDto.getEventId(), loginUserId, loginUserName, ProcessTypeEnum.OUTCALL_PHONE.getCode(), RecordTypeEnum.OUTBOUND_CHECK.getCode());

        List<Dweller> dwellerList = new ArrayList<>();
        List<TbCdcewInfectedOutcallNotice> tbCdcewInfectedOutcallNotices = new ArrayList<>();

        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewInfectedOutcallNotice tbCdcewInfectedOutcallNotice = new TbCdcewInfectedOutcallNotice();
                tbCdcewInfectedOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_notice")));
                tbCdcewInfectedOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewInfectedOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewInfectedOutcallNotice.setPersonType(createCallTaskDto.getPersonType());
                tbCdcewInfectedOutcallNotice.setCreateTime(new Date());
                tbCdcewInfectedOutcallNotice.setUpdateTime(new Date());
                tbCdcewInfectedOutcallNotice.setStatDimId(userOrg.getId());
                tbCdcewInfectedOutcallNotice.setStatDimName(userOrg.getName());
                tbCdcewInfectedOutcallNotice.setName(uapUserPo.getName());
                tbCdcewInfectedOutcallNotice.setPhone(resident.getTelephone());
                tbCdcewInfectedOutcallNotice.setProcessRecordId(processRecordId);
                tbCdcewInfectedOutcallNotices.add(tbCdcewInfectedOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewInfectedOutcallNotice tbCdcewInfectedOutcallNotice = new TbCdcewInfectedOutcallNotice();
                tbCdcewInfectedOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outcall_notice")));
                tbCdcewInfectedOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewInfectedOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewInfectedOutcallNotice.setPersonType(createCallTaskDto.getPersonType());
                tbCdcewInfectedOutcallNotice.setCreateTime(new Date());
                tbCdcewInfectedOutcallNotice.setUpdateTime(new Date());
                tbCdcewInfectedOutcallNotice.setStatDimId(person.getStatDimId());
                tbCdcewInfectedOutcallNotice.setStatDimName(person.getStatDimName());
                tbCdcewInfectedOutcallNotice.setName(person.getName());
                tbCdcewInfectedOutcallNotice.setPhone(person.getPhone());
                tbCdcewInfectedOutcallNotice.setPositionName(person.getPositionName());
                tbCdcewInfectedOutcallNotice.setProcessRecordId(processRecordId);
                tbCdcewInfectedOutcallNotices.add(tbCdcewInfectedOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        }

        CallTask callTask = getInfectedNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewInfectedOutcallBatch tbCdcewInfectedOutcallBatch = new TbCdcewInfectedOutcallBatch();
            tbCdcewInfectedOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_infected_outcall_batch")));
            tbCdcewInfectedOutcallBatch.setEventId(createCallTaskDto.getEventId());
            tbCdcewInfectedOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
            tbCdcewInfectedOutcallBatch.setCreateTime(new Date());
            tbCdcewInfectedOutcallBatch.setUpdateTime(new Date());
            tbCdcewInfectedOutcallBatchMapper.insert(tbCdcewInfectedOutcallBatch);

            tbCdcewInfectedOutcallNotices.forEach(tbCdcewOutcallNotice -> {
                tbCdcewOutcallNotice.setBatchId(tbCdcewInfectedOutcallBatch.getId());
                tbCdcewOutcallNotice.setCallBatchId(batchId);
                tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcewInfectedOutcallNotices)) {
            saveDataToInfectedOucCallNotice(createCallTaskDto, tbCdcewInfectedOutcallNotices);
        }
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<CreateCallRs> createCallSpeechTaskForPoisonBusiness(CreateCallTaskDto createCallTaskDto) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();

        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        List<Dweller> dwellerList = new ArrayList<>();
        List<TbCdcewPoisonOutcallNotice> tbCdcewPoisonOutcallNotices = new ArrayList<>();

        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewPoisonOutcallNotice tbCdcewPoisonOutcallNotice = new TbCdcewPoisonOutcallNotice();
                tbCdcewPoisonOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_poison_outcall_notice")));
                tbCdcewPoisonOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewPoisonOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewPoisonOutcallNotice.setPersonType(createCallTaskDto.getPersonType());
                tbCdcewPoisonOutcallNotice.setCreateTime(new Date());
                tbCdcewPoisonOutcallNotice.setUpdateTime(new Date());
                tbCdcewPoisonOutcallNotice.setStatDimId(userOrg.getId());
                tbCdcewPoisonOutcallNotice.setStatDimName(userOrg.getName());
                tbCdcewPoisonOutcallNotice.setName(uapUserPo.getName());
                tbCdcewPoisonOutcallNotice.setPhone(resident.getTelephone());
                tbCdcewPoisonOutcallNotices.add(tbCdcewPoisonOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewPoisonOutcallNotice tbCdcewPoisonOutcallNotice = new TbCdcewPoisonOutcallNotice();
                tbCdcewPoisonOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_poison_outcall_notice")));
                tbCdcewPoisonOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewPoisonOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewPoisonOutcallNotice.setPersonType(createCallTaskDto.getPersonType());
                tbCdcewPoisonOutcallNotice.setCreateTime(new Date());
                tbCdcewPoisonOutcallNotice.setUpdateTime(new Date());
                tbCdcewPoisonOutcallNotice.setStatDimId(person.getStatDimId());
                tbCdcewPoisonOutcallNotice.setStatDimName(person.getStatDimName());
                tbCdcewPoisonOutcallNotice.setName(person.getName());
                tbCdcewPoisonOutcallNotice.setPhone(person.getPhone());
                tbCdcewPoisonOutcallNotice.setPositionName(person.getPositionName());
                tbCdcewPoisonOutcallNotices.add(tbCdcewPoisonOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        }
        CallTask callTask = getPoisonNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewPoisonOutcallBatch tbCdcewPoisonOutcallBatch = new TbCdcewPoisonOutcallBatch();
            tbCdcewPoisonOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_poison_outcall_batch")));
            tbCdcewPoisonOutcallBatch.setEventId(createCallTaskDto.getEventId());
            tbCdcewPoisonOutcallBatch.setPersonType(createCallTaskDto.getPersonType().shortValue());
            tbCdcewPoisonOutcallBatch.setCreateTime(new Date());
            tbCdcewPoisonOutcallBatch.setUpdateTime(new Date());
            tbCdcewPoisonOutcallBatchMapper.insert(tbCdcewPoisonOutcallBatch);

            tbCdcewPoisonOutcallNotices.forEach(tbCdcewOutcallNotice -> {
                tbCdcewOutcallNotice.setBatchId(tbCdcewPoisonOutcallBatch.getId());
                tbCdcewOutcallNotice.setCallBatchId(batchId);
                tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcewPoisonOutcallNotices)) {
            saveDataToPoisonOucCallNotice(createCallTaskDto, tbCdcewPoisonOutcallNotices);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> createCallSpeechTaskForOutpatientBusiness(CreateCallTaskDto createCallTaskDto) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();

        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        List<Dweller> dwellerList = new ArrayList<>();
        List<TbCdcewOutpatientOutcallNotice> tbCdcewOutpatientOutcallNotices = new ArrayList<>();

        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewOutpatientOutcallNotice tbCdcewOutpatientOutcallNotice = new TbCdcewOutpatientOutcallNotice();
                tbCdcewOutpatientOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outpatient_outcall_notice")));
                tbCdcewOutpatientOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewOutpatientOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewOutpatientOutcallNotice.setPersonType(createCallTaskDto.getPersonType());
                tbCdcewOutpatientOutcallNotice.setCreateTime(new Date());
                tbCdcewOutpatientOutcallNotice.setUpdateTime(new Date());
                tbCdcewOutpatientOutcallNotice.setStatDimId(userOrg.getId());
                tbCdcewOutpatientOutcallNotice.setStatDimName(userOrg.getName());
                tbCdcewOutpatientOutcallNotice.setName(uapUserPo.getName());
                tbCdcewOutpatientOutcallNotice.setPhone(resident.getTelephone());
                tbCdcewOutpatientOutcallNotices.add(tbCdcewOutpatientOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewOutpatientOutcallNotice tbCdcewOutpatientOutcallNotice = new TbCdcewOutpatientOutcallNotice();
                tbCdcewOutpatientOutcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outpatient_outcall_notice")));
                tbCdcewOutpatientOutcallNotice.setEventId(createCallTaskDto.getEventId());
                tbCdcewOutpatientOutcallNotice.setPersonId(resident.getPersonId());
                tbCdcewOutpatientOutcallNotice.setPersonType(createCallTaskDto.getPersonType());
                tbCdcewOutpatientOutcallNotice.setCreateTime(new Date());
                tbCdcewOutpatientOutcallNotice.setUpdateTime(new Date());
                tbCdcewOutpatientOutcallNotice.setStatDimId(person.getStatDimId());
                tbCdcewOutpatientOutcallNotice.setStatDimName(person.getStatDimName());
                tbCdcewOutpatientOutcallNotice.setName(person.getName());
                tbCdcewOutpatientOutcallNotice.setPhone(person.getPhone());
                tbCdcewOutpatientOutcallNotice.setPositionName(person.getPositionName());
                tbCdcewOutpatientOutcallNotices.add(tbCdcewOutpatientOutcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        }
        CallTask callTask = getOutpatientNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewOutpatientOutcallBatch tbCdcewOutpatientOutcallBatch = new TbCdcewOutpatientOutcallBatch();
            tbCdcewOutpatientOutcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_outpatient_outcall_batch")));
            tbCdcewOutpatientOutcallBatch.setEventId(createCallTaskDto.getEventId());
            tbCdcewOutpatientOutcallBatch.setPersonType(createCallTaskDto.getPersonType().intValue());
            tbCdcewOutpatientOutcallBatch.setCreateTime(new Date());
            tbCdcewOutpatientOutcallBatch.setUpdateTime(new Date());
            tbCdcewOutpatientOutcallBatchMapper.insert(tbCdcewOutpatientOutcallBatch);

            tbCdcewOutpatientOutcallNotices.forEach(tbCdcewOutcallNotice -> {
                tbCdcewOutcallNotice.setBatchId(tbCdcewOutpatientOutcallBatch.getId());
                tbCdcewOutcallNotice.setCallBatchId(batchId);
                tbCdcewOutcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcewOutpatientOutcallNotices)) {
            saveDataToOutpatientOutCallNotice(createCallTaskDto, tbCdcewOutpatientOutcallNotices);
        }
        return response;
    }

    private CallTask getOutpatientNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
        String callContent = getOutpatientCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
            String appContent = getOutpatientCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    @Override
    public Response<CreateCallRs> createCallSpeechTaskForUnknownReasonBusiness(CreateCallTaskDto createCallTaskDto) {
        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();

        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        List<Dweller> dwellerList = new ArrayList<>();
        List<TbCdcewUnknownReasonOutcallNotice> outcallNotices = new ArrayList<>();

        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewUnknownReasonOutcallNotice outcallNotice = new TbCdcewUnknownReasonOutcallNotice();
                outcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_unknown_reason_outcall_notice")));
                outcallNotice.setEventId(createCallTaskDto.getEventId());
                outcallNotice.setPersonId(resident.getPersonId());
                outcallNotice.setPersonType(createCallTaskDto.getPersonType());
                outcallNotice.setCreateTime(new Date());
                outcallNotice.setUpdateTime(new Date());
                outcallNotice.setStatDimId(userOrg.getId());
                outcallNotice.setStatDimName(userOrg.getName());
                outcallNotice.setName(uapUserPo.getName());
                outcallNotice.setPhone(resident.getTelephone());
                outcallNotices.add(outcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewUnknownReasonOutcallNotice outcallNotice = new TbCdcewUnknownReasonOutcallNotice();
                outcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_unknown_reason_outcall_notice")));
                outcallNotice.setEventId(createCallTaskDto.getEventId());
                outcallNotice.setPersonId(resident.getPersonId());
                outcallNotice.setPersonType(createCallTaskDto.getPersonType());
                outcallNotice.setCreateTime(new Date());
                outcallNotice.setUpdateTime(new Date());
                outcallNotice.setStatDimId(person.getStatDimId());
                outcallNotice.setStatDimName(person.getStatDimName());
                outcallNotice.setName(person.getName());
                outcallNotice.setPhone(person.getPhone());
                outcallNotice.setPositionName(person.getPositionName());
                outcallNotices.add(outcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        }
        CallTask callTask = getUnknownReasonNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewUnknownReasonOutcallBatch outcallBatch = new TbCdcewUnknownReasonOutcallBatch();
            outcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_unknown_reason_outcall_batch")));
            outcallBatch.setEventId(createCallTaskDto.getEventId());
            outcallBatch.setPersonType(createCallTaskDto.getPersonType().intValue());
            outcallBatch.setCreateTime(new Date());
            outcallBatch.setUpdateTime(new Date());
            tbCdcewUnknownReasonOutcallBatchMapper.insert(outcallBatch);

            outcallNotices.forEach(outcallNotice -> {
                outcallNotice.setBatchId(outcallBatch.getId());
                outcallNotice.setCallBatchId(batchId);
                outcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(outcallNotices)) {
            saveDataToUnknownReasonOutCallNotice(createCallTaskDto, outcallNotices);
        }
        return response;
    }

    private CallTask getUnknownReasonNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.UNKNOWN_REASON.getId()));
        String callContent = getUnknownReasonCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.UNKNOWN_REASON.getId()));
            String appContent = getUnknownReasonCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    @Override
    public Response<CreateCallRs> createCallSpeechTaskForPreventionControlBusiness(CreateCallTaskDto createCallTaskDto) {

        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();

        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        List<Dweller> dwellerList = new ArrayList<>();
        List<TbCdcewPreventionControlOutcallNotice> outcallNotices = new ArrayList<>();

        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewPreventionControlOutcallNotice outcallNotice = new TbCdcewPreventionControlOutcallNotice();
                outcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_prevention_control_outcall_notice")));
                outcallNotice.setEventId(createCallTaskDto.getEventId());
                outcallNotice.setPersonId(resident.getPersonId());
                outcallNotice.setPersonType(createCallTaskDto.getPersonType());
                outcallNotice.setCreateTime(new Date());
                outcallNotice.setUpdateTime(new Date());
                outcallNotice.setStatDimId(userOrg.getId());
                outcallNotice.setStatDimName(userOrg.getName());
                outcallNotice.setName(uapUserPo.getName());
                outcallNotice.setPhone(resident.getTelephone());
                outcallNotices.add(outcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewPreventionControlOutcallNotice outcallNotice = new TbCdcewPreventionControlOutcallNotice();
                outcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_prevention_control_outcall_notice")));
                outcallNotice.setEventId(createCallTaskDto.getEventId());
                outcallNotice.setPersonId(resident.getPersonId());
                outcallNotice.setPersonType(createCallTaskDto.getPersonType());
                outcallNotice.setCreateTime(new Date());
                outcallNotice.setUpdateTime(new Date());
                outcallNotice.setStatDimId(person.getStatDimId());
                outcallNotice.setStatDimName(person.getStatDimName());
                outcallNotice.setName(person.getName());
                outcallNotice.setPhone(person.getPhone());
                outcallNotice.setPositionName(person.getPositionName());
                outcallNotices.add(outcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        }
        CallTask callTask = getPreventionControlNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewPreventionControlOutcallBatch outcallBatch = new TbCdcewPreventionControlOutcallBatch();
            outcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_prevention_control_outcall_batch")));
            outcallBatch.setEventId(createCallTaskDto.getEventId());
            outcallBatch.setPersonType(createCallTaskDto.getPersonType().intValue());
            outcallBatch.setCreateTime(new Date());
            outcallBatch.setUpdateTime(new Date());
            tbCdcewPreventionControlOutcallBatchMapper.insert(outcallBatch);

            outcallNotices.forEach(outcallNotice -> {
                outcallNotice.setBatchId(outcallBatch.getId());
                outcallNotice.setCallBatchId(batchId);
                outcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(outcallNotices)) {
            saveDataToPreventionControlOutCallNotice(createCallTaskDto, outcallNotices);
        }
        return response;
    }

    private CallTask getPreventionControlNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.PREVENTION_CONTROL.getId()));
        String callContent = getPreventionControlCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.PREVENTION_CONTROL.getId()));
            String appContent = getPreventionControlCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    private void saveDataToInfectedOucCallNotice(CreateCallTaskDto createCallTaskDto,
                                                 List<TbCdcewInfectedOutcallNotice> tbCdcewInfectedOutcallNotices) {
        List<TbCdcewInfectedOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewInfectedOutcallNotice> insertList = new ArrayList<>();

        List<TbCdcewInfectedOutcallNotice> tbCdcewInfectedOutcallNotices1 = tbCdcewInfectedOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType());
        Map<String, List<TbCdcewInfectedOutcallNotice>> valueMap = Optional.ofNullable(tbCdcewInfectedOutcallNotices1).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(TbCdcewInfectedOutcallNotice::getPersonId));
        tbCdcewInfectedOutcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<TbCdcewInfectedOutcallNotice> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                TbCdcewInfectedOutcallNotice businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewInfectedOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewInfectedOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private void saveDataToPoisonOucCallNotice(CreateCallTaskDto createCallTaskDto,
                                               List<TbCdcewPoisonOutcallNotice> tbCdcewPoisonOutcallNotices) {
        List<TbCdcewPoisonOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewPoisonOutcallNotice> insertList = new ArrayList<>();

        List<TbCdcewPoisonOutcallNotice> tbCdcewPoisonOutcallNotices1 = tbCdcewPoisonOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType());
        Map<String, List<TbCdcewPoisonOutcallNotice>> valueMap = Optional.ofNullable(tbCdcewPoisonOutcallNotices1).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(TbCdcewPoisonOutcallNotice::getPersonId));
        tbCdcewPoisonOutcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<TbCdcewPoisonOutcallNotice> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                TbCdcewPoisonOutcallNotice businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewPoisonOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewPoisonOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private void saveDataToOutpatientOutCallNotice(CreateCallTaskDto createCallTaskDto,
                                                   List<TbCdcewOutpatientOutcallNotice> tbCdcewOutpatientOutcallNotices) {
        List<TbCdcewOutpatientOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewOutpatientOutcallNotice> insertList = new ArrayList<>();

        List<TbCdcewOutpatientOutcallNotice> tbCdcewOutpatientOutcallNotices1 = tbCdcewOutpatientOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType());
        Map<String, List<TbCdcewOutpatientOutcallNotice>> valueMap = Optional.ofNullable(tbCdcewOutpatientOutcallNotices1).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(TbCdcewOutpatientOutcallNotice::getPersonId));
        tbCdcewOutpatientOutcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<TbCdcewOutpatientOutcallNotice> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                TbCdcewOutpatientOutcallNotice businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewOutpatientOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewOutpatientOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private void saveDataToUnknownReasonOutCallNotice(CreateCallTaskDto createCallTaskDto,
                                                   List<TbCdcewUnknownReasonOutcallNotice> outcallNotices) {
        List<TbCdcewUnknownReasonOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewUnknownReasonOutcallNotice> insertList = new ArrayList<>();

        List<TbCdcewUnknownReasonOutcallNotice> outcallNotices1 = tbcdcewUnknownReasonOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType());
        Map<String, List<TbCdcewUnknownReasonOutcallNotice>> valueMap = Optional.ofNullable(outcallNotices1).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(TbCdcewUnknownReasonOutcallNotice::getPersonId));
        outcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<TbCdcewUnknownReasonOutcallNotice> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                TbCdcewUnknownReasonOutcallNotice businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbcdcewUnknownReasonOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbcdcewUnknownReasonOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private void saveDataToPreventionControlOutCallNotice(CreateCallTaskDto createCallTaskDto,
                                                      List<TbCdcewPreventionControlOutcallNotice> outcallNotices) {
        List<TbCdcewPreventionControlOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewPreventionControlOutcallNotice> insertList = new ArrayList<>();

        List<TbCdcewPreventionControlOutcallNotice> outcallNotices1 = tbCdcewPreventionControlOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType());
        Map<String, List<TbCdcewPreventionControlOutcallNotice>> valueMap = Optional.ofNullable(outcallNotices1).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(TbCdcewPreventionControlOutcallNotice::getPersonId));
        outcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<TbCdcewPreventionControlOutcallNotice> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                TbCdcewPreventionControlOutcallNotice businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewPreventionControlOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewPreventionControlOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

    private CallTask getCallTask(CreateCallTaskDto createCallTaskDto,
                                 String orgName,
                                 String symptom,
                                 String date,
                                 boolean isChild) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.CALL_PERSON_2.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.CALL_PERSON_2.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("机构名称").content(createCallTaskDto.getOutCallOrgName()).build());
        vars.add(Var.builder().remarks("症状名称").content(symptom).build());
        vars.add(Var.builder().remarks("日期").content(date).build());
        vars.add(Var.builder().remarks("具体称谓").content(isChild ? "您的小孩" : "您").build());
        vars.add(Var.builder().remarks("卫生院名称").content(orgName).build());
        callTask.setVars(vars);
        return callTask;
    }

    /**
     * 从文件服务器获取人员对象列表
     *
     * @param createCallTaskDto
     * @return
     */
    private List<PersonVO> getPersonVOS(CreateCallTaskDto createCallTaskDto) {
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(createCallTaskDto.getAttachmentId());
        if (null == tbCdcAttachment) {
            log.info("附件没有没有外呼对象！");
            return new ArrayList<>();
        }
        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);
        List<PersonVO> personVOList = ExcelUtils.readExcel(PersonVO.class, tbCdcAttachment.getAttachmentName(), storageObject.getInputStream());
        return personVOList;
    }

    private List<PatientVO> getPatientVOS(CreateCallTaskDto createCallTaskDto) {
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(createCallTaskDto.getAttachmentId());
        if (null == tbCdcAttachment) {
            log.info("附件没有没有外呼对象！");
            return new ArrayList<>();
        }
        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);
        List<PatientVO> personVOList = ExcelUtils.readExcel(PatientVO.class, tbCdcAttachment.getAttachmentName(), storageObject.getInputStream());
        return personVOList;
    }

    /**
     * 创建单任务外呼
     *
     * @return
     */
    private Response<CreateCallRs> createDoctorOutCall(CallTask callTask, List<TbCdcHisMedicalExtendVo> tbCdcHisMedicalExtends) {
        Response<CreateCallRs> createCallRsResponse;
        try {
            createCallRsResponse = createOutboundService.execCallTask(callTask);
            //外呼任务创建成功
            if (createCallRsResponse != null
                    && null != createCallRsResponse.getData()
                    && cn.hutool.core.collection.CollectionUtil.isNotEmpty(createCallRsResponse.getData().getBatch())) {
                //获取外呼批次号
                String batchId = createCallRsResponse.getData().getBatch().get(0);
                if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(tbCdcHisMedicalExtends)) {
                    for (TbCdcHisMedicalExtendVo hisMedicalExtendVo : tbCdcHisMedicalExtends) {
                        TbCdcHisMedicalExtend cdcHisMedicalExtend = TbCdcHisMedicalExtend.builder()
                                .id(idWorker.nextId())
                                //呼叫医生
                                .patientName(hisMedicalExtendVo.getDoctorName())
                                .patientPhone(hisMedicalExtendVo.getPatientPhone())
                                .eventId(hisMedicalExtendVo.getEventId())
                                .detailId(hisMedicalExtendVo.getDetailId())
                                .sourceKey(hisMedicalExtendVo.getSourceKey())
                                .callBatchId(batchId)
                                .callStatus(MedicalCallStatusEnum.CALLING.getCode())
                                .confirmedResult(hisMedicalExtendVo.getConfirmedResult())
                                .connectStatus(hisMedicalExtendVo.getConnectStatus())
                                .createTime(new Date()).build();
                        cdcHisMedicalExtendMapper.updateCallStatusAndCallResultByParameters(cdcHisMedicalExtend);
                    }
                }
                log.info("创建外呼 {} 成功！", createCallRsResponse.getData().getBatch());
            }
        } catch (Exception e) {
            log.error("调用外呼任务创建接口失败", e);
            throw new MedicalBusinessException("11451102", "调用外呼任务创建接口失败");
        }
        return createCallRsResponse;
    }

    @Override
    public List<ChartDataVo> getOutCallResult(String eventId, String dataSourceTypeCode) {
        List<CdcCallChartInfo> chartInfos = cdcCallChartInfoMapper.findByEventId(eventId);
        List<ChartDataVo> chartDataVos = new ArrayList<>();
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(chartInfos)) {
            //将查询出的图表数据根据图表名称分组
            Map<String, List<CdcCallChartInfo>> chartInfoByBatchId = chartInfos.stream().collect(Collectors.groupingBy
                    (CdcCallChartInfo::getChartName));
            for (Map.Entry<String, List<CdcCallChartInfo>> entry : chartInfoByBatchId.entrySet()) {
                List<CdcCallChartInfo> callChartInfo = entry.getValue();
                //汇总相同图表的数据
                ChartDataVo chartDataVo = new ChartDataVo();
                CdcCallChartInfo cdcCallChartInfo = callChartInfo.get(0);
                chartDataVo.setChartName(cdcCallChartInfo.getChartName());
                chartDataVo.setChartType(cdcCallChartInfo.getChartType());
                chartDataVo.setCallCount(cdcCallChartInfo.getCallCount());
                chartDataVo.setConnectCount(cdcCallChartInfo.getConnectCount());

                List<Serie> series = new ArrayList<>();
                Serie serie = new Serie();
                //使用LinkedHashMap确保坐标值的顺序
                LinkedHashMap<String, Integer> chartInfoMap = new LinkedHashMap<>();
                callChartInfo.forEach(chartInfo -> {
                    //开始汇总数据
                    List<String> xs = Arrays.asList(chartInfo.getX().split(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER));
                    List<String> datas = Arrays.asList(chartInfo.getData().split(CommonConstants.PARSING_VERTICAL_LINE_DELIMITER));
                    //转换为数值类型
                    List<Integer> dataInt = datas.stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
                    for (int i = 0; i < xs.size(); i++) {
                        chartInfoMap.merge(xs.get(i), dataInt.get(i), Integer::sum);
                    }
                });
                List<String> x = new ArrayList<>(chartInfoMap.keySet());
                List<Integer> data = new ArrayList<>(chartInfoMap.values());
                chartDataVo.setX(x);
                serie.setData(data);
                series.add(serie);
                chartDataVo.setSeries(series);
                chartDataVos.add(chartDataVo);
            }
        } else {
            log.info("该事件下没有外呼结果数据，或者该事件没有进行过外呼操作，事件id：{}", eventId);
            throw new MedicalBusinessException("11451103", "该事件下没有外呼结果数据，或者该事件没有进行过外呼操作");
        }
        return chartDataVos;
    }


    @Override
    public ResponseEntity<byte[]> exportInfectedCallResultExcel(String eventId) {
        List<InfectedPatientVO> infectedPatientVOList = cdcReportCardService.getInfectedPatientsByEventId(eventId, null, null, null, null);
        List<InfectedCallResultExcelVo> infectedCallResultExcelVoList = infectedPatientVOList.stream().map(InfectedCallResultExcelVo::fromEntity).collect(Collectors.toList());
        //校验是否超出文件导出最大值
        restService.checkExportMax(infectedCallResultExcelVoList);

        return FileUtils.exportExcel(infectedCallResultExcelVoList, InfectedCallResultExcelVo.class);
    }

    @Override
    public ResponseEntity<byte[]> exportCallResultExcelByAttentionId(String loginUserId, String attentionId, String dataSourceTypeCode) {
        AttentionCondition ac = attentionConditionMapper.findByCreatorAndId(loginUserId, attentionId);
        List<String> cityCodes = CollectionUtil.getSplitList(ac.getCityCodeList());
        List<String> districtCodes = CollectionUtil.getSplitList(ac.getDistrictCodeList());
        List<String> symptoms = CollectionUtil.getSplitList(ac.getSymptoms());

        symptoms = cdcEventService.getSymptomsFromSyndromes(symptoms);

        //获取对应的事件
        EventCriteria eventCriteria = EventCriteria.builder().cityCode(cityCodes).districtCode(districtCodes)
                .symptomTypeCollection(symptoms)
                .processingStatusCollection(ProcessingStatus.ALL_STATUS)
                .minBeginTime(ac.getBeginDate())
                .maxBeginTime(ac.getEndDate())
                .sortByAiScreen(false)
                .sortType(SortType.EVENT_START_TIME_ASC).build();
        List<CdcWarningEvent> events = cdcWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());

        List<CallResultExcelVo> callResultExcelVos = new ArrayList<>();
        try {
            List<String> eventIdList = events.stream().map(CdcWarningEvent::getId).collect(Collectors.toList());
            if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(eventIdList)) {
                callResultExcelVos = cdcCallResultDetailMapper.findByEventIdsIn(eventIdList);
            }
        } catch (Exception e) {
            log.error("根据事件号查询明细数据错误");
            throw new MedicalBusinessException("11451104", "根据事件号查询明细数据错误:");
        }
        //校验是否超出文件导出最大值
        restService.checkExportMax(callResultExcelVos);

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(callResultExcelVos)) {
            //添加监测症状和其他症状数据
            callResultExcelVos.forEach(callResultExcelVo -> {
                callResultExcelVo.setMusclePain();
                callResultExcelVo.setRash();
            });
        }

        return FileUtils.exportExcel(callResultExcelVos, CallResultExcelVo.class);
    }

    @Override
    public ResponseEntity<byte[]> exportInfectedCallResultExcelByAttentionId(String loginUserId, String attentionId) {
        AttentionCondition ac = attentionConditionMapper.findByCreatorAndId(loginUserId, attentionId);
        List<String> cityCodes = CollectionUtil.getSplitList(ac.getCityCodeList());
        List<String> districtCodes = CollectionUtil.getSplitList(ac.getDistrictCodeList());
        List<String> symptoms = CollectionUtil.getSplitList(ac.getSymptoms());

        Collection<String> codeList = new HashSet<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(symptoms)) {
            codeList = getInfectedCodeList(symptoms);
        }

        //获取对应的事件
        EventCriteria eventCriteria = EventCriteria.builder().cityCode(cityCodes).districtCode(districtCodes)
                .symptomTypeCollection(codeList)
                .processingStatusCollection(ProcessingStatus.ALL_STATUS)
                .minBeginTime(ac.getBeginDate())
                .maxBeginTime(ac.getEndDate())
                .sortByAiScreen(false)
                .sortType(SortType.EVENT_START_TIME_ASC).build();
        List<TbCdcInfectedWarningEvent> events = tbCdcInfectedWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());

        List<CallResultExcelVo> callResultExcelVos = null;
//        try {
        List<String> eventIdList = events.stream().map(TbCdcInfectedWarningEvent::getId).collect(Collectors.toList());
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(eventIdList)) {
            callResultExcelVos = cdcCallResultDetailMapper.findByEventIdsInInfected(eventIdList);
        }
//        } catch (Exception e) {
//            log.error("根据事件号查询明细数据错误");
//            throw new MedicalBusinessException("根据事件号查询明细数据错误:");
//        }

        //校验是否超出文件导出最大值
        restService.checkExportMax(callResultExcelVos);
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(callResultExcelVos)) {
            //添加监测症状和其他症状数据
            callResultExcelVos.forEach(callResultExcelVo -> {
                callResultExcelVo.setMusclePain();
                callResultExcelVo.setRash();
            });
        }

        return FileUtils.exportExcel(callResultExcelVos, CallResultExcelVo.class);
    }


    @Override
    public SymptomTypeEnum getSymptomTypeEnum(String symptomType) {
        //统一使用一般话术
        return SymptomTypeEnum.NORMAL;
    }

    @Override
    public Response<CreateCallRs> sendSyndromeSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {
        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();

        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getSyndromeAIScreenPhone(org.getId());
            CdcWarningEvent event = cdcWarningEventMapper.selectByPrimaryKey(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
            final String until = sf.format(smsVo.getUntil());

//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(until);
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(until,smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.SYNDROME.getName());

            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);

            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);
            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.SYMPTOM_DETAIL+"?id="+event.getId() ,
                    htmlTxt,targetIds,ConfigCategoryEnum.SYNDROME.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    /**
     *  处理 短信与协同平台 发送人信息
     * @param adminPhone
     * @param dwellers
     * @param loginName
     * @param targetIds
     */
    private void dealSmsRceUser(String[] adminPhone, List<Dweller> dwellers, String loginName, List<String> targetIds) {
        for (int i = 0; i < adminPhone.length; i++) {
            //短信详情不会显示名字，所有短信传入当前登录名字
            dwellers.add(Dweller.builder().dwellerName(loginName).telephone(adminPhone[i]).build());
            final String userIdByPhone = restService.getUapUserIdByPhone(adminPhone[i]);
            if (StringUtils.isNotBlank(userIdByPhone)){
                targetIds.add(userIdByPhone);
            }
        }
    }

    @Override
    public Response<CreateCallRs> sendUnknownReasonSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {
        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getUnknownReasonAIScreenPhone(org.getId());
            TbCdcewUnknownReasonWarningEvent event = tbCdcewUnknownReasonWarningEventMapper.findById(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);

//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.UNKNOWN_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.UNKNOWN_REASON.getName());
            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);


            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);

            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.UNKNOWN_DETAIL+"?id="+event.getId() , htmlTxt,targetIds,
                    ConfigCategoryEnum.UNKNOWN_REASON.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendInfectedSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {
        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getInfectedAIScreenPhone(org.getId());
            TbCdcInfectedWarningEvent event = tbCdcInfectedWarningEventMapper.selectByPrimaryKey(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);

//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
//            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML,htmlValList.toArray());

            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.INFECTIOUS.getName());

            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);
            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);

            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.INFECTIOUS_DETAIL+"?id="+event.getId() , htmlTxt,targetIds,
                    ConfigCategoryEnum.INFECTED.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendScSymptomSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {
        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getScSymptomAIScreenPhone(org.getId());
            TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);

//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.SC_SYMPTOM.getName());
            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);


            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);

            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.SYMPTOM_MONITOR_DETAIL+"?id="+event.getId() , htmlTxt,
                    targetIds,ConfigCategoryEnum.SYMPTOM.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendPoisonSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {
        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getPoisonAIScreenPhone(org.getId());
            TbCdcewPoisonWarningEvent event = tbCdcewPoisonWarningEventMapper.findByEventId(smsVo.getEventId());
            String poisonType = event.getPoisonCode().substring(0, 2);

            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);

//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.POISON.getName());
            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);

            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);
            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.POISONING_DETAIL+"?id="+event.getId()+"&eventType="+ poisonType,
                    htmlTxt,targetIds,ConfigCategoryEnum.POISON.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendOutpatientSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {
        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getOutpatientAIScreenPhone(org.getId());
            TbCdcewOutpatientWarningEvent event = tbCdcewOutpatientWarningEventMapper.findByEventId(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.OUTPATIENT.getName());
            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);

            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);
            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);
            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.OUTPATIENT_DETAIL+"?id="+event.getId() , htmlTxt,
                    targetIds,ConfigCategoryEnum.OUTPATIENT.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendPreventionControlSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {

        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getPreventionControlAIScreenPhone(org.getId());
            TbCdcewPreventionControlWarningEvent event = tbCdcewPreventionControlWarningEventMapper.selectByPrimaryKey(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);

//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
//            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.PREVENTION_CONTROL_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.PREVENTION_CONTROL.getName());
            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);

            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);
            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.JOINT_CONTROL_DETAIL+"?id="+event.getId() , htmlTxt,targetIds,
                    ConfigCategoryEnum.PREVENTION_CONTROL.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendCustomizedSmsForOnTop(String loginUserId, String loginUserName, SmsVo smsVo) {

        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(1);
        smsTask.setPlanName("预警平台-短信");
        boolean rcsSwitch = restService.getRceSwitch();
        Response<CreateCallRs> response = null;
        try {
            List<Dweller> dwellers = new ArrayList<>();
            String loginName = restService.getUser(loginUserId).getName();
            UapOrgPo org = restService.getUserOrg(loginUserName);
            String[] adminPhone = restService.getCustomizedAIScreenPhone(org.getId());
            TbCdcewCustomizedWarningEvent event = tbCdcewCustomizedWarningEventMapper.selectByPrimaryKey(smsVo.getEventId());
            List<String> targetIds = new ArrayList<>();
            //处理 短信与协同平台 发送人信息
            dealSmsRceUser(adminPhone, dwellers, loginName, targetIds);
            smsTask.setDwellers(dwellers);

            SimpleDateFormat sf = new SimpleDateFormat(TimeConstant.NORM_DATE_PATTERN);
//            List<String> htmlValList = new ArrayList<>();
//            htmlValList.add(sf.format(smsVo.getUntil()));
//            htmlValList.add(smsVo.getHospitalName());
//            htmlValList.add(smsVo.getSyndromeName());
//            htmlValList.add(event.getEventNum());
//            htmlValList.add(String.valueOf(smsVo.getToday()));
            //拼接协同平台消息体
//            String htmlTxt = MessageFormat.format(SmsRceConstant.SYNDROME_SMS_ON_TOP_HTML,htmlValList.toArray());
            String htmlTxt = callTemplateService.buildCommonTopContext(sf.format(smsVo.getUntil()),smsVo.getHospitalName(),smsVo.getSyndromeName(),
                    event.getEventNum(),String.valueOf(smsVo.getToday()),WarningTypeCodeEnum.CUSTOMIZED.getName());
            //拼接短信消息体
            String smsContent = com.iflytek.fpva.cdc.util.StringUtils.formatSms(htmlTxt);

            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(smsContent).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);
            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行病例核实-短信通知成功: {}", response);

            //发送协同平台消息
            rceMsgRecordService.sendUrlMsg(SmsRceConstant.CUSTOM_DETAIL+"?id="+event.getId() , htmlTxt,targetIds,
                    ConfigCategoryEnum.CUSTOMIZED.getType(),event.getId(),null,SmsRceConstant.ONCE_SEND,rcsSwitch);

        } catch (Exception e) {
            log.error("执行病例核实-短信通知失败！", e);
        }
        return response;
    }

    @Override
    public Response<CreateCallRs> sendSms(CreateSmsVO createSmsVO) {
        Response<CreateCallRs> response = null;

        SmsTask smsTask = new SmsTask();
        smsTask.setSmsId(smsId);
        smsTask.setType(Integer.valueOf(createSmsVO.getType()));
        smsTask.setPlanName("预警平台-短信");
        smsTask.setDate(createSmsVO.getDate());

        //1，获取列表选中人员
        List<Resident> residents = createSmsVO.getResidents();
        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }
        try {
            //2，获取excel上传人员
            //2，从文件读取数据
            List<PersonVO> personVOList = new ArrayList<>();
            if (StringUtils.isNotBlank(createSmsVO.getAttachmentId())) {
                TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(createSmsVO.getAttachmentId());
                String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
                StorageObject storageObject = storageClientUtil.getObject(objectName);
                personVOList = ExcelUtils.readExcel(PersonVO.class, tbCdcAttachment.getAttachmentName(), storageObject.getInputStream());
            }

            List<Dweller> dwellers = new ArrayList<>();
            personVOList.forEach(item -> {
                dwellers.add(Dweller.builder().dwellerName(item.getName()).telephone(item.getPhone()).build());
            });

            residents.forEach(resident -> {
                dwellers.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).build());
            });
            smsTask.setDwellers(dwellers);

            String content = getSmsContent(createSmsVO.getEventId(), createSmsVO.getSmsContent(), createSmsVO.getSmsTemplateId());
            List<Var> vars = new ArrayList<>();
            vars.add(Var.builder().remarks("content").content(content).build());
            vars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(vars);
            response = createOutboundService.execSmsTask(smsTask);
            log.info("执行短信通知成功: {}", response);
        } catch (Exception e) {
            log.error("短信通知失败！", e);
        }
        return response;
    }

    /**
     * 获取短信内容：如果前端没有传入内容，从模板里获取默认内容
     *
     * @param eventId
     * @param content
     * @return
     */
    private String getSmsContent(String eventId, String content, String smsTemplateId) {
        if (StringUtils.isBlank(content)) {
            TbCdcCallTemplate tbCdcCallTemplate = tbCdcCallTemplateMapper.selectByPrimaryKey(smsTemplateId);
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                CdcWarningEvent cdcWarningEvent = cdcEventService.getCdcWaringEventByEventId(eventId);
                if (Objects.nonNull(cdcWarningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), cdcWarningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), cdcWarningEvent.getSyndromeName());
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    /**
     * 获取外呼内容：如果前端没有传入内容，从模板里获取默认内容
     *
     * @param eventId
     * @param content
     * @return
     */
    @Override
    public String getCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                CdcWarningEvent cdcWarningEvent = cdcEventService.getCdcWaringEventByEventId(eventId);
                List<MedicalInfoVO> medsByEventIds = hisMedicalInfoMapper.findRecordsByEventIds(Arrays.asList(eventId));
                if (Objects.nonNull(cdcWarningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), cdcWarningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), cdcWarningEvent.getSyndromeName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(cdcWarningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), cdcWarningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), medsByEventIds.size());
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    public String getInfectedCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                TbCdcInfectedWarningEvent infectedWarningEvent = tbCdcInfectedWarningEventMapper.selectByPrimaryKey(eventId);
                Integer count = tbCdcewInfectedMedRelationMapper.countByEventId(eventId);
                if (Objects.nonNull(infectedWarningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), infectedWarningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), infectedWarningEvent.getInfectedName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(infectedWarningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), infectedWarningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    public String getPoisonCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                TbCdcewPoisonWarningEvent poisonWarningEvent = tbCdcewPoisonWarningEventMapper.selectByPrimaryKey(eventId);
                Integer count = tbCdcewPoisonMedRelationMapper.countByEventId(eventId);
                if (Objects.nonNull(poisonWarningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), poisonWarningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), poisonWarningEvent.getPoisonName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(poisonWarningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), poisonWarningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    public String getOutpatientCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                TbCdcewOutpatientWarningEvent outpatientWarningEvent = tbCdcewOutpatientWarningEventMapper.selectByPrimaryKey(eventId);
                Integer count = tbCdcewOutpatientMedRelationMapper.countByEventId(eventId);
                if (Objects.nonNull(outpatientWarningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), outpatientWarningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), outpatientWarningEvent.getOutpatientTypeName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(outpatientWarningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), outpatientWarningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    public String getUnknownReasonCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                TbCdcewUnknownReasonWarningEvent warningEvent = tbCdcewUnknownReasonWarningEventMapper.selectByPrimaryKey(eventId);
                Integer count = tbcdcewUnknownReasonMedRelationMapper.countByEventId(eventId);
                if (Objects.nonNull(warningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), warningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), warningEvent.getDiseaseName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(warningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), warningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    public String getPreventionControlCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                TbCdcewPreventionControlWarningEvent warningEvent = tbCdcewPreventionControlWarningEventMapper.selectByPrimaryKey(eventId);
                Integer count = tbCdcewPreventionControlMedRelationMapper.countByEventId(eventId);
                if (Objects.nonNull(warningEvent)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), warningEvent.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), warningEvent.getPreventionControlName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(warningEvent.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), warningEvent.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    @Async
    public void medicalConfirm() {
        long uid = batchUidService.getUid("delay_task");
        DelayedTask delayedTask = new DelayedTask(String.valueOf(uid), ParamConfig.OUT_CALL_DELAY_TIME);

        delayedTasks.add(delayedTask);
        try {
            log.info("创建延迟任务{}成功！", delayedTask.getTaskName());
            DelayedTask task = delayedTasks.take();
            List<Response<CreateCallRs>> response = new ArrayList<>();

            List<TbCdcHisMedicalExtendVo> tbCdcHisMedicalExtendList = cdcHisMedicalExtendMapper.findByZyzlStatusAndZyzlBatchIdIsNotNull(BooleanEnum.TRUE.getIntVal(),WarningTypeCodeEnum.SYNDROME.getName());
            List<TbCdcHisMedicalExtendVo> outCallList = tbCdcHisMedicalExtendList.stream().filter(tbCdcHisMedicalExtend -> StringUtils.isNotBlank(tbCdcHisMedicalExtend.getDoctorName())).collect(Collectors.toList());

            //如果没有外呼对象，不继续外呼任务
            if (cn.hutool.core.collection.CollectionUtil.isEmpty(outCallList)) {
                log.info("没有外呼对象！");
                throw new MedicalBusinessException("11451105", "没有外呼对象!");
            }

            // 医生姓名存在的话  根据  机构id+医生姓名  去  tb_cdcew_diagnose_person中找到对应医生的电话
            DiagnosisDoctorQueryVO queryVO = new DiagnosisDoctorQueryVO();
            queryVO.setStatus(CommonConstants.STATUS_VALID);

            List<String> statDimIdList = outCallList.stream().map(TbCdcHisMedicalExtendVo::getStatDimId).collect(Collectors.toList());
            queryVO.setStatDimIdList(statDimIdList);

            List<TbCdcewDiagnosisDoctor> diagnosisDoctorList = diagnosisDoctorMapper.listByQueryVO(queryVO);
            outCallList.forEach(tbCdcHisMedicalExtendVo -> {
                String statDimId = tbCdcHisMedicalExtendVo.getStatDimId();
                String doctorName = tbCdcHisMedicalExtendVo.getDoctorName();

                List<TbCdcewDiagnosisDoctor> diagnosisDoctors = diagnosisDoctorList.stream().filter(tbCdcewDiagnosisDoctor -> statDimId.equals(tbCdcewDiagnosisDoctor.getStatDimId()) &&
                        doctorName.equals(tbCdcewDiagnosisDoctor.getName())).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(diagnosisDoctors)) {
                    TbCdcewDiagnosisDoctor tbCdcewDiagnosisDoctor = diagnosisDoctors.get(0);
                    tbCdcHisMedicalExtendVo.setDoctorPhone(tbCdcewDiagnosisDoctor.getPhone());
                }
            });

            // 过滤出有手机号的外呼医生列表
            outCallList = outCallList.stream().filter(tbCdcHisMedicalExtendVo -> StringUtils.isNotBlank(tbCdcHisMedicalExtendVo.getDoctorPhone())).collect(Collectors.toList());

            //外呼对象没有电话号码，不继续外呼任务
            if (cn.hutool.core.collection.CollectionUtil.isEmpty(outCallList)) {
                log.info("没有外呼对象！");
                throw new MedicalBusinessException("11451106", "外呼对象没有手机号!");
            }

            Map<String, List<TbCdcHisMedicalExtendVo>> outCallMap = outCallList.stream().collect(Collectors.groupingBy(
                    TbCdcHisMedicalExtendVo::getDoctorPhone
            ));

            outCallMap.forEach((doctorPhone, tbCdcHisMedicalExtends) -> {
                //姓名清洗
                DoctorVO doctorVO = new DoctorVO();
                doctorVO.setName(ValidateUtils.cleanName(tbCdcHisMedicalExtends.get(0).getDoctorName()));
                doctorVO.setId(tbCdcHisMedicalExtends.get(0).getDoctorId());
                doctorVO.setPhone(doctorPhone);
                CallTask doctorCallTask = getDoctorCallTask(doctorVO, tbCdcHisMedicalExtends);
                response.add(createDoctorOutCall(doctorCallTask, tbCdcHisMedicalExtends));
            });

            log.info("执行延迟任务{}成功！", task.getTaskName() + " - " + response.size());
        } catch (InterruptedException e) {
            log.error("延迟任务执行失败！", e);
            Thread.currentThread().interrupt();
        }
    }

    private CallTask getDoctorCallTask(DoctorVO doctorVO, List<TbCdcHisMedicalExtendVo> tbCdcHisMedicalExtends) {
        SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
        TbCdcHisMedicalExtendVo tbCdcHisMedicalExtendVo = tbCdcHisMedicalExtends.get(0);
        String date = sp.format(Optional.ofNullable(tbCdcHisMedicalExtendVo.getCreateTime()).orElse(new Date()));

        Set<String> callSystems = new HashSet<>(5);
        Map<Integer, List<TbCdcHisMedicalExtendVo>> medicalExtendMap = tbCdcHisMedicalExtends.stream().collect(Collectors.groupingBy(TbCdcHisMedicalExtendVo::getDataSourceTypeCode));
        medicalExtendMap.forEach((dataSourceTypeCode, extendsVo) -> {
            if (DataSourceTypeEnum.WISDOM_MEDICAL_ASSISTANT.getValue().equals(dataSourceTypeCode)) {
                callSystems.add("智医助理");
            } else {
                callSystems.add("传染病智能监测系统");
            }
        });

        String callSystem = StringUtils.join(callSystems, "/");
//        String content = "您好！您在" + date + "接诊了" + tbCdcHisMedicalExtends.size() + "名患者，初步判定具有传染病聚集性风险！请在" + callSystem + "完成病例信息反馈！";
        String content = callTemplateService.buildMedicalConfirmContent(date,tbCdcHisMedicalExtends.size(),callSystem);

        CallTask callTask = new CallTask();
        callTask.setType(1);
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());

        List<Dweller> dwellerList = new ArrayList<>();
        dwellerList.add(Dweller.builder().dwellerName(doctorVO.getName()).telephone(doctorVO.getPhone()).build());
        callTask.setDwellers(dwellerList);

        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(content).build());
        vars.add(Var.builder().remarks("外呼机构").content(appName).build());
        callTask.setVars(vars);

        Sms sms = new Sms();
        sms.setSmsId(smsId);
        sms.setSmsCondition(3);
        List<Var> smsVars = new ArrayList<>();
        smsVars.add(Var.builder().remarks("content").content(content).build());
        smsVars.add(Var.builder().remarks("center").content(appName).build());
        sms.setSmsVars(smsVars);
        callTask.setSms(sms);

        return callTask;
    }



    @Override
    public OutCallResultVO getCheckResult(String loginUserId, String eventId) {
        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        OutCallResultVO resultVO = new OutCallResultVO();
        List<OutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<OutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = outcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            OutCallResultVO.BusinessPersonBatchVO batchVO = new OutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(OutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            OutCallResultVO.BusinessPersonBatchVO batchVO = new OutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(OutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        // 关联人员结果
        List<OutCallResultVO.PatientRelationBatchVO> patientRelationBatchList = getPatientRelationList(eventId);
        if (patientRelationBatchList != null) {
            patientRelationBatchList.sort(
                    Comparator.comparing(OutCallResultVO.PatientRelationBatchVO::getOutCallTime).reversed());
        }

        resultVO.setPatientRelationBatchList(patientRelationBatchList);

        if (desensitization) {
            DesensitizeVOUtils.desensitizeOutCallResultVO(resultVO);
        }

        return resultVO;
    }

    @Override
    public InfectedOutCallResultVO getInfectedCheckResult(String loginUserId, String eventId) {
        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        InfectedOutCallResultVO resultVO = new InfectedOutCallResultVO();
        List<InfectedOutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<InfectedOutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbCdcewInfectedOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            InfectedOutCallResultVO.BusinessPersonBatchVO batchVO = new InfectedOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(InfectedOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            InfectedOutCallResultVO.BusinessPersonBatchVO batchVO = new InfectedOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(InfectedOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        if (desensitization) {
            DesensitizeVOUtils.desensitizeInfectedOutCallResultVO(resultVO);
        }

        return resultVO;
    }

    @Override
    public PoisonOutCallResultVO getPoisonCheckResult(String loginUserId, String eventId) {
        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        PoisonOutCallResultVO resultVO = new PoisonOutCallResultVO();
        List<PoisonOutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<PoisonOutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbCdcewPoisonOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            PoisonOutCallResultVO.BusinessPersonBatchVO batchVO = new PoisonOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(PoisonOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            PoisonOutCallResultVO.BusinessPersonBatchVO batchVO = new PoisonOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(PoisonOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        if (desensitization) {
            DesensitizeVOUtils.desensitizePoisonOutCallResultVO(resultVO);
        }
        return resultVO;
    }

    @Override
    public OutpatientOutCallResultVO getOutpatientCheckResult(String loginUserId, String eventId) {
        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        OutpatientOutCallResultVO resultVO = new OutpatientOutCallResultVO();
        List<OutpatientOutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<OutpatientOutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbCdcewOutpatientOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            OutpatientOutCallResultVO.BusinessPersonBatchVO batchVO = new OutpatientOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(OutpatientOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            OutpatientOutCallResultVO.BusinessPersonBatchVO batchVO = new OutpatientOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(OutpatientOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        if (desensitization) {
            DesensitizeVOUtils.desensitizeOutpatientOutCallResultVO(resultVO);
        }
        return resultVO;
    }

    @Override
    public UnknownReasonOutCallResultVO getUnknownReasonCheckResult(String loginUserId, String eventId) {
        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        UnknownReasonOutCallResultVO resultVO = new UnknownReasonOutCallResultVO();
        List<UnknownReasonOutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<UnknownReasonOutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbcdcewUnknownReasonOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            UnknownReasonOutCallResultVO.BusinessPersonBatchVO batchVO = new UnknownReasonOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(UnknownReasonOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            UnknownReasonOutCallResultVO.BusinessPersonBatchVO batchVO = new UnknownReasonOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(UnknownReasonOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        if (desensitization) {
            DesensitizeVOUtils.desensitizeUnknownReasonOutCallResultVO(resultVO);
        }
        return resultVO;
    }

    @Override
    public PreventionControlOutCallResultVO getPreventionControlCheckResult(String loginUserId, String eventId) {

        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        PreventionControlOutCallResultVO resultVO = new PreventionControlOutCallResultVO();
        List<PreventionControlOutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<PreventionControlOutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbCdcewPreventionControlOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            PreventionControlOutCallResultVO.BusinessPersonBatchVO batchVO = new PreventionControlOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(PreventionControlOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            PreventionControlOutCallResultVO.BusinessPersonBatchVO batchVO = new PreventionControlOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(PreventionControlOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        if (desensitization) {
            DesensitizeVOUtils.desensitizePreventionControlOutCallResultVO(resultVO);
        }
        return resultVO;
    }

    @Override
    public CustomizedOutCallResultVO getCustomizedCheckResult(String loginUserId, String eventId) {

        boolean desensitization = tbCdcConfigService.isDesensitization(loginUserId);

        CustomizedOutCallResultVO resultVO = new CustomizedOutCallResultVO();
        List<CustomizedOutCallResultVO.BusinessPersonBatchVO> batchList1 = new ArrayList<>();
        List<CustomizedOutCallResultVO.BusinessPersonBatchVO> batchList2 = new ArrayList<>();

        List<BusinessPersonOutCallDTO> dtoList = tbCdcewCustomizedOutcallNoticeMapper.listByEventId(eventId);
        Map<Integer, List<BusinessPersonOutCallDTO>> dtoMap = dtoList.stream().collect(Collectors.groupingBy(e -> e.getPersonType()));

        List<BusinessPersonOutCallDTO> businessPersonResultList1 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_3, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap1 = businessPersonResultList1.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap1.forEach((orgName, tempList) -> {
            CustomizedOutCallResultVO.BusinessPersonBatchVO batchVO = new CustomizedOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(CustomizedOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList1.add(batchVO);
        });

        List<BusinessPersonOutCallDTO> businessPersonResultList2 = dtoMap.computeIfAbsent(CommonConstants.OUT_CALL_TYPE_4, e -> new ArrayList<>());
        Map<String, List<BusinessPersonOutCallDTO>> orgResultMap2 = businessPersonResultList2.stream().collect(Collectors.groupingBy(e -> e.getOrgName()));
        orgResultMap2.forEach((orgName, tempList) -> {
            CustomizedOutCallResultVO.BusinessPersonBatchVO batchVO = new CustomizedOutCallResultVO.BusinessPersonBatchVO();
            batchVO.setOrgName(orgName);
            batchVO.setList(getResultList(tempList).stream().map(CustomizedOutCallResultVO.BusinessPersonResultVO::fromDTO).collect(Collectors.toList()));
            batchList2.add(batchVO);
        });

        resultVO.setBatchList1(batchList1);
        resultVO.setBatchList2(batchList2);

        if (desensitization) {
            DesensitizeVOUtils.desensitizeCustomizedOutCallResultVO(resultVO);
        }
        return resultVO;
    }

    private List<OutCallResultVO.PatientRelationBatchVO> getPatientRelationList(String eventId) {
        List<TbCdcEventPersonRelation> personRelationList = tbCdcEventPersonRelationMapper.listByEventId(eventId);
        Map<String, List<TbCdcEventPersonRelation>> personRelationMap = personRelationList.stream()
                .collect(Collectors.groupingBy(e -> e.getBatchId()));

        List<TbCdcewOutcallBatch> batchList = outcallBatchMapper.listByEventId(eventId);
        Map<String, TbCdcewOutcallBatch> batchIdMap = batchList.stream()
                .collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k1));

        List<OutCallResultVO.PatientRelationBatchVO> patientRelationBatchList = new ArrayList<>();
        personRelationMap.forEach((batchId, tempList) -> {
            TbCdcewOutcallBatch batch = batchIdMap.computeIfAbsent(batchId, e -> new TbCdcewOutcallBatch());
            OutCallResultVO.PatientRelationBatchVO batchVO = new OutCallResultVO.PatientRelationBatchVO();
            List<OutCallResultVO.PatientRelationResultVO> tempResultList = new ArrayList<>();
            tempList.forEach(personRelation -> {
                OutCallResultVO.PatientRelationResultVO tempResultVO = new OutCallResultVO.PatientRelationResultVO();
                tempResultVO.setPatientName(personRelation.getPatientName());
                tempResultVO.setRelationName(personRelation.getPersonName());
                tempResultVO.setRelationType(personRelation.getRelationType());
                tempResultVO.setIsSick(personRelation.getIsSick());
                tempResultVO.setRelationPhone(personRelation.getPersonPhone());
                tempResultVO.setAudioUrl(personRelation.getAudioUrl());
                tempResultList.add(tempResultVO);
            });
            batchVO.setOutCallTime(batch.getCreateTime());
            batchVO.setResultList(tempResultList);
            patientRelationBatchList.add(batchVO);
        });
        return patientRelationBatchList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Scheduled(fixedRate = 5 * 60 * 1000)
    @Override
    public Map<String, Map<String, Integer>> syncOutboundResult() {
        Map<String, Map<String, Integer>> returnMap = new HashMap<>();
        OutboundPersonTypeEnum[] values = OutboundPersonTypeEnum.values();

        //症候群
        Map<String, Integer> syncNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.SYMPTOM.getId()));
        returnMap.put(MonitorCategoryEnum.SYMPTOM.getDesc(), syncNumMaps);

        //传染病
        Map<String, Integer> syncInfectedNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.INFECTED.getId()));
        returnMap.put(MonitorCategoryEnum.INFECTED.getDesc(), syncInfectedNumMaps);

        //学校症状
        Map<String, Integer> syncSchoolNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.SCHOOL.getId()));
        returnMap.put(MonitorCategoryEnum.SCHOOL.getDesc(), syncSchoolNumMaps);

        //中毒
        Map<String, Integer> syncPoisonNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.POISON.getId()));
        returnMap.put(MonitorCategoryEnum.POISON.getDesc(), syncPoisonNumMaps);

        //门诊
        Map<String, Integer> syncOutpatientNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.OUTPATIENT.getId()));
        returnMap.put(MonitorCategoryEnum.OUTPATIENT.getDesc(), syncOutpatientNumMaps);

        //不明原因
        Map<String, Integer> syncUnknownNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.UNKNOWN_REASON.getId()));
        returnMap.put(MonitorCategoryEnum.UNKNOWN_REASON.getDesc(), syncUnknownNumMaps);

        //联防联控
        Map<String, Integer> syncPreventionNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.PREVENTION_CONTROL.getId()));
        returnMap.put(MonitorCategoryEnum.PREVENTION_CONTROL.getDesc(), syncPreventionNumMaps);

        //自定义预警外呼结果同步
        Map<String, Integer> syncCustomizedNumMaps = getSyncNumMap(values, Integer.valueOf(MonitorCategoryEnum.CUSTOMIZED.getId()));
        returnMap.put(MonitorCategoryEnum.CUSTOMIZED.getDesc(), syncCustomizedNumMaps);

        return returnMap;
    }

    private Map<String, Integer> getSyncNumMap(OutboundPersonTypeEnum[] values, Integer monitorType) {
        Map<String, Integer> syncInfectedNumMaps = new HashMap<>();
        for (int i = 0; i < values.length; i++) {
            OutboundPersonTypeEnum value = values[i];
            OutboundResultSyncService outboundResultSynchronizer = getOutboundResultSynchronizer(value.getType());
            int sync = outboundResultSynchronizer.sync(value.getType(), monitorType);
            syncInfectedNumMaps.put(value.getDesc(), sync);
        }
        return syncInfectedNumMaps;
    }

    @Transactional(rollbackFor = Exception.class)
    @Scheduled(fixedRate = 5 * 60 * 1000)
    @Override
    public Map<String, Map<String, Integer>> syncOutboundResultNew() {
        Map<String, Map<String, Integer>> returnMap = new HashMap<>();
        OutboundPersonTypeEnum[] outboundPersonTypeEnums = OutboundPersonTypeEnum.values();
        WarningTypeCodeEnum[] warningTypeCodeEnums = WarningTypeCodeEnum.values();

        for (WarningTypeCodeEnum warningTypeCodeEnum : warningTypeCodeEnums) {
            Map<String, Integer> syncInfectedNumMaps = new HashMap<>();
            for (OutboundPersonTypeEnum personTypeEnum : outboundPersonTypeEnums) {
                OutboundResultSyncService outboundResultSynchronizer = getOutboundResultSynchronizer(personTypeEnum.getType());
                int sync = outboundResultSynchronizer.syncNew(personTypeEnum.getType(),warningTypeCodeEnum.getName());
                syncInfectedNumMaps.put(personTypeEnum.getDesc(), sync);
            }
            returnMap.put(warningTypeCodeEnum.getDesc() , syncInfectedNumMaps);
        }

        return returnMap;
    }

    @Override
    public byte[] downloadMedicalTemplate(String eventId) {

        List<MedicalInfoVO> medList = hisMedicalInfoMapper.findRecordsByEventIds(Arrays.asList(eventId));
        //校验是否超出文件导出最大值
        restService.checkExportMax(medList);

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        sheet.setColumnHidden(0, true);

        sheet.setColumnWidth(0, 4000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);

        Row firstRow = sheet.createRow(0);

        createCellAndSetValue(firstRow, "病历ID", 0);
        createCellAndSetValue(firstRow, "居民姓名", 1);
        createCellAndSetValue(firstRow, "电话号码", 2);

        for (int i = 0; i < medList.size(); i++) {

            MedicalInfoVO vo = medList.get(i);
            Row row = sheet.createRow(i + 1);
            createCellAndSetValue(row, vo.getSourceKey(), 0);
            createCellAndSetValue(row, vo.getPatientName(), 1);
        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);

        return outputStream.toByteArray();
    }

    @Override
    public UploadResultVO uploadRelationFile(MultipartFile file, String eventId) {

        List<MedicalInfoVO> medList = hisMedicalInfoMapper.findRecordsByEventIds(Arrays.asList(eventId));
        Set<String> patientNameSet = medList.stream().map(e -> e.getPatientName())
                .collect(Collectors.toSet());

        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (IOException e) {
            log.error("读取上传文件失败", e);
            throw new MedicalBusinessException("11451107", "读取上传文件失败");
        }
        XSSFSheet sheet = workbook.getSheet("Sheet1");

        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {
            Row row = sheet.getRow(index);
            String patientName = getStringValue(row.getCell(0));
            String name = getStringValue(row.getCell(1));
            String phone = getStringValue(row.getCell(2));
            String relationType = getStringValue(row.getCell(3));

            StringBuilder sb = new StringBuilder("");

            List<Integer> errorList = new ArrayList<>();

            if (StringUtils.isBlank(patientName)) {
                sb.append("患者姓名必须填写;");
                errorList.add(0);
            } else if (StringUtils.length(patientName) > 25) {
                sb.append("患者姓名不能超过25位;");
                errorList.add(0);
            } else if (!patientNameSet.contains(patientName)) {
                sb.append("患者姓名不存在;");
                errorList.add(0);
            }

            if (StringUtils.isBlank(name)) {
                sb.append("相关人群姓名必须填写;");
                errorList.add(1);
            } else if (StringUtils.length(name) > 25) {
                sb.append("相关人群姓名不能超过25;");
                errorList.add(1);
            }

            if (StringUtils.isBlank(relationType)) {
                sb.append("与患者关系必须填写;");
                errorList.add(3);
            }

            if (StringUtils.isBlank(phone)) {
                sb.append("关联人群号码必须填写;");
                errorList.add(2);
            } else if (!StringUtils.isNumeric(phone)) {
                sb.append("关联人群号码必须是数字;");
                errorList.add(2);
            } else if (phone.length() != 11) {
                sb.append("关联人群号码必须是11位;");
                errorList.add(2);
            }

            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {
                failedCount++;
                createCellAndSetValue(row, errorMsg, 4);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        String resultAttachmentId = fileService.upload(outputStream.toByteArray(), file.getOriginalFilename());
        String attachmentId = fileService.upload(file).get(0);

        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);
        resultVO.setAttachmentId(attachmentId);
        resultVO.setResultAttachmentId(resultAttachmentId);

        return resultVO;
    }

    @Override
    public UploadResultVO uploadMedicalFile(MultipartFile file) {
        XSSFWorkbook workbook;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (IOException e) {
            log.error("读取上传文件失败", e);
            throw new MedicalBusinessException("11451107", "读取上传文件失败");
        }
        XSSFSheet sheet = workbook.getSheet("Sheet1");
        int totalCount = 0;
        int successCount = 0;
        int failedCount = 0;
        for (int index = sheet.getFirstRowNum() + 1; index <= sheet.getLastRowNum(); index++) {

            Row row = sheet.getRow(index);
            String sourceKey = getStringValue(row.getCell(0));
            String patientName = getStringValue(row.getCell(1));
            String phone = getStringValue(row.getCell(2));

            List<Integer> errorList = new ArrayList<>();

            StringBuilder sb = new StringBuilder("");

            // 患者与电话号码同时为空，则视为删除该行
            if (StringUtils.isBlank(patientName) && StringUtils.isBlank(phone)) {
                continue;
            }

            TbCdcewHisMedicalInfo medical = null;
            if (StringUtils.isBlank(sourceKey)) {
                sb.append("病历ID必须填写;");
                errorList.add(0);
            } else {
                medical = hisMedicalInfoMapper.findBySourceKey(sourceKey);
                if (medical == null) {
                    sb.append("病历信息不存在;");
                    errorList.add(0);
                }
            }

            if (StringUtils.isBlank(patientName)) {
                sb.append("患者姓名必须填写;");
                errorList.add(1);
            } else if (medical != null && !patientName.equals(medical.getPatientName())) {
                sb.append("患者姓名错误;");
                errorList.add(1);
            }

            if (StringUtils.isNotBlank(phone)) {
                if (!StringUtils.isNumeric(phone)) {
                    sb.append("电话号码必须是数字;");
                    errorList.add(2);
                } else if (phone.length() != 11) {
                    sb.append("电话号码必须是11位;");
                    errorList.add(2);
                }
            }

            String errorMsg = sb.toString();
            if (StringUtils.isNotBlank(errorMsg)) {

                failedCount++;
                createCellAndSetValue(row, errorMsg, 4);

                for (int errorIndex : errorList) {
                    Cell cell = row.getCell(errorIndex);
                    if (cell == null) {
                        createCellAndSetValue(row, "", errorIndex);
                        cell = row.getCell(errorIndex);
                    }
                    CellStyle style = workbook.createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cell.setCellStyle(style);
                }

            } else {
                successCount++;
            }
            totalCount++;

        }

        ByteArrayOutputStream outputStream = generateOutputStream(workbook);
        String resultAttachmentId = fileService.upload(outputStream.toByteArray(), file.getOriginalFilename());
        String attachmentId = fileService.upload(file).get(0);

        UploadResultVO resultVO = new UploadResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(successCount);
        resultVO.setFailedCount(failedCount);
        resultVO.setAttachmentId(attachmentId);
        resultVO.setResultAttachmentId(resultAttachmentId);

        return resultVO;
    }

    private String getStringValue(Cell cell) {

        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:  //字符串类型
                return cell.getStringCellValue();
            case BOOLEAN:   //布尔类型
                return String.valueOf(cell.getBooleanCellValue());
            case NUMERIC:  //数值类型
                return String.format("%.0f", cell.getNumericCellValue());
            default:
                return null;
        }

    }

    private ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11451108", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        cell.setCellValue(value);
    }

    public OutboundResultSyncService getOutboundResultSynchronizer(Integer personType) {
        if (OutboundPersonTypeEnum.MEDICAL.getType().equals(personType)) {
            return patientOutBoundResultSync;
        } else if (OutboundPersonTypeEnum.ADDITION.getType().equals(personType)) {
            return additionPersonOutBoundResultSync;
        } else {
            return businessPersonOutBoundResultSync;
        }
    }

    private List<BusinessPersonOutCallDTO> getResultList(List<BusinessPersonOutCallDTO> sourceList) {

        List<BusinessPersonOutCallDTO> retList = new ArrayList<>();
        Map<String, List<BusinessPersonOutCallDTO>> sourceMap = sourceList.stream()
                .collect(Collectors.groupingBy(BusinessPersonOutCallDTO::getPersonId));

        sourceMap.values().forEach(dtoList -> {
            MutableBoolean isFind = new MutableBoolean(false);
            for (BusinessPersonOutCallDTO dto : dtoList) {
                if (CallConnectStatusEnum.CONNECTED.getCode().equals(dto.getConnectStatus())) {
                    isFind.setTrue();
                    retList.add(dto);
                    break;
                }
            }

            // 没有已接通的记录
            if (!isFind.booleanValue()) {
                retList.add(dtoList.get(0));
            }
        });

        return retList;
    }

    private Collection<String> getInfectedCodeList(Collection<String> srcList) {
        List<CascadeVO> infectedCodeList = cdcReportCardService.getInfectedNameList();
        List<String> resultList = new ArrayList<>();
        for (String code : srcList) {
            CascadeVO vo = findInfectedCode(code, infectedCodeList);
            if (vo != null && org.springframework.util.CollectionUtils.isEmpty(vo.getChildren())) {
                resultList.add(vo.getValue());
            } else if (vo != null && !org.springframework.util.CollectionUtils.isEmpty(vo.getChildren())) {
                for (CascadeVO childVO : vo.getChildren()) {
                    addInfectedCodes(resultList, childVO);
                }
            }
        }

        if (resultList.isEmpty()) {
            resultList.add("-1");
        }

        return resultList;
    }


    private CascadeVO findInfectedCode(String code, List<CascadeVO> infectedCodeList) {
        for (CascadeVO vo : infectedCodeList) {
            if (code.equals(vo.getValue())) {
                return vo;
            } else if (!org.springframework.util.CollectionUtils.isEmpty(vo.getChildren())) {
                CascadeVO resultVO = findInfectedCode(code, vo.getChildren());
                if (resultVO != null) {
                    return resultVO;
                }
            }
        }
        return null;
    }

    private void addInfectedCodes(Collection<String> resultList, CascadeVO vo) {
        if (org.springframework.util.CollectionUtils.isEmpty(vo.getChildren())) {
            resultList.add(vo.getValue());
        } else {
            for (CascadeVO childVO : vo.getChildren()) {
                addInfectedCodes(resultList, childVO);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<CreateCallRs> createCallSpeechTaskForCustomizedBusiness(CreateCallTaskDto createCallTaskDto) {

        //1，从对象列表获取数据
        List<Resident> residents = createCallTaskDto.getResidents();

        if (CollectionUtils.isEmpty(residents)) {
            throw new MedicalBusinessException("缺少对应机构和症状的预警管理员");
        }

        List<Dweller> dwellerList = new ArrayList<>();
        List<TbCdcewCustomizedOutcallNotice> outcallNotices = new ArrayList<>();

        if (userCenterVersion.equals("5.0.0")) {
            residents.forEach(resident -> {
                UapUserPo uapUserPo = restService.getUser(resident.getPersonId());
                UapOrgPo userOrg = restService.getUserOrg(uapUserPo.getLoginName());
                TbCdcewCustomizedOutcallNotice outcallNotice = new TbCdcewCustomizedOutcallNotice();
                outcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_customized_outcall_notice")));
                outcallNotice.setEventId(createCallTaskDto.getEventId());
                outcallNotice.setPersonId(resident.getPersonId());
                outcallNotice.setPersonType(createCallTaskDto.getPersonType());
                outcallNotice.setCreateTime(new Date());
                outcallNotice.setUpdateTime(new Date());
                outcallNotice.setStatDimId(userOrg.getId());
                outcallNotice.setStatDimName(userOrg.getName());
                outcallNotice.setName(uapUserPo.getName());
                outcallNotice.setPhone(resident.getTelephone());
                outcallNotices.add(outcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        } else {
            residents.forEach(resident -> {
                TbCdcewBusinessPerson person = businessPersonMapper.selectByPrimaryKey(resident.getPersonId());
                TbCdcewCustomizedOutcallNotice outcallNotice = new TbCdcewCustomizedOutcallNotice();
                outcallNotice.setId(String.valueOf(batchUidService.getUid("tb_cdcew_customized_outcall_notice")));
                outcallNotice.setEventId(createCallTaskDto.getEventId());
                outcallNotice.setPersonId(resident.getPersonId());
                outcallNotice.setPersonType(createCallTaskDto.getPersonType());
                outcallNotice.setCreateTime(new Date());
                outcallNotice.setUpdateTime(new Date());
                outcallNotice.setStatDimId(person.getStatDimId());
                outcallNotice.setStatDimName(person.getStatDimName());
                outcallNotice.setName(person.getName());
                outcallNotice.setPhone(person.getPhone());
                outcallNotice.setPositionName(person.getPositionName());
                outcallNotices.add(outcallNotice);
                dwellerList.add(Dweller.builder().dwellerName(resident.getDwellerName()).telephone(resident.getTelephone()).relationId(resident.getPersonId()).build());
            });
        }
        CallTask callTask = getCustomizedNormalCallTask(createCallTaskDto);
        callTask.setDwellers(dwellerList);
        Response<CreateCallRs> response = createOutboundService.execCallTask(callTask);
        if (response.isSuccess()) {
            CreateCallRs data = response.getData();
            String batchId = data.getBatch().get(0);
            TbCdcewCustomizedOutcallBatch outcallBatch = new TbCdcewCustomizedOutcallBatch();
            outcallBatch.setId(String.valueOf(batchUidService.getUid("tb_cdcew_customized_outcall_batch")));
            outcallBatch.setEventId(createCallTaskDto.getEventId());
            outcallBatch.setPersonType(createCallTaskDto.getPersonType().intValue());
            outcallBatch.setCreateTime(new Date());
            outcallBatch.setUpdateTime(new Date());
            tbCdcewCustomizedOutcallBatchMapper.insert(outcallBatch);

            outcallNotices.forEach(outcallNotice -> {
                outcallNotice.setBatchId(outcallBatch.getId());
                outcallNotice.setCallBatchId(batchId);
                outcallNotice.setCallStatus(MedicalCallStatusEnum.CALLING.getCode());
            });
            log.info("执行外呼成功: {}", response);
        } else {
            log.error("执行外呼失败：{}", response);
        }

        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(outcallNotices)) {
            saveDataToCustomizedOutCallNotice(createCallTaskDto, outcallNotices);
        }
        return response;
    }

    private CallTask getCustomizedNormalCallTask(CreateCallTaskDto createCallTaskDto) {
        CallTask callTask = new CallTask();
        callTask.setType(Integer.valueOf(Optional.ofNullable(createCallTaskDto.getType()).orElse("1")));
        callTask.setSpeechId(Long.valueOf(SymptomTypeEnum.NORMAL_CALL.getVerbalTrickId()));
        callTask.setPlanName(SymptomTypeEnum.NORMAL_CALL.getPlanName());
        callTask.setDate(createCallTaskDto.getDate());

        Recall recall = new Recall();
        recall.setRecallCount(Integer.valueOf(createCallTaskDto.getRecallCount()));
        recall.setTimeInterval(Integer.valueOf(createCallTaskDto.getTimeInterval()));
        callTask.setRecall(recall);

        TbCdcCallTemplate tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.OUTCALL.getType(), Integer.valueOf(MonitorCategoryEnum.CUSTOMIZED.getId()));
        String callContent = getCustomizedCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getCallContent(), tbCdcCallTemplate);
        List<Var> vars = new ArrayList<>();
        vars.add(Var.builder().remarks("通知内容").content(callContent).build());
        vars.add(Var.builder().remarks("外呼机构").content(createCallTaskDto.getOutCallOrgName()).build());
        callTask.setVars(vars);

        if (StringUtils.isNotBlank(createCallTaskDto.getSmsCondition())) {
            tbCdcCallTemplate = getDefaultTbCdcCallTemplate(createCallTaskDto.getPersonType(), NoteTypeEnum.SMS.getType(), Integer.valueOf(MonitorCategoryEnum.CUSTOMIZED.getId()));
            String appContent = getCustomizedCallContent(createCallTaskDto.getEventId(), createCallTaskDto.getSmsContent(), tbCdcCallTemplate);
            Sms sms = new Sms();
            List<Var> smsVars = new ArrayList<>();
            smsVars.add(Var.builder().remarks("content").content(appContent).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            sms.setSmsVars(smsVars);
            sms.setSmsId(smsId);
            sms.setSmsCondition(Integer.valueOf(createCallTaskDto.getSmsCondition()));
            callTask.setSms(sms);
        }
        return callTask;
    }

    public String getCustomizedCallContent(String eventId, String content, TbCdcCallTemplate tbCdcCallTemplate) {
        if (StringUtils.isBlank(content)) {
            Map<String, Object> mapping = new HashMap<>(10);
            mapping.putAll(TemplateParaEnum.MAPPING);
            if (null != eventId && eventId.length() > 0) {
                TbCdcewCustomizedWarningEvent event = tbCdcewCustomizedWarningEventMapper.selectByPrimaryKey(eventId);
                Integer count = tbCdcewCustomizedMedRelationMapper.countByEventId(eventId);
                if (Objects.nonNull(event)) {
                    mapping.put(TemplateParaEnum.ORG_NAME.getCode(), event.getStatDimName());
                    mapping.put(TemplateParaEnum.SYMPTOM.getCode(), event.getDiseaseName());
                    SimpleDateFormat sp = new SimpleDateFormat(TimeConstant.DATETIME_PATTERN_CN_MONTH_DAY);
                    String date = sp.format(Optional.ofNullable(event.getBeginDate()).orElse(new Date()));
                    mapping.put(TemplateParaEnum.CREATE_DATE.getCode(), date);
                    mapping.put(TemplateParaEnum.DISTRICT_NAME.getCode(), event.getDistrictName());
                    mapping.put(TemplateParaEnum.MED_COUNT.getCode(), count);
                }
            }
            String templateContent = Optional.ofNullable(tbCdcCallTemplate).orElse(new TbCdcCallTemplate()).getTemplateContent();
            if (StringUtils.isNotBlank(templateContent)) {
                content = com.iflytek.fpva.cdc.util.StringUtils.templateParse(mapping, templateContent);
            }
        }
        return content;
    }

    private void saveDataToCustomizedOutCallNotice(CreateCallTaskDto createCallTaskDto, List<TbCdcewCustomizedOutcallNotice> outcallNotices) {
        List<TbCdcewCustomizedOutcallNotice> updateList = new ArrayList<>();
        List<TbCdcewCustomizedOutcallNotice> insertList = new ArrayList<>();

        List<TbCdcewCustomizedOutcallNotice> outcallNotices1 = tbCdcewCustomizedOutcallNoticeMapper.listByEventIdAndPersonType(createCallTaskDto.getEventId(), createCallTaskDto.getPersonType());
        Map<String, List<TbCdcewCustomizedOutcallNotice>> valueMap = Optional.ofNullable(outcallNotices1).orElse(new ArrayList<>())
                .stream().filter(item -> StringUtils.isNotBlank(item.getPersonId()))
                .collect(Collectors.groupingBy(TbCdcewCustomizedOutcallNotice::getPersonId));
        outcallNotices.forEach(tbCdcewOutcallNotice -> {
            List<TbCdcewCustomizedOutcallNotice> businessPersonOutCallDTOS1 = valueMap.computeIfAbsent(tbCdcewOutcallNotice.getPersonId(), v -> null);
            if (CollectionUtils.isNotEmpty(businessPersonOutCallDTOS1)) {
                TbCdcewCustomizedOutcallNotice businessPersonOutCallDTO = businessPersonOutCallDTOS1.get(0);
                tbCdcewOutcallNotice.setId(businessPersonOutCallDTO.getId());
                updateList.add(tbCdcewOutcallNotice);
            } else {
                insertList.add(tbCdcewOutcallNotice);
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            tbCdcewCustomizedOutcallNoticeMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            tbCdcewCustomizedOutcallNoticeMapper.batchUpdate(updateList);
        }
    }

}


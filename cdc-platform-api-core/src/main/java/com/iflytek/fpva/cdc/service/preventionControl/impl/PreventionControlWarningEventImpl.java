package com.iflytek.fpva.cdc.service.preventionControl.impl;

import com.iflytek.fpva.cdc.apiService.AdminServiceApi;
import com.iflytek.fpva.cdc.apiService.UapServiceApi;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewFormConfig;
import com.iflytek.fpva.cdc.mapper.preventionControl.PreventionControlWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.preventionControl.TbCdcewPreventionControlMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.preventionControl.TbCdcewPreventionControlWarningEventMapper;
import com.iflytek.fpva.cdc.model.common.CommonEventPointVO;
import com.iflytek.fpva.cdc.model.dto.RelatedEventDto;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.model.warningEvent.dto.MedicalListQueryDTO;
import com.iflytek.fpva.cdc.model.common.CommonWarningEventVO;
import com.iflytek.fpva.cdc.model.warningEvent.vo.*;
import com.iflytek.fpva.cdc.service.common.FormConfigService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.WarningEventService;
import com.iflytek.fpva.cdc.service.EventCommonUtilsService;
import com.iflytek.fpva.cdc.common.utils.DesensitizedUtils;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.SensitiveInterceptor.threadLocal;

@Service
public class PreventionControlWarningEventImpl implements WarningEventService {

    @Resource
    private TbCdcConfigService tbCdcConfigService;

    @Resource
    private EventCommonUtilsService eventCommonUtilsService;

    @Resource
    private PreventionControlWarningEventMapper preventionControlWarningEventMapper;

    @Resource
    private TbCdcewPreventionControlMedRelationMapper tbCdcewPreventionControlMedRelationMapper;

    @Resource
    private TbCdcewPreventionControlWarningEventMapper tbCdcewPreventionControlWarningEventMapper;

    @Resource
    private AdminServiceApi adminServiceApi;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private FormConfigService formConfigService;

    @Override
    public String getWarningEventType() {
        return WarningTypeCodeEnum.PREVENTION_CONTROL.getName();
    }

    @Override
    public boolean isEnableAiScreen() {
        //是否能根据AI初筛进行排序  --  联防联控
        return eventCommonUtilsService.isEnableAiScreen(CommonConstants.CONFIG_KEY_PREVENTION_CONTROL_IS_AI_SCREEN_TOP);
    }

    @Override
    public List<WarningEventListVO> getWarningEventListBy(EventCriteria eventCriteria) {
        return tbCdcewPreventionControlWarningEventMapper.getWarningEventListBy(eventCriteria);
    }

    @Override
    public List<RelatedEventDto> getRelatedEvents(List<String> eventIds) {
        return Collections.emptyList();
    }

    @Override
    public List<PreventionControlMedicalInfoVO> getMedicalInfoListBy(MedicalListQueryDTO dto) {
        return preventionControlWarningEventMapper.getPreventionControlMedicalInfoList(dto);
    }

    @Override
    public ResponseEntity<byte[]> exportEventMedicalList(List<Object> medicalList, MedicalListQueryDTO dto, String loginUserId) {

        List<PreventionControlMedicalInfoVO> medicalInfoList = medicalList.stream().map(o -> (PreventionControlMedicalInfoVO)o).collect(Collectors.toList());
        //校验是否超出文件导出最大值
        adminServiceApi.checkExportMax(medicalInfoList);
        //判断导出是否需要脱敏
//        boolean isDesensitized = tbCdcConfigService.isDesensitization(loginUserId);
//        if(isDesensitized){
//            medicalInfoList.forEach(DesensitizedUtils::desensitizedObject);
//        }
        boolean isDesensitized = threadLocal.get().getNormalFlag();
        boolean isDiagnoseRole = threadLocal.get().getDiagnoseVO().getDiagnoseFlag();

        for (PreventionControlMedicalInfoVO r : medicalInfoList) {
            if (isDesensitized){
                DesensitizedUtils.desensitizedObject(r);
            }
            if (isDiagnoseRole){
                DesensitizedUtils.desensitizedDiagnoseForVO(r,threadLocal.get().getDiagnoseVO().getDiagnoseNames());
            }
        }


        List<PreventionControlMedicalExcelVO> resultList = medicalInfoList.stream()
                                                                          .map(PreventionControlMedicalExcelVO::new)
                                                                          .collect(Collectors.toList());
        TbCdcewFormConfig formConfig = formConfigService.getConfigByLoginUserId(loginUserId, dto.getFormType());
        //没有列表表头配置时，直接置为null
        String formConfigDesc = formConfig == null ? null : formConfig.getConfigDesc();
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(PreventionControlMedicalExcelVO.class, formConfigDesc);
        return FileUtils.exportExcelByFormConfig(resultList, PreventionControlMedicalExcelVO.class, true, dto.getEventId() + ".xlsx", propertyMetaList);
    }

    @Override
    public EventTypeCountVO getEventTypeCountVO(String loginUserId, String loginUserName) {
        return new EventTypeCountVO();
    }

    @Override
    public List<String> getCompanyByEventId(String eventId) {
        return tbCdcewPreventionControlMedRelationMapper.getCompanyByEventId(eventId);
    }

    @Override
    public <T extends CommonWarningEventVO> T getWarningEventMsgByEventId(String eventId) {

        return (T) tbCdcewPreventionControlWarningEventMapper.selectByPrimaryKey(eventId);
    }

    @Override
    public Double findByConfigKey(String diseaseCode) {
        //联防联控没有设置周边机构距离
        return 0.0;
    }

    @Override
    public List<OrgMedCount> getOrgMedCountByIds(Date statDate, String diseaseCode, List<String> orgIds) {
        return preventionControlWarningEventMapper.getOrgMedCountByIds(statDate, diseaseCode, orgIds);
    }

    @Override
    public List<CommonEventPointVO> getAllEventPoints(EventCriteria eventCriteria) {
        return tbCdcewPreventionControlWarningEventMapper.getAllEventPointsBy(eventCriteria);
    }
}

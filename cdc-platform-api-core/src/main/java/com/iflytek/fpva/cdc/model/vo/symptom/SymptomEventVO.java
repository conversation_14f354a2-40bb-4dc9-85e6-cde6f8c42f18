package com.iflytek.fpva.cdc.model.vo.symptom;

import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SymptomEventVO {

    @ApiModelProperty("信号名称/症状")
    @Sensitive(type = SensitiveTypeEnum.DIAGNOSE)
    private String symptom;

    @ApiModelProperty("区域")
    @Sensitive(type = SensitiveTypeEnum.REGION)
    private String regionName;

    @ApiModelProperty("信号编号")
    private String eventNum;

    @ApiModelProperty("预警时间")
    private Date createTime;

    @ApiModelProperty("发生地点")
    @Sensitive(type = SensitiveTypeEnum.ORG)
    private String statDimName;

    @ApiModelProperty("响应时间")
    private Date responseTime;

    @ApiModelProperty("处置时间")
    private Date processTime;

    @ApiModelProperty("现场调查开始时间")
    private Date esBeginTime;

    @ApiModelProperty("现场调查结束时间")
    private Date esEndTime;

    @ApiModelProperty("第一次研判时间")
    private Date analysisTime1;

    @ApiModelProperty("第二次研判时间")
    private Date analysisTime2;

    @ApiModelProperty("第三次研判时间")
    private Date analysisTime3;

    @ApiModelProperty("继续关注次数")
    private Integer attentionCount;
}

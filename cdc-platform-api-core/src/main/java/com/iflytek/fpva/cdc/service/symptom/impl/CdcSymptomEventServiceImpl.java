package com.iflytek.fpva.cdc.service.symptom.impl;

import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.OvertimeStatus;
import com.iflytek.fpva.cdc.constant.ProcessingStatus;
import com.iflytek.fpva.cdc.constant.enums.ProcessTypeEnum;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.mapper.symptom.*;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.vo.EventAnalysisResultVO;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.service.symptom.CdcSymptomEventService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.util.UapAccessUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CdcSymptomEventServiceImpl implements CdcSymptomEventService {

    @Resource
    private BatchUidService batchUidService;

    @Resource
    TbCdcSymptomEventAnalysisMapper tbCdcSymptomEventAnalysisMapper;

    @Resource
    TbCdcSymptomEventAnalysisAttachmentMapper tbCdcSymptomEventAnalysisAttachmentMapper;

    @Resource
    TbCdcewSympWarningEventMapper tbCdcewSympWarningEventMapper;

    @Resource
    TbCdcewSympWarningDetailMapper tbCdcewSympWarningDetailMapper;

    @Resource
    private RestService restService;

    @Resource
    private CdcSymptomEventProcessRecordMapper cdcSymptomEventProcessRecordMapper;

    @Resource
    private TbCdcConfigService tbCdcConfigService;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void startProcess(String loginUserId, String eventId, int status, String loginUserName) {
        log.info("症候群手动改变事件状态开始,信息: loginUserId:" + loginUserId + " ,eventId:" + eventId
                + " ,status:" + status + " ,loginUserName:" + loginUserName);
        if (status >= ProcessingStatus.REMOVED_AI) {
            throw new MedicalBusinessException("11441005", "无效的状态");
        }
        TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(eventId);
        //查询不到 或 状态重复 不能进行操作
        if (event == null) {
            throw new MedicalBusinessException("11450001", "不存在对应ID的事件");
        }
        //操作权限检查
        UapOrgPo uapOrg = restService.getUserOrg(loginUserName);
        UapAccessUtil.hasAccess(uapOrg, event.getProvinceCode(), event.getCityCode(), event.getDistrictCode());
        //新增一条处理记录
        CdcSymptomEventProcessRecord record = new CdcSymptomEventProcessRecord();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_symp_event_record")));
        record.setEventId(eventId);
        record.setProcessOrgCode(uapOrg.getCode());
        record.setProcessOrgName(uapOrg.getName());
        record.setProcessTime(new Date());
        record.setProcessStatus(status);
        record.setProcessLoginUserId(loginUserId);
        record.setProcessType(ProcessTypeEnum.STATUS.getCode());
        Date endTime = null;
        switch (status) {
            case ProcessingStatus.PROCESSING:
                record.setProcessDesc(ProcessingStatus.PROCESSING_STR);
                break;
            //如果是排除和上报 则要修改事件的endTime
            case ProcessingStatus.REMOVED_MANUAL:
                record.setProcessDesc(ProcessingStatus.REMOVED_MANUAL_STR);
                endTime = new Date();
                break;
            case ProcessingStatus.POSITIVE:
                record.setProcessDesc(ProcessingStatus.POSITIVE_STR);
                endTime = new Date();
                break;
            default:
                break;
        }
        cdcSymptomEventProcessRecordMapper.insertSelective(record);
        //将原始事件表里的processing_status改为正在处理
        tbCdcewSympWarningEventMapper.updateProcessingStatusAndEndTimeById(status, endTime, eventId,null);
        //需要将所关联的detail中的状态也同步修改
        tbCdcewSympWarningDetailMapper.updateProcessingStatusByEventId(status, eventId);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public TbCdcSymptomEventAnalysis createEventAnalysisResult(EventAnalysisResultVO eventAnalysisResultVO, String loginUserId, String loginUserName) {
        //传入字段校验
        if (eventAnalysisResultVO.getOtherDiseaseName() != null && eventAnalysisResultVO.getOtherDiseaseName().length() > 15) {
            throw new MedicalBusinessException("11441027", "疾病名称超出15字限制");
        }
        if ((eventAnalysisResultVO.getDeadCaseNum() < 0) || (eventAnalysisResultVO.getDeadCaseNum() > 999)) {
            throw new MedicalBusinessException("11441028", "死亡病例数不合规");
        }
        if ((eventAnalysisResultVO.getTotalCaseNum() < 0) || (eventAnalysisResultVO.getTotalCaseNum() > 999)) {
            throw new MedicalBusinessException("11441029", "总病例数不合规");
        }
        //1, 增加研判信息
        String analysisId = String.valueOf(batchUidService.getUid("tb_cdcew_symp_event_analysis"));
        TbCdcSymptomEventAnalysis tbCdcSymptomEventAnalysis = TbCdcSymptomEventAnalysis.builder()
                .id(analysisId)
                .conclusions(eventAnalysisResultVO.getConclusions())
                .educationInvolved(eventAnalysisResultVO.getEducationInvolved())
                .comments(eventAnalysisResultVO.getComments())
                .eventId(eventAnalysisResultVO.getEventId())
                .createTime(new Date())
                .orgName(eventAnalysisResultVO.getOrgName())
                .orgId(eventAnalysisResultVO.getOrgId())
                .investigationTime(eventAnalysisResultVO.getInvestigationTime())
                .investigationMethod(eventAnalysisResultVO.getInvestigationMethod())
                .epiHistory(eventAnalysisResultVO.getEpiHistory())
                .pathogenDetection(eventAnalysisResultVO.getPathogenDetection())
                .totalCaseNum(eventAnalysisResultVO.getTotalCaseNum())
                .deadCaseNum(eventAnalysisResultVO.getDeadCaseNum())
                .creatorName(eventAnalysisResultVO.getCreatorName())
                .creatorId(loginUserId)
                .fillingDate(eventAnalysisResultVO.getFillingDate())
                .diseaseCode(eventAnalysisResultVO.getDiseaseCode())
                .diseaseName(eventAnalysisResultVO.getDiseaseName())
                .otherDiseaseName(eventAnalysisResultVO.getOtherDiseaseName())
                .positiveEventType(eventAnalysisResultVO.getPositiveEventType())
                .build();

        //2, 增加附件信息
        List<TbCdcAttachment> attachmentList = eventAnalysisResultVO.getAttachmentList();
        List<TbCdcSymptomEventAnalysisAttachment> tbCdcSymptomEventAnalysisAttachmentList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(attachmentList)) {
            attachmentList.forEach(item -> {
                String uid = String.valueOf(batchUidService.getUid("tb_cdcew_symp_event_analysis_attachment"));
                TbCdcSymptomEventAnalysisAttachment tbCdcSymptomEventAnalysisAttachment = new TbCdcSymptomEventAnalysisAttachment();
                tbCdcSymptomEventAnalysisAttachment.setAnalysisId(analysisId);
                tbCdcSymptomEventAnalysisAttachment.setAttachmentId(item.getId());
                tbCdcSymptomEventAnalysisAttachment.setId(uid);
                tbCdcSymptomEventAnalysisAttachmentList.add(tbCdcSymptomEventAnalysisAttachment);
            });
        }

        //3, 存储数据库
        tbCdcSymptomEventAnalysisMapper.insert(tbCdcSymptomEventAnalysis);
        if (!CollectionUtils.isEmpty(tbCdcSymptomEventAnalysisAttachmentList)) {
            tbCdcSymptomEventAnalysisAttachmentMapper.batchInsert(tbCdcSymptomEventAnalysisAttachmentList);
        }
        // 根据研判结论判断
        if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_SUSPECT || eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_CONFIRM) {

            startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.POSITIVE, loginUserName);

        } else if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_REMOVED) {

            startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.REMOVED_MANUAL, loginUserName);

        } else if (eventAnalysisResultVO.getConclusions() == CommonConstants.ANALYSIS_RESULT_CONCLUSION_FOCUS) {

            // 持续关注，延长处置超时时间
            TbCdcewSympWarningEvent event = tbCdcewSympWarningEventMapper.selectByPrimaryKey(eventAnalysisResultVO.getEventId());
            if (event == null) {
                throw new MedicalBusinessException("11450001", "不存在对应ID的事件：" + eventAnalysisResultVO.getEventId());
            }

            // 处置超时状态，不延长处置超时时间
            if (OvertimeStatus.PROCESS_OVERTIME_OUT !=
                    Optional.ofNullable(event.getProcessingTimeOutStatus()).orElse(OvertimeStatus.PROCESS_OVERTIME_NORMAL)) {
                long processTimeout = tbCdcConfigService.getSchoolProcessingTimeoutConfig();
                Date processLatestTime = DateUtils.addHours(new Date(), (int) processTimeout);
                tbCdcewSympWarningEventMapper.updateProcessingLatestTimeByEventId(eventAnalysisResultVO.getEventId(), processLatestTime);
            }

            startProcess(loginUserId, eventAnalysisResultVO.getEventId(), ProcessingStatus.PROCESSING, loginUserName);

        }
        return tbCdcSymptomEventAnalysis;
    }
}

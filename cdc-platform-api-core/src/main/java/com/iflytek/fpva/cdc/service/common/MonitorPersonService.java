package com.iflytek.fpva.cdc.service.common;

import com.iflytek.fpva.cdc.model.vo.MonitorPersonQueryVO;
import com.iflytek.fpva.cdc.model.vo.MonitorPersonVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.common.model.vo.UploadResultVO;
import org.springframework.web.multipart.MultipartFile;

public interface MonitorPersonService {

    void add(MonitorPersonVO vo, String loginUserName);

    void update(MonitorPersonVO vo, String loginUserName);

    void delete(String id, String loginUserName);

    void updateStatus(String id, Integer status, String loginUserName);

    PageData<MonitorPersonVO> list(MonitorPersonQueryVO queryVO);

    byte[] downloadTemplate(String cityCode, String districtCode);

    UploadResultVO uploadFile(MultipartFile file);

    UploadResultVO batchAdd(String attachmentId, String loginUserName);

}

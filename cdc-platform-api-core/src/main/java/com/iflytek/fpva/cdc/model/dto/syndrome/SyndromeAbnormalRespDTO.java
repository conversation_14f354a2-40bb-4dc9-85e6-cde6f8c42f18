package com.iflytek.fpva.cdc.model.dto.syndrome;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SyndromeAbnormalRespDTO {
    @ApiModelProperty("异常病例数")
    private Long abnormalMedCnt;

    @ApiModelProperty("是否异常; 是:Y, 否:N")
    private String isAbnormal;

    @ApiModelProperty("症候群编码")
    private String syndromeCode;

    @ApiModelProperty("症候群名称")
    private String syndromeName;

    @ApiModelProperty("配置规则")
    private TbCdcmrDiseaseMonitorRule rule;

    @ApiModelProperty("数据列表")
    private List<SyndromeMonitorRespDTO>  syndromeMonitorRespDTOS;


}

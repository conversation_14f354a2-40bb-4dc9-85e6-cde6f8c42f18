package com.iflytek.fpva.cdc.model.vo.pharmacy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 所购药品信息
 *
 * <AUTHOR>
 * @date 2022-05-20 16:42
 */
@Data
@ApiModel("所购药品信息")
public class Medicine {
    /**
     * 批准文号
     */
    @ApiModelProperty("批准文号")
    private String approvalNumber;
    /**
     * 药械名称
     */
    @ApiModelProperty("药械名称")
    private String drugName;
    /**
     * 与购药人关系
     */
    @ApiModelProperty("与购药人关系")
    private String drugUseName;
    /**
     * 用药人症状
     */
    @ApiModelProperty("用药人症状")
    private String symptomDesc;
    /**
     * 生产厂家
     */
    @ApiModelProperty("生产厂家")
    private String drugsFactory;
    /**
     * 生产批号
     */
    @ApiModelProperty("生产批号")
    private String batchNumber;
    /**
     * 购药数量
     */
    @ApiModelProperty("购药数量")
    private String amount;
    /**
     * 药品单位
     */
    @ApiModelProperty("药品单位")
    private String unit;
    /**
     * 是否为处方药
     */
    @ApiModelProperty("是否为处方药")
    private String prescriptionFlag;
    /**
     * 药品类型
     */
    @ApiModelProperty("药品类型")
    private String drugsType;
    /**
     * 处方审核人
     */
    @ApiModelProperty("处方审核人")
    private String prescriptionReviewer;
}

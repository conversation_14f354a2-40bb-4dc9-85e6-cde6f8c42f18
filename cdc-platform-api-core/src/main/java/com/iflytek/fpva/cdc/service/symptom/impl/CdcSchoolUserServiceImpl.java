package com.iflytek.fpva.cdc.service.symptom.impl;

import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.entity.TbCdcEventScUser;
import com.iflytek.fpva.cdc.entity.TbCdcWarningDetail;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcScCheckInfoMapper;
import com.iflytek.fpva.cdc.mapper.syndrome.TbCdcWarningDetailMapper;
import com.iflytek.fpva.cdc.service.symptom.CdcSchoolUserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CdcSchoolUserServiceImpl implements CdcSchoolUserService {

    @Autowired
    private TbCdcScCheckInfoMapper tbCdcScCheckInfoMapper;

    @Autowired
    private TbCdcWarningDetailMapper tbCdcWarningDetailMapper;

    @Override
    public List<TbCdcEventScUser> findByRelationSourceKeyAndDataSourceTypeCode(List<String> sourceKeyCollection, String dataSourceTypeCode) {
        return tbCdcScCheckInfoMapper.findByRelationSourceKeyAndDataSourceTypeCode(sourceKeyCollection, dataSourceTypeCode);
    }

    @Override
    public List<TbCdcEventScUser> findByEventId(String eventId, boolean total) {
        List<TbCdcEventScUser> tbCdcEventScUserList;
        if (total) {
            //累计数据
            tbCdcEventScUserList = tbCdcScCheckInfoMapper.findByEventId(eventId, null);
        } else {
            //事件开始当天数据
            //取该事件当天的那条信号
            TbCdcWarningDetail detail = tbCdcWarningDetailMapper.findFirstByEventId(eventId);
            tbCdcEventScUserList = tbCdcScCheckInfoMapper.findByEventId(eventId, detail.getFullDate());
        }
        return tbCdcEventScUserList;
    }

    @Override
    public List<TbCdcEventScUser> findStudentByEventTimeRange(String eventId, String beginDate, String endDate) {
        return tbCdcScCheckInfoMapper.findStudentByEventTimeRange(eventId,
                StringUtils.isNotBlank(beginDate) ? TimeConstant.formatDate(beginDate) : null,
                StringUtils.isNotBlank(endDate) ? TimeConstant.formatDate(endDate) : null);
    }

    @Override
    public TbCdcEventScUser findBySchoolUserId(String userId, String eventId) {
        return tbCdcScCheckInfoMapper.findByEventIdAndUserId(userId, eventId);
    }
}

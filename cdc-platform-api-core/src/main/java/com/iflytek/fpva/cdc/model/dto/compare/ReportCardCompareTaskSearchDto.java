package com.iflytek.fpva.cdc.model.dto.compare;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class ReportCardCompareTaskSearchDto {

    @ApiModelProperty("状态编码")
    private String status;

    @ApiParam("查询开始日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date minBeginDate;

    @ApiParam("查询结束日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private Date maxBeginDate;

    private String provinceCode;

    private String cityCode;

    private String districtCode;

    private int pageSize;

    private int pageIndex;
}

package com.iflytek.fpva.cdc.model.vo.patient;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("医生病历分布")
public class DocMedDistribution {

    /**
     * 医生姓名
     * **/
    @ApiModelProperty("医生姓名")
    private String doctorName;

    /**
     * 卫生院名称
     */
    @ApiModelProperty("卫生院名称")
    private String hospitalName;

    /**
     * 医生当日病历数
     * **/
    @ApiModelProperty("医生当日病历数")
    private Integer medicalNum;

    /**
     * 病历数占比
     */
    @ApiModelProperty("病历数占比")
    private String rate;

}

package com.iflytek.fpva.cdc.model.dto.unknownReason;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
@Data
public class UnknownReasonMappingResult {

    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String orgName;

    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;

    /**
     * 功能区域编码
     */
    @ApiModelProperty("功能区域编码")
    private String districtCode;

    /**
     * 功能区域名称
     */
    @ApiModelProperty("功能区域名称")
    private String districtName;

    /**
     * 统计机构编码
     */
    @ApiModelProperty("统计机构编码")
    private String statOrgId;

    /**
     * 统计机构名称
     */
    @ApiModelProperty("统计机构名称")
    private String statOrgName;

    /**
     * 病历ID
     */
    @ApiModelProperty("病历ID")
    private String medicalId;

    /**
     * 门诊类型代码
     */
    @ApiModelProperty("门诊类型代码")
    private String tagCode;

    /**
     * 门诊类型名称
     */
    @ApiModelProperty("门诊类型名称")
    private String tagName;

    /**
     * 主索引居民ID
     */
    @ApiModelProperty("主索引居民ID")
    private String globalPersonId;

    /**
     * 标准化学校/单位ID
     */
    @ApiModelProperty("标准化学校/单位ID")
    private String stdCompanyId;

    /**
     * 标准化街道/区域ID
     */
    @ApiModelProperty("标准化街道/区域ID")
    private String stdStreetId;

    private Date fullDate;

    /**
     * 事件日期时间
     */
    private Date eventDatetime;

    /**
     * 病历来源类型
     */
    private String dataSrc;

    /**
     * ETL创建时间
     */
    private Date etlCreateDatetime;

    /**
     * ETL更新时间
     */
    private Date etlUpdateDatetime;

    private String livingAddrDetail;

    private String patientName;

    /**
     * 患者现住址
     */
    private String livingAddress;

    /**
     * 现住址经度
     */
    private BigDecimal livingAddressLongitude;

    /**
     * 现住址维度
     */
    private BigDecimal livingAddressLatitude;

    /**
     * 事件ID
     */
    private String eventId;
}

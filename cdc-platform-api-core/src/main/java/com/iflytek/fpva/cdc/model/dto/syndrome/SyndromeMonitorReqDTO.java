package com.iflytek.fpva.cdc.model.dto.syndrome;

import com.alibaba.fastjson.JSONObject;
import com.iflytek.fpva.cdc.entity.RegionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2023/11/2 18:47
 * @description:SyndromeMonitorReqDTO
 */
@Data
public class SyndromeMonitorReqDTO implements Serializable {


    @ApiModelProperty("查询开始日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty("症候群编码")
    private String syndromeCode;

    @ApiModelProperty("症候群名称")
    private String syndromeName;

    @ApiModelProperty("区域列表")
    List<RegionInfo> regionInfoList;

    @ApiModelProperty(value = "症候群编码集合",hidden = true)
    private List<String> symptom;
    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}

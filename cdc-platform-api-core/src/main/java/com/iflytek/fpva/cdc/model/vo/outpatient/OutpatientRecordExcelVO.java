package com.iflytek.fpva.cdc.model.vo.outpatient;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.Data;

@Data
public class OutpatientRecordExcelVO {

    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;

    @ExcelColumn(name = "性别", column = 2)
    private String sexDesc;

    @ExcelColumn(name = "年龄", column = 3)
    private String diagnoseAge;

    @ExcelColumn(name = "诊断", column = 4)
    private String diagnoseName;

    @ExcelColumn(name = "诊断时间", column = 5)
    private String outPatientTime;

    @ExcelColumn(name = "工作单位/学校", column = 6)
    private String companyName;

    @ExcelColumn(name = "来源", column = 7)
    private String orgName;

    public OutpatientRecordExcelVO(OutpatientRecordVO outpatientRecordVO) {
        this.patientName = outpatientRecordVO.getPatientName();
        this.sexDesc = outpatientRecordVO.getSexDesc();
        if (!StringUtils.isBlank(outpatientRecordVO.getDiagnoseAge())) {
            if (!StringUtils.isBlank(outpatientRecordVO.getDiagnoseAgeUnit())) {
                this.diagnoseAge = outpatientRecordVO.getDiagnoseAge() + outpatientRecordVO.getDiagnoseAgeUnit();
            }
        } else {
            this.diagnoseAge = "-";
        }
        this.diagnoseName = outpatientRecordVO.getDiagnoseName();
        this.outPatientTime = DateUtils.parseDate(outpatientRecordVO.getOutPatientTime());

        this.companyName = outpatientRecordVO.getCompanyName();

        this.orgName = outpatientRecordVO.getOrgName();
    }
}

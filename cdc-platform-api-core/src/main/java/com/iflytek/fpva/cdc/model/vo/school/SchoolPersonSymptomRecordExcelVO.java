package com.iflytek.fpva.cdc.model.vo.school;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.util.DateFormatUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import lombok.Data;

@Data
public class SchoolPersonSymptomRecordExcelVO {

    @ExcelColumn(name = "姓名", column = 1)
    private String patientName;

    @ExcelColumn(name = "性别", column = 2)
    private String sexDesc;

    @ExcelColumn(name = "年龄", column = 3)
    private String diagnoseAge;

    @ExcelColumn(name = "主要症状", column = 4)
    private String symptomType;

    @ExcelColumn(name = "症状日期", column = 5)
    private String fullDate;

    /**
     * 单位名称
     */
    @ExcelColumn(name = "学校", column = 6)
    private String schoolName;

    @ExcelColumn(name = "数据来源", column = 7)
    private String sourceTypeName;

    @ExcelColumn(name = "就诊机构", column = 8)
    private String visitOrgName;

    public static SchoolPersonSymptomRecordExcelVO fromEntity(SchoolPersonSymptomRecordVO schoolPersonSymptomRecordVO) {
        SchoolPersonSymptomRecordExcelVO schoolPersonSymptomRecordExcelVO = new SchoolPersonSymptomRecordExcelVO();

        schoolPersonSymptomRecordExcelVO.setPatientName(schoolPersonSymptomRecordVO.getPatientName());
        schoolPersonSymptomRecordExcelVO.setSexDesc(schoolPersonSymptomRecordVO.getSexDesc());
        schoolPersonSymptomRecordExcelVO.setSymptomType(schoolPersonSymptomRecordVO.getSymptomType());
        schoolPersonSymptomRecordExcelVO.setFullDate(DateFormatUtils.parseDate(schoolPersonSymptomRecordVO.getFullDate(), TimeConstant.NORM_DATE_PATTERN));
        schoolPersonSymptomRecordExcelVO.setSchoolName(StringUtils.isNotEmpty(schoolPersonSymptomRecordVO.getSchoolName())? schoolPersonSymptomRecordVO.getSchoolName() : "--");
        schoolPersonSymptomRecordExcelVO.setSourceTypeName(schoolPersonSymptomRecordVO.getSourceTypeName());
        schoolPersonSymptomRecordExcelVO.setVisitOrgName(StringUtils.isNotEmpty(schoolPersonSymptomRecordVO.getVisitOrgName()) ? schoolPersonSymptomRecordVO.getVisitOrgName() : "--");
        schoolPersonSymptomRecordExcelVO.setDiagnoseAge(schoolPersonSymptomRecordVO.getDiagnoseAge());
        if (!StringUtils.isEmpty(schoolPersonSymptomRecordVO.getDiagnoseAge())) {
            schoolPersonSymptomRecordExcelVO.setDiagnoseAge(schoolPersonSymptomRecordVO.getDiagnoseAge().concat(schoolPersonSymptomRecordVO.getDiagnoseAgeUnit()));
        }

        return schoolPersonSymptomRecordExcelVO;
    }
}

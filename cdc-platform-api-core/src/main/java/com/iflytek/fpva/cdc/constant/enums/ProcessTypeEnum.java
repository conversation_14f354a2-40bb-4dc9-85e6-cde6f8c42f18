package com.iflytek.fpva.cdc.constant.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 事件处理方式枚举
 */
public enum ProcessTypeEnum {

    STATUS("0", "状态处理"),
    TOP("1", "置顶"),
    ATTENTION("2", "标记"),
    CHECKED("3", "已审"),


    WINDOW_START("11","监测窗口起始点"),
    WINDOW_FLUCT("12","窗口期波动点"),

    FIRST_MEDICAL_RECORD("15","第1例病例"),
    N_MEDICAL_RECORD("16","第N例病例"),


    OUTCALL_PHONE("31","电话通知"),
    OUTCALL_SMS("32","短信通知"),
    ;

    private String code;
    private String name;

    ProcessTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        ProcessTypeEnum[] values = ProcessTypeEnum.values();

        Map<String, String> map = new HashMap<>();
        for (ProcessTypeEnum ageUnitEnum : values) {
            map.put(ageUnitEnum.getCode(), ageUnitEnum.getName());
        }

        return map.getOrDefault(code,code);
    }

}

package com.iflytek.fpva.cdc.service.symptom.impl;

import com.iflytek.fpva.cdc.apiService.AdminServiceApi;
import com.iflytek.fpva.cdc.apiService.UapServiceApi;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.TbCdcewFormConfig;
import com.iflytek.fpva.cdc.mapper.symptom.SymptomWarningEventMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympMedRelationMapper;
import com.iflytek.fpva.cdc.mapper.symptom.TbCdcewSympWarningEventMapper;
import com.iflytek.fpva.cdc.model.common.CommonEventPointVO;
import com.iflytek.fpva.cdc.model.dto.RelatedEventDto;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.resultmap.OrgMedCount;
import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.model.warningEvent.dto.MedicalListQueryDTO;
import com.iflytek.fpva.cdc.model.common.CommonWarningEventVO;
import com.iflytek.fpva.cdc.model.warningEvent.vo.*;
import com.iflytek.fpva.cdc.service.common.BusinessPersonService;
import com.iflytek.fpva.cdc.service.common.FormConfigService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.WarningEventService;
import com.iflytek.fpva.cdc.service.EventCommonUtilsService;
import com.iflytek.fpva.cdc.common.utils.DesensitizedUtils;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.SensitiveInterceptor.threadLocal;

@Service
public class SymptomWarningEventImpl implements WarningEventService {

    @Resource
    private TbCdcConfigService tbCdcConfigService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private BusinessPersonService businessPersonService;

    @Resource
    private EventCommonUtilsService eventCommonUtilsService;

    @Resource
    private SymptomWarningEventMapper symptomWarningEventMapper;

    @Resource
    private TbCdcewSympMedRelationMapper tbCdcewSympMedRelationMapper;

    @Resource
    private TbCdcewSympWarningEventMapper tbCdcewSympWarningEventMapper;

    @Resource
    private AdminServiceApi adminServiceApi;

    @Resource
    private FormConfigService formConfigService;

    @Override
    public String getWarningEventType() {
        return WarningTypeCodeEnum.SC_SYMPTOM.getName();
    }

    @Override
    public boolean isEnableAiScreen() {
        //是否能根据AI初筛进行排序  --  学校症状
        return eventCommonUtilsService.isEnableAiScreen(CommonConstants.CONFIG_KEY_SC_SYMPTOM_IS_AI_SCREEN_TOP);
    }

    @Override
    public List<WarningEventListVO> getWarningEventListBy(EventCriteria eventCriteria) {
        return tbCdcewSympWarningEventMapper.getWarningEventListBy(eventCriteria);
    }

    @Override
    public List<RelatedEventDto> getRelatedEvents(List<String> eventIds) {
        return Collections.emptyList();
    }

    @Override
    public List<SymptomMedicalInfoVO> getMedicalInfoListBy(MedicalListQueryDTO dto) {

        List<SymptomMedicalInfoVO> symptomMedicalInfoVOS = symptomWarningEventMapper.getSymptomMedicalInfoList(dto);
        symptomMedicalInfoVOS.forEach(elem -> {
            elem.setSymptomType(SymptomMedicalInfoVO.getSymptomTypeBy(elem.getSourceType(), elem.getSymptomContent(), elem.getSymptomName()));
            elem.setSourceTypeName(SymptomMedicalInfoVO.getSourceTypeNameBy(elem));
            elem.setDataSource(SymptomMedicalInfoVO.getSourceTypeNameBy(elem));
        });
        return symptomMedicalInfoVOS;
    }

    @Override
    public ResponseEntity<byte[]> exportEventMedicalList(List<Object> medicalList,MedicalListQueryDTO dto, String loginUserId) {

        List<SymptomMedicalInfoVO> medicalInfoList = medicalList.stream().map(o -> (SymptomMedicalInfoVO)o).collect(Collectors.toList());
        //校验是否超出文件导出最大值
        adminServiceApi.checkExportMax(medicalInfoList);
        //判断导出是否需要脱敏
//        boolean isDesensitized = tbCdcConfigService.isDesensitization(loginUserId);
//        if(isDesensitized){
//            medicalInfoList.forEach(DesensitizedUtils::desensitizedObject);
//        }
        boolean isDesensitized = threadLocal.get().getNormalFlag();
        boolean isDiagnoseRole = threadLocal.get().getDiagnoseVO().getDiagnoseFlag();

        for (SymptomMedicalInfoVO r : medicalInfoList) {
            if (isDesensitized){
                DesensitizedUtils.desensitizedObject(r);
            }
            if (isDiagnoseRole){
                DesensitizedUtils.desensitizedDiagnoseForVO(r,threadLocal.get().getDiagnoseVO().getDiagnoseNames());
            }
        }

        List<SymptomMedicalExcelVO> resultList = medicalInfoList.stream()
                                                                .map(SymptomMedicalExcelVO::new)
                                                                .collect(Collectors.toList());
        TbCdcewFormConfig formConfig = formConfigService.getConfigByLoginUserId(loginUserId, dto.getFormType());
        //没有列表表头配置时，直接置为null
        String formConfigDesc = formConfig == null ? null : formConfig.getConfigDesc();
        //获取导出列表表头
        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListByConfigDesc(SymptomMedicalExcelVO.class, formConfigDesc);
        return FileUtils.exportExcelByFormConfig(resultList, SymptomMedicalExcelVO.class, true, dto.getEventId() + ".xlsx", propertyMetaList);
    }

    @Override
    public EventTypeCountVO getEventTypeCountVO(String loginUserId, String loginUserName) {

        UapOrgPo userOrg = uapServiceApi.getUserOrg(loginUserName);
        List<String> diseaseCodes = businessPersonService.getCompletedDiseaseCodesByAuth(loginUserId, WarningTypeCodeEnum.SC_SYMPTOM.getType());

        return symptomWarningEventMapper.getEventTypeCount(userOrg.getProvinceCode(), userOrg.getCityCode(), userOrg.getDistrictCode(), diseaseCodes);
    }

    @Override
    public List<String> getCompanyByEventId(String eventId) {
        return tbCdcewSympMedRelationMapper.getCompanyByEventId(eventId);
    }

    @Override
    public <T extends CommonWarningEventVO> T getWarningEventMsgByEventId(String eventId) {

        return (T) tbCdcewSympWarningEventMapper.selectByPrimaryKey(eventId);
    }

    @Override
    public Double findByConfigKey(String diseaseCode) {
        //症状没有设置周边机构距离
        return 0.0;
    }

    @Override
    public List<OrgMedCount> getOrgMedCountByIds(Date statDate, String diseaseCode, List<String> orgIds) {
        return symptomWarningEventMapper.getOrgMedCountByIds(statDate, diseaseCode, orgIds);
    }

    @Override
    public List<CommonEventPointVO> getAllEventPoints(EventCriteria eventCriteria) {
        return tbCdcewSympWarningEventMapper.getAllEventPointsBy(eventCriteria);
    }
}

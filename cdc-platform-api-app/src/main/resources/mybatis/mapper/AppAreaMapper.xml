<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.app.mapper.AppAreaMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.app.entity.AppArea">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="operator" jdbcType="BIGINT" property="operator"/>
        <result column="operator_ip" jdbcType="VARCHAR" property="operatorIp"/>
        <result column="initial" jdbcType="VARCHAR" property="initial"/>
        <result column="areacode" jdbcType="VARCHAR" property="areacode"/>
        <result column="street" jdbcType="VARCHAR" property="street"/>
        <result column="district_abbreviation" jdbcType="VARCHAR" property="districtAbbreviation" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, code, province, city, parent_id, create_time, district, last_update_time, operator,
        operator_ip, initial, areacode, street, district_abbreviation
    </sql>

    <!--auto generated by MybatisCodeHelper on 2020-10-16-->
    <select id="findAllArea" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_district
    </select>
</mapper>
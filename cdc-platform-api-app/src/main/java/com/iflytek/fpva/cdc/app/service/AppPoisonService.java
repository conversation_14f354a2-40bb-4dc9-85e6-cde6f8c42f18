package com.iflytek.fpva.cdc.app.service;

import com.iflytek.fpva.cdc.app.model.vo.AppPoisonEventVO;
import com.iflytek.fpva.cdc.app.model.vo.AppPoisonMedicalVO;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonEventListQueryDTO;
import com.iflytek.fpva.cdc.model.vo.PageData;

/**
 * <AUTHOR>
public interface AppPoisonService {

    /**
     * 中毒预警信号已读处理
     * */
    void poisonEventProcessed(String eventId, String loginUserId);

    /**
     * 中毒信号病例已读处理
     * */
    void poisonMedicalProcessed(String eventId, String loginUserId, String personId);

    PageData<AppPoisonEventVO> getPoisonEventList(PoisonEventListQueryDTO dto, String loginUserName, String loginUserId);

    PageData<AppPoisonMedicalVO> getPoisonRecordList(String eventId, String loginUserId, String patientName, Integer pageIndex, Integer pageSize, Integer sortType);
}

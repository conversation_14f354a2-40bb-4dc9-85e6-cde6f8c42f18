package com.iflytek.fpva.cdc.app.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.fpva.cdc.model.vo.EventVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 移动端症候群事件VO
 */
@Data
public class AppInfectedEventVO {

    @ApiModelProperty("事件id")
    private String id;

    @ApiModelProperty("症状")
    private String symptom;

    @ApiModelProperty("事件发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date eventStartDate;

    @ApiModelProperty("事件终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date eventEndDate;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("区县名称")
    private String districtName;

    @ApiModelProperty("区县编码")
    private String districtCode;

    @ApiModelProperty("街道编码")
    private String statDimId;

    @ApiModelProperty("街道名称")
    private String statDimName;

    @ApiModelProperty("街道名称")
    private String streetName;

    @ApiModelProperty("信号点经度")
    private Double longitude;

    @ApiModelProperty("信号点纬度")
    private Double latitude;

    @ApiModelProperty("发生地点")
    private String place;

    @ApiModelProperty("关注度")
    private BigDecimal warningAttentionValue;

    @ApiModelProperty("关注度趋势")
    private String warningAttentionValueTrend;

    @ApiModelProperty("剩余时间（响应或处置时间）")
    private Long leftTime;

    @ApiModelProperty("处置机构名称/上报机构")
    private String operator;

    @ApiModelProperty("处理人")
    private String processor;

    @ApiModelProperty("处理时间")
    private Date processTime;

    @ApiModelProperty("外呼状态")
    private String outboundCallStatus;

    @ApiModelProperty("处理状态 0-未处理 1-处理中 2-人工排除 3-阳性事件 4-AI排除")
    private String processingStatus;

    @ApiModelProperty("是否AI初筛")
    private Integer isAiScreen;

    @ApiModelProperty("普通/标记/置顶 级别 0:普通 1:标记二 2:标记一 9:置顶")
    private Integer attentionLevel;

    @ApiModelProperty("审查级别 未审/已审 0:未审 1:已审")
    private Integer checkedLevel;

    @ApiModelProperty("病例数")
    private Integer medCount;

    @ApiModelProperty("今日病例数")
    private Integer todayMedicalCaseCn;

    @ApiModelProperty("昨日病例数")
    private Integer preMedicalCaseCn;

    @ApiModelProperty("病例数变化趋势")
    private String medicalCaseCnTrend;

    @ApiModelProperty("是否地域聚集")
    private String isCluster;

    @ApiModelProperty("增长率")
    private String growthRate;

    @ApiModelProperty("数据截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date until;

    @ApiModelProperty("预警类型名称")
    private String sponseTypeName;

    @ApiModelProperty("是否发起过流调")
    private Integer isEs;

    @ApiModelProperty("响应超时状态 0.未超时 1.响应超时")
    private Integer responseTimeOutStatus;

    @ApiModelProperty("处置超时状态 0.未超时 1.处置超时")
    private Integer processingTimeOutStatus;

    @ApiModelProperty("预警编号")
    private String eventNum;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty
    private Date createTime;

    @ApiModelProperty("疾病编码")
    private String infectedCode;

    @ApiModelProperty("传染病类别编码")
    private String infectedTypeCode;

    @ApiModelProperty("传染病类别名称")
    private String infectedTypeName;

    @ApiModelProperty("是否已读 0：未读 1：已读")
    private Integer isProcessed;

    @ApiModelProperty("病例列表")
    private List<?> medicalInfoVOList;

    public static AppInfectedEventVO fromEventVO(EventVO event) {
        AppInfectedEventVO appInfectedEventVO = new AppInfectedEventVO();

        appInfectedEventVO.setId(event.getId());
        appInfectedEventVO.setProvinceName(event.getProvinceName());
        appInfectedEventVO.setProvinceCode(event.getProvinceCode());
        appInfectedEventVO.setCityName(event.getCityName());
        appInfectedEventVO.setCityCode(event.getCityCode());
        appInfectedEventVO.setDistrictCode(event.getDistrictCode());
        appInfectedEventVO.setDistrictName(event.getDistrictName());
        appInfectedEventVO.setSymptom(event.getSymptom());
        appInfectedEventVO.setInfectedCode(event.getInfectedCode());
        appInfectedEventVO.setEventStartDate(event.getEventStartDate());
        appInfectedEventVO.setEventEndDate(event.getEventEndDate());
        appInfectedEventVO.setSponseTypeName(event.getSponseTypeName());
        appInfectedEventVO.setInfectedTypeCode(event.getInfectedTypeCode());
        appInfectedEventVO.setInfectedTypeName(event.getInfectedTypeName());
        appInfectedEventVO.setStatDimName(event.getStatDimName());
        appInfectedEventVO.setStatDimId(event.getStatDimId());
        appInfectedEventVO.setProcessingStatus(event.getProcessingStatus());
        appInfectedEventVO.setResponseTimeOutStatus(event.getResponseTimeOutStatus());
        appInfectedEventVO.setProcessingTimeOutStatus(event.getProcessingTimeOutStatus());
        appInfectedEventVO.setEventNum(event.getEventNum());
        appInfectedEventVO.setUpdateTime(event.getUpdateTime());
        appInfectedEventVO.setCreateTime(event.getCreateTime());
        appInfectedEventVO.setIsEs(event.getIsEs());
        appInfectedEventVO.setLeftTime(event.getLeftTime());
        appInfectedEventVO.setMedCount(event.getMedCount());
        appInfectedEventVO.setTodayMedicalCaseCn(Optional.ofNullable(event.getTodayMedicalCaseCn()).orElse(0));
        appInfectedEventVO.setPreMedicalCaseCn(Optional.ofNullable(event.getPreMedicalCaseCn()).orElse(0));
        appInfectedEventVO.setMedicalCaseCnTrend(event.getMedicalCaseCnTrend());
        appInfectedEventVO.setGrowthRate(event.getGrowthRate());
        appInfectedEventVO.setAttentionLevel(event.getAttentionLevel());
        appInfectedEventVO.setProcessor(event.getProcessor());
        appInfectedEventVO.setProcessTime(event.getProcessTime());

        return appInfectedEventVO;
    }

}

package com.iflytek.fpva.cdc.app.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.app.entity.TbCdcewAppUnknownReasonEventProcessed;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
public interface TbCdcewAppUnknownReasonEventProcessedMapper extends BaseMapper<TbCdcewAppUnknownReasonEventProcessed> {

    List<TbCdcewAppUnknownReasonEventProcessed> listByLoginUserId(@Param("loginUserId") String loginUserId);
}

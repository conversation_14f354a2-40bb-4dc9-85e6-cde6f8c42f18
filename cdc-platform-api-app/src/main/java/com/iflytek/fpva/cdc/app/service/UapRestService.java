package com.iflytek.fpva.cdc.app.service;


import com.iflytek.fpva.cdc.app.dto.EditPwdDto;
import com.iflytek.fpva.cdc.app.dto.LoginResult;
import com.iflytek.fpva.cdc.app.dto.UapLoginParam;
import com.iflytek.zhyl.uap.ext.pojo.Token;
import com.iflytek.zhyl.uap.usercenter.pojo.UapAuthResource;
import com.iflytek.zhyl.uap.usercenter.pojo.UapUserDto;

import java.util.List;


public interface UapRestService {

    /**
     * 刷新token
     *
     * @param refreshToken
     * @return
     */
    public Token refreshToken(String refreshToken, String accessToken);

    /**
     * 修改密码
     */
    void editPassword(EditPwdDto editPwdDto, Integer passWordType, Integer loginType);

    LoginResult getTokenFromUAP(UapLoginParam uapLoginParam, Integer loginType);

    /**
     * 获取用户对应应用下的权限
     */
    List<UapAuthResource> getUserAuthBy(String loginUserId);
}

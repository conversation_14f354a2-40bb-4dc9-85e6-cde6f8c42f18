package com.iflytek.fpva.cdc.app.controller;


import com.iflytek.fpva.cdc.app.model.vo.AppPoisonEventVO;
import com.iflytek.fpva.cdc.app.model.vo.AppPoisonMedicalVO;
import com.iflytek.fpva.cdc.app.service.AppPoisonService;
import com.iflytek.fpva.cdc.constant.enums.PoisonTypeEnum;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.entity.*;
import com.iflytek.fpva.cdc.model.vo.PatientExtendVO;
import com.iflytek.fpva.cdc.model.dto.poison.PoisonEventListQueryDTO;
import com.iflytek.fpva.cdc.model.vo.EventAnalysisResultVO;
import com.iflytek.fpva.cdc.model.vo.EventVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.vo.poison.*;
import com.iflytek.fpva.cdc.model.warningEvent.dto.EventProcessDTO;
import com.iflytek.fpva.cdc.service.WarningEventProcessCommonService;
import com.iflytek.fpva.cdc.service.common.HisMedicalInfoService;
import com.iflytek.fpva.cdc.service.poison.PoisonDetailService;
import com.iflytek.fpva.cdc.service.poison.PoisonEventService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.common.impl.HisMedicalInfoServiceImpl;
import com.iflytek.fpva.cdc.service.syndrome.TbCdcewEventAnalysisService;
import com.iflytek.fpva.cdc.util.DesensitizeVOUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "移动端-中毒类接口")
@RestController
public class AppPoisoningController {
    @Resource
    PoisonEventService poisonEventService;
    @Resource
    PoisonDetailService poisonDetailService;
    @Resource
    HisMedicalInfoService hisMedicalInfoService;
    @Resource
    TbCdcConfigService tbCdcConfigService;

    @Resource
    AppPoisonService appPoisonService;

    @Resource
    HisMedicalInfoServiceImpl hisMedicalInfoServiceImpl;
    @Resource
    private WarningEventProcessCommonService commonService;
    @Resource
    private TbCdcewEventAnalysisService tbCdcewEventAnalysisService;

    @PostMapping("/pt/v1/app/poison/eventList")
    @ApiOperation("获取中毒事件列表")
    public PageData<AppPoisonEventVO> getPoisonEventList(@RequestBody PoisonEventListQueryDTO dto,
                                                         @RequestParam String loginUserName,
                                                         @RequestParam String loginUserId) {
        return appPoisonService.getPoisonEventList(dto, loginUserName, loginUserId);
    }

    @GetMapping("/pt/v1/app/poison/personal/getPoisonNameList")
    @ApiOperation("查询中毒类型列表")
    public List<CascadeVO> getPoisonNameList(@RequestParam String loginUserId) {
        return poisonEventService.getPoisonNameListByLoginUserId(loginUserId);
    }
    @GetMapping("/pt/v1/app/poison/personal/getPoisonNameListForMenu")
    @ApiOperation("查询中毒类型列表")
    public List<CascadeVO> getPoisonNameListForMenu(@RequestParam String loginUserId) {
        return poisonEventService.getPoisonNameListForMenu(loginUserId);
    }

    @GetMapping("/pt/v1/app/poison/eventDetail/{eventId}")
    public EventVO eventDetail(@PathVariable String eventId,
                               @RequestParam String loginUserId) {
        return poisonDetailService.eventDetail(eventId, loginUserId);
    }


    @GetMapping("/pt/v1/app/poison/eventDetail/poisonRecordList")
    @ApiOperation("根据eventId获取中毒病历列表")
    public PageData<AppPoisonMedicalVO> getPoisonRecordList(@RequestParam String eventId,
                                                            @RequestParam String loginUserId,
                                                            @RequestParam(required = false) String patientName,
                                                            @RequestParam(required = false) Integer pageIndex,
                                                            @RequestParam(required = false) Integer pageSize,
                                                            @RequestParam(required = false) Integer sortType) {
        appPoisonService.poisonEventProcessed(eventId, loginUserId);
        return appPoisonService.getPoisonRecordList(eventId, loginUserId, patientName, pageIndex, pageSize, sortType);

    }

    @GetMapping("/pt/v1/app/poison/medicalDetail")
    @ApiOperation("获取中毒病历详情以及对应的诊断日志详情")
    public Object getMedicalDetail(@RequestParam List<String> sourceKeyLists,
                                   @RequestParam String loginUserId,
                                   @RequestParam String eventId,
                                   @RequestParam String personId) {

        // v1 中毒病例
        PatientExtendVO hisMedicalDetail = hisMedicalInfoService.getMedicalDetail(sourceKeyLists, loginUserId);
        appPoisonService.poisonMedicalProcessed(eventId, loginUserId, personId);
        if (hisMedicalDetail != null && CollectionUtils.isNotEmpty(hisMedicalDetail.getCdcHisMedicalInfo())) {
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                List<TbCdcewHisMedicalInfo> medicalInfos = hisMedicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(hisMedicalDetail.getPatientDetailVO());
            }
            return hisMedicalDetail;
        }

        //v2报卡病例
        String poisonTypeCode = poisonEventService.getPoisonTypeCodeByEventId(eventId);
        //职业中毒和放射性疾病的来源是病历
        if (poisonTypeCode.equals(PoisonTypeEnum.CAREER.getCode()) || poisonTypeCode.equals(PoisonTypeEnum.RADIATE.getCode())) {
            PatientExtendVO patientExtendVO = hisMedicalInfoService.getMedicalDetail(sourceKeyLists, loginUserId);
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                List<TbCdcewHisMedicalInfo> medicalInfos = patientExtendVO.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(patientExtendVO.getPatientDetailVO());
            }
            return patientExtendVO;
        }
        //高温中暑
        if (poisonTypeCode.equals(PoisonTypeEnum.HEATSTROKE.getCode())) {

            HeatstrokePoisonPatientExtendVO medicalDetail = hisMedicalInfoService.getHeatstrokeMedicalDetail(sourceKeyLists, loginUserId);
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                List<TbCdcewHisMedicalInfo> medicalInfos = medicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(medicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardHeatstrokeReport> heatstrokeReports = medicalDetail.getReportCardInfo();
                heatstrokeReports.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardHeatstrokeReport);
            }
            return medicalDetail;
        }
        //食物中毒
        if (poisonTypeCode.equals(PoisonTypeEnum.FOOD.getCode())) {

            FoodPoisonPatientExtendVO medicalDetail = hisMedicalInfoService.getFoodMedicalDetail(sourceKeyLists, loginUserId);
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                List<TbCdcewHisMedicalInfo> medicalInfos = medicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(medicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardFoodBorneReport> foodBorneReports = medicalDetail.getReportCardInfo();
                foodBorneReports.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardFoodBorneReport);
            }
            return medicalDetail;
        }
        //一氧化碳中毒
        if (poisonTypeCode.equals(PoisonTypeEnum.CARBON_MONOXIDE.getCode())) {

            CarbonMonoxidePoisonPatientExtendVO medicalDetail = hisMedicalInfoService.getCarbonMonoxideMedicalDetail(sourceKeyLists, loginUserId);
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                List<TbCdcewHisMedicalInfo> medicalInfos = medicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(medicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardCarbonMethysisReport> reportCardInfo = medicalDetail.getReportCardInfo();
                reportCardInfo.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardCarbonMethysisReport);
            }
            return medicalDetail;
        }
        //农药中毒
        if (poisonTypeCode.equals(PoisonTypeEnum.PESTICIDE.getCode())) {

            PesticidePoisonPatientExtendVO medicalDetail = hisMedicalInfoService.getPesticideMedicalDetail(sourceKeyLists, loginUserId);
            if (tbCdcConfigService.isDesensitization(loginUserId)) {
                List<TbCdcewHisMedicalInfo> medicalInfos = medicalDetail.getCdcHisMedicalInfo();
                medicalInfos.forEach(DesensitizeVOUtils::desensitizeTbcdcewHisMedicalInfo);
                DesensitizeVOUtils.desensitizePatientDetailVO(medicalDetail.getPatientDetailVO());
                List<TbCdcewRepcardPesticidePoisonReport> reportCardInfo = medicalDetail.getReportCardInfo();
                reportCardInfo.forEach(DesensitizeVOUtils::desensitizeTbCdcewRepcardPesticideReport);
            }
            return medicalDetail;
        }
        return null;
    }


    @PostMapping("/pt/v1/app/poison/eventProcess/{eventId}")
    @ApiOperation("手动改变中毒事件处理状态")
    public void startProcess(
            @PathVariable String eventId,
            @RequestParam String loginUserId,
            @RequestParam String loginUserName,
            @RequestParam int status) {
//        poisonEventService.startProcess(loginUserId, eventId, status, loginUserName);
        EventProcessDTO dto = new EventProcessDTO();
        dto.setWarningType(WarningTypeCodeEnum.POISON.getName());
        dto.setEventId(eventId);
        dto.setStatus(status);
        commonService.startProcess(dto, loginUserId, loginUserName);
    }

    @PostMapping("/pt/v1/app/poison/eventDetail/createAnalysisResult")
    @ApiOperation("新增综合研判结论")
    public String createAnalysisResult(@RequestBody EventAnalysisResultVO eventAnalysisResultVO,
                                       @RequestParam String loginUserId,
                                       @RequestParam String loginUserName) {
        eventAnalysisResultVO.setWarningType(WarningTypeCodeEnum.POISON.getName());
        return tbCdcewEventAnalysisService.eventAnalysis(eventAnalysisResultVO, loginUserId, loginUserName).getId();
    }
}

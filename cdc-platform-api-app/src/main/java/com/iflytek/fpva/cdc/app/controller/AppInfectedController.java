package com.iflytek.fpva.cdc.app.controller;

import com.iflytek.fpva.cdc.app.constant.AppConstant;
import com.iflytek.fpva.cdc.app.model.vo.AppInfectedEventVO;
import com.iflytek.fpva.cdc.app.model.vo.AppInfectedMedicalVO;
import com.iflytek.fpva.cdc.app.service.AppInfectedService;
import com.iflytek.fpva.cdc.app.service.EventOrCaseProcessedService;
import com.iflytek.fpva.cdc.constant.enums.WarningTypeCodeEnum;
import com.iflytek.fpva.cdc.model.dto.infected.InfectedEventListQueryDTO;
import com.iflytek.fpva.cdc.model.vo.EventAnalysisResultVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.model.warningEvent.dto.EventProcessDTO;
import com.iflytek.fpva.cdc.service.WarningEventProcessCommonService;
import com.iflytek.fpva.cdc.service.infected.CdcInfectedEventService;
import com.iflytek.fpva.cdc.service.syndrome.TbCdcewEventAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "移动端-传染病接口")
@Slf4j
public class AppInfectedController {

    @Resource
    private CdcInfectedEventService cdcInfectedEventService;

    @Resource
    private AppInfectedService appInfectedService;

    @Resource
    private EventOrCaseProcessedService eventOrCaseProcessedService;

    @Resource
    private WarningEventProcessCommonService commonService;
    @Resource
    private TbCdcewEventAnalysisService tbCdcewEventAnalysisService;

    @GetMapping("/pt/v1/app/syndrome/getInfectedList")
    @ApiOperation("传染病-获取传染病疾病列表")
    public List<CascadeVO> getInfectedList(@RequestParam String loginUserId) {
        return appInfectedService.getInfectedList(loginUserId);
    }

    @PostMapping("/pt/v1/app/infected/eventList")
    @ApiOperation("传染病-获取事件列表")
    public PageData<AppInfectedEventVO> getEventList(@Valid @RequestBody InfectedEventListQueryDTO dto,
                                                     @RequestParam String loginUserName,
                                                     @RequestParam String loginUserId) {
        return appInfectedService.getEventList(dto, loginUserName, loginUserId);
    }

    @GetMapping("/pt/v1/app/infected/eventDetail/{eventId}")
    @ApiOperation("传染病-获取事件详情")
    public AppInfectedEventVO eventDetail(@PathVariable String eventId,
                                          @RequestParam String loginUserName,
                                          @RequestParam String loginUserId,
                                          @RequestParam String isProcessed) {
        AppInfectedEventVO appInfectedEventVO = appInfectedService.eventDetail(eventId, loginUserName, loginUserId);
        // 执行事件已读操作
        if (AppConstant.STR_ZERO.equals(isProcessed)) {
            eventOrCaseProcessedService.infectiousEventProcessed(eventId, loginUserId);
        }
        return appInfectedEventVO;
    }

    @GetMapping("/pt/v1/app/infected/medicalList")
    @ApiOperation("传染病-获取病例列表")
    public PageData<AppInfectedMedicalVO> medicalList(@RequestParam String eventId,
                                                      @RequestParam String loginUserName,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam(name = "patientName", required = false) String patientName,
                                                      @RequestParam(required = false) Integer pageIndex,
                                                      @RequestParam(required = false) Integer pageSize) {
        return appInfectedService.medicalList(eventId, loginUserId, loginUserName, patientName, pageIndex, pageSize);
    }

    @GetMapping("/pt/v1/app/infected/medicalDetail")
    @ApiOperation("传染病-获取病历详情和上报卡详情")
    public Object getMedicalDetail(@RequestParam String eventId,
                                   @RequestParam List<String> sourceKeyLists,
                                   @RequestParam String loginUserName,
                                   @RequestParam String loginUserId,
                                   @RequestParam String isProcessed,
                                   @RequestParam(required = false) String caseId) {
        return appInfectedService.getMedicalDetail(eventId, sourceKeyLists, loginUserName, loginUserId, isProcessed, caseId);
    }


    @PostMapping("/pt/v1/app/infected/eventProcess/{eventId}")
    @ApiOperation("传染病-手动改变事件处理状态(关注操作)")
    public void infectedStartProcess(@PathVariable String eventId,
                                     @RequestParam String loginUserId,
                                     @RequestParam String loginUserName,
                                     @RequestParam int status) {
//        cdcInfectedEventService.startProcess(loginUserId, eventId, status, loginUserName);
        EventProcessDTO dto = new EventProcessDTO();
        dto.setWarningType(WarningTypeCodeEnum.INFECTIOUS.getName());
        dto.setEventId(eventId);
        dto.setStatus(status);
        commonService.startProcess(dto, loginUserId, loginUserName);
    }

    @PostMapping("/pt/v1/app/infected/createAnalysisResult")
    @ApiOperation("传染病-新增综合研判结论(排除操作)")
    public String infectedCreateAnalysisResult(@RequestBody EventAnalysisResultVO eventAnalysisResultVO,
                                               @RequestParam String loginUserId,
                                               @RequestParam String loginUserName) {
        eventAnalysisResultVO.setWarningType(WarningTypeCodeEnum.INFECTIOUS.getName());
        return tbCdcewEventAnalysisService.eventAnalysis(eventAnalysisResultVO, loginUserId, loginUserName).getId();
    }


}

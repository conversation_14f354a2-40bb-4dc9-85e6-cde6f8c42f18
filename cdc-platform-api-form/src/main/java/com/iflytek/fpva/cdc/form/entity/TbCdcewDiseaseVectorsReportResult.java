package com.iflytek.fpva.cdc.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@TableName("tb_cdcew_disease_vectors_report_result")
public class TbCdcewDiseaseVectorsReportResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableField("id")
    private String id;

    /**
     * 病媒上报id
     */
    @TableField("report_id")
    private String reportId;

    /**
     * 模板id
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 字段编码
     */
    @TableField("column_code")
    private String columnCode;

    /**
     * 字段值
     */
    @TableField("column_value")
    private String columnValue;

    /**
     * 字段值所在行
     */
    @TableField("column_value_row")
    private Integer columnValueRow;

    /**
     * 字段名称
     */
    @TableField("column_name")
    private String columnName;


}

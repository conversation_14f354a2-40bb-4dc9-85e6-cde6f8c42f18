package com.iflytek.fpva.cdc.form.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.form.dto.DiseaseVectorsQueryDto;
import com.iflytek.fpva.cdc.form.dto.SummaryDateDto;
import com.iflytek.fpva.cdc.form.entity.TbCdcewDiseaseVectorsReport;
import com.iflytek.fpva.cdc.form.param.QueryMajorFormParam;
import com.iflytek.fpva.cdc.form.vo.DiseaseVectorsReportLists;
import com.iflytek.fpva.cdc.form.vo.MajorReportVO;
import com.iflytek.fpva.cdc.form.vo.SummaryFormResult;

import java.util.List;

/**
 * <p>
 * 病媒上报 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TbCdcewDiseaseVectorsReportMapper extends BaseMapper<TbCdcewDiseaseVectorsReport> {

    List<DiseaseVectorsReportLists> queryReportResult(DiseaseVectorsQueryDto diseaseVectorsQueryDto);

    List<MajorReportVO> listMajorReportResult(QueryMajorFormParam pageParam);

    List<SummaryDateDto> cockroachSummaryResult(DiseaseVectorsQueryDto diseaseVectorsQueryDto);

    List<SummaryFormResult> getSummaryDetail(List<String> reportIds);

    void deleteLogicByReportId(String reportId);

}

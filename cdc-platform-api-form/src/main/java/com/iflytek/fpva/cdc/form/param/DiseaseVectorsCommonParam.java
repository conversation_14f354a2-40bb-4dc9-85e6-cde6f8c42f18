package com.iflytek.fpva.cdc.form.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
public class DiseaseVectorsCommonParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("上报id")
    private String reportId;

    @ApiModelProperty("模板id")
    private String templateId;

    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;

    /**
     * 区编码
     */
    @ApiModelProperty("区编码")
    private String districtCode;

    /**
     * 区名称
     */
    @ApiModelProperty("区名称")
    private String districtName;

    /**
     * 机构编码(监测单位)
     */
    @ApiModelProperty("机构编码(监测单位)")
    private String statDimId;

    /**
     * 机构名称(监测单位)
     */
    @ApiModelProperty("机构名称(监测单位)")
    private String statDimName;

    /**
     * 填表人
     */
    @ApiModelProperty("填表人")
    private String fillUser;

    /**
     * 填表单位id
     */
    @ApiModelProperty("填表单位id")
    private String fillDimId;

    /**
     * 填表单位名称
     */
    @ApiModelProperty("填表单位名称")
    private String fillDimName;

    /**
     * 填表时间
     */
    @ApiModelProperty("填表时间")
    @Pattern(regexp = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))))$"
            , message = "填表时间格式不准确")
    private String fillDate;


    /**
     * 发生单位名称
     */
    @ApiModelProperty("发生单位名称")
    private String occurDimName;

    /**
     * 调查时间
     */
    @ApiModelProperty("调查时间(监测时间)")
    @Pattern(regexp = "^(((20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))))$"
            , message = "调查时间格式不准确")
    private String fullDate;

    /**
     * 病媒品种 1. 鼠 2. 蚊 3. 蝇 4. 蟑螂 5. 蜱
     */
    @ApiModelProperty("病媒品种 1. 鼠 2. 蚊 3. 蝇 4. 蟑螂 5. 蜱 6. 霍乱 7. 炭疽 8. 流感 9. 登革热 10. 血吸虫病")
    @Range(min = 1, max = 5, message = "病媒品种传参错误")
    @NotNull(message = "请传病媒品种")
    private Integer surveyType;

    /**
     * 监测人
     */
    @ApiModelProperty("监测人")
    private String monitor;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String auditor;

    /**
     * 检测方法
     */
    @ApiModelProperty("检测方法 检测方法 10. 夹夜法 11. 笼夜法 12. 粘鼠板法 13. 路径法 21. 诱蚊灯法 22 CO2诱蚊灯法 23. 人诱停落法 24. 双层叠帐法 25. 布雷图指数法 26. 幼蚊（蛹）路径法 27. 幼蚊（蛹）勺捕法 31. 笼诱法 32. 目测法 41. 粘捕法 42. 目测法 51. 寄生蜱 52. 游离蜱 61.霍乱病例个案调查 62.水产品检测结果登记 71.动物疫情调查结果 81.流感样病例暴发疫情 82.流感样病例暴发采样 83.流感样病例调查\n" +
            "91.登革热入户调查 101.全国血吸虫病钉螺检测 102.全国血吸虫病流动人群检测 103. 全国血吸虫病家畜检测")
    @NotBlank(message = "请传检测方法")
    private String surveyMethod;

    /**
     * 表单编号
     */
    @ApiModelProperty("表单编号")
    private String formNumber;

    @ApiModelProperty("病媒上报详情")
    @NotEmpty(message = "请填写表格")
    private List<VectorReportDetailParam> vectorReportDetailList;

}

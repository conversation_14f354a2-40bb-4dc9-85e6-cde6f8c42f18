package com.iflytek.fpva.cdc.form.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.form.entity.TbCdcewDiseaseVectorsReportResult;
import com.iflytek.fpva.cdc.form.param.VectorReportDetailParam;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TbCdcewDiseaseVectorsReportResultMapper extends BaseMapper<TbCdcewDiseaseVectorsReportResult> {

    List<VectorReportDetailParam> getReportDetailList(List<String> reportIds);
}

package com.iflytek.fpva.cdc.form.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * @desc: 重大重点传染病查询传参
 * @author: junxiong3
 * @date: 2023/11/14 16:07
 */
@Data
public class QueryMajorFormParam implements Serializable {

    private static final long serialVersionUID = 4594157418559006298L;

    @ApiModelProperty(value = "监测(调查)开始时间")
    private String startDate;

    @ApiModelProperty(value = "监测(调查)结束时间")
    private String endDate;

    @ApiModelProperty(value = "填表开始时间")
    private String fillStartDate;

    @ApiModelProperty(value = "填表结束时间")
    private String fillEndDate;

    @ApiModelProperty("表单编号")
    private String formNumber;

    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("填表单位id")
    private String fillDimId;

    @ApiModelProperty("事件发生单位名称")
    private String occurDimName;

    @ApiModelProperty("监测单位id")
    private String statDimId;

    @ApiModelProperty("监测单位名称")
    private String statDimName;

    @ApiModelProperty("监测点县国标")
    private String monitorCountryStandard;

    @ApiModelProperty("监测点村国标")
    private String monitorVillageStandard;

    @ApiModelProperty("监测方式")
    private String monitorStyle;

    @ApiModelProperty(value = "检测方法 10. 夹夜法 11. 笼夜法 12. 粘鼠板法 13. 路径法 21. 诱蚊灯法 22 CO2诱蚊灯法 23. 人诱停落法 24. 双层叠帐法 25. 布雷图指数法 26. 幼蚊（蛹）路径法 27. 幼蚊（蛹）勺捕法 31. 笼诱法 32. 目测法 41. 粘捕法 42. 目测法 51. 寄生蜱 52. 游离蜱 61.霍乱病例个案调查 62.水产品检测结果登记 71.动物疫情调查结果 81.流感样病例暴发疫情 82.流感样病例暴发采样 83.流感样病例调查\n" +
            "91.登革热入户调查 101.全国血吸虫病钉螺检测 102.全国血吸虫病流动人群检测 103. 全国血吸虫病家畜检测")
    private String surveyMethod;

    @ApiModelProperty(value = "第几页")
    @Range(min = 1, message = "请传第几页")
    private int pageIndex;

    @Range(max = 40, min = 20, message = "页面大小范围20只40")
    @ApiModelProperty(value = "分页大小")
    private int pageSize;
}

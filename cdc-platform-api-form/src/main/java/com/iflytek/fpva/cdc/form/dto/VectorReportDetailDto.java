package com.iflytek.fpva.cdc.form.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @desc: 上报详情
 * @author: junxiong3
 * @date: 2023/10/24 14:37
 */
@Data
public class VectorReportDetailDto implements Serializable {
    private static final long serialVersionUID = 945453861357664056L;
    /**
     * 检测方法
     */
    @ApiModelProperty("检测方法")
    private String surveyMethod;

    /**
     * 上报id
     */
    @ApiModelProperty("上报id")
    private String reportId;

    /**
     * 分组值,默认和column_value的值相等,当数据为表格时，则为当前行分组key
     */
    @ApiModelProperty("分组值,默认和column_value的值相等,当数据为表格时，则为当前行分组key")
    private String groupKey;
    /**
     * 字段编码
     */
    @ApiModelProperty("字段编码")
    private String columnCode;

    /**
     * 字段值
     */
    @ApiModelProperty("字段值")
    private String columnValue;

    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    private String columnName;
}

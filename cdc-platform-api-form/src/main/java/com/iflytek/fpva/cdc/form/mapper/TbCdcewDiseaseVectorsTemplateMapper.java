package com.iflytek.fpva.cdc.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.form.entity.TbCdcewDiseaseVectorsTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 病媒填报模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TbCdcewDiseaseVectorsTemplateMapper extends BaseMapper<TbCdcewDiseaseVectorsTemplate> {

    TbCdcewDiseaseVectorsTemplate getTemplateByCode(@Param("code") String code);
}

package com.iflytek.fpva.cdc.form.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @desc:
 * @author: junxiong3
 * @date: 2023/10/25 10:13
 */
@Data
@ApiModel
public class BaseSummaryVO implements Serializable {

    @ApiModelProperty(value = "内容")
    private List<List<String>> contentList;
}

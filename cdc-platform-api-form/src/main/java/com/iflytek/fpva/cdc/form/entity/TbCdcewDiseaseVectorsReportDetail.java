package com.iflytek.fpva.cdc.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 病媒上报统计维度的详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_cdcew_disease_vectors_report_detail")
public class TbCdcewDiseaseVectorsReportDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableField("id")
    private String id;

    /**
     * 病媒上报id
     */
    @TableField("report_id")
    private String reportId;

    /**
     * 检测方法 10. 夹夜法 11. 笼夜法 12. 粘鼠板法 13. 路径法 21. 诱蚊灯法 22 CO2诱蚊灯法 23. 人诱停落法 24. 双层叠帐法
     * 25. 布雷图指数法 26. 幼蚊（蛹）路径法 27. 幼蚊（蛹）勺捕法 31. 笼诱法 32. 目测法 41. 粘捕法 42. 目测法 51. 寄生蜱 52. 游离蜱
     */
    @TableField("survey_method")
    private String surveyMethod;

    /**
     * 模板id
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 字段编码
     */
    @TableField("column_code")
    private String columnCode;

    /**
     * 字段值
     */
    @TableField("column_value")
    private String columnValue;

    /**
     * 字段名称
     */
    @TableField("column_name")
    private String columnName;

    /**
     * 分组值,默认和column_value的值相等,当数据为表格时，则为当前行分组key
     */
    @TableField("group_key")
    private String groupKey;


}

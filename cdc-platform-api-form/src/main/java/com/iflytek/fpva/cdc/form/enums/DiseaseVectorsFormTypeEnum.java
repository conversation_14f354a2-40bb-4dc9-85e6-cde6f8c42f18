package com.iflytek.fpva.cdc.form.enums;

import java.util.HashMap;
import java.util.Map;

public enum DiseaseVectorsFormTypeEnum {

    MOUSE_TABLE_CAGE("mouse_table_cage", "鼠-夹/笼/粘鼠板法-表单"),

    MOUSE_TABLE_PATH("mouse_table_path", "鼠-路径法-表单"),

    MOSQUITO_TABLE_CO2_ENV("mosquito_table_co2_env", "蚊-成蚊(CO2)诱蚊灯法-表单"),

    MOSQUITO_TABLE_PEOPLE_STOP("mosquito_table_people_stop", "蚊-人诱停落法/双层叠帐法-表单"),

    MOSQUITO_TABLE_BRETOU("mosquito_table_bretou", "蚊-布雷图指数法-表单"),

    MOSQUITO_TABLE_JUVENILEPUPA_PATH("mosquito_table_juvenilepupa_path", "蚊-幼蚊(蛹)路径法汇总-表单"),

    MOSQUITO_TABLE_JUVENILEPUPA_SPOON("mosquito_table_juvenilepupa_spoon", "蚊-幼蚊(蛹)勺捕法汇总-表单"),

    FLY_TABLE_CAGE("fly_table_cage", "蝇-笼诱法-表单"),

    FLY_TABLE_VISUAL("fly_table_visual", "蝇-目测法-表单"),

    COCKROACH_TABLE_STICK("cockroach_table_stick", "蟑螂-粘捕法-表单"),

    COCKROACH_TABLE_VISUAL("cockroach_table_visual", "蟑螂-目测法-表单"),

    TICK_TABLE_SURFACE("tick_table_surface", "蜱-寄生蜱-表单"),

    TICK_TABLE_DISSOCIATE("tick_table_dissociate", "蜱-游离蜱-表单");

    private final String code;
    private final String name;

    DiseaseVectorsFormTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        DiseaseVectorsFormTypeEnum[] values = DiseaseVectorsFormTypeEnum.values();
        Map<String, String> map = new HashMap<>();
        for (DiseaseVectorsFormTypeEnum diseaseVectorsFormTypeEnum : values) {
            map.put(diseaseVectorsFormTypeEnum.getCode(), diseaseVectorsFormTypeEnum.getName());
        }
        return map.get(code);
    }
}

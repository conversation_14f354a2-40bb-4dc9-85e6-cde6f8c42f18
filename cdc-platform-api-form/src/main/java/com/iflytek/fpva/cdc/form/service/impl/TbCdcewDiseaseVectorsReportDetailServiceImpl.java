package com.iflytek.fpva.cdc.form.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.constant.MouseConstant;
import com.iflytek.fpva.cdc.form.constant.DiseaseVectorsConstants;
import com.iflytek.fpva.cdc.form.dto.VectorReportDetailDto;
import com.iflytek.fpva.cdc.form.entity.TbCdcewDiseaseVectorsReportDetail;
import com.iflytek.fpva.cdc.form.enums.*;
import com.iflytek.fpva.cdc.form.mapper.TbCdcewDiseaseVectorsReportDetailMapper;
import com.iflytek.fpva.cdc.form.mapper.TbCdcewDiseaseVectorsReportMapper;
import com.iflytek.fpva.cdc.form.mapper.TbCdcewFormDictMapper;
import com.iflytek.fpva.cdc.form.service.CockroachReportDetailService;
import com.iflytek.fpva.cdc.form.service.FlyReportDetailService;
import com.iflytek.fpva.cdc.form.service.TbCdcewDiseaseVectorsReportDetailService;
import com.iflytek.fpva.cdc.form.service.TickReportDetailService;
import com.iflytek.fpva.cdc.form.vo.MonitorCheckedVO;
import com.iflytek.fpva.cdc.form.vo.SummaryFormResult;
import com.iflytek.fpva.cdc.form.vo.SummaryResultVO;
import com.iflytek.fpva.cdc.util.NumberUtil;
import com.iflytek.fpva.cdc.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 病媒上报统计维度的详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class TbCdcewDiseaseVectorsReportDetailServiceImpl extends ServiceImpl<TbCdcewDiseaseVectorsReportDetailMapper,
        TbCdcewDiseaseVectorsReportDetail> implements TbCdcewDiseaseVectorsReportDetailService {

    @Resource
    private TbCdcewDiseaseVectorsReportDetailMapper diseaseVectorsReportDetailMapper;

    @Resource
    private TbCdcewFormDictMapper formDictMapper;

    @Resource
    private TbCdcewDiseaseVectorsReportMapper tbCdcewDiseaseVectorsReportMapper;

    @Resource
    private CockroachReportDetailService cockroachReportDetailService;

    @Resource
    private FlyReportDetailService flyReportDetailService;

    @Resource
    private TickReportDetailService tickReportDetailService;

    public void batchInsert(List<TbCdcewDiseaseVectorsReportDetail> reportDetailList){
        saveBatch(reportDetailList);
    }

    @Override
    public Map<String , SummaryResultVO> mouseReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();

        List<VectorReportDetailDto> vectorReportDetailList = diseaseVectorsReportDetailMapper.mouseReportSummary(reportIdList);
        // 捕鼠方法数组
        Set<String> surveyMethodList = vectorReportDetailList.stream().map(VectorReportDetailDto::getSurveyMethod)
                .collect(Collectors.toSet());
        // 表单方法是否选中
        List<MonitorCheckedVO> monitorList = Lists.newArrayList();
        for (MouseMethodEnum mouseMethodEnum : MouseMethodEnum.values()) {
            MonitorCheckedVO monitorVo  = MonitorCheckedVO.builder().monitorCode(mouseMethodEnum.getCode())
                    .monitorName(mouseMethodEnum.getName()).checkFlag(false).build();
            if (surveyMethodList.contains(mouseMethodEnum.getCode())) {
                monitorVo.setCheckFlag(true);
            }
            monitorList.add(monitorVo);
        }
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(vectorReportDetailList)) {
            resultMap.put(DiseaseVectorsFormTypeEnum.MOUSE_TABLE_CAGE.getCode(), resultVo);
            return resultMap;
        }

        // 布夹环境数组
        List<String> surveyHabitatList = vectorReportDetailList.stream().map(VectorReportDetailDto::getGroupKey)
                .distinct().collect(Collectors.toList());

        Map<String, String> contentMap = vectorReportDetailList.stream().collect(Collectors.toMap(
                k -> StringUtils.mergeString(k.getSurveyMethod(), k.getGroupKey(), k.getColumnCode()),
                v -> v.getColumnValue(), (k1, k2) -> k1));

        List<Map<String, String>> contentList = Lists.newArrayList();
        for (String surveyHabitat : surveyHabitatList) {
            List<String> content = Lists.newArrayList();

            List<String> effectClipList = Lists.newArrayList();
            List<String> catchList= Lists.newArrayList();
            // 捕获率
            List<String> catchRateList= Lists.newArrayList();
            // 鼠种
            List<List<String>> mouseTypeList = Lists.newArrayList(Lists.newArrayList(), Lists.newArrayList(),
                    Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
            int i = 0;
            for (MouseMethodEnum mouseMethodEnum : MouseMethodEnum.values()) {
                // 有效夹/笼/板数
                String effectClipsKey = StringUtils.mergeString(mouseMethodEnum.getCode(), surveyHabitat,
                        MouseHeaderEnum.EFFECT_CLIPS_NUM.getCode());
                // 捕鼠数
                String catchesKey = StringUtils.mergeString(mouseMethodEnum.getCode(), surveyHabitat,
                        MouseHeaderEnum.CATCH_MOUSE_NUM.getCode());
                if (contentMap.containsKey(effectClipsKey)) {
                    effectClipList.add(contentMap.get(effectClipsKey));
                } else {
                    effectClipList.add(CommonConstants.STR_ZERO);
                }
                if (contentMap.containsKey(catchesKey)) {
                    catchList.add(contentMap.get(catchesKey));
                } else {
                    catchList.add(CommonConstants.STR_ZERO);
                }
                catchRateList.add(NumberUtil.scoreRate(catchList.get(i), effectClipList.get(i)).toString());

                // 鼠种类枚举
                int j = 0;
                for (MouseSpeciesEnum mouseSpeciesEnum : MouseSpeciesEnum.values()) {
                    String key = StringUtils.mergeString(mouseMethodEnum.getCode(), surveyHabitat,
                            mouseSpeciesEnum.getCode());
                    if (contentMap.containsKey(key)) {
                        mouseTypeList.get(j).add(contentMap.get(key));
                    } else {
                        mouseTypeList.get(j).add(CommonConstants.STR_ZERO);
                    }
                    j++;
                }
                i++;
            }
            content.add(formDictMapper.selectValueByCode(surveyHabitat));
            content.add(StringUtils.join(effectClipList, "/"));
            content.add(StringUtils.join(catchList, "/"));
            // 捕获率
            content.add(StringUtils.join(catchRateList, "/"));
            for (int j = 0; j < mouseTypeList.size(); j++) {
                content.add(StringUtils.join(mouseTypeList.get(j), "/"));
            }
            contentList.add(convertToMap(MouseHeaderEnum.getCodeList(), content));
        }
        List<VectorReportDetailDto> amountList = diseaseVectorsReportDetailMapper.mouseReportSummaryByCode(reportIdList);
        Map<String, String> amountMap = amountList.stream().collect(Collectors.toMap(k -> k.getColumnCode(), v -> v.getColumnValue()));
        // 合计
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(CommonConstants.AMOUNT_ROW);
        amountRow.add(amountMap.get(MouseTitleEnum.EFFECT_CLIPS_NUM.getCode()));
        amountRow.add(amountMap.get(MouseTitleEnum.CATCH_MOUSE_NUM.getCode()));
        amountRow.add(NumberUtil.scoreRate(amountRow.get(2), amountRow.get(1)).toString());
        // 鼠种类枚举
        for (MouseSpeciesEnum mouseSpeciesEnum : MouseSpeciesEnum.values()) {
            amountRow.add(amountMap.get(mouseSpeciesEnum.getCode()));
        }
        contentList.add(convertToMap(MouseHeaderEnum.getCodeList(), amountRow));
        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOUSE_TABLE_CAGE.getCode(), resultVo);
        return resultMap;
    }

    public Map<String, String> convertToMap(List<String> codeList, List<String> content) {
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < codeList.size(); i++) {
            map.put(codeList.get(i), content.get(i));
        }
        return map;
    }

    @Override
    public Map<String , SummaryResultVO> pathReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();

        List<String> surveyMethodList = Lists.newArrayList(MouseConstant.THIRTEEN);
        List<String> columnCodeList = Lists.newArrayList(MouseTitleEnum.PATH_CHECK_DISTANCE.getCode(),
                MouseTitleEnum.PATH_MOUSE_TRACE_NUM.getCode());
        List<VectorReportDetailDto> vectorReportDetailList = diseaseVectorsReportDetailMapper.reportSummary(
                surveyMethodList, reportIdList, columnCodeList);

        List<MonitorCheckedVO> monitorList = Lists.newArrayList();
        MonitorCheckedVO vo = MonitorCheckedVO.builder().monitorCode(MethodEnum.MOUSE_TRACE_PATH.getCode())
                .monitorName(MethodEnum.MOUSE_TRACE_PATH.getName()).build();
        vo.setCheckFlag(CollectionUtil.isNotEmpty(vectorReportDetailList) ? true : false);
        monitorList.add(vo);
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(vectorReportDetailList)) {
            resultMap.put(DiseaseVectorsFormTypeEnum.MOUSE_TABLE_PATH.getCode(), resultVo);
            return resultMap;
        }

        Map<String, String> contentMap = vectorReportDetailList.stream().collect(Collectors.toMap(
                k -> StringUtils.mergeString(k.getGroupKey(), k.getColumnCode()), v -> v.getColumnValue(), (k1, k2) -> k1));
        // 单位类型数组
        Set<String> unitTypeList = vectorReportDetailList.stream().map(VectorReportDetailDto::getGroupKey)
                .collect(Collectors.toSet());

        List<Map<String, String>> contentList = Lists.newArrayList();
        unitTypeList.forEach(unitType -> {
            List<String> list = Lists.newArrayList();
            // 单位类型
            list.add(formDictMapper.selectValueByCode(unitType));
            // 检查距离（千米）
            String distanceKey = StringUtils.mergeString(unitType, MouseTitleEnum.PATH_CHECK_DISTANCE.getCode());
            if (contentMap.containsKey(distanceKey)) {
                list.add(contentMap.get(distanceKey));
            } else {
                list.add(CommonConstants.STR_ZERO);
            }
            // 鼠迹数
            String mouseTraceKey = StringUtils.mergeString(unitType, MouseTitleEnum.PATH_MOUSE_TRACE_NUM.getCode());
            if (contentMap.containsKey(mouseTraceKey)) {
                list.add(contentMap.get(mouseTraceKey));
            } else {
                list.add(CommonConstants.STR_ZERO);
            }
            // 路径指数
            list.add(NumberUtil.divide(list.get(2), list.get(1)).toString());
            // 备注
            list.add("");
            contentList.add(convertToMap(MousePathHeaderEnum.getCodeList(), list));
        });

        // 合计对应列编码和值
        Map<String, String> amountMap = getAmountMap(surveyMethodList, reportIdList, columnCodeList);
        // 合计
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(CommonConstants.AMOUNT_ROW);
        amountRow.add(amountMap.get(MouseTitleEnum.PATH_CHECK_DISTANCE.getCode()));
        amountRow.add(amountMap.get(MouseTitleEnum.PATH_MOUSE_TRACE_NUM.getCode()));
        amountRow.add(NumberUtil.divide(amountRow.get(2), amountRow.get(1)).toString());
        amountRow.add("");
        contentList.add(convertToMap(MousePathHeaderEnum.getCodeList(), amountRow));
        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOUSE_TABLE_PATH.getCode(), resultVo);
        return resultMap;
    }


    @Override
    public Map<String , SummaryResultVO> co2ReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();
        // 表单方法是否选中
        List<MonitorCheckedVO> monitorList = Lists.newArrayList();

        List<String> surveyMethodList = Lists.newArrayList(MethodEnum.MOSQUITO_LURE_LAMP_ONE.getCode(),
                MethodEnum.MOSQUITO_LURE_LAMP_TWO.getCode());
        // 用于统计每个groupKey上报了多少次
        List<VectorReportDetailDto> lampDetailList = diseaseVectorsReportDetailMapper.listGroupReport(surveyMethodList
                , reportIdList);
        // 布灯数
        Map<String, Long> lampMap = lampDetailList.stream().collect(Collectors.groupingBy(VectorReportDetailDto::getGroupKey,
                Collectors.counting()));

        List<String> columnCodeList = Lists.newArrayList();
        columnCodeList.addAll(MosquitoTypeEnum.getCodes());
        columnCodeList.add(MosquitoTitleEnum.CO2_ENV_FEMALE.getCode());
        List<VectorReportDetailDto> reportDetailList = diseaseVectorsReportDetailMapper.reportSummary(surveyMethodList,
                reportIdList, columnCodeList);

        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.MOSQUITO_LURE_LAMP_ONE.getCode(),
                MethodEnum.MOSQUITO_LURE_LAMP_ONE.getName()));
        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.MOSQUITO_LURE_LAMP_TWO.getCode(),
                MethodEnum.MOSQUITO_LURE_LAMP_TWO.getName()));
        // 设置勾选方法值
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(reportDetailList)) {
            return resultMap;
        }

        // 环境类型数组
        List<String> surveyHabitatList = reportDetailList.stream().map(VectorReportDetailDto::getGroupKey)
                .distinct().collect(Collectors.toList());

        Map<String, String> contentMap = reportDetailList.stream().collect(Collectors.toMap(
                k -> StringUtils.mergeString(k.getGroupKey(), k.getColumnCode()), v -> v.getColumnValue(), (k1, k2) -> k1));

        List<Map<String, String>> contentList = Lists.newArrayList();
        for (String surveyHabitat : surveyHabitatList) {
            List<String> list = Lists.newArrayList();
            list.add(formDictMapper.selectValueByCode(surveyHabitat));
            list.add(lampMap.containsKey(surveyHabitat) ? String.valueOf(lampMap.get(surveyHabitat)) : CommonConstants.STR_ZERO);
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.CO2_ENV_FEMALE.getCode())));
            list.add(NumberUtil.divide(list.get(2), list.get(1)).toString());
            for (String code : MosquitoTypeEnum.getCodes()) {
                list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, code)));
            }
            contentList.add(convertToMap(MosquitoCoHeaderEnum.getCodeList(), list));
        }

        // 合计对应列编码和值
        Map<String, String> amountMap = getAmountMap(surveyMethodList, reportIdList, columnCodeList);
        // 合计
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(CommonConstants.AMOUNT_ROW);
        amountRow.add(String.valueOf(lampDetailList.size()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.CO2_ENV_FEMALE.getCode()));
        amountRow.add(NumberUtil.divide(amountRow.get(2), amountRow.get(1)).toString());
        for (String code : MosquitoTypeEnum.getCodes()) {
            amountRow.add(amountMap.get(code));
        }
        contentList.add(convertToMap(MosquitoCoHeaderEnum.getCodeList(), amountRow));

        resultVo.setMonitorCheckedVOList(monitorList);
        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_CO2_ENV.getCode(), resultVo);
        return resultMap;
    }

    public String getValueByMap(Map<String, String> contentMap, String key) {
        if (contentMap.containsKey(key)) {
            return contentMap.get(key);
        }
        return CommonConstants.STR_ZERO;
    }

    @Override
    public Map<String , SummaryResultVO> peopleStopReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();
        // 表单方法是否选中
        List<MonitorCheckedVO> monitorList = Lists.newArrayList();

        List<String> surveyMethodList = Lists.newArrayList(MethodEnum.PEOPLE_STOP_LURE_ONE.getCode(),
                MethodEnum.PEOPLE_STOP_LURE_TWO.getCode());
        // 构建查询条件
        List<String> columnCodeList = Lists.newArrayList();
        columnCodeList.add(MosquitoTitleEnum.PERSON_OR_ACCOUNT_NUM.getCode());
        columnCodeList.add(MosquitoTitleEnum.MOSQUITO_TYPE_THREE_FEMALE.getCode());
        columnCodeList.add(MosquitoTitleEnum.MOSQUITO_TYPE_FOUR_FEMALE.getCode());
        columnCodeList.add(MosquitoTitleEnum.MOSQUITO_AMOUNT.getCode());

        List<VectorReportDetailDto> reportDetailList = diseaseVectorsReportDetailMapper.reportSummary(surveyMethodList,
                reportIdList, columnCodeList);
        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.PEOPLE_STOP_LURE_ONE.getCode(),
                MethodEnum.PEOPLE_STOP_LURE_ONE.getName()));
        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.PEOPLE_STOP_LURE_TWO.getCode(),
                MethodEnum.PEOPLE_STOP_LURE_TWO.getName()));
        // 设置勾选方法值
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(reportDetailList)) {
            resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_PEOPLE_STOP.getCode(), resultVo);
            return resultMap;
        }
        // 环境类型数组
        List<String> surveyHabitatList = reportDetailList.stream().map(VectorReportDetailDto::getGroupKey)
                .distinct().collect(Collectors.toList());

        Map<String, String> contentMap = reportDetailList.stream().collect(Collectors.toMap(
                k -> StringUtils.mergeString(k.getGroupKey(), k.getColumnCode()), v -> v.getColumnValue(), (k1, k2) -> k1));

        List<Map<String, String>> contentList = Lists.newArrayList();
        for (String surveyHabitat : surveyHabitatList) {
            List<String> list = Lists.newArrayList();
            list.add(formDictMapper.selectValueByCode(surveyHabitat));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.PERSON_OR_ACCOUNT_NUM.getCode())));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.MOSQUITO_TYPE_THREE_FEMALE.getCode())));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.MOSQUITO_TYPE_FOUR_FEMALE.getCode())));
            list.add(NumberUtil.addInt(list.get(2), list.get(3)));
            list.add(NumberUtil.divide(list.get(4), list.get(1)).multiply(new BigDecimal(2)).toString());
            contentList.add(convertToMap(MosquitoPeopleStopHeaderEnum.getCodeList(), list));
        }
        // 合计对应列编码和值
        Map<String, String> amountMap = getAmountMap(surveyMethodList, reportIdList, columnCodeList);
          // 合计
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(CommonConstants.AMOUNT_ROW);
        amountRow.add(amountMap.get(MosquitoTitleEnum.PERSON_OR_ACCOUNT_NUM.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.MOSQUITO_TYPE_THREE_FEMALE.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.MOSQUITO_TYPE_FOUR_FEMALE.getCode()));
        amountRow.add(NumberUtil.addInt(amountRow.get(2), amountRow.get(3)));
        amountRow.add(NumberUtil.divide(amountRow.get(4), amountRow.get(1)).multiply(new BigDecimal(2)).toString());
        contentList.add(convertToMap(MosquitoPeopleStopHeaderEnum.getCodeList(), amountRow));
        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_PEOPLE_STOP.getCode(), resultVo);
        return resultMap;
    }


    public MonitorCheckedVO createMonitorEntity(List<VectorReportDetailDto> reportDetailList, String code,
                                                String name) {
        MonitorCheckedVO monitorVo  = MonitorCheckedVO.builder().monitorCode(code).monitorName(name).checkFlag(false).build();
        if (CollectionUtil.isEmpty(reportDetailList)) {
            return monitorVo;
        }
        // 人诱停方法数组
        Set<String> surveyMethodSets = new HashSet<>();
        reportDetailList.stream().forEach(reportDetail -> {
            List<String> splitList = Lists.newArrayList(reportDetail.getSurveyMethod().split(","));
            surveyMethodSets.addAll(splitList);
        });
        if (surveyMethodSets.contains(code)) {
            monitorVo.setCheckFlag(true);
        }
        return monitorVo;
    }

    /**
     * 合计对应列编码和值
     * @param surveyMethodList
     * @param reportIdList
     * @param columnCodeList
     * @return
     */
    public  Map<String, String> getAmountMap(List<String> surveyMethodList, List<String> reportIdList, List<String> columnCodeList) {
        List<VectorReportDetailDto> amountList = diseaseVectorsReportDetailMapper.reportSummaryByCode(surveyMethodList,
                reportIdList, columnCodeList);
        Map<String, String> amountMap = new HashMap<>();
        if (CollectionUtil.isEmpty(amountList)) {
            return amountMap;
        }
        amountMap = amountList.stream().collect(Collectors.toMap(k -> k.getColumnCode(),
                v -> v.getColumnValue()));
        return amountMap;
    }

    @Override
    public Map<String , SummaryResultVO> bretouReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();
        // 表单方法是否选中
        List<MonitorCheckedVO> monitorList = Lists.newArrayList();

        List<String> surveyMethodList = Lists.newArrayList(MethodEnum.BRETOU_INDEX.getCode());
        List<String> breAddress = Lists.newArrayList(MosquitoTitleEnum.BRE_ADDRESS_NUMBER.getCode());
        List<String> columnCodeList = Lists.newArrayList();
        columnCodeList.add(MosquitoTitleEnum.BRE_SURVEY_CONTAINER_NUM.getCode());
        columnCodeList.add(MosquitoTitleEnum.BRE_POSITIVE_CONTAINER_NUM.getCode());
        // 统计容器数和阳性容器数
        List<VectorReportDetailDto> reportDetailList = diseaseVectorsReportDetailMapper.reportSummary(surveyMethodList,
                reportIdList, columnCodeList);
        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.BRETOU_INDEX.getCode(),
                MethodEnum.BRETOU_INDEX.getName()));
        // 设置勾选方法值
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(reportDetailList)) {
            resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_BRETOU.getCode(), resultVo);
            return resultMap;
        }
        // 统计调查户数
        List<VectorReportDetailDto> householdList = diseaseVectorsReportDetailMapper.listGroupColumnCode(
                surveyMethodList, reportIdList, breAddress);
        Map<String, Long> householdMap = householdList.stream().collect(Collectors.groupingBy(
                VectorReportDetailDto::getGroupKey, Collectors.counting()));


        // 环境类型数组
        List<String> surveyHabitatList = reportDetailList.stream().map(VectorReportDetailDto::getGroupKey)
                .distinct().collect(Collectors.toList());

        Map<String, String> contentMap = reportDetailList.stream().collect(Collectors.toMap(k ->
                StringUtils.mergeString(k.getGroupKey(), k.getColumnCode()), v -> v.getColumnValue(), (k1, k2) -> k1));

        List<Map<String, String>> contentList = Lists.newArrayList();
        for (String surveyHabitat : surveyHabitatList) {
            List<String> list = Lists.newArrayList();
            String householdKey = StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.BRE_ADDRESS_NUMBER.getCode());
            list.add(formDictMapper.selectValueByCode(surveyHabitat));
            list.add(String.valueOf(householdMap.getOrDefault(householdKey, CommonConstants.ZERO)));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.BRE_SURVEY_CONTAINER_NUM.getCode())));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.BRE_POSITIVE_CONTAINER_NUM.getCode())));
            list.add(NumberUtil.divide(list.get(3), list.get(1)).toString());
            contentList.add(convertToMap(MosquitoBreTouHeaderEnum.getCodeList(), list));
        }
        // 合计对应列编码和值
        Map<String, String> amountMap = getAmountMap(surveyMethodList, reportIdList, columnCodeList);
        // 合计
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(CommonConstants.AMOUNT_ROW);
        amountRow.add(String.valueOf(householdList.size()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.BRE_SURVEY_CONTAINER_NUM.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.BRE_POSITIVE_CONTAINER_NUM.getCode()));
        amountRow.add(NumberUtil.divide(amountRow.get(3), amountRow.get(1)).toString());
        contentList.add(convertToMap(MosquitoBreTouHeaderEnum.getCodeList(), amountRow));

        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_BRETOU.getCode(), resultVo);
        return resultMap;
    }

    @Override
    public  Map<String , SummaryResultVO> juvenilePupaPathReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();
        // 表单方法是否选中
        List<MonitorCheckedVO> monitorList = Lists.newArrayList();

        List<String> surveyMethodList = Lists.newArrayList(MethodEnum.JUVENILE_PUPA_PATH.getCode());
        List<String> columnCodeList = Lists.newArrayList();
        columnCodeList.add(MosquitoTitleEnum.TRAVEL_DISTANCE.getCode());
        columnCodeList.add(MosquitoTitleEnum.SURVEYED_WATER_BODY_NUM.getCode());
        columnCodeList.add(MosquitoTitleEnum.POSITIVE_WATER_BODY_NUM.getCode());

        // 统计行走距离、调查水体数和阳性水体数
        List<VectorReportDetailDto> reportDetailList = diseaseVectorsReportDetailMapper.reportSummary(surveyMethodList,
                reportIdList, columnCodeList);

        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.JUVENILE_PUPA_PATH.getCode(),
                MethodEnum.JUVENILE_PUPA_PATH.getName()));
        // 设置勾选方法值
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(reportDetailList)) {
            resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_JUVENILEPUPA_PATH.getCode(), resultVo);
            return resultMap;
        }
        // 环境类型数组
        List<String> surveyHabitatList = reportDetailList.stream().map(VectorReportDetailDto::getGroupKey)
                .distinct().collect(Collectors.toList());

        Map<String, String> contentMap = reportDetailList.stream().collect(Collectors.toMap(k ->
                StringUtils.mergeString(k.getGroupKey(), k.getColumnCode()), v -> v.getColumnValue(), (k1, k2) -> k1));
        List<Map<String, String>> contentList = Lists.newArrayList();
        for (String surveyHabitat : surveyHabitatList) {
            List<String> list = Lists.newArrayList();
            list.add(formDictMapper.selectValueByCode(surveyHabitat));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.TRAVEL_DISTANCE.getCode())));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.SURVEYED_WATER_BODY_NUM.getCode())));
            list.add(getValueByMap(contentMap, StringUtils.mergeString(surveyHabitat, MosquitoTitleEnum.POSITIVE_WATER_BODY_NUM.getCode())));
            list.add(NumberUtil.divide(list.get(3), list.get(1)).toString());
            contentList.add(convertToMap(MosquitoJuvenilePupaPathHeaderEnum.getCodeList(), list));
        }

        // 合计对应列编码和值
        Map<String, String> amountMap = getAmountMap(surveyMethodList, reportIdList, columnCodeList);
        // 合计
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(CommonConstants.AMOUNT_ROW);
        amountRow.add(amountMap.get(MosquitoTitleEnum.TRAVEL_DISTANCE.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.SURVEYED_WATER_BODY_NUM.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.POSITIVE_WATER_BODY_NUM.getCode()));
        amountRow.add(NumberUtil.divide(amountRow.get(3), amountRow.get(1)).toString());
        contentList.add(convertToMap(MosquitoJuvenilePupaPathHeaderEnum.getCodeList(), amountRow));
        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_JUVENILEPUPA_PATH.getCode(), resultVo);
        return resultMap;
    }

    @Override
    public  Map<String , SummaryResultVO> juvenilePupaSpoonReportSummary(List<String> reportIdList) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();
        // 表单方法是否选中
        List<MonitorCheckedVO> monitorList = Lists.newArrayList();

        List<String> surveyMethodList = Lists.newArrayList(MethodEnum.JUVENILE_PUPA_SPOON_CATCH.getCode());
        List<String> columnCodeList = Lists.newArrayList();
        columnCodeList.add(MosquitoTitleEnum.SPOON_CATCH_NUM.getCode());
        columnCodeList.add(MosquitoTitleEnum.POSITIVE_SPOON_CATCH_NUM.getCode());
        columnCodeList.add(MosquitoTitleEnum.SPOON_CATCH_CULEX_MOSQUITO.getCode());
        columnCodeList.add(MosquitoTitleEnum.SPOON_CATCH_ANOPHELES_MOSQUITO.getCode());
        columnCodeList.add(MosquitoTitleEnum.SPOON_CATCH_AEDES_MOSQUITO.getCode());

        // 统计调查水体数
        List<String> waterBodyCodeList = Lists.newArrayList(MosquitoTitleEnum.SPOON_WATER_TYPE.getCode());
        List<VectorReportDetailDto> reportDetailList = diseaseVectorsReportDetailMapper.listReportGroupColumnCode(surveyMethodList,
                reportIdList, waterBodyCodeList);

        monitorList.add(createMonitorEntity(reportDetailList, MethodEnum.JUVENILE_PUPA_SPOON_CATCH.getCode(),
                MethodEnum.JUVENILE_PUPA_SPOON_CATCH.getName()));
        // 设置勾选方法值
        resultVo.setMonitorCheckedVOList(monitorList);
        if (CollectionUtil.isEmpty(reportDetailList)) {
            resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_JUVENILEPUPA_SPOON.getCode(), resultVo);
            return resultMap;
        }

        // 汇总列编码和值
        Map<String, String> amountMap = getAmountMap(surveyMethodList, reportIdList, columnCodeList);
        List<Map<String, String>> contentList = Lists.newArrayList();
        List<String> amountRow = Lists.newArrayList();
        amountRow.add(String.valueOf(reportDetailList.size()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.SPOON_CATCH_NUM.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.POSITIVE_SPOON_CATCH_NUM.getCode()));
        amountRow.add(NumberUtil.scoreRate(amountRow.get(2), amountRow.get(1)).toString());
        amountRow.add(amountMap.get(MosquitoTitleEnum.SPOON_CATCH_CULEX_MOSQUITO.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.SPOON_CATCH_ANOPHELES_MOSQUITO.getCode()));
        amountRow.add(amountMap.get(MosquitoTitleEnum.SPOON_CATCH_AEDES_MOSQUITO.getCode()));
        amountRow.add(NumberUtil.addInt(amountRow.get(4), amountRow.get(5), amountRow.get(6)));
        contentList.add(convertToMap(MosquitoJuvenilePupaSpoonHeaderEnum.getCodeList(), amountRow));
        resultVo.setDetailMapList(contentList);
        resultMap.put(DiseaseVectorsFormTypeEnum.MOSQUITO_TABLE_JUVENILEPUPA_SPOON.getCode(), resultVo);
        return resultMap;
    }

    @Override
    public Map<String, SummaryResultVO> flySummaryDetail(List<String> reportIds) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();

        List<SummaryFormResult> summaryFormResults = tbCdcewDiseaseVectorsReportMapper.getSummaryDetail(reportIds);
        if(CollectionUtils.isEmpty(summaryFormResults)){
            return null;
        }
        Map<String, List<SummaryFormResult>> formMap = summaryFormResults.stream().collect(Collectors.groupingBy(SummaryFormResult::getSurveyMethod));
        for(Map.Entry<String, List<SummaryFormResult>> entry : formMap.entrySet()) {
            //笼诱法
            if (DiseaseVectorsConstants.FLY_CAGE.equals(entry.getKey())) {
                Map<String, List<SummaryFormResult>> cageMap = new HashMap<>();
                cageMap.put(entry.getKey(), entry.getValue());
                resultVo = flyReportDetailService.getCageSummaryDetail(cageMap);
                resultMap.put(DiseaseVectorsFormTypeEnum.FLY_TABLE_CAGE.getCode(), resultVo);
            }
            //目测法
            if (DiseaseVectorsConstants.FLY_VISUAL.equals(entry.getKey())) {
                Map<String, List<SummaryFormResult>> visualMap = new HashMap<>();
                visualMap.put(entry.getKey(), entry.getValue());
                resultVo = flyReportDetailService.getVisualSummaryDetail(visualMap);
                resultMap.put(DiseaseVectorsFormTypeEnum.FLY_TABLE_VISUAL.getCode(), resultVo);
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, SummaryResultVO> cockroachSummaryDetail(List<String> reportIds) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();

        List<SummaryFormResult> summaryFormResults = tbCdcewDiseaseVectorsReportMapper.getSummaryDetail(reportIds);
        if(CollectionUtils.isEmpty(summaryFormResults)){
            return null;
        }
        Map<String, List<SummaryFormResult>> formMap = summaryFormResults.stream().collect(Collectors.groupingBy(SummaryFormResult::getSurveyMethod));
        for(Map.Entry<String, List<SummaryFormResult>> entry : formMap.entrySet()) {
            //粘捕法
            if (CockroachMonitorEnum.COCKROACH_STICK.getCode().equals(entry.getKey())) {
                Map<String, List<SummaryFormResult>> stickMap = new HashMap<>();
                stickMap.put(entry.getKey(), entry.getValue());
                resultVo = cockroachReportDetailService.getStickSummaryDetail(stickMap);
                resultMap.put(DiseaseVectorsFormTypeEnum.COCKROACH_TABLE_STICK.getCode(), resultVo);
            }
            //目测法
            if (CockroachMonitorEnum.COCKROACH_VISUAL.getCode().equals(entry.getKey())) {
                Map<String, List<SummaryFormResult>> visualMap = new HashMap<>();
                visualMap.put(entry.getKey(), entry.getValue());
                resultVo = cockroachReportDetailService.getVisualSummaryDetail(visualMap);
                resultMap.put(DiseaseVectorsFormTypeEnum.COCKROACH_TABLE_VISUAL.getCode(), resultVo);
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, SummaryResultVO> tickSummaryDetail(List<String> reportIds) {
        Map<String , SummaryResultVO> resultMap = new HashMap<>();
        SummaryResultVO resultVo = new SummaryResultVO();

        List<SummaryFormResult> summaryFormResults = tbCdcewDiseaseVectorsReportMapper.getSummaryDetail(reportIds);
        if(CollectionUtils.isEmpty(summaryFormResults)){
            return null;
        }
        Map<String, List<SummaryFormResult>> formMap = summaryFormResults.stream().collect(Collectors.groupingBy(SummaryFormResult::getSurveyMethod));
        for(Map.Entry<String, List<SummaryFormResult>> entry : formMap.entrySet()) {
            //寄生蜱
            if (DiseaseVectorsConstants.TICK_SURFACE.equals(entry.getKey())) {
                Map<String, List<SummaryFormResult>> surfaceMap = new HashMap<>();
                surfaceMap.put(entry.getKey(), entry.getValue());
                resultVo = tickReportDetailService.getSurfaceSummaryDetail(surfaceMap);
                resultMap.put(DiseaseVectorsFormTypeEnum.TICK_TABLE_SURFACE.getCode(), resultVo);
            }
            //游离蜱
            if (DiseaseVectorsConstants.TICK_DISSOCIATE.equals(entry.getKey())) {
                Map<String, List<SummaryFormResult>> visualMap = new HashMap<>();
                visualMap.put(entry.getKey(), entry.getValue());
                resultVo = tickReportDetailService.getDissociateSummaryDetail(visualMap);
                resultMap.put(DiseaseVectorsFormTypeEnum.TICK_TABLE_DISSOCIATE.getCode(), resultVo);
            }
        }
        return resultMap;
    }
}

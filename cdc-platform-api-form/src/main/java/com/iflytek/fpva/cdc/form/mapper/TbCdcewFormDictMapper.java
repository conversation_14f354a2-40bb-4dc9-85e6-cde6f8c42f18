package com.iflytek.fpva.cdc.form.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.entity.TbCdcewFormDict;
import com.iflytek.fpva.cdc.form.dto.FormDictDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 表单字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TbCdcewFormDictMapper extends BaseMapper<TbCdcewFormDict> {

    int insertIntoFormDict(List<TbCdcewFormDict> tbCdcewFormDictList);

    String selectValueByCode(@Param("code") String code);

    List<FormDictDto> getFormDictByGroupNames(List<String> groupNames);
}

package com.iflytek.fpva.cdc.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 病媒填报模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@TableName("tb_cdcew_disease_vectors_template")
public class TbCdcewDiseaseVectorsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("id")
    private String id;

    @TableField("template_code")
    private String templateCode;

    @TableField("template_name")
    private String templateName;

    /**
     * 模板json
     */
    @TableField("content")
    private String content;

    /**
     * 不同病媒的方法，例：鼠（夹/笼/粘鼠板）
     */
    @TableField("method")
    private String method;

    /**
     * 病媒种类
     */
    @TableField("type")
    private String type;

    /**
     * 删除标签
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

    @TableField("creator")
    private String creator;

    @TableField("updater")
    private String updater;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;


}

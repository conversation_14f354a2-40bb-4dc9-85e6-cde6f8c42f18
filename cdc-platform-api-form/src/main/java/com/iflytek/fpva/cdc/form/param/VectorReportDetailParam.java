package com.iflytek.fpva.cdc.form.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @desc: 病媒上报详情
 * @author: junxiong3
 * @date: 2023/10/18 15:10
 */
@Data
@ApiModel
public class VectorReportDetailParam implements Serializable {

    /**
     * 上报id
     */
    @ApiModelProperty("上报id")
    private String reportId;

    /**
     * 字段编码
     */
    @ApiModelProperty("字段编码")
    private String columnCode;

    /**
     * 字段值
     */
    @ApiModelProperty("字段值")
    private String columnValue;
    /**
     * 字段值所在行
     */
    @ApiModelProperty("字段值所在行")
    private Integer columnValueRow;
    /**
     * 字段名称
     */
    @ApiModelProperty("字段名称")
    private String columnName;


}

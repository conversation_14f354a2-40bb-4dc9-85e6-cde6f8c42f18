package com.iflytek.fpva.cdc.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 病媒上报
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@TableName("tb_cdcew_disease_vectors_report")
public class TbCdcewDiseaseVectorsReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField("id")
    private String id;

    /**
     * 省编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 省名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 区编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 区名称
     */
    @TableField("district_name")
    private String districtName;

    /**
     * 机构编码(监测单位)
     */
    @TableField("stat_dim_id")
    private String statDimId;

    /**
     * 机构名称(监测单位)
     */
    @TableField("stat_dim_name")
    private String statDimName;

    /**
     * 调查时间
     */
    @TableField("full_date")
    private String fullDate;

    /**
     * 病媒品种 1. 鼠 2. 蚊 3. 蝇 4. 蟑螂 5. 蜱
     */
    @TableField("survey_type")
    private Integer surveyType;

    /**
     * 监测人
     */
    @TableField("monitor")
    private String monitor;

    /**
     * 审核人
     */
    @TableField("auditor")
    private String auditor;

    /**
     * 调查年份
     */
    @TableField("survey_year")
    private Integer surveyYear;

    /**
     * 调查年月
     */
    @TableField("survey_year_month")
    private String surveyYearMonth;

    /**
     * 创建者,id
     */
    @TableField("creator")
    private String creator;

    /**
     * 信息创建日期时间
     */
    @TableField("create_datetime")
    private LocalDateTime createDatetime;

    /**
     * 更新人,id
     */
    @TableField("updator")
    private String updator;

    /**
     * 信息更新日期时间
     */
    @TableField("update_datetime")
    private LocalDateTime updateDatetime;

    /**
     * 删除标志(1/是;0/否)
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

    /**
     * 检测方法 10. 夹夜法 11. 笼夜法 12. 粘鼠板法 13. 路径法 21. 诱蚊灯法 22 CO2诱蚊灯法 23. 人诱停落法 24. 双层叠帐法 25. 布雷图指数法 26. 幼蚊（蛹）路径法 27. 幼蚊（蛹）勺捕法 31. 笼诱法 32. 目测法 41. 粘捕法 42. 目测法 51. 寄生蜱 52. 游离蜱 61.霍乱病例个案调查 62.水产品检测结果登记 71.动物疫情调查结果 81.流感样病例暴发疫情 82.流感样病例暴发采样 83.流感样病例调查	91.登革热入户调查 101.全国血吸虫病钉螺检测 102.全国血吸虫病流动人群检测 103. 全国血吸虫病家畜检测
     */
    @TableField("survey_method")
    private String surveyMethod;

    /**
     * 填表人
     */
    @TableField("fill_user")
    private String fillUser;

    /**
     * 填报时间
     */
    @TableField("fill_date")
    private String fillDate;

    /**
     * 填表单位id
     */
    @TableField("fill_dim_id")
    private String fillDimId;

    /**
     * 填表单位名称
     */
    @TableField("fill_dim_name")
    private String fillDimName;

    /**
     * 发生单位名称
     */
    @TableField("occur_dim_name")
    private String occurDimName;

    /**
     * 事件名称
     */
    @TableField("event_name")
    private String eventName;

    /**
     * 监测点县国标
     */
    @TableField("monitor_country_standard")
    private String monitorCountryStandard;

    /**
     * 监测点村国标
     */
    @TableField("monitor_village_standard")
    private String monitorVillageStandard;

    /**
     * 监测方式
     */
    @TableField("monitor_style")
    private String monitorStyle;

    /**
     * 表单编号
     */
    @TableField("form_number")
    private String formNumber;
}

package com.iflytek.fpva.cdc.form.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @desc:
 * @author: junxiong3
 * @date: 2023/10/24 14:57
 */
@Data
@ApiModel
public class ReportSummaryParam implements Serializable {

    private static final long serialVersionUID = 4653098994863153253L;
    /**
     * 上报id数组
     */
    @ApiModelProperty("检测方法")
    @NotEmpty(message = "请传上报id")
    private List<String> reportIdList;
}

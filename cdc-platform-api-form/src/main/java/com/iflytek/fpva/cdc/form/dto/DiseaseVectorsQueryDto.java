package com.iflytek.fpva.cdc.form.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
public class DiseaseVectorsQueryDto {

    private int pageIndex;

    @Range(max = 40, min = 20)
    private int pageSize;

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "区县编码")
    private String districtCode;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "病媒种类")
    private Integer surveyType;

    @ApiModelProperty(value = "检测方法 10. 夹夜法 11. 笼夜法 12. 粘鼠板法 13. 路径法 21. 诱蚊灯法 22 CO2诱蚊灯法 23. 人诱停落法 24. 双层叠帐法 25. 布雷图指数法 26. 幼蚊（蛹）路径法 27. 幼蚊（蛹）勺捕法 31. 笼诱法 32. 目测法 41. 粘捕法 42. 目测法 51. 寄生蜱 52. 游离蜱 61.霍乱病例个案调查 62.水产品检测结果登记 71.动物疫情调查结果 81.流感样病例暴发疫情 82.流感样病例暴发采样 83.流感样病例调查\n" +
            "91.登革热入户调查 101.全国血吸虫病钉螺检测 102.全国血吸虫病流动人群检测 103. 全国血吸虫病家畜检测")
    private String surveyMethod;
}

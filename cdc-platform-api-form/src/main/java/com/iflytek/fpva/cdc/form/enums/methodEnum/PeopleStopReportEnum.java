package com.iflytek.fpva.cdc.form.enums.methodEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @desc:
 * @author: junxiong3
 * @date: 2023/11/2 10:24
 */
public enum PeopleStopReportEnum {

    MONITORING_METHOD("monitoring_method", "监测方法"),
    ENVIRONMENT_TYPE("environment_type", "环境类型"),
    PEOPLE_STOP_START_DATE("people_stop_start_date", "起始时间"),
    PEOPLE_STOP_END_DATE("people_stop_end_date", "结束时间"),
    MOSQUITO_TYPE_THREE_FEMALE("mosquito_type_3_female", "雌-白纹伊蚊数"),
    MOSQUITO_TYPE_THREE_MALE("mosquito_type_3_male", "雄-白纹伊蚊数"),
    MOSQUITO_TYPE_FOUR_FEMALE("mosquito_type_4_female", "雌-埃及伊蚊数"),
    MOSQUITO_TYPE_FOUR_MALE("mosquito_type_4_male", "雄-埃及伊蚊数")
    ;

    private String code;
    private String name;

    PeopleStopReportEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static Map<String, String> convertToMap() {
        PeopleStopReportEnum[] values = PeopleStopReportEnum.values();
        Map<String, String> map = new HashMap<>();
        for (PeopleStopReportEnum value : values) {
            map.put(value.getCode(), value.getName());
        }
        return map;
    }

}

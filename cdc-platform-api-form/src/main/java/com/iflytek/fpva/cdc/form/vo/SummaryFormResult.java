package com.iflytek.fpva.cdc.form.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SummaryFormResult {

    @ApiModelProperty(value = "监测方法")
    private String surveyMethod;

    @ApiModelProperty(value = "字段编码")
    private String columnCode;

    @ApiModelProperty(value = "字段值")
    private String columnValue;

    @ApiModelProperty(value = "字段名称")
    private String columnName;

    @ApiModelProperty(value = "分组值")
    private String groupKey;

    @ApiModelProperty(value = "该字段值总和")
    private Double res;
}

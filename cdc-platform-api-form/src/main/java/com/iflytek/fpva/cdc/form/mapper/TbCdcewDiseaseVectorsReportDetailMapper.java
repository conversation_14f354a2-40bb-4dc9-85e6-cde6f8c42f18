package com.iflytek.fpva.cdc.form.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.form.dto.VectorReportDetailDto;
import com.iflytek.fpva.cdc.form.entity.TbCdcewDiseaseVectorsReportDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 病媒上报统计维度的详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface TbCdcewDiseaseVectorsReportDetailMapper extends BaseMapper<TbCdcewDiseaseVectorsReportDetail> {

    int batchInsertIntoReportDetail(List<TbCdcewDiseaseVectorsReportDetail> reportDetailList);

    List<VectorReportDetailDto> reportSummary(@Param("surveyMethodList") List<String> surveyMethodList,
                                              @Param("reportIdList") List<String> reportIdList,
                                              @Param("columnCodeList") List<String> columnCodeList);

    List<VectorReportDetailDto> reportSummaryByCode(@Param("surveyMethodList") List<String> surveyMethodList,
                                                    @Param("reportIdList") List<String> reportIdList,
                                                    @Param("columnCodeList") List<String> columnCodeList);

    List<VectorReportDetailDto> mouseReportSummary(@Param("reportIdList") List<String> reportIdList);

    List<VectorReportDetailDto> mouseReportSummaryByCode(@Param("reportIdList") List<String> reportIdList);

    /**
     * 用于统计每个groupKey上报了多少次（例如布灯数）
     * @param surveyMethodList
     * @param reportIdList
     * @return
     */
    List<VectorReportDetailDto> listGroupReport(@Param("surveyMethodList") List<String> surveyMethodList,
                                                @Param("reportIdList") List<String> reportIdList);



    /**
     * 用于统计每个groupKey的columnCode有多少个
     * @param surveyMethodList
     * @param reportIdList
     * @param columnCodeList
     * @return
     */
    List<VectorReportDetailDto> listGroupColumnCode(@Param("surveyMethodList") List<String> surveyMethodList,
                                                    @Param("reportIdList") List<String> reportIdList,
                                                    @Param("columnCodeList") List<String> columnCodeList);


    /**
     * 用于统计每次上报每个groupKey的columnCode有多少个
     * @param surveyMethodList
     * @param reportIdList
     * @param columnCodeList
     * @return
     */
    List<VectorReportDetailDto> listReportGroupColumnCode(@Param("surveyMethodList") List<String> surveyMethodList,
                                                          @Param("reportIdList") List<String> reportIdList,
                                                          @Param("columnCodeList") List<String> columnCodeList);

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.emergency.mapper.TbCdcewEmergencyEventTimelineMapper">

    <insert id="insertBatch">
        insert into tb_cdcew_emergency_event_timeline
        (id, event_id, moment_type_code, moment_type_name, moment_time, current_case_count, change_case_count,
        current_source_key_list, signal_id, event_type, notes, creator_id, creator, create_time)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.id}, #{item.eventId}, #{item.momentTypeCode}, #{item.momentTypeName}, #{item.momentTime},
            #{item.currentCaseCount}, #{item.changeCaseCount}, #{item.currentSourceKeyList}, #{item.signalId},
            #{item.eventType}, #{item.notes}, #{item.creatorId}, #{item.creator}, #{item.createTime})
        </foreach>
    </insert>


    <select id="groupByDateAndType" resultType="com.iflytek.fpva.cdc.warning.vo.MedChangeLogVO">
        select
        moment_time::date  as statDate,
        moment_type_name as changeType,
        sum(change_case_count) as changeCase
        from tb_cdcew_emergency_event_timeline
        where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="item" index="index">
            #{item}
        </foreach>
        and event_type = #{eventType}
        group by statDate, moment_type_name
    </select>

    <select id="listByEventIds"
            resultType="com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEventTimeline">
        SELECT
        id, event_id, moment_type_code, moment_type_name, moment_time, current_case_count, change_case_count,
        current_source_key_list, signal_id, event_type, notes, creator_id, creator, create_time
        FROM tb_cdcew_emergency_event_timeline
        WHERE event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="item" index="index">
            #{item}
        </foreach>
        <if test="momentTypes != null and momentTypes.size() > 0">
            AND moment_type_code IN
            <foreach collection="momentTypes" open="(" separator="," close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        and event_type = #{eventType}
        order by moment_time
    </select>

</mapper>
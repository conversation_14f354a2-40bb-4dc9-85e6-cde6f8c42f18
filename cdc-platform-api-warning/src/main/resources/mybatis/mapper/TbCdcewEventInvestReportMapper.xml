<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.emergency.mapper.TbCdcewEventInvestReportMapper">

    <insert id="upsert">
        insert into tb_cdcew_event_invest_report
        (id, event_id, fill_date, province_code, province_name, city_code, city_name, district_code, district_name,
        model_id, model_name, model_version_id, data_json, event_type, create_time, creator, creator_id, update_time,
        updater, updater_id)
        values
        (#{id}, #{eventId}, #{fillDate}, #{provinceCode}, #{provinceName}, #{cityCode}, #{cityName}, #{districtCode},
        #{districtName}, #{modelId}, #{modelName}, #{modelVersionId}, #{dataJson}, #{eventType}, #{createTime},
        #{creator}, #{creatorId}, #{updateTime}, #{updater}, #{updaterId})
        on conflict (id) do update set
        update_time = excluded.update_time,
        updater = excluded.updater,
        <if test="dataJson != null and dataJson != ''">
            data_json = excluded.data_json,
        </if>
        updater_id = excluded.updater_id
    </insert>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.warning.mapper.TbCdcewInfectedDiseaseInfoMapper">

    <sql id="Base_Column_List">
        id, infected_class_code, infected_class_name, disease_type_code, disease_type_name, parent_disease_id,
        parent_disease_code, parent_disease_name, level_type, disease_code, disease_name, order_flag,
        transmission_type_code, transmission_type_name, management_type_code, management_type_name, alias, notes,
        status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater
    </sql>

    <select id="findRepeatDiseaseBy"
            resultType="com.iflytek.fpva.cdc.warning.entity.TbCdcewInfectedDiseaseInfo">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_infected_disease_info
        where delete_flag = '0'
        and disease_code in
        <if test="diseaseCodes != null and diseaseCodes.size()>0">
            <foreach collection="diseaseCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="parentCode !=null and parentCode !=''">
            and ( parent_disease_id is null or parent_disease_id = #{parentCode} )
        </if>
    </select>

    <select id="findCodeByParent" resultType="com.iflytek.fpva.cdc.warning.entity.TbCdcewInfectedDiseaseInfo">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_infected_disease_info
        where delete_flag = '0'
        and ( parent_disease_id = #{parentCode} or disease_code = #{parentCode} )
    </select>

    <select id="findDiseaseCodes" resultType="java.lang.String">
        select disease_code
        from tb_cdcew_infected_disease_info
        where delete_flag = '0'
        and (disease_type_code = #{code} or parent_disease_id = #{code} or disease_code = #{code})
    </select>
    <select id="getDiagnosticCriteriaInfo"
            resultType="com.iflytek.fpva.cdc.reportcard.vo.DiagnosticCriteriaInfoVO">
        select
        i.id as diseaseCode,
        i.disease_name as diseaseName,
        m.monitor_definition as clinicalDiagnosisInfo
        from app.tb_cdcmr_infected_disease_info i
        left join app.tb_cdcmr_infected_disease_monitor m
        on i.id = m.infected_disease_info_id
        where i.delete_flag = '0'
        and m.delete_flag = '0'
        and m.infected_disease_info_id = #{diseaseCode}
        and m.title = '临床诊断病例'
    </select>

</mapper>
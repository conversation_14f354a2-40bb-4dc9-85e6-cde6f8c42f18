<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.reportcard.mapper.ReportUploadRecordMapper">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t1.id,t1.attachment_id,t1.report_card_id,t1.status,t1.name,t1.parent_name,t1.valid_cert_type,t1.valid_cert_number,t1.company,
        t1.phone,t1.attribution,t1.address_code,t1.address_name,t1.human_category,t1.cases_category,t1.cases_category2,t1.onset_date,
        t1.diagnose_time,t1.death_date,t1.disease_name,t1.disease_code,t1.revised_previous_disease,t1.revised_previous_diagnose_time,
        t1.revised_previous_check_time,t1.fill_doctor,t1.fill_date,t1.unit_code,t1.unit_name,t1.unit_type,t1.record_time,t1.record_user,
        t1.record_user_company,t1.district_check_time,t1.city_check_time,t1.province_check_time,t1.check_status,
        t1.revised_report_time,t1.revised_final_check_time,t1.final_check_death_time,t1.revised_user,t1.revised_user_company,
        t1.delete_time,t1.delete_user,t1.delete_user_company,t1.delete_reason,t1.remark,t1.create_time,t1.update_time,t1.creator_id,
        t1.creator,t1.global_person_id,t1.report_card_code,t1.sex_desc,t1.birthday,t1.age,t1.age_unit,t1.province_code,t1.province_name,
        t1.city_code,t1.city_name,t1.district_code,t1.district_name,t1.report_class,report_org_addr_province_code,
        report_org_addr_province,report_org_addr_city_code,report_org_addr_city,report_org_addr_district_code,report_org_addr_district,
        t1.life_id,t1.archive_id,t1.symptom_sign,t1.contact_history,t1.quarantine_flag,t1.card_delete_flag,t1.card_delete_remark,
        t1.card_exclude_flag,t1.card_exclude_remark,t1.report_card_class_type,t1.sentinel_title,t1.sentinel_code,t1.sentinel_region_name,
        t1.sentinel_region_code,t1.sentinel_address_detail
    </sql>
    <insert id="batchUpsert">
        insert into app.tb_cdcew_report_upload_record(id, attachment_id, report_card_id, status, name, parent_name,
        valid_cert_type, valid_cert_number, company, phone, attribution, address_code, address_name, human_category,
        cases_category, cases_category2, onset_date, diagnose_time, death_date, disease_name, disease_code,
        revised_previous_disease, revised_previous_diagnose_time, revised_previous_check_time, fill_doctor, fill_date,
        unit_code, unit_name, unit_type, record_time, record_user, record_user_company, district_check_time,
        city_check_time, province_check_time, check_status, revised_report_time, revised_final_check_time,
        final_check_death_time, revised_user, revised_user_company, delete_time, delete_user, delete_user_company,
        delete_reason, remark, create_time, update_time, creator_id, creator, global_person_id, report_card_code,
        sex_desc, birthday, age, age_unit, province_code, province_name, city_code, city_name, district_code,
        district_name,  report_class,report_org_addr_province_code,report_org_addr_province,
        report_org_addr_city_code,report_org_addr_city,report_org_addr_district_code,report_org_addr_district,
        life_id,archive_id)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.id}, #{item.attachmentId}, #{item.reportCardId}, #{item.status}, #{item.name}, #{item.parentName},
            #{item.validCertType}, #{item.validCertNumber}, #{item.company}, #{item.phone}, #{item.attribution},
            #{item.addressCode}, #{item.addressName}, #{item.humanCategory}, #{item.casesCategory},
            #{item.casesCategory2}, #{item.onsetDate}, #{item.diagnoseTime}, #{item.deathDate}, #{item.diseaseName},
            #{item.diseaseCode}, #{item.revisedPreviousDisease}, #{item.revisedPreviousDiagnoseTime},
            #{item.revisedPreviousCheckTime}, #{item.fillDoctor}, #{item.fillDate}, #{item.unitCode}, #{item.unitName},
            #{item.unitType}, #{item.recordTime}, #{item.recordUser}, #{item.recordUserCompany},
            #{item.districtCheckTime}, #{item.cityCheckTime}, #{item.provinceCheckTime}, #{item.checkStatus},
            #{item.revisedReportTime}, #{item.revisedFinalCheckTime}, #{item.finalCheckDeathTime}, #{item.revisedUser},
            #{item.revisedUserCompany}, #{item.deleteTime}, #{item.deleteUser}, #{item.deleteUserCompany},
            #{item.deleteReason}, #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.creatorId},
            #{item.creator}, #{item.globalPersonId}, #{item.reportCardCode}, #{item.sexDesc}, #{item.birthday},
            #{item.age}, #{item.ageUnit}, #{item.provinceCode}, #{item.provinceName}, #{item.cityCode},
            #{item.cityName}, #{item.districtCode}, #{item.districtName},
            #{item.reportClass},
            #{item.reportOrgAddrProvinceCode}, #{item.reportOrgAddrProvince}, #{item.reportOrgAddrCityCode},
            #{item.reportOrgAddrCity}, #{item.reportOrgAddrDistrictCode}, #{item.reportOrgAddrDistrict},
            #{item.lifeId}, #{item.archiveId}
            )
        </foreach>
        on conflict (id) do update set
        attachment_id = excluded.attachment_id,
        report_card_id = excluded.report_card_id,
        status = excluded.status,
        name = excluded.name,
        parent_name = excluded.parent_name,
        valid_cert_type = excluded.valid_cert_type,
        valid_cert_number = excluded.valid_cert_number,
        company = excluded.company,
        phone = excluded.phone,
        attribution = excluded.attribution,
        address_code = excluded.address_code,
        address_name = excluded.address_name,
        human_category = excluded.human_category,
        cases_category = excluded.cases_category,
        cases_category2 = excluded.cases_category2,
        onset_date = excluded.onset_date,
        diagnose_time = excluded.diagnose_time,
        death_date = excluded.death_date,
        disease_name = excluded.disease_name,
        disease_code = excluded.disease_code,
        revised_previous_disease = excluded.revised_previous_disease,
        revised_previous_diagnose_time = excluded.revised_previous_diagnose_time,
        revised_previous_check_time = excluded.revised_previous_check_time,
        fill_doctor = excluded.fill_doctor,
        fill_date = excluded.fill_date,
        unit_code = excluded.unit_code,
        unit_name = excluded.unit_name,
        unit_type = excluded.unit_type,
        record_time = excluded.record_time,
        record_user = excluded.record_user,
        record_user_company = excluded.record_user_company,
        district_check_time = excluded.district_check_time,
        city_check_time = excluded.city_check_time,
        province_check_time = excluded.province_check_time,
        check_status = excluded.check_status,
        revised_report_time = excluded.revised_report_time,
        revised_final_check_time = excluded.revised_final_check_time,
        final_check_death_time = excluded.final_check_death_time,
        revised_user = excluded.revised_user,
        revised_user_company = excluded.revised_user_company,
        delete_time = excluded.delete_time,
        delete_user = excluded.delete_user,
        delete_user_company = excluded.delete_user_company,
        delete_reason = excluded.delete_reason,
        remark = excluded.remark,
        create_time = excluded.create_time,
        update_time = excluded.update_time,
        creator_id = excluded.creator_id,
        creator = excluded.creator,
        global_person_id = excluded.global_person_id,
        report_card_code = excluded.report_card_code,
        sex_desc = excluded.sex_desc,
        birthday = excluded.birthday,
        age = excluded.age,
        age_unit = excluded.age_unit,
        province_code = excluded.province_code,
        province_name = excluded.province_name,
        city_code = excluded.city_code,
        city_name = excluded.city_name,
        district_code = excluded.district_code,
        district_name = excluded.district_name,
        report_class = excluded.report_class,
        report_org_addr_province_code = excluded.report_org_addr_province_code,
        report_org_addr_province = excluded.report_org_addr_province,
        report_org_addr_city_code = excluded.report_org_addr_city_code,
        report_org_addr_city = excluded.report_org_addr_city,
        report_org_addr_district_code = excluded.report_org_addr_district_code,
        report_org_addr_district = excluded.report_org_addr_district,
        life_id = excluded.life_id,
        archive_id = excluded.archive_id
    </insert>

    <select id="findList" resultType="com.iflytek.fpva.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="Base_Column_List"/>
        from app.tb_cdcew_report_upload_record  t1
        join app.tb_cdcmr_infected_disease_info idi on t1.disease_code = idi.id
        where 1=1
        <if test="casesCategory != null and casesCategory!=''">
            and t1.cases_category = #{casesCategory}
        </if>
        <if test="attribution != null and attribution!=''">
            and t1.attribution = #{attribution}
        </if>

        <if test="status != null and status!=''">
            and t1.status = #{status}
        </if>
        <if test="checkStatus != null and checkStatus!=''">
            and t1.check_status = #{checkStatus}
        </if>
        <if test="reportCardId != null and reportCardId!=''">
            and t1.report_card_id like concat('%',#{reportCardId},'%')
        </if>
        <if test="name != null and name!=''">
            and t1.name like concat('%',#{name},'%')
        </if>
        <if test="validCertNumber != null and validCertNumber!=''">
            and t1.valid_cert_number like concat('%',#{validCertNumber},'%')
        </if>
        <if test="diseaseName != null and diseaseName!=''">
            and t1.disease_name like concat('%',#{diseaseName},'%')
        </if>
        <if test="unitName != null and unitName!=''">
            and t1.unit_name like concat('%',#{unitName},'%')
        </if>
        <if test="queryKey!= null and queryKey!=''">
            and (t1.name like concat('%',#{queryKey},'%')
            or t1.report_card_id like concat('%',#{queryKey},'%')
            or t1.disease_name like concat('%',#{queryKey},'%')
            or t1.valid_cert_number like concat('%',#{queryKey},'%')
            or t1.unit_name like concat('%',#{queryKey},'%')
            or t1.address_name like concat('%',#{queryKey},'%')
            )
        </if>
        <if test="addressName != null and addressName!=''">
            and t1.address_name like concat('%',#{addressName},'%')
        </if>
        <choose>
            <when test="timeType == 1">
                <if test="startDate != null">
                    and t1.diagnose_time::date >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.diagnose_time::date &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 2">
                <if test="startDate != null">
                    and t1.fill_date::date >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.fill_date::date &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 4">
                <if test="startDate != null">
                    and t1.onset_date::date >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.onset_date::date &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 5">
                <if test="startDate != null">
                    and t1.revised_final_check_time::date >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.revised_final_check_time::date &lt;= #{endDate}
                </if>
            </when>
        </choose>

            <if test="infectCodeList !=null and infectCodeList.size() &gt; 0">
                and (idi.id in
                <foreach item="item" index="index" collection="infectCodeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                or idi.parent_disease_id in
                <foreach item="item" index="index" collection="infectCodeList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
        <if test="infectTypeList !=null and infectTypeList.size() &gt; 0">
            and idi.disease_type_code in
            <foreach item="item" index="index" collection="infectTypeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="reportCardDataType != 0">
            <include refid="reportCardDataType"/>
        </if >
        <if test="reportCardDataType == 0">
            <if test="provinceCode != null and provinceCode!=''">
                and t1.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode!=''">
                and t1.city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode!=''">
                and t1.district_code = #{districtCode}
            </if>
            <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                and t1.report_org_addr_province_code = #{reportOrgAddrProvinceCode}
            </if>
            <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                and t1.report_org_addr_city_code = #{reportOrgAddrCityCode}
            </if>
            <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                and t1.report_org_addr_district_code = #{reportOrgAddrDistrictCode}
            </if>
        </if>
        order by t1.record_time desc


    </select>
    <select id="findByReportCardId" resultType="com.iflytek.fpva.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="Base_Column_List"/>
        from app.tb_cdcew_report_upload_record t1
        where t1.report_card_id = #{reportCardId}
        limit 1
    </select>

    <select id="findByRuleDiseaseCode"
            resultType="com.iflytek.fpva.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="Base_Column_List"/>
        from app.tb_cdcew_report_upload_record  t1
        where 1=1
        and  t1.disease_code in
            <foreach item="item" index="index" collection="diseaseCodes"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        and t1.record_time::date between #{startDate} and #{endDate}

    </select>
    <sql id="reportCardDataType">
        <trim prefix="and">
            <if test="reportCardDataType == 1">
                <choose>
                    <when test="regionLevel == 1">
                        t1.report_org_addr_province_code != #{reportOrgAddrProvinceCode} and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.report_org_addr_province_code = #{provinceCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.city_code != #{reportOrgAddrCityCode} and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_org_addr_province_code != #{reportOrgAddrProvinceCode}
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.district_code != #{reportOrgAddrDistrictCode}
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.report_org_addr_province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.district_code != #{reportOrgAddrDistrictCode} and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        and t1.city_code != #{reportOrgAddrCityCode} and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        t1.report_org_addr_province_code != #{reportOrgAddrProvinceCode} and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.report_org_addr_province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="reportCardDataType == 2">
                <choose>
                    <when test="regionLevel == 1">
                        t1.report_org_addr_province_code = #{provinceCode} and t1.report_addr_province_code != #{provinceCode}
                    </when>
                    <when test="regionLevel == 2">
                        t1.city_code = #{cityCode} and t1.report_addr_city_code != #{cityCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.province_code = #{provinceCode}
                            and t1.report_addr_province_code != #{provinceCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                            and t1.report_addr_district_code != #{districtCode}
                        </if>
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.district_code = #{provinceCode} and t1.report_addr_district_code != #{provinceCode}
                        and t1.city_code = #{cityCode} and t1.report_addr_city_code != #{cityCode}
                        t1.province_code = #{districtCode} and t1.report_addr_province_code != #{districtCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="reportCardDataType == 3">
                <choose>
                    <when test="regionLevel == 1">
                        t1.province_code = #{provinceCode}
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.province_code = #{provinceCode}
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.province_code = #{provinceCode}
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="reportCardDataType == 4">
                <choose>
                    <when test="regionLevel == 1">
                        t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                </choose>
            </if>
        </trim>
    </sql>
</mapper>

package com.iflytek.fpva.cdc.warning.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.fpva.cdc.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_cdcew_signal_judge_process")
public class TbCdcewSignalJudgeProcess extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "tb_cdcew_signal_judge_process";

    /**
     * 研判流程名称
     */
    @TableField("judge_process_name")
    @ApiModelProperty("研判流程名称")
    private String judgeProcessName;

    /**
     * 研判角色code
     */
    @TableField("expert_category_codes")
    @ApiModelProperty("研判角色code")
    private String expertCategoryCodes;

    /**
     * 研判角色
     */
    @TableField("expert_categories")
    @ApiModelProperty("研判角色")
    private String expertCategories;

    /**
     * 研判专家code
     */
    @TableField("judge_expert_codes")
    @ApiModelProperty("研判专家code")
    private String judgeExpertCodes;

    /**
     * 研判专家
     */
    @TableField("judge_experts")
    @ApiModelProperty("研判专家")
    private String judgeExperts;

    /**
     * 初步诊断
     */
    @TableField("init_diagnose")
    @ApiModelProperty("初步诊断")
    private String initDiagnose;

    /**
     * 风险等级
     */
    @TableField("risk_level")
    @ApiModelProperty("风险等级")
    private String riskLevel;

    /**
     * 预警类型
     */
    @TableField("warning_type")
    @ApiModelProperty("预警类型")
    private String warningType;

    /**
     * 专家信息
     * */
    @TableField("expert_info")
    @ApiModelProperty("专家信息")
    private String expertInfo;

    /**
     * 研判处理时限
     * */
    @TableField("judge_deadline")
    @ApiModelProperty("研判处理时限")
    private Integer judgeDeadline;

}

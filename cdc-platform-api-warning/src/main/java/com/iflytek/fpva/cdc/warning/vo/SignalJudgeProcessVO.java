package com.iflytek.fpva.cdc.warning.vo;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("信号研判流程vo")
@Data
public class SignalJudgeProcessVO {

    /**
     * 研判流程id
     */
    @ApiModelProperty("研判流程id")
    private String id;
    /**
     * 研判流程名称
     */
    @ApiModelProperty("研判流程名称")
    private String judgeProcessName;

    /**
     * 初步诊断
     */
    @ApiModelProperty("初步诊断")
    private String initDiagnose;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("预警类型")
    private String warningType;

    /**
     * 研判角色code列表
     */
    @ApiModelProperty("研判角色code列表")
    private List<String> expertCategoryCodes;
    /**
     * 研判角色列表
     */
    @ApiModelProperty("研判角色列表")
    private List<String> expertCategories;
    /**
     * 研判专家code列表
     */
    @ApiModelProperty("研判专家code列表")
    private List<String> judgeExpertCodes;
    /**
     * 研判专家列表
     */
    @ApiModelProperty("研判专家列表")
    private List<String> judgeExperts;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creator;
    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private Date updateTime;
    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人")
    private String updater;
    /**
     * 研判时限
     */
    @ApiModelProperty("研判时限-（）小时")
    private String judgeDeadline;

    /**
     * 专家信息
     * */
    @ApiModelProperty("该研判流程的专家信息")
    private String expertInfo;

}

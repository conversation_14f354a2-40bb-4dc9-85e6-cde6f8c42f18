package com.iflytek.fpva.cdc.warning.dto;

import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("预警事件推送参数")
@Data
public class PushEmergencyEventDTO extends TbCdcewEmergencyEvent {
    /**
     * 信号id
     */
    @ApiModelProperty(value = "信号id")
    private String signalId;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    private String warningType;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;
}

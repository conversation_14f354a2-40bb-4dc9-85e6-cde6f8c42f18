package com.iflytek.fpva.cdc.warning.entity;

import lombok.Data;

import java.util.Date;

/**
 * 传染病信号-病例关联表
 */
@Data
public class TbCdcewInfectedWarningSignalRelation {

    public static final String TABLE_NAME = "tb_cdcew_infected_warning_signal_relation";

    /**
     * 主键
     */
    private String id;

    /**
     * 信号id
     */
    private String signalId;

    /**
     * 信号详情id
     */
    private String detailId;

    /**
     * 日期
     */
    private Date fullDate;

    /**
     * 预警地点编码：地点/区划/单位/学校等
     */
    private String statDimId;

    /**
     * 预警地点名称：地点/区划/单位/学校等
     */
    private String statDimName;

    /**
     * 疾病编码
     */
    private String diseaseCode;

    /**
     * 疾病名称
     */
    private String diseaseName;

    /**
     * 源数据类型:病程;报卡
     */
    private String sourceKeyType;

    /**
     * 源数据唯一键
     */
    private String sourceKey;

    /**
     * 病例疾病状态
     */
    private String diseaseStatus;

    /**
     * 创建时间
     */
    private Object createTime;

    /**
     * 更新时间
     */
    private Object updateTime;
}
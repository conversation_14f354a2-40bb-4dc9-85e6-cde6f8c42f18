package com.iflytek.fpva.cdc.warning.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 新发突发传染病信号病例关联表;
 * <AUTHOR> dingyuan
 * @date : 2025-7-8
 */
@ApiModel(value = "新发突发传染病信号病例关联表")
@Data
public class TbCdcewEmergingWarningSignalRelation implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id ;

    /**
     * 信号id
     */
    @ApiModelProperty(value = "信号id")
    private String signalId ;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Date fullDate ;

    /**
     * 预警地点编码：地点/区划/单位/学校等
     */
    @ApiModelProperty(value = "预警地点编码：地点/区划/单位/学校等")
    private String statDimId ;

    /**
     * 预警地点名称：地点/区划/单位/学校等
     */
    @ApiModelProperty(value = "预警地点名称：地点/区划/单位/学校等")
    private String statDimName ;

    /**
     * 疾病编码
     */
    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode ;

    /**
     * 疾病名称
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName ;

    /**
     * 源数据类型:病程
     */
    @ApiModelProperty(value = "源数据类型:病程")
    private String sourceKeyType ;

    /**
     * 源数据唯一键
     */
    @ApiModelProperty(value = "源数据唯一键")
    private String sourceKey ;

    /**
     * 病例疾病状态
     */
    @ApiModelProperty(value = "病例疾病状态")
    private String diseaseStatus ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

}

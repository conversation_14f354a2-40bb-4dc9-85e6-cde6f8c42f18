package com.iflytek.fpva.cdc.emergency.model.dto;

import com.iflytek.fpva.cdc.model.epi.BasePageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("操作日志分页查询VO")
public class OperationLogPageQueryDTO extends BasePageParam {
    @ApiModelProperty(value = "事件id")
    private String eventId;
    @ApiModelProperty(value = "任务id")
    private List<String> taskIds;
    @ApiModelProperty(value = "电话登记编号")
    private String registerNumber;
    @ApiModelProperty(value = "电话登记名称")
    private String registerName;
}

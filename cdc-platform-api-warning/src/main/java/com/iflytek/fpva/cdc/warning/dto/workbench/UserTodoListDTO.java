package com.iflytek.fpva.cdc.warning.dto.workbench;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.iflytek.fpva.cdc.warning.vo.EventTaskVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserTodoListDTO {
    // -----------------外部接口提供返回参数开始--------------------
    @ApiModelProperty(value = "业务id  信号id、病例id、事件id")
    private String id;

//    @ApiModelProperty(value = "业务id  信号id、病例id、事件id")
//    private String businessId ;

    @ApiModelProperty(value = "事件名称")
    private String taskName;

    @ApiModelProperty(value = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date taskStartDate;

    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date taskProcessTime;

    @ApiModelProperty(value = "任务类别名称")
    private String taskClassName;

    @ApiModelProperty(value = "任务类别编码")
    private String taskClassCode;

    @ApiModelProperty(value = "传染病信号疾病类别")
    private String infectedTypeName;

    @ApiModelProperty(value = "跳转参数")
    private String param;

    // -----------------外部接口提供返回参数结束--------------------

    @ApiModelProperty(value = "app编码")
    private String appCode;
    @ApiModelProperty(value = "app编码")
    private String appName;

    @ApiModelProperty(value = "系统来源编码")
    private String systemSourceCode;

    @ApiModelProperty(value = "系统来源名称")
    private String systemSourceName;

    @ApiModelProperty(value = "相对路径")
    private String relativePath;

//    @ApiModelProperty(value = "对应详情页面-系统来源编码")
//    private String childSystemSourceCode;
//
//    @ApiModelProperty(value = "对应详情页面-系统来源名称")
//    private String childSystemSourceName;
//
//    @ApiModelProperty(value = "对应详情页面-相对路径")
//    private String childRelativePath;

    public static UserTodoListDTO buildFromSignalTask(EventTaskVO vo){
        UserTodoListDTO dto = new UserTodoListDTO();
        dto.setId(vo.getSignalId());
//        dto.setBusinessId(vo.getSignalId());
        dto.setTaskStartDate(vo.getStartTime());
        dto.setTaskProcessTime(vo.getFinishedTime());
        dto.setTaskName(vo.getTaskName());
        buildParam(dto);
        return dto;
    }
    private static void buildParam(UserTodoListDTO dto) {
        JSONObject param = new JSONObject();
        param.put("id", dto.getId());
        param.put("formWorkPlat",true);
        dto.setParam(param.toJSONString());
    }
}

package com.iflytek.fpva.cdc.warning.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SignalJudgeProcessDTO {

        /**
     * 研判流程id
     */
    @ApiModelProperty("研判流程id")
    private String id;
    /**
     * 研判流程名称
     */
    @ApiModelProperty("研判流程名称")
    @NotBlank(message = "研判流程名称不能为空")
    private String judgeProcessName;

    /**
     * 初步诊断
     */
    @ApiModelProperty("初步诊断")
    private String initDiagnose;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("预警类型")
    private String warningType;

    /**
     * 研判角色
     */
    @ApiModelProperty("研判角色code列表")
    @NotBlank(message = "研判角色不能为空")
    private List<String> expertCategoryCodes;

    /**
     * 研判角色名称
     */
    @ApiModelProperty("研判角色名称列表")
    @NotBlank(message = "研判角色不能为空")
    private List<String> expertCategories;

    /**
     * 研判专家code列表
     */
    @ApiModelProperty("研判专家code列表")
    @NotBlank(message = "研判专家名称列表不能为空")
    private List<String> judgeExpertCodes;

    /**
     * 研判专家名称列表
     */
    @ApiModelProperty("研判专家名称列表")
    @NotBlank(message = "研判专家名称列表不能为空")
    private List<String> judgeExperts;

    @ApiModelProperty("专家信息")
    private String expertInfo;

    @ApiModelProperty("研判时限")
    private Integer judgeDeadline;

}

package com.iflytek.fpva.cdc.emergency.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
public enum EventLevelEnum {

    NO_LEVEL("0", "未分级", Lists.newArrayList("关注", "")),

    LEVEL_1("1", "特别重大（Ⅰ级）", Lists.newArrayList("Ⅰ级特别重大")),

    LEVEL_2("2", "重大（Ⅱ级）", Lists.newArrayList("Ⅱ级重大")),

    LEVEL_3("3", "较大（Ⅲ级）", Lists.newArrayList("Ⅲ级较大")),

    LEVEL_4("4", "一般（Ⅳ级）", Lists.newArrayList("Ⅳ级一般")),

    ;

    private final String code;
    private final String name;
    private final List<String> otherNames;

    EventLevelEnum(String code, String name, List<String> otherNames) {
        this.code = code;
        this.name = name;
        this.otherNames = otherNames;
    }

    private static final Map<String, EventLevelEnum> KEY_MAP = Maps.newHashMap();
    static {
        for (EventLevelEnum eventLevelEnum : EventLevelEnum.values()) {
            KEY_MAP.put(eventLevelEnum.name, eventLevelEnum);
            KEY_MAP.put(eventLevelEnum.code, eventLevelEnum);
            eventLevelEnum.otherNames.forEach(name -> KEY_MAP.put(name, eventLevelEnum));
        }
    }

    public static EventLevelEnum of(String codeOrName) {
        return KEY_MAP.getOrDefault(codeOrName, EventLevelEnum.NO_LEVEL);
    }

}

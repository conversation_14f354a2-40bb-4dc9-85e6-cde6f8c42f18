package com.iflytek.fpva.cdc.reportcard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.reportcard.entity.TbCdcewReportRepeatRule;
import com.iflytek.fpva.cdc.reportcard.vo.ReportRepeatRuleVO;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 报告卡规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface ReportUploadRepeatRuleMapper extends BaseMapper<TbCdcewReportRepeatRule> {

    TbCdcewReportRepeatRule findByDiseaseCode(@Param("diseaseCode") String diseaseCode);

    List<ReportRepeatRuleVO> getRuleList(@Param("ruleName")String ruleName);

    TbCdcewReportRepeatRule checkDiseaseCode(@Param("diseaseCode")String diseaseCode, @Param("id")String id);

    void logicDeleteById(@Param("id")String id, @Param("userName")String userName, @Param("userId")String userId);

    ReportRepeatRuleVO findRuleDetailByDisease(@Param("diseaseCode")String diseaseCode);
}

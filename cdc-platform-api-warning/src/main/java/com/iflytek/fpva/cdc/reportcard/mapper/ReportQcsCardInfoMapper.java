package com.iflytek.fpva.cdc.reportcard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.reportcard.dto.qcs.ReportQcsAnalysisDTO;
import com.iflytek.fpva.cdc.reportcard.entity.ReportQcsCardInfo;
import com.iflytek.fpva.cdc.reportcard.vo.qcs.DelayedReportVO;
import com.iflytek.fpva.cdc.reportcard.vo.qcs.LeakReportsVO;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Mapper
public interface ReportQcsCardInfoMapper extends BaseMapper<ReportQcsCardInfo> {

    List<LeakReportsVO> leakReport(ReportQcsAnalysisDTO dto);

    List<DelayedReportVO> delayedReport(ReportQcsAnalysisDTO dto);
}

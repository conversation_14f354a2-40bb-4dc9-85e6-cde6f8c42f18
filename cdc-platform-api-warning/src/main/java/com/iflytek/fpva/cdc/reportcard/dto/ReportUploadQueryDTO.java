package com.iflytek.fpva.cdc.reportcard.dto;

import cn.hutool.core.util.StrUtil;
import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.dto.ExportTaskCommonDTO;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("报告卡管理查询ReportUploadQueryDTO")
public class ReportUploadQueryDTO extends ExportTaskCommonDTO {

    @ApiModelProperty("时间类型 1-诊断时间；2-填卡时间；3-报卡创建时间；4-发病日期；5-订正终审时间")
    private Integer timeType;
    @ApiModelProperty(value = "传染病种类")
    private List<String> infectTypeList;

    @ApiModelProperty(value = "最新诊断传染病code")
    private List<String> infectCodeList;

    @ApiModelProperty("诊断状态")
    private String casesCategory;

    @ApiModelProperty("卡片状态")
    private String status;

    @ApiModelProperty(value = "报告卡id")
    private String reportCardId;

    @ApiModelProperty(value = "重卡识别主记录id")
    private String mainRecordId;

    @ApiModelProperty(value = "患者姓名")
    private String name;

    @ApiModelProperty(value = "报告单位")
    private String unitName;

    @ApiModelProperty(value = "重卡处理状态 0-未处理；1-已处理")
    private Integer repeatDisposeStatus;

    @ApiModelProperty(value = "病人属于")
    private String attribution;
    @ApiModelProperty(value = "报告单位地址-省编码")
    private String reportOrgAddrProvinceCode;

    @ApiModelProperty(value = "报告单位地址-市编码")
    private String reportOrgAddrCityCode;

    @ApiModelProperty(value = "报告单位地址-区编码")
    private String reportOrgAddrDistrictCode;

    //报卡id集合(用于导出选中报卡传参)
    @ApiModelProperty(value = "报卡id集合(用于导出选中报卡传参)")
    private List<String> ids;

    /**
     * 识别开始时间
     */
    @ApiModelProperty(value = "识别开始时间")
    private Date startDate;
    @ApiModelProperty(value = "识别结束时间")
    private Date endDate;

    public void setReportUploadRepeatRecordId(String reportUploadRepeatRecordId) {
        this.mainRecordId = reportUploadRepeatRecordId;
    }

    @ApiModelProperty(value = "现住址")
    private String addressName;

    @ApiModelProperty(value = "报卡数据类型 0:全部, 1-外地报至本地, 2-本地报至外地 , 3-按现住地区浏览, 4-按报告地区浏览")
    private Integer reportCardDataType = 0;

    @ApiModelProperty(value = "审核状态")
    private String checkStatus;

    @ApiModelProperty(value = "有效证件号")
    private String validCertNumber;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    /**
     * 区域等级 用于判断
     */
    private Integer regionLevel;

    public Integer getRegionLevel() {
        if (StrUtil.isNotBlank(super.getDistrictCode()) || StrUtil.isNotBlank(this.getReportOrgAddrDistrictCode())){
            return 3;
        } else if (StrUtil.isNotBlank(super.getCityCode()) || StrUtil.isNotBlank(this.getReportOrgAddrCityCode()) ){
            return 2;
        } else if (StrUtil.isNotBlank(super.getProvinceCode()) || StrUtil.isNotBlank(this.getReportOrgAddrProvinceCode())){
            return 1;
        }
        return 0;
    }

    @ApiModelProperty(value = "列表展示类型 1:全部 2:已删除 3:已排除")
    private Integer listDisplayType = 0;


    /**
     * 报告卡类型区分 01 法定传染病 02 非法定传染病 09 其他疾病 10 其他信息报送
     */
    private String reportCardClassType;
}

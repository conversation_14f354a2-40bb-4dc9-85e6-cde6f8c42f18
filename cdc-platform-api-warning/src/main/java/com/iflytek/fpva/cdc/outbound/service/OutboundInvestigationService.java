package com.iflytek.fpva.cdc.outbound.service;

import com.iflytek.fpva.cdc.outbound.entity.TbCdcewOutboundInvestigation;
import com.iflytek.fpva.cdc.outbound.model.dto.CommonOutboundInvestInfo;

public interface OutboundInvestigationService {

    String getOutboundBusinessType();

    /**
     * 获取外呼模板
     * */
    String getOutboundTemplateContent(String taskId, String templateId);

    /**
     * 设置主动排查其他信息
     * */
    CommonOutboundInvestInfo getInvestInfoAndSetRefId(TbCdcewOutboundInvestigation entity);

}

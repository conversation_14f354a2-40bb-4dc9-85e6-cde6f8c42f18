package com.iflytek.fpva.cdc.warning.mapper;

import com.iflytek.fpva.cdc.warning.dto.PushRecordQueryDTO;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewWarningPushRecord;
import com.iflytek.fpva.cdc.warning.vo.PersonVO;
import com.iflytek.fpva.cdc.warning.vo.SignalPushRecordTimeTrendVO;
import com.iflytek.fpva.cdc.warning.vo.WarningPushRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcewWarningPushRecordMapper {
    /**
     * 插入
     */
    void insert(TbCdcewWarningPushRecord entity);

    /**
     * 批量插入
     */
    int batchInsert(List<TbCdcewWarningPushRecord> entities);


    /**
     * 查询推送记录
     */
    List<WarningPushRecordVO> listPushRecordVO(PushRecordQueryDTO queryDTO);

    /**
     * 推送人
     */
    List<PersonVO> listPusher(PushRecordQueryDTO queryDTO);

    /**
     * 被推送人
     */
    List<PersonVO> listReceiver(PushRecordQueryDTO queryDTO);

    /**
     * 根据id列表查预警信号询推送记录
     */
    List<WarningPushRecordVO> selectPushRecordVOListByIds(@Param("ids") List<String> ids);


    /**
     *  统计信号推送成功和失败的数量
     */
    List<SignalPushRecordTimeTrendVO> signalPushRecordTimeTrend(PushRecordQueryDTO queryDTO);



}

package com.iflytek.fpva.cdc.warning.dto;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("预警研究数据查询")
public class WarningResearchDataQueryDTO extends CommonQueryDTO{

    @ApiModelProperty("研究id")
    private String researchId;

    @ApiModelProperty("排除标识")
    private String excludeFlag;

    @ApiModelProperty("逻辑操作符")
    private List<Logic> logics;
    
    @ApiModelProperty("ids")
    private List<String> ids;

}

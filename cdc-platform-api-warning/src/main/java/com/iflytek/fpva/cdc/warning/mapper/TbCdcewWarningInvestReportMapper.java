package com.iflytek.fpva.cdc.warning.mapper;

import com.iflytek.fpva.cdc.warning.dto.ReportQueryDTO;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewWarningInvestReport;

import java.util.List;

public interface TbCdcewWarningInvestReportMapper {

    List<TbCdcewWarningInvestReport> selectInvestTaskBy(ReportQueryDTO dto);

    /**
     * 新增数据
     *
     * @param tbCdcewWarningInvestReport 实例对象
     * @return 影响行数
     */
    int insert(TbCdcewWarningInvestReport tbCdcewWarningInvestReport);

    /**
     * 更新数据
     *
     * @param tbCdcewWarningInvestReport 实例对象
     * @return 影响行数
     */
    int update(TbCdcewWarningInvestReport tbCdcewWarningInvestReport);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    int upsert(TbCdcewWarningInvestReport tbCdcewWarningInvestReport);

    int deleteByIds(List<String> ids);
}

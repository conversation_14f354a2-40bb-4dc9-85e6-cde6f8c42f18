package com.iflytek.fpva.cdc.reportcard.vo.qcs;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;

import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 报卡督导管理-按机构统计-导出对象
 */
@ContentStyle(
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class QcsReportOrgExportVO {

    @ExcelProperty("医疗机构")
    private String reportOrgName;

    @ExcelProperty("机构所属地区")
    private String reportOrgAddrDistrict;

    @ExcelProperty({"报告情况", "应报告数"})
    private Integer identifyCnt;

    @ExcelProperty({"报告情况", "报告数"})
    private Integer reportCnt;

    @ExcelProperty({"报告情况", "报告率"})
    private String reportRate;

    @ExcelProperty({"报告情况", "漏报数"})
    private Integer leakCnt;

    @ExcelProperty({"报告情况", "漏报率"})
    private String leakRate;

    @ExcelProperty({"及时情况", "及时报告数"})
    private Integer timelyCnt;

    @ExcelProperty({"及时情况", "及时报告率"})
    private String timelyRate;

    @ExcelProperty({"及时情况", "迟报数"})
    private Integer delayCnt;

    @ExcelProperty({"及时情况", "迟报率"})
    private String delayRate;

    public static QcsReportOrgExportVO of(QcsReportVO vo) {
        QcsReportOrgExportVO model = new QcsReportOrgExportVO();
        BeanUtils.copyProperties(vo, model);
        return model;
    }
}

package com.iflytek.fpva.cdc.warning.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("态势感知json参数")
@Data
public class AwarenessParamConfig {
    @ApiModelProperty("信号id")
    private String dataId;
    @ApiModelProperty("配置json")
    private String configJson;
    @ApiModelProperty
    private String deductionAlgorithmType;
    @ApiModelProperty
    private String paramType;
}

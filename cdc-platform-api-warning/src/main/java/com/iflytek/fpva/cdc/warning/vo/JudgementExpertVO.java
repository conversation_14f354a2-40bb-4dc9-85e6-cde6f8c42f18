package com.iflytek.fpva.cdc.warning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("研判专家")
@Data
public class JudgementExpertVO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("角色id")
    private String roleId;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("专家id")
    private String expertId;

    @ApiModelProperty("专家姓名")
    private String expertName;

    @ApiModelProperty("专家电话")
    private String expertPhone;

    @ApiModelProperty("省code")
    private String provinceCode;

    @ApiModelProperty("市code")
    private String cityCode;

    @ApiModelProperty("县区code")
    private String districtCode;

    @ApiModelProperty("省名")
    private String provinceName;

    @ApiModelProperty("市名")
    private String cityName;

    @ApiModelProperty("县区名")
    private String districtName;

    @ApiModelProperty("修改人id")
    private String updaterId;

    @ApiModelProperty("修改人姓名")
    private String updater;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("删除标识")
    private String deleteFlag;
}

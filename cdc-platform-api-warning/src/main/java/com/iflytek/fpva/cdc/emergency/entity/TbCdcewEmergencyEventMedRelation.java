package com.iflytek.fpva.cdc.emergency.entity;

import com.iflytek.fpva.cdc.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 突发公共卫生事件与病例关联表;
 * <AUTHOR> dingyuan
 * @date : 2025-5-6
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "突发公共卫生事件与病例关联表")
@Data
public class TbCdcewEmergencyEventMedRelation extends BaseEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 信号id
     */
    @ApiModelProperty(value = "信号id")
    private String signalId ;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id")
    private String eventId ;

    /**
     * 病例id
     */
    @ApiModelProperty(value = "病例id")
    private String sourceKey ;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType ;

}

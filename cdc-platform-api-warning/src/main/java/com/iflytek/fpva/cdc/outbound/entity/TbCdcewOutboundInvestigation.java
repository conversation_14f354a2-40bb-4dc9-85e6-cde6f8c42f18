package com.iflytek.fpva.cdc.outbound.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iflytek.fpva.cdc.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 外呼调查记录;
 * <AUTHOR> fengwang35
 * @date : 2024-12-26
 */
@ApiModel(value = "外呼调查记录")
@TableName("tb_cdcew_outbound_investigation")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdcewOutboundInvestigation extends BaseEntity implements Serializable{
    public static final String TABLE_NAME = "tb_cdcew_outbound_investigation";

    /** 信号id */
    @ApiModelProperty(value = "外键id")
    private String refId ;
    /** 任务id */
    @ApiModelProperty(value = "任务id")
    private String taskId ;
    /** 通知对象ID */
    @ApiModelProperty(value = "通知对象ID")
    private String targetId ;
    /** 通知对象名称 */
    @ApiModelProperty(value = "通知对象名称")
    private String targetName ;
    /** 执行方式 */
    @ApiModelProperty(value = "执行方式")
    private String executionMethod ;
    /** 执行时间 */
    @ApiModelProperty(value = "执行时间")
    private String executionTimeType ;
    /** 指定时间 */
    @ApiModelProperty(value = "指定时间")
    private Date specifiedTime ;
    /** 电话模板 */
    @ApiModelProperty(value = "电话模板")
    private String templateId ;
    /** 电话预览 */
    @ApiModelProperty(value = "电话预览")
    private String reviewContent ;
    /** 重拨次数 */
    @ApiModelProperty(value = "重拨次数")
    private Integer redialCount ;
    /** 时间间隔 */
    @ApiModelProperty(value = "时间间隔")
    private Integer timeInterval ;
    /** 短信提醒 */
    @ApiModelProperty(value = "短信提醒")
    private String smsReminderType ;
    @ApiModelProperty(value = "外呼记录id")
    private String outboundRecordId;

    @ApiModelProperty(value = "用户所在机构id")
    private String userOrgId;
    
    @ApiModelProperty(value = "用户所在机构名称")
    private String userOrgName;
    
    @ApiModelProperty(value = "通知对象所在省编码")
    private String targetProvinceCode;
    
    @ApiModelProperty(value = "通知对象所在省")
    private String targetProvince;
    
    @ApiModelProperty(value = "通知对象所在市编码")
    private String targetCityCode;
    
    @ApiModelProperty(value = "通知对象所在市")
    private String targetCity;
    
    @ApiModelProperty(value = "通知对象所在区县编码")
    private String targetDistrictCode;
    
    @ApiModelProperty(value = "通知对象所在区县")
    private String targetDistrict;

    @ApiModelProperty(value = "业务类型")
    @NotNull
    private String businessType;

    @ApiModelProperty(value = "事件类型")
    @TableField(exist = false)
    private String eventType;

}
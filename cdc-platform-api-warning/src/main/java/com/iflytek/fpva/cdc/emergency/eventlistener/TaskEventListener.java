package com.iflytek.fpva.cdc.emergency.eventlistener;

import cn.hutool.core.lang.Pair;
import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEvent;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEventProcessTask;
import com.iflytek.fpva.cdc.emergency.enums.EventStatusEnum;
import com.iflytek.fpva.cdc.emergency.enums.TaskResultEnum;
import com.iflytek.fpva.cdc.emergency.enums.TaskTypeEnum;
import com.iflytek.fpva.cdc.emergency.event.TaskCreateEvent;
import com.iflytek.fpva.cdc.emergency.event.TaskResultEvent;
import com.iflytek.fpva.cdc.emergency.service.EmergencyCommonService;
import com.iflytek.fpva.cdc.emergency.service.EmergencyEventProcessTaskService;
import com.iflytek.fpva.cdc.emergency.service.EmergencyEventService;
import com.iflytek.fpva.cdc.util.StringUtils;
import com.iflytek.fpva.cdc.warning.enums.TimeLineMomentTypeEnum;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
@Slf4j
public class TaskEventListener {
    
    @Resource
    private EmergencyEventProcessTaskService emergencyEventProcessTaskService;

    @Resource
    private EmergencyEventService emergencyEventService;

    @Resource
    private AdminServiceProApi adminServiceProApi;

    @Resource
    private EmergencyCommonService emergencyCommonService;

    /**
     * 事件根据任务的结果进行变更
     */
    @EventListener(TaskResultEvent.class)
    public void eventUpdateOnTaskResult(TaskResultEvent event){
        TbCdcewEmergencyEventProcessTask task = event.getTask();
        String taskResult = task.getTaskResult();
        Date finishedTime = task.getFinishedTime();
        String taskStatus = task.getTaskStatus();
        TbCdcewEmergencyEvent emergencyEvent = emergencyEventService.getById(task.getEventId());
        TaskTypeEnum byCode = TaskTypeEnum.getByCode(task.getTaskType());
        String eventStatus = EventStatusEnum.getStatusByTaskResult(taskResult);
        if (eventStatus != null){
            emergencyEvent.setEventStatus(eventStatus);
        }
        switch (byCode){
            case CHECKED_TASK:
                emergencyEvent.setCheckStatus(taskStatus);
                emergencyEvent.setCheckResult(taskResult);
                emergencyEvent.setCheckFinishTime(finishedTime);
                emergencyCommonService.generateEventTimeline(null, emergencyEvent, TimeLineMomentTypeEnum.EVENT_VERIFICATION);
                break;
            case INVESTED_TASK:
                emergencyEvent.setInvestStatus(task.getTaskStatus());
                emergencyEvent.setInvestResult(taskResult);
                emergencyEvent.setInvestFinishTime(finishedTime);
                if (TaskResultEnum.SUSPECTED_INCIDENT.getCode().equals(taskResult)){
                    emergencyEvent.setEventStatus(EventStatusEnum.WAIT_JUDGMENT.getCode());
                }
                emergencyCommonService.generateEventTimeline(null, emergencyEvent, TimeLineMomentTypeEnum.EVENT_INVESTIGATION);
                break;
            case JUDGED_TASK:
                Pair<Boolean, Boolean> judgedConfirmed = emergencyEventProcessTaskService.judgeConfirmed(task.getEventId());
                if (judgedConfirmed.getKey()){
                    emergencyEvent.setJudgeStatus(taskStatus);
                    emergencyEvent.setJudgeResult(judgedConfirmed.getValue() ? TaskResultEnum.CONFIRMATION_OF_OUTBREAKS.getCode() : TaskResultEnum.EXCLUDED.getCode());
                    emergencyEvent.setJudgeFinishTime(finishedTime);
                    emergencyEvent.setEventStatus(judgedConfirmed.getValue() ? EventStatusEnum.CONFIRMED.getCode() : EventStatusEnum.EXCLUDED.getCode());
                }else {
                    emergencyEvent.setEventStatus(EventStatusEnum.JUDGING.getCode());
                }
                emergencyCommonService.generateEventTimeline(null, emergencyEvent, TimeLineMomentTypeEnum.EVENT_JUDGMENT);
                break;
        }

        emergencyEventService.editEventInfo(emergencyEvent);
    }

    /**
     * 推送应急
     */
    @EventListener(TaskResultEvent.class)
    public void pushEpiTask(TaskResultEvent event){
        TbCdcewEmergencyEventProcessTask task = event.getTask();
        if (TaskTypeEnum.JUDGED_TASK.getCode().equals(task.getTaskType())){
            Pair<Boolean, Boolean> judgedConfirmed = emergencyEventProcessTaskService.judgeConfirmed(task.getEventId());
            if (!(judgedConfirmed.getKey() && judgedConfirmed.getValue())){
                return;
            }
            emergencyEventService.addEpiTask(task.getEventId());
        }else if (TaskTypeEnum.INVESTED_TASK.getCode().equals(task.getTaskType())){
            if (!TaskResultEnum.CONFIRMATION_OF_OUTBREAKS.getCode().equals(task.getTaskResult())){
                return;
            }
            emergencyEventService.addEpiTask(task.getEventId());
        }


    }

    /**
     * 创建后续的任务
     */
    @EventListener(TaskResultEvent.class)
    public void createNextOnTaskResult(TaskResultEvent event){
        TbCdcewEmergencyEventProcessTask task = event.getTask();
        if (!TaskTypeEnum.CHECKED_TASK.getCode().equals(task.getTaskType())){
            return;
        }
        if (TaskResultEnum.notHandleCode().contains(task.getTaskResult())){
            return;
        }
        TbCdcewEmergencyEventProcessTask exited = emergencyEventProcessTaskService.getById(task.getId());
        TbCdcewEmergencyEventProcessTask newTask = new TbCdcewEmergencyEventProcessTask();
        newTask.setEventId(exited.getEventId());
        newTask.setStartTime(new Date());
        newTask.setTaskType(TaskTypeEnum.nextTaskCode(exited.getTaskType()));
        newTask.setChargePersonId(exited.getChargePersonId());
        newTask.setChargePersonName(exited.getChargePersonName());
        emergencyEventProcessTaskService.create(newTask);
    }

    /**
     * 时间线基于任务的结果变更
     */
    @EventListener(TaskResultEvent.class)
    public void timeLineOnTaskResult(TaskResultEvent event){


    }

    /**
     * 停止消息提醒
     */
    @EventListener(TaskResultEvent.class)
    public void stopMessageRemind(TaskResultEvent event){
        emergencyEventProcessTaskService.stopMessageRemind(event.getTask());
    }

    /**
     * 任务创建后变更事件数据
     */
    @EventListener(TaskCreateEvent.class)
    public void eventUpdateOnTaskCreate(TaskCreateEvent event){
        TbCdcewEmergencyEventProcessTask task = event.getTask();
        TbCdcewEmergencyEvent emergencyEvent = emergencyEventService.getById(task.getEventId());
        emergencyEvent.setChargePersonId(task.getChargePersonId());
        emergencyEvent.setChargePersonName(task.getChargePersonName());
        TaskTypeEnum byCode = TaskTypeEnum.getByCode(task.getTaskType());
        switch (byCode){
            case CHECKED_TASK:
                emergencyEvent.setCheckStatus(task.getTaskStatus());
                emergencyEvent.setEventStatus(EventStatusEnum.WAIT_VERIFIED.getCode());
                break;
            case INVESTED_TASK:
                emergencyEvent.setInvestStatus(task.getTaskStatus());
                emergencyEvent.setEventStatus(EventStatusEnum.WAIT_INVESTIGATED.getCode());
                break;
            case JUDGED_TASK:
                emergencyEvent.setJudgeStatus(task.getTaskStatus());
                emergencyEvent.setEventStatus(EventStatusEnum.JUDGING.getCode());
                break;
        }
        emergencyEventService.editEventInfo(emergencyEvent);
    }

    /**
     * 任务创建后发送消息
     */
    @EventListener(TaskCreateEvent.class)
    public void sendMessageOnTaskCreate(TaskCreateEvent event){
        TbCdcewEmergencyEventProcessTask task = event.getTask();
        emergencyEventProcessTaskService.sendMessage(StringUtils.isEmpty(task.getCreatorId()) ? "system" : task.getCreatorId(),
                StringUtils.isEmpty(task.getCreator()) ? "系统" : task.getCreator(), event.getTask());
    }

}

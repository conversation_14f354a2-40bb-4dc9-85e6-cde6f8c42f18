package com.iflytek.fpva.cdc.warning.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.warning.dto.JudgementRoleDTO;
import com.iflytek.fpva.cdc.warning.vo.JudgementRoleVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface JudgementRoleService {
    /**
     * 研判角色分页查询
     * @param judgementRoleDTO
     * @return JudgementRoleVO
     */
    PageInfo<JudgementRoleVO> judgementRolePageInfo(JudgementRoleDTO judgementRoleDTO);

    /**
     * 新增研判角色
     * @param judgementRoleDTO
     */
    void addJudgementRole(JudgementRoleDTO judgementRoleDTO);

    /**
     * 研判角色查询
     * @param judgementRoleId
     * @return
     */
    JudgementRoleVO selectJudgementRole(String judgementRoleId);

    /**
     * 更新研判角色
     * @param judgementRoleDTO
     */
    void updateJudgementRole(JudgementRoleDTO judgementRoleDTO);

    /**
     * 删除研判角色
     * @param judgementRoleDTO
     */
    void deleteJudgementRole(JudgementRoleDTO judgementRoleDTO);

    /**
     * 研判角色下拉选
     */
    List<JudgementRoleVO> selectJudgementRoleList();

    /**
     * 研判角色导出
     * @param judgementRoleDTO
     * @return
     */
    void exportJudgementRoleData(JudgementRoleDTO judgementRoleDTO, HttpServletResponse response) throws IOException, IllegalAccessException;
}

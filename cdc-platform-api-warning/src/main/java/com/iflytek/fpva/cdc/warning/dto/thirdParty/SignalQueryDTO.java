package com.iflytek.fpva.cdc.warning.dto.thirdParty;

import com.iflytek.fpva.cdc.common.model.dto.CommonQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("信号查询")
public class SignalQueryDTO extends CommonQueryDTO {
    @ApiModelProperty("预警类型")
    @NotEmpty(message = "预警类型不能为空")
    private String warningType;
    @ApiModelProperty("预警信号id")
    @NotEmpty(message = "信号id不能为空")
    private String signalId;


}

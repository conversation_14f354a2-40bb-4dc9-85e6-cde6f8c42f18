package com.iflytek.fpva.cdc.warning.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 症候群信号表,业务键: 疾病编码-预警时间-预警规则-预警地点;
 * <AUTHOR> dingyuan
 * @date : 2024-9-19
 */
@ApiModel(value = "症候群信号表,业务键: 疾病编码-预警时间-预警规则-预警地点")
@Data
public class TbCdcewSyndromeWarningSignal implements Serializable{

    private static final long serialVersionUID = 1L;

    public static final String TABLE_NAME = "tb_cdcew_syndrome_warning_signal";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String signalId ;

    /**
     * 预警信号编号
     */
    @ApiModelProperty(value = "预警信号编号")
    private String signalNum ;

    /**
     * 预警时间
     */
    @ApiModelProperty(value = "预警时间")
    private Date warningTime ;

    /**
     * 首例病例时间
     */
    @ApiModelProperty(value = "首例病例时间")
    private Date firstCaseTime ;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Date beginDate ;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endDate ;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode ;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称")
    private String provinceName ;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode ;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String cityName ;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码")
    private String districtCode ;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String districtName ;

    /**
     * 街道编码
     */
    @ApiModelProperty(value = "街道编码")
    private String streetCode ;

    /**
     * 街道名称
     */
    @ApiModelProperty(value = "街道名称")
    private String streetName ;

    /**
     * 预警地区编码：地点/区划/单位/学校等
     */
    @ApiModelProperty(value = "预警地区编码：地点/区划/单位/学校等")
    private String statDimId ;

    /**
     * 预警地区名称：地点/区划/单位/学校等
     */
    @ApiModelProperty(value = "预警地区名称：地点/区划/单位/学校等")
    private String statDimName ;

    /**
     * 疾病编码
     */
    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode ;

    /**
     * 疾病名称
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName ;

    /**
     * 预警地点类型
     */
    @ApiModelProperty(value = "预警地点类型")
    private String warningLocationType ;

    /**
     * 死亡数量
     */
    @ApiModelProperty(value = "死亡数量")
    private Integer deathCnt ;

    /**
     * 病例数
     */
    @ApiModelProperty(value = "病例数")
    private Integer medicalCaseCnt ;

    /**
     * 信号当前状态
     */
    @ApiModelProperty(value = "信号当前状态")
    private String processingStatus ;

    /**
     * 当前流程最晚处理时间
     */
    @ApiModelProperty(value = "当前流程最晚处理时间")
    private Date processingLatestTime ;

    /**
     * 风险等级ID
     */
    @ApiModelProperty(value = "风险等级ID")
    private String riskLevelId ;

    /**
     * 风险等级详情ID
     */
    @ApiModelProperty(value = "风险等级详情ID")
    private String riskLevelDetailId ;

    /**
     * 预警规则ID
     */
    @ApiModelProperty(value = "预警规则ID")
    private String warningRuleId ;

    /**
     * 预警规则描述
     */
    @ApiModelProperty(value = "预警规则描述")
    private String warningRuleDesc ;

    /**
     * 预警方法名称
     */
    @ApiModelProperty(value = "预警方法名称")
    private String warningMethodName ;

    /**
     * 预警阈值
     */
    @ApiModelProperty(value = "预警阈值")
    private Double warningThreshold ;

    /**
     * 责任人ID
     */
    @ApiModelProperty(value = "责任人ID")
    private String warningChargePersonId ;

    /**
     * 责任人姓名
     */
    @ApiModelProperty(value = "责任人姓名")
    private String warningChargePersonName ;

    /**
     * 核实状态
     */
    @ApiModelProperty(value = "核实状态")
    private String checkStatus ;

    /**
     * 核实最晚完成时间
     */
    @ApiModelProperty(value = "核实最晚完成时间")
    private Date checkLatestTime ;

    /**
     * 核实实际完成时间
     */
    @ApiModelProperty(value = "核实实际完成时间")
    private Date checkFinishTime ;

    /**
     * 核实结果
     */
    @ApiModelProperty(value = "核实结果")
    private String checkResult ;

    /**
     * 调查状态
     */
    @ApiModelProperty(value = "调查状态")
    private String investStatus ;

    /**
     * 调查最晚完成时间
     */
    @ApiModelProperty(value = "调查最晚完成时间")
    private Date investLatestTime ;

    /**
     * 调查实际完成时间
     */
    @ApiModelProperty(value = "调查实际完成时间")
    private Date investFinishTime ;

    /**
     * 调查结果
     */
    @ApiModelProperty(value = "调查结果")
    private String investResult ;

    /**
     * 研判状态
     */
    @ApiModelProperty(value = "研判状态")
    private String judgeStatus ;

    /**
     * 研判最晚完成时间
     */
    @ApiModelProperty(value = "研判最晚完成时间")
    private Date judgeLatestTime ;

    /**
     * 研判实际完成时间
     */
    @ApiModelProperty(value = "研判实际完成时间")
    private Date judgeFinishTime ;

    /**
     * 研判结果
     */
    @ApiModelProperty(value = "研判结果")
    private String judgeResult ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

    /**
     * 信号发生地经度
     */
    @ApiModelProperty(value = "信号发生地经度")
    private Double longitude ;

    /**
     * 信号发生地纬度
     */
    @ApiModelProperty(value = "信号发生地纬度")
    private Double latitude ;

}

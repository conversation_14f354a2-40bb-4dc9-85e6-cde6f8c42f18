package com.iflytek.fpva.cdc.warning.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("信号数据变更信息")
public class WarningSignalUpdateDTO {
    @ApiModelProperty("数据id")
    private String id;

    @ApiModelProperty("责任人id")
    private String warningChargePersonId;

    @ApiModelProperty("责任人名称")
    private String warningChargePersonName;

    @ApiModelProperty("信号结束日期")
    private Date endDate;

    @ApiModelProperty("核实状态")
    private String checkStatus;
    @ApiModelProperty("核实最晚完成时间")
    private Date checkLatestTime;
    @ApiModelProperty("核实实际完成时间")
    private Date checkFinishTime;
    @ApiModelProperty("核实结果")
    private String checkResult;

    @ApiModelProperty("调查状态")
    private String investStatus;
    @ApiModelProperty("调查最晚完成时间")
    private Date investLatestTime;
    @ApiModelProperty("调查实际完成时间")
    private Date investFinishTime;
    @ApiModelProperty("调查结果")
    private String investResult;

    @ApiModelProperty("研判状态")
    private String judgeStatus;
    @ApiModelProperty("研判最晚完成时间")
    private Date judgeLatestTime;
    @ApiModelProperty("研判实际完成时间")
    private Date judgeFinishTime;
    @ApiModelProperty("研判结果")
    private String judgeResult;

    @ApiModelProperty("信号状态")
    private String processingStatus;

    @ApiModelProperty("当前流程最晚处理时间")
    private Date processingLatestTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;

    private String signalType;
}

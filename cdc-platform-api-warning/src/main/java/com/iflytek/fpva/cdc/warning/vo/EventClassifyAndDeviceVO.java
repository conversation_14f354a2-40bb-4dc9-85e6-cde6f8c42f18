package com.iflytek.fpva.cdc.warning.vo;

import com.iflytek.fpva.cdc.common.model.vo.admin.DispositionAdviceResponseVO;
import com.iflytek.fpva.cdc.common.model.vo.admin.EventClassifyResponseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("风险研判")
@Data
public class EventClassifyAndDeviceVO {
    @ApiModelProperty("风险研判")
    private EventClassifyResponseVO eventClassifyResponseVO;

    @ApiModelProperty("处置建议")
    private DispositionAdviceResponseVO dispositionAdviceResponseVO;
}

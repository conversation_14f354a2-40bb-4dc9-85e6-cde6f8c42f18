package com.iflytek.fpva.cdc.reportcard.vo.qcs;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LeakReportsVO {

    private String id;

    private String mainRecordId;

    @ApiModelProperty("姓名")
    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    private String patientName;

    @ApiModelProperty("性别")
    private String sexName;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("年龄单位")
    private String ageUnit;

    @ApiModelProperty("所属地区-省")
    private String livingAddrProvince;

    @ApiModelProperty("所属地区-市")
    private String livingAddrCity;

    @ApiModelProperty("所属地区-区")
    private String livingAddrDistrict;

    @ApiModelProperty("发病日期")
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN, timezone = "GMT+8")
    private Date onsetTime;

    @ApiModelProperty("首次就诊时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN, timezone = "GMT+8")
    private Date firstVisitTime;

    @ApiModelProperty("首次就诊机构")
    private String orgName;

    @ApiModelProperty("机构所属地区-省")
    private String orgAddrProvince;

    @ApiModelProperty("机构所属地区-市")
    private String orgAddrCity;

    @ApiModelProperty("机构所属地区-区")
    private String orgAddrDistrict;

    @ApiModelProperty("漏报传染病")
    private String diseaseName;

    @ApiModelProperty("诊断时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_MINUTE_PATTERN, timezone = "GMT+8")
    private Date diagTime;

    // 以下导出需要的额外字段

    private String visitTypeName;

    private String firstDiagFlag;

    @Sensitive(type = SensitiveTypeEnum.ID_NUM)
    private String patientIdentityNo;

    private Date patientBirthDay;

    private String company;

    @Sensitive(type = SensitiveTypeEnum.PHONE)
    private String patientPhone;

    @Sensitive(type = SensitiveTypeEnum.ADDRESS)
    private String livingAddrDetail;

    private String personType;

    private Date deadTime;

    private String identifyClass;

    @ApiModelProperty("页面使用的发病日期")
    public String getOnsetDate() {
        return DateUtil.format(onsetTime, DatePattern.NORM_DATE_PATTERN);
    }

    public String getAgeWithUnit() {
        return age != null ? age + StrUtil.nullToEmpty(ageUnit) : null;
    }
}

package com.iflytek.fpva.cdc.reportcard.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ContentStyle(
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment =  VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
@EqualsAndHashCode(callSuper = false)
public class OtherReportCardExcelVO {
    @ExcelProperty("卡片ID")
    @ColumnWidth(23)
    private String reportCardId;

    @ExcelColumn(column = 1, name = "*患者姓名", required = true)
    @ExcelProperty("*患者姓名")
    @ColumnWidth(23)
    private String name;

    @ExcelColumn(column = 2, name = "有效证件类型", required = true)
    @ExcelProperty("有效证件类型")
    @ColumnWidth(23)
    private String validCertType;

    @ExcelColumn(column = 3, name = "有效证件号码", required = true, strip = {"'", "\""})
    @ExcelProperty("有效证件号码")
    @ColumnWidth(23)
    private String validCertNumber;

    @ExcelColumn(column = 4, name = "性别", required = true)
    @ExcelProperty("性别")
    @ColumnWidth(23)
    private String sexDesc;

    @ExcelColumn(column = 5, name = "出生日期", required = true)
    @ExcelProperty(value = "出生日期",converter = DateYYYYMMDDConverter.class)
    @ColumnWidth(23)
    private Date birthday;

    @ExcelColumn(column = 6, name = "年龄")
    @ExcelProperty("年龄")
    @ColumnWidth(23)
    private String age;

    @ExcelColumn(column = 7, name = "年龄单位")
    @ExcelProperty("年龄单位")
    @ColumnWidth(23)
    private String ageUnit;

    @ExcelColumn(column = 8, name = "患者工作单位")
    @ExcelProperty("患者工作单位")
    @ColumnWidth(23)
    private String company;

    @ExcelColumn(column = 9, name = "*联系电话", required = true, strip = {"'", "\""})
    @ExcelProperty("*联系电话")
    @ColumnWidth(23)
    private String phone;

    @ExcelColumn(column = 10, name = "病人属于", required = true)
    @ExcelProperty("病人属于")
    @ColumnWidth(23)
    private String attribution;

    @ExcelColumn(column = 11, name = "现住地址国标", required = true)
    @ExcelProperty("现住地址国标")
    @ColumnWidth(23)
    private String addressCode;

    @ExcelColumn(column = 12, name = "现住详细地址", required = true)
    @ExcelProperty("现住详细地址")
    @ColumnWidth(23)
    private String addressName;

    @ExcelColumn(column = 13, name = "症状体征", required = true)
    @ExcelProperty("症状体征")
    @ColumnWidth(23)
    private String symptomSign;

    @ExcelColumn(column = 14, name = "人群分类", required = true)
    @ExcelProperty("人群分类")
    @ColumnWidth(23)
    private String humanCategory;

    @ExcelColumn(column = 15, name = "病例分类", required = true)
    @ExcelProperty("病例分类")
    @ColumnWidth(23)
    private String casesCategory;

    @ExcelColumn(column = 16, name = "病例分类2")
    @ExcelProperty("病例分类2")
    @ColumnWidth(23)
    private String casesCategory2;

    @ExcelColumn(column = 17, name = "*疾病名称", required = true)
    @ExcelProperty("*疾病名称")
    @ColumnWidth(23)
    private String diseaseName;

    @ExcelColumn(column = 18, name = "诊断时间", required = true)
    @ExcelProperty("诊断时间")
    @ColumnWidth(23)
    private Date diagnoseTime;

    @ExcelColumn(column = 19, name = "发病日期", required = true)
    @ExcelProperty("发病日期")
    @ColumnWidth(23)
    private Date onsetDate;

    @ExcelColumn(column = 20, name = "死亡日期")
    @ExcelProperty("死亡日期")
    @ColumnWidth(23)
    private Date deathDate;

    @ExcelColumn(column = 21, name = "填卡医生", required = true)
    @ExcelProperty("填卡医生")
    @ColumnWidth(23)
    private String fillDoctor;

    @ExcelColumn(column = 22, name = "医生填卡日期", required = true)
    @ExcelProperty("医生填卡日期")
    @ColumnWidth(23)
    private Date fillDate;

    @ExcelColumn(column = 23, name = "报告单位", required = true)
    @ExcelProperty("报告单位")
    @ColumnWidth(23)
    private String unitName;

    @ExcelColumn(column = 24, name = "单位类型")
    @ExcelProperty("单位类型")
    @ColumnWidth(23)
    private String unitType;

    @ExcelColumn(column = 25, name = "报告单位地区编码")
    @ExcelProperty("报告单位地区编码")
    @ColumnWidth(23)
    private String unitCode;

    @ExcelColumn(column = 26, name = "录卡用户", required = true)
    @ExcelProperty("录卡用户")
    @ColumnWidth(23)
    private String recordUser;

    @ExcelColumn(column = 27, name = "报告卡录入时间", required = true)
    @ExcelProperty("报告卡录入时间")
    @ColumnWidth(23)
    private Date recordTime;

    @ExcelColumn(column = 28, name = "录卡用户所属单位", required = true)
    @ExcelProperty("录卡用户所属单位")
    @ColumnWidth(23)
    private String recordUserCompany;

    @ExcelColumn(column = 29, name = "备注")
    @ExcelProperty("备注")
    @ColumnWidth(23)
    private String remark;
}

package com.iflytek.fpva.cdc.outbound.service.impl;

import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.model.outbound.*;
import com.iflytek.fpva.cdc.common.model.po.UapUserPo;
import com.iflytek.fpva.cdc.common.utils.SpringElUtils;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEvent;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEventProcessTask;
import com.iflytek.fpva.cdc.emergency.enums.EventTypeEnum;
import com.iflytek.fpva.cdc.emergency.service.EmergencyEventProcessTaskService;
import com.iflytek.fpva.cdc.emergency.service.EmergencyEventService;
import com.iflytek.fpva.cdc.outbound.entity.TbCdcewOutboundInvestigation;
import com.iflytek.fpva.cdc.outbound.enums.OutboundBusinessTypeEnum;
import com.iflytek.fpva.cdc.outbound.service.OutboundInvestigationService;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.outbound.model.dto.CommonOutboundInvestInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.iflytek.fpva.cdc.common.interceptor.UserInfoInterceptor.userInfo;

@Service
@Slf4j
public class EventOutboundInvestigationServiceImpl implements OutboundInvestigationService {

    @Resource
    private EmergencyEventProcessTaskService emergencyEventProcessTaskService;

    @Resource
    private EmergencyEventService emergencyEventService;

    @Resource
    private AdminServiceProApi adminServiceProApi;


    @Override
    public String getOutboundBusinessType() {

        return OutboundBusinessTypeEnum.EMERGENCY_EVENT.getCode();
    }

    @Override
    public CommonOutboundInvestInfo getInvestInfoAndSetRefId(TbCdcewOutboundInvestigation entity) {

        CommonOutboundInvestInfo info = new CommonOutboundInvestInfo();

        TbCdcewEmergencyEventProcessTask processTask = emergencyEventProcessTaskService.getById(entity.getTaskId());
        TbCdcewEmergencyEvent event = emergencyEventService.getById(processTask.getEventId());
        entity.setRefId(event.getEventId());
        info.setEventTime(event.getEventTime());
        info.setStatDimName(event.getStatDimName());
        info.setDiseaseName(event.getDiseaseName());

        return info;
    }

    @Override
    public String getOutboundTemplateContent(String taskId, String templateId) {
        TbCdcewEmergencyEventProcessTask processTask = emergencyEventProcessTaskService.getById(taskId);
        TbCdcewEmergencyEvent event = emergencyEventService.getById(processTask.getEventId());
        OutboundTemplate outboundTemplate = adminServiceProApi.loadOutboundTemplate(templateId);
        return getTemplateContent(processTask, event, outboundTemplate.getTemplateContent());
    }

    private String getTemplateContent(TbCdcewEmergencyEventProcessTask processTask, TbCdcewEmergencyEvent event, String  templateContent){
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("task", processTask);
        event.setEventTypeName(EventTypeEnum.getDescByCode(event.getEventType()));
        contentMap.put("signal", event);
        contentMap.put("warningTime",DateUtils.parseDate(event.getEventTime(), "yyyy-MM-dd") );
        UapUserPo uapUserPo = userInfo.get();
        contentMap.put("currUser", uapUserPo);
        return SpringElUtils.parseTemplate(templateContent, contentMap);
    }

}

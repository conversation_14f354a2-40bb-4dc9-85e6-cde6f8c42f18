package com.iflytek.fpva.cdc.warning.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("Arima 综合分析")
public class ArimaComprehensiveQueryDTO extends NonDynamicsDiseasePredictDTO{


    @ApiModelProperty("区域查询类型 病例现住址-livingAddress;监测单位-orgAddress")
    private String addressType;

    /**
     *         APPROVE_TIME("approveTime", "审核时间"),
     *         IDENTIFY_TIME("identifyTime", "识别时间"),
     *         VISIT_TIME("visitTime", "就诊时间")
     */
    @ApiModelProperty("时间查询类型  系统报告时间-identifyTime ;报告审核时间-approveTime ;病例发病时间-visitTime")
    private String timeType;

}

package com.iflytek.fpva.cdc.warning.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.reportcard.vo.DiagnosticCriteriaInfoVO;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewInfectedDiseaseInfo;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewInfectedDiseaseWarning;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewInfectedDiseaseInfoMapper extends BaseMapper<TbCdcewInfectedDiseaseInfo> {

    List<TbCdcewInfectedDiseaseInfo> findRepeatDiseaseBy(@Param(value = "diseaseCodes") List<String> diseaseCodes,
                                                         @Param(value = "parentCode") String parentCode);

    List<TbCdcewInfectedDiseaseInfo> findCodeByParent(@Param(value = "parentCode")String diseaseCode);

    /**
     *  查询病种 diseaseCode
     */
    List<String> findDiseaseCodes(@Param(value = "code")String code);

    DiagnosticCriteriaInfoVO getDiagnosticCriteriaInfo(String diseaseCode);
}

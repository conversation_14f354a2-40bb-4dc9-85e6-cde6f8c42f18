package com.iflytek.fpva.cdc.warning.enums;

import com.iflytek.fpva.cdc.common.enums.BaseEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum FlagEnum implements BaseEnum {
    YES("1", "是"),
    NO("0", "否"),;
    private final String code;
    private final String desc;

    FlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public  static Map<String, Object> mapValues() {
        return Arrays.stream(values()).collect(Collectors.toMap(FlagEnum::name, s -> s));
    }


}

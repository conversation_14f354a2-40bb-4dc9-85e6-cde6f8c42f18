package com.iflytek.fpva.cdc.reportcard.dto;

import com.iflytek.fpva.cdc.common.model.dto.CommonQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("报告卡管理查询ReportUploadPrintQueryDTO")
public class ReportUploadPrintQueryDTO extends CommonQueryDTO {

    @ApiModelProperty("时间类型 1-诊断时间；2-填卡时间；3-打印时间")
    private Integer timeType;
    @ApiModelProperty(value = "传染病种类")
    private List<String> infectTypeList;

    @ApiModelProperty(value = "最新诊断传染病code")
    private List<String> infectCodeList;

    @ApiModelProperty("诊断状态")
    private String casesCategory;

    @ApiModelProperty(value = "打印用户")
    private String printUserName;
    @ApiModelProperty(value = "患者姓名")
    private String name;

    private
}

package com.iflytek.fpva.cdc.warning.dto.riskAssessment;

import com.iflytek.fpva.cdc.warning.dto.CommonQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("风险评估分析查询dto")
@Data
public class RiskAssessmentQueryDTO extends CommonQueryDTO {
    /**
     * 风险评估类别
     */
    @ApiModelProperty("风险评估类别")
    private String riskAssessmentType;
    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;
    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;

    /**
     * 系统来源code
     */
    @ApiModelProperty("系统来源code: 传染病监测: /cdc-main-app; 报卡督导: /cdc/report-card-manager")
    private String sourceSystemCode;
}

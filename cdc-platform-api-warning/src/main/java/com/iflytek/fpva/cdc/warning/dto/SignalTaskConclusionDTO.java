package com.iflytek.fpva.cdc.warning.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("结论")
public class SignalTaskConclusionDTO {
    @ApiModelProperty("任务id")
    @NotNull(message = "任务id不能为空")
    private String taskId;

    @ApiModelProperty("预警类型")
    @NotNull(message = "预警类型")
    private String warningType;

    @ApiModelProperty("结论")
    @NotNull(message = "结论不能为空")
    private String conclusion;

    @ApiModelProperty("判断依据")
    @NotNull(message = "判断依据不能为空")
    private String judgmentBasis;
}

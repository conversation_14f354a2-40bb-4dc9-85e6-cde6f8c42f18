package com.iflytek.fpva.cdc.warning.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("研判角色dto")
@Data
public class JudgementRoleDTO extends CommonQueryDTO{

    public static final String TABLE_NAME = "tb_cdcew_judgement_role";

    @ApiModelProperty("角色id")
    private String id;

    @ApiModelProperty("角色姓名")
    private String roleName;

    @ApiModelProperty("备注")
    private String notes;

    @ApiModelProperty("创建人id")
    private String creatorId;

    @ApiModelProperty("创建人姓名")
    private String creator;

    @ApiModelProperty("创建人时间")
    private Date createTime;

    @ApiModelProperty("修改人id")
    private String updaterId;

    @ApiModelProperty("修改人姓名")
    private String updater;

    @ApiModelProperty("修改人时间")
    private Date updateTime;

    @ApiModelProperty("删除标识")
    private String deleteFlag = "0";
}

package com.iflytek.fpva.cdc.reportcard.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.common.entity.TbCdcmrExportTask;
import com.iflytek.fpva.cdc.entity.TbCdcewFileUpload;
import com.iflytek.fpva.cdc.reportcard.dto.*;
import com.iflytek.fpva.cdc.reportcard.vo.DiagnosticCriteriaInfoVO;
import com.iflytek.fpva.cdc.reportcard.vo.MedicalRecordVO;
import com.iflytek.fpva.cdc.reportcard.vo.ReportUploadPrintVO;
import com.iflytek.fpva.cdc.reportcard.vo.ReportUploadRecordVO;
import org.springframework.web.multipart.MultipartFile;

public interface InfectedReportCardService {
    Boolean create(ReportCardInfoDataDTO dto, String loginUserId);

    TbCdcewFileUpload uploadFileAndInsert(MultipartFile file);

    Boolean checkPop(String loginUserId);

    DiagnosticCriteriaInfoVO getDiagnosticCriteriaInfo(String diseaseCode);

    MedicalRecordVO getMedicalRecordDetail(String id);

    ReportUploadRecordVO getReportCardDetail(String id);

    String addPrint(String loginUserId, String id);

    Boolean deleteReportCard(String id);

    PageInfo<ReportUploadPrintVO> printList(ReportUploadPrintQueryDTO queryDTO, String loginUserId);

    TbCdcmrExportTask exportSelectRecord(ReportUploadQueryDTO dto, String loginUserId);

    Boolean operateReportCard(ReportCardOperateDto dto);
}

package com.iflytek.fpva.cdc.warning.event;

import com.iflytek.fpva.cdc.warning.entity.TbCdcewWarningProcessTask;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class SignalTaskResultEvent extends ApplicationEvent {

    private TbCdcewWarningProcessTask task;

    public SignalTaskResultEvent(Object source) {
        super(source);
    }

    public SignalTaskResultEvent(Object source, TbCdcewWarningProcessTask task){
        super(source);
        this.task = task;
    }

}

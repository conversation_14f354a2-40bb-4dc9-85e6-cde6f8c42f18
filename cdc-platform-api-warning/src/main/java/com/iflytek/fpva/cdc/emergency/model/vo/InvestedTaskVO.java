package com.iflytek.fpva.cdc.emergency.model.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iflytek.fpva.cdc.emergency.enums.EventLevelEnum;
import com.iflytek.fpva.cdc.warning.enums.TaskResultTypeEnum;
import com.iflytek.fpva.cdc.warning.enums.TaskStatusTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 事件调查任务
 */
@Data
@ExcelIgnoreUnannotated
public class InvestedTaskVO {

    private static Map<String, String> eventLevelMap;

    private static Map<String, String> taskResultTypeMap;

    private static Map<String, String> taskStatusTypeMap;

    static {
        taskResultTypeMap = Arrays.stream(TaskResultTypeEnum.values())
                .collect(Collectors.toMap(TaskResultTypeEnum::getCode, TaskResultTypeEnum::getDesc));

        taskStatusTypeMap = Arrays.stream(TaskStatusTypeEnum.values())
                .collect(Collectors.toMap(TaskStatusTypeEnum::getCode, TaskStatusTypeEnum::getDesc));

        eventLevelMap = Arrays.stream(EventLevelEnum.values())
                .collect(Collectors.toMap(EventLevelEnum::getCode, EventLevelEnum::getName));
    }

    @ApiModelProperty(value = "任务id")
    @ExcelProperty(value = "任务ID", order = 1)
    private String taskId;


    @ApiModelProperty(value = "责任人姓名")
    @ExcelProperty(value = "责任人", order = 2)
    private String chargePersonName;

    @ApiModelProperty(value = "任务状态")
    @ExcelProperty(value = "任务状态", order = 3)
    private String taskStatus;

    @ApiModelProperty(value = "调查结论")
    @ExcelProperty(value = "调查结论", order = 4)
    private String taskResult;

    @ApiModelProperty(value = "事件id")
    @ExcelProperty(value = "事件ID", order = 5)
    private String eventId;

    @ApiModelProperty(value = "事件严重等级")
    @ExcelProperty(value = "事件严重等级", order = 6)
    private String eventLevel;

    @ApiModelProperty("发生地区")
    @ExcelProperty(value = "发生地区", order = 7)
    private String occurLocation;

    @ApiModelProperty(value = "事件发生地点名称")
    @ExcelProperty(value = "详细地址", order = 8)
    private String statDimName;

    public String getEventLevel() {
        return eventLevelMap.get(this.eventLevel);
    }

    public String getTaskStatus() {
        return taskStatusTypeMap.get(this.taskStatus);
    }

    public String getTaskResult() {
        return taskResultTypeMap.get(this.taskResult);
    }

}

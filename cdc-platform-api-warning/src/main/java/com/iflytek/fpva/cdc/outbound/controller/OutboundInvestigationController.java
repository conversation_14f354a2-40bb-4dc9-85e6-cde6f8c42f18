package com.iflytek.fpva.cdc.outbound.controller;

import com.iflytek.fpva.cdc.common.model.dto.OutboundInvestResultQueryDTO;
import com.iflytek.fpva.cdc.outbound.constants.OutboundInvestigationConstant;
import com.iflytek.fpva.cdc.outbound.entity.TbCdcewOutboundInvestigation;
import com.iflytek.fpva.cdc.outbound.model.vo.OutboundInvestResultVO;
import com.iflytek.fpva.cdc.outbound.service.OutboundInvestigationCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "主动排查模块")
@RequestMapping("/pt/{version}/outboundInvestigation")
public class OutboundInvestigationController {

    @Resource
    private OutboundInvestigationCommonService commonService;

    @PostMapping("/outboundInvest")
    @ApiOperation("主动排查")
    public void outboundInvest(@RequestBody TbCdcewOutboundInvestigation dto,
                               @RequestParam String loginUserId){

        commonService.create(dto);
    }

    @PostMapping("/getOutboundTemplateContent")
    @ApiOperation("获取主动排查的外呼模板内容")
    public String getOutboundTemplateContent(@RequestParam String taskId,
                                             @RequestParam String templateId,
                                             @RequestParam String businessType){

        return commonService.getOutboundTemplateContent(taskId, templateId, businessType);
    }

    @PostMapping("/listOutboundInvestResult")
    @ApiOperation("获取主动调查的结果")
    public List<OutboundInvestResultVO> listOutboundInvestResult(@RequestBody OutboundInvestResultQueryDTO queryDTO){

        return commonService.listOutboundInvestResult(queryDTO);
    }

    @GetMapping("/getOutboundInvestigationConstant")
    @ApiOperation("获取主动排查的变量")
    public OutboundInvestigationConstant getOutboundInvestigationConstant(){

        return OutboundInvestigationConstant.OUTBOUND_INVESTIGATION_CONSTANT;
    }

    @PostMapping("/getExpertJudgeTemplateContent")
    @ApiOperation("获取专家研判通知短信模板内容")
    public String getExpertJudgeTemplateContent(@RequestParam String refId,
                                                @RequestParam String templateId,
                                                @RequestParam String businessType){

        return commonService.getExpertJudgeTemplateContent(refId, templateId, businessType);
    }

}

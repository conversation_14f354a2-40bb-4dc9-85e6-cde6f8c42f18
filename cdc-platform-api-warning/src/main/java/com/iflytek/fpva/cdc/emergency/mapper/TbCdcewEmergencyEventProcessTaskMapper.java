package com.iflytek.fpva.cdc.emergency.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEventProcessTask;
import com.iflytek.fpva.cdc.emergency.model.dto.EventTaskQueryDTO;
import com.iflytek.fpva.cdc.emergency.model.vo.EventTaskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewEmergencyEventProcessTaskMapper extends BaseMapper<TbCdcewEmergencyEventProcessTask> {

    /**
     * 获取信号的任务列表
     * */
    List<EventTaskVO> getEventTaskList(EventTaskQueryDTO dto);


    /**
     * 查询任务列表 包括 in ids 查询 或者 条件查询
     * @param ids
     * @return
     */
    List<EventTaskVO> selectTaskList(EventTaskQueryDTO dto);
}

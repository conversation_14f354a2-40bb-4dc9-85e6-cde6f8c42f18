package com.iflytek.fpva.cdc.warning.controller;

import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.warning.dto.WarningResearchFieldSettingQueryDTO;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewWarningResearchFieldSetting;
import com.iflytek.fpva.cdc.warning.service.WarningResearchFieldSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.Getter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "研究模板字段")
@RequestMapping("/pt/{version}/warningResearchFieldSetting")
public class WarningResearchFieldSettingController {
    
    @Resource
    private WarningResearchFieldSettingService warningResearchFieldSettingService;
    
    @PostMapping("/create")
    @ApiOperation("创建预警研究字段设置")
    public TbCdcewWarningResearchFieldSetting create(@RequestBody TbCdcewWarningResearchFieldSetting input, @RequestParam String loginUserId){
        return warningResearchFieldSettingService.create(input);
    }
    
    @PostMapping("/update")
    @ApiOperation("修改")
    public void update(@RequestBody TbCdcewWarningResearchFieldSetting input, @RequestParam String loginUserId){
        warningResearchFieldSettingService.update(input);
    }
    
    @PostMapping("/delete")
    @ApiOperation("删除")
    public void deleteById(@RequestParam String id, @RequestParam String loginUserId){
        warningResearchFieldSettingService.deleteById(id);
    }

    @PostMapping("/pageList")
    @ApiOperation("分页查询")
    public PageInfo<TbCdcewWarningResearchFieldSetting> pageList(@RequestBody WarningResearchFieldSettingQueryDTO queryDTO, @RequestParam String loginUserId){
        return warningResearchFieldSettingService.pageList(queryDTO);
    }

    @PostMapping("/exportTemplate")
    @ApiOperation("模板导出")
    public ResponseEntity<byte[]> exportTemplate(@RequestBody Input input){
        return warningResearchFieldSettingService.exportTemplate(input.ids);
    }

    @Data
    public static class Input {
        @ApiModelProperty("数据id集合")
        private List<String> ids;
    }
}

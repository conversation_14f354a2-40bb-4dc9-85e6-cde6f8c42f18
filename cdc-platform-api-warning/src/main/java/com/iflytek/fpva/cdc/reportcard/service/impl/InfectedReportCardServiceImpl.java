package com.iflytek.fpva.cdc.reportcard.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.common.entity.TbCdcmrExportTask;
import com.iflytek.fpva.cdc.common.mapper.TbCdcAttachmentMapper;
import com.iflytek.fpva.cdc.common.mapper.TbCdcmrDiseaseLexiconMapper;
import com.iflytek.fpva.cdc.common.model.dto.ExportTaskDTO;
import com.iflytek.fpva.cdc.common.model.po.UapUserPo;
import com.iflytek.fpva.cdc.common.model.vo.DiseaseToInfectiousVO;
import com.iflytek.fpva.cdc.common.model.vo.admin.ReportRemindTimeGapConfigVO;
import com.iflytek.fpva.cdc.common.service.ExportTaskService;
import com.iflytek.fpva.cdc.common.service.FileService;
import com.iflytek.fpva.cdc.common.utils.DesensitizedUtils;
import com.iflytek.fpva.cdc.common.utils.ExcelUtils;
import com.iflytek.fpva.cdc.common.utils.FileUtils;
import com.iflytek.fpva.cdc.common.utils.StorageClientUtil;
import com.iflytek.fpva.cdc.constant.enums.DeleteFlagEnum;
import com.iflytek.fpva.cdc.constant.enums.ReportCardCompareTaskStatusEnum;
import com.iflytek.fpva.cdc.entity.Area;
import com.iflytek.fpva.cdc.entity.TbCdcewFileUpload;
import com.iflytek.fpva.cdc.mapper.common.AreaMapper;
import com.iflytek.fpva.cdc.reportcard.dto.*;
import com.iflytek.fpva.cdc.reportcard.entity.ReportUploadPrint;
import com.iflytek.fpva.cdc.reportcard.entity.ReportUploadRecord;
import com.iflytek.fpva.cdc.reportcard.mapper.ReportUploadPrintMapper;
import com.iflytek.fpva.cdc.reportcard.mapper.ReportUploadRecordMapper;
import com.iflytek.fpva.cdc.reportcard.service.InfectedReportCardService;
import com.iflytek.fpva.cdc.reportcard.service.ReportUploadRecordService;
import com.iflytek.fpva.cdc.reportcard.util.ReportCommonToolService;
import com.iflytek.fpva.cdc.reportcard.vo.*;
import com.iflytek.fpva.cdc.util.StringUtils;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewInfectedDiseaseInfo;
import com.iflytek.fpva.cdc.warning.mapper.TbCdcewInfectedDiseaseInfoMapper;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.SensitiveInterceptor.threadLocal;
import static com.iflytek.fpva.cdc.common.interceptor.UserInfoInterceptor.userInfo;


@Slf4j
@Service
public class InfectedReportCardServiceImpl implements InfectedReportCardService {


    private TbCdcmrDiseaseLexiconMapper tbCdcmrDiseaseLexiconMapper;

    @Autowired
    public void setTbCdcmrDiseaseLexiconMapper(TbCdcmrDiseaseLexiconMapper tbCdcmrDiseaseLexiconMapper) {
        this.tbCdcmrDiseaseLexiconMapper = tbCdcmrDiseaseLexiconMapper;
    }

    private TbCdcewInfectedDiseaseInfoMapper infectedDiseaseInfoMapper;

    @Autowired
    public void setInfectedDiseaseInfoMapper(TbCdcewInfectedDiseaseInfoMapper infectedDiseaseInfoMapper) {
        this.infectedDiseaseInfoMapper = infectedDiseaseInfoMapper;
    }

    private ReportCommonToolService reportCommonToolService;

    @Autowired
    public void setReportCommonToolService(ReportCommonToolService reportCommonToolService) {
        this.reportCommonToolService = reportCommonToolService;
    }

    private AreaMapper areaMapper;

    @Autowired
    public void setAreaMapper(AreaMapper areaMapper) {
        this.areaMapper = areaMapper;
    }

    private ReportUploadRecordMapper reportUploadRecordMapper;

    @Autowired
    public void setReportUploadRecordMapper(ReportUploadRecordMapper reportUploadRecordMapper) {
        this.reportUploadRecordMapper = reportUploadRecordMapper;
    }

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }

    private ReportUploadRecordService reportUploadRecordService;

    @Autowired
    public void setReportUploadRecordService(ReportUploadRecordService reportUploadRecordService) {
        this.reportUploadRecordService = reportUploadRecordService;
    }

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Resource
    private StorageClientUtil storageClientUtil;

    @Resource
    private FileService fileService;

    @Resource
    private AdminServiceProApi adminServiceProApi;

    @Resource
    private ReportUploadPrintMapper reportUploadPrintMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Override
    public Boolean create(ReportCardInfoDataDTO dto, String loginUserId) {
        if (dto == null){
            log.info("缺少添加数据");
            throw new MedicalBusinessException("缺少数据");
        }
        // 进行字段校验 区分公共字段 和  单独字段
        Pair<Boolean,String> checkResult = checkData(dto);
        Boolean checkFlag = checkResult.getLeft();
        if (!checkFlag){
            String checkMsg = checkResult.getRight();
            throw new MedicalBusinessException(checkMsg);
        }
        try {
            ReportUploadRecord record = buildReportCardData(dto);
            // 设置一个attachmentId
            record.setAttachmentId(String.valueOf(batchUidService.getUid(ReportUploadRecord.TB_NAME)));
            reportUploadRecordService.save(record);
            return true;
        } catch (Exception e) {
            log.error("新增报卡信息填报出错: {}", e.getMessage());
            return false;
        }
    }

    private ReportUploadRecord buildReportCardData(ReportCardInfoDataDTO dto) {
        ReportUploadRecord record = new ReportUploadRecord();
        BeanUtils.copyProperties(dto,record);
        UapUserPo uapUserPo = userInfo.get();
        List<Area> allArea = areaMapper.findAll();
        Map<String, Area> areaMap = allArea.stream()
                .collect(Collectors.toMap(Area::getCode, Function.identity(), (v1, v21) -> v1));

        reportCommonToolService.fillAreaData(record, areaMap);
        reportCommonToolService.fillOrgAreaData(record, areaMap);

        ReportUploadRecordVO existsRecode = reportUploadRecordMapper
                .findByReportCardId(record.getReportCardId());
        if (Objects.nonNull(existsRecode)) {
            record.setId(existsRecode.getId());
            record.setUpdateTime(new Date());
        } else {
            record.setId(String.valueOf(batchUidService.getUid(ReportUploadRecord.TB_NAME)));
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            record.setCreatorId(Objects.nonNull(uapUserPo) ? uapUserPo.getId() : null);
            record.setCreator(Objects.nonNull(uapUserPo) ? uapUserPo.getName() : null);
        }

        reportUploadRecordService.fillAgeGroup(record);
        reportUploadRecordService.fillEdrIdentityInfo(record,Objects.nonNull(uapUserPo) ? uapUserPo.getOrgId() : null);
        return record;
    }

    private Pair<Boolean, String> checkData(ReportCardInfoDataDTO dto) {
        if (dto == null){
            return new MutablePair<>(false,"信息必填");
        }
        // 报告卡类型区分 01 法定传染病 02 非法定传染病 09 其他疾病 10 其他信息报送
        String reportCardClassType = dto.getReportCardClassType();
        if (StrUtil.isBlank(reportCardClassType)){
            return new MutablePair<>(false,"缺少报告卡类型");
        }
        String reportCardId = dto.getReportCardId();
        if (StrUtil.isBlank(reportCardId)){
            return new MutablePair<>(false,"卡片ID必填");
        }
        if (StrUtil.equals(reportCardClassType,"01")){
            String status = dto.getStatus();
            if (StrUtil.isBlank(status)){
                return new MutablePair<>(false,"卡片状态必填");
            }
        }
        String name = dto.getName();
        if (StrUtil.isBlank(name)){
            return new MutablePair<>(false,"患者姓名必填");
        }
        String validCertType = dto.getValidCertType();
        if (StrUtil.isBlank(validCertType)){
            return new MutablePair<>(false,"有效证件类型必填");
        }

        String validCertNumber = dto.getValidCertNumber();
        if (StrUtil.isBlank(validCertNumber)){
            return new MutablePair<>(false,"有效证件号必填");
        }

        String sexDesc = dto.getSexDesc();
        if (StrUtil.isBlank(sexDesc)){
            return new MutablePair<>(false,"性别必填");
        }

        Date birthday = dto.getBirthday();
        if (birthday == null){
            return new MutablePair<>(false,"出生日期必填");
        }

        String phone = dto.getPhone();
        if (StrUtil.isBlank(phone)){
            return new MutablePair<>(false,"联系电话必填");
        }
        String attribution = dto.getAttribution();
        if (StrUtil.isBlank(attribution)){
            return new MutablePair<>(false,"病人属于必填");
        }

        String addressCode = dto.getAddressCode();
        if (StrUtil.isBlank(addressCode)){
            return new MutablePair<>(false,"现住地址国标必填");
        }
        String addressName = dto.getAddressName();
        if (StrUtil.isBlank(addressName)){
            return new MutablePair<>(false,"现住详细地址必填");
        }
        String humanCategory = dto.getHumanCategory();
        if (StrUtil.isBlank(humanCategory)){
            return new MutablePair<>(false,"人群分类必填");
        }

        String casesCategory = dto.getCasesCategory();
        if (StrUtil.isBlank(casesCategory)){
            return new MutablePair<>(false,"病例分类必填");
        }
        Date onsetDate = dto.getOnsetDate();
        if (onsetDate == null){
            return new MutablePair<>(false,"发病日期必填");
        }

        Date diagnoseTime = dto.getDiagnoseTime();
        if (diagnoseTime == null){
            return new MutablePair<>(false,"诊断时间必填");
        }

        String diseaseName = dto.getDiseaseName();
        if (StrUtil.isBlank(diseaseName)){
            return new MutablePair<>(false,"疾病名称必填");
        } else {
            if (StrUtil.equals(reportCardClassType,"01")){
                String diseaseCode = dto.getDiseaseCode();
                if (StrUtil.isBlank(diseaseCode)){
                    // 获取疾病编码
                    handleDiseaseCodeAndName(dto);
                }
            }
        }
        String fillDoctor = dto.getFillDoctor();
        if (StrUtil.isBlank(fillDoctor)){
            return new MutablePair<>(false,"填卡医生必填");
        }
        Date fillDate = dto.getFillDate();
        if (fillDate == null){
            return new MutablePair<>(false,"医生填卡日期必填");
        }
        String unitName = dto.getUnitName();
        if (StrUtil.isBlank(unitName)){
            return new MutablePair<>(false,"报告单位必填");
        }
        Date recordTime = dto.getRecordTime();
        if (recordTime == null){
            return new MutablePair<>(false,"报告卡录入时间必填");
        }

        String recordUser = dto.getRecordUser();
        if (StrUtil.isBlank(recordUser)){
            return new MutablePair<>(false,"录卡用户必填");
        }
        String recordUserCompany = dto.getRecordUserCompany();
        if (StrUtil.isBlank(recordUserCompany)){
            return new MutablePair<>(false,"录卡用户所属单位必填");
        }

        if (StrUtil.equals(reportCardClassType,"01")){
            if (StrUtil.isEmpty(dto.getCheckStatus())) {
                return new MutablePair<>(false,"审核状态必填");
            }
        }

        if (StrUtil.equals(reportCardClassType,"10")){
            if (StrUtil.isEmpty(dto.getSentinelTitle())) {
                return new MutablePair<>(false,"哨点/监测点名称必填");
            }
            if (StrUtil.isEmpty(dto.getSentinelCode())) {
                return new MutablePair<>(false,"哨点/监测点编号必填");
            }
        }
        return new MutablePair<>(true,"");
    }

    private void handleDiseaseCodeAndName(ReportCardInfoDataDTO dto) {
        String diseaseName = dto.getDiseaseName();
        List<DiseaseToInfectiousVO> diseaseToInfectiousVOList = this.tbCdcmrDiseaseLexiconMapper.queryByDiseasesName(diseaseName);
        if(diseaseToInfectiousVOList != null && !diseaseToInfectiousVOList.isEmpty()){
            DiseaseToInfectiousVO diseaseToInfectiousVO = diseaseToInfectiousVOList.get(0);
            String infectedCode = diseaseToInfectiousVO.getInfectedCode();
            String infectedName = diseaseToInfectiousVO.getInfectedName();
            dto.setDiseaseCode(infectedCode);
            dto.setDiseaseName(infectedName);
        } else {
            LambdaQueryWrapper<TbCdcewInfectedDiseaseInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TbCdcewInfectedDiseaseInfo::getDeleteFlag, DeleteFlagEnum.NO.getCode());
            queryWrapper.eq(TbCdcewInfectedDiseaseInfo::getDiseaseName, dto.getDiseaseName());
            TbCdcewInfectedDiseaseInfo diseaseInfo = this.infectedDiseaseInfoMapper.selectOne(queryWrapper.last("limit 1"));
            if (Objects.nonNull(diseaseInfo)) {
                dto.setDiseaseCode(diseaseInfo.getDiseaseCode());
            }
        }
    }

    @Override
    public TbCdcewFileUpload uploadFileAndInsert(MultipartFile file) {
        TbCdcewFileUpload tbCdcewFileUpload = reportCommonToolService.uploadOriginalFile(file);
        List<InfectedReportCardExcelDTO> excelDtoList = extractExcelData(tbCdcewFileUpload.getAttachmentId());
        checkExcelData(excelDtoList, tbCdcewFileUpload, file.getOriginalFilename());
        try {
            if (ReportCardCompareTaskStatusEnum.ERROR.getCode().equals(tbCdcewFileUpload.getStatus())) {
                return tbCdcewFileUpload;
            }
            if (tbCdcewFileUpload.getFailCount() <= 0) {
                UapUserPo uapUserPo = userInfo.get();

                List<Area> allArea = areaMapper.findAll();
                Map<String, Area> areaMap = allArea.stream()
                        .collect(Collectors.toMap(Area::getCode, Function.identity(), (v1, v21) -> v1));

                tbCdcewFileUpload.setResultAttachmentId(tbCdcewFileUpload.getAttachmentId());
                List<ReportUploadRecord> records = excelDtoList.stream().map(item -> {
                            ReportUploadRecord reportUploadRecord = new ReportUploadRecord();
                            BeanUtils.copyProperties(item,reportUploadRecord);
                            return reportUploadRecord;
                        })
                        .collect(Collectors.toList());
                for (ReportUploadRecord record : records) {
                    record.setAttachmentId(tbCdcewFileUpload.getAttachmentId());
                    // 填充省市区
                    reportCommonToolService.fillAreaData(record, areaMap);
                    reportCommonToolService.fillOrgAreaData(record, areaMap);

                    ReportUploadRecordVO existsRecode = reportUploadRecordMapper
                            .findByReportCardId(record.getReportCardId());
                    if (Objects.nonNull(existsRecode)) {
                        record.setId(existsRecode.getId());
                        record.setUpdateTime(new Date());
                    } else {
                        record.setId(String.valueOf(batchUidService.getUid(ReportUploadRecord.TB_NAME)));
                        record.setCreateTime(new Date());
                        record.setUpdateTime(new Date());
                        record.setCreatorId(Objects.nonNull(uapUserPo) ? uapUserPo.getId() : null);
                        record.setCreator(Objects.nonNull(uapUserPo) ? uapUserPo.getName() : null);
                    }
                    // 填充年龄段
                    reportUploadRecordService.fillAgeGroup(record);
                    // 关联 EDR
                    reportUploadRecordService.fillEdrIdentityInfo(record, Objects.nonNull(uapUserPo) ? uapUserPo.getOrgId() : null);
                }
                reportUploadRecordService.saveOrUpdateBatch(records);
            }

            // 修改文件上传任务状态
            tbCdcewFileUpload.setUpdateTime(new Date());
            tbCdcewFileUpload.setStatus(ReportCardCompareTaskStatusEnum.FINISH.getCode());
        } catch (Exception e) {
            String errorDesc;
            if (StringUtils.isEmpty(tbCdcewFileUpload.getErrorDesc())) {
                errorDesc = "文件解析失败,请重新上传";
            } else {
                errorDesc = tbCdcewFileUpload.getErrorDesc();
            }

            if (e.getMessage().equals("文件内容过大")) {
                errorDesc = e.getMessage();
            }
            // 修改文件上传任务状态
            tbCdcewFileUpload.setUpdateTime(new Date());
            tbCdcewFileUpload.setStatus(ReportCardCompareTaskStatusEnum.ERROR.getCode());
            tbCdcewFileUpload.setErrorDesc(errorDesc);

            log.error("导入报告卡失败: {} ", e.getMessage());
            throw new MedicalBusinessException("导入报告卡失败，" + e.getMessage());
        }
        return tbCdcewFileUpload;

    }

    private void checkExcelData(List<InfectedReportCardExcelDTO> excelDtoList, TbCdcewFileUpload tbCdcewFileUpload, String originalFilename) {
        int totalCount = excelDtoList.size();
        if (totalCount > 50000) {
            // 修改文件上传任务状态
            tbCdcewFileUpload.setUpdateTime(new Date());
            tbCdcewFileUpload.setStatus(ReportCardCompareTaskStatusEnum.ERROR.getCode());
            tbCdcewFileUpload.setTotalCount(totalCount);
            tbCdcewFileUpload.setErrorDesc("excel最多上传50000条数据");

            log.error("文件行数大于50000条，解析任务终止");
            return;
        }
        int successCount = 0;
        int failedCount = 0;
        List<InfectedReportCardExcelResultDTO> resultDtoList = new ArrayList<>();
        for (InfectedReportCardExcelDTO excelDTO : excelDtoList) {
            InfectedReportCardExcelResultDTO infectedReportCardExcelResultDTO = new InfectedReportCardExcelResultDTO();
            BeanUtils.copyProperties(excelDTO,infectedReportCardExcelResultDTO);

            ReportCardInfoDataDTO dto = new ReportCardInfoDataDTO();
            BeanUtils.copyProperties(excelDTO,dto);
            dto.setReportCardClassType("01");
            Pair<Boolean, String> checkResult = this.checkData(dto);
            Boolean checkFlag = checkResult.getLeft();
            String checkMsg = checkResult.getRight();
            if (checkFlag){
                successCount++;
            } else {
                failedCount++;
                infectedReportCardExcelResultDTO.setReason(checkMsg);
            }
            resultDtoList.add(infectedReportCardExcelResultDTO);
        }
        tbCdcewFileUpload.setTotalCount(totalCount);
        tbCdcewFileUpload.setSuccessCount(successCount);
        tbCdcewFileUpload.setFailCount(failedCount);

        if (failedCount > 0) {
            log.info("开始生成结果文件的文件流");
            ResponseEntity<byte[]> responseEntity = FileUtils.exportExcel(resultDtoList, InfectedReportCardExcelResultDTO.class);
            log.info("结束生成结果文件的文件流");
            log.info("开始上传结果文件");
            String resultAttachmentId = fileService.upload(responseEntity.getBody(), originalFilename);
            log.info("结束上传结果文件");
            tbCdcewFileUpload.setResultAttachmentId(resultAttachmentId);
        }
    }

    public List<InfectedReportCardExcelDTO> extractExcelData(String attachmentId) {
        log.info("开始解析原始文件");
        // 此处使用swift重新获取文件流
        TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(attachmentId);
        String objectName = storageClientUtil.getObjectName(tbCdcAttachment.getId(), tbCdcAttachment.getAttachmentName());
        StorageObject storageObject = storageClientUtil.getObject(objectName);
        List<InfectedReportCardExcelDTO> excelDtos = ExcelUtils.readExcel(InfectedReportCardExcelDTO.class, tbCdcAttachment.getAttachmentName(), storageObject.getInputStream());
        log.info("结束解析原始文件");
        return excelDtos;
    }

    @Override
    public Boolean checkPop(String loginUserId) {
        // 查询最近一条记录
        LambdaQueryWrapper<ReportUploadRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportUploadRecord::getCreatorId,loginUserId)
                .orderByDesc(ReportUploadRecord::getCreateTime)
                .last(" limit 1 ");
        ReportUploadRecord one = reportUploadRecordService.getOne(wrapper);
        if (one == null){
            return true;
        }
        Date createTime = one.getCreateTime();
        // 查询配置
        try {
            ReportRemindTimeGapConfigVO reportRemindTimeGapConfigVO = adminServiceProApi.getReportRemindTimeGapConfigVO(loginUserId);
            if (reportRemindTimeGapConfigVO == null){
                return true;
            }
            Integer timeGap = reportRemindTimeGapConfigVO.getTimeGap();
            String timeUnit = reportRemindTimeGapConfigVO.getTimeUnit();
            if (StrUtil.equals(timeUnit,"month")){
                // 获取当前时间
                Date now = new Date();

                // 使用Calendar计算月份差值
                Calendar createCalendar = Calendar.getInstance();
                createCalendar.setTime(createTime);

                Calendar nowCalendar = Calendar.getInstance();
                nowCalendar.setTime(now);

                // 计算月份差值
                int yearDiff = nowCalendar.get(Calendar.YEAR) - createCalendar.get(Calendar.YEAR);
                int monthDiff = nowCalendar.get(Calendar.MONTH) - createCalendar.get(Calendar.MONTH);
                int totalMonthDiff = yearDiff * 12 + monthDiff;

                // 判断是否在timeGap范围内
                // 在时间间隔内，不弹出提醒
                // 超过时间间隔，需要弹出提醒
                return totalMonthDiff > timeGap;
            }

            // 如果timeUnit不是month，默认返回true（弹出提醒）
            return true;
        } catch (Exception e) {
            log.error("查询报告时间提醒配置出错: {}", e.getMessage());
            return true;
        }
    }

    @Override
    public DiagnosticCriteriaInfoVO getDiagnosticCriteriaInfo(String diseaseCode) {
        return infectedDiseaseInfoMapper.getDiagnosticCriteriaInfo(diseaseCode);
    }

    @Override
    public MedicalRecordVO getMedicalRecordDetail(String id) {
        ReportUploadRecord record = reportUploadRecordService.getById(id);
        if (record == null){
            return new MedicalRecordVO();
        }
        MedicalRecordVO medicalRecordVO = new MedicalRecordVO();
        medicalRecordVO.setId(record.getId());
        medicalRecordVO.setValidCertNumber(record.getValidCertNumber());
        return medicalRecordVO;
    }

    @Override
    public ReportUploadRecordVO getReportCardDetail(String id) {
        ReportUploadRecord record = reportUploadRecordService.getById(id);
        return ReportUploadRecordVO.fromEntity(record);
    }

    @Override
    public String addPrint(String loginUserId, String recordId) {
        UapUserPo uapUserPo = userInfo.get();
        ReportUploadPrint add = new ReportUploadPrint();
        String uuid = String.valueOf(batchUidService.getUid(ReportUploadPrint.TB_NAME)) ;
        add.setId(uuid);
        add.setReportUploadRecordId(recordId);
        add.setCreateTime(LocalDateTime.now());
        add.setCreator(uapUserPo.getName());
        add.setCreatorId(loginUserId);
        reportUploadPrintMapper.insert(add);
        return uuid;
    }

    @Override
    public Boolean deleteReportCard(String id) {
        ReportUploadRecord record = reportUploadRecordService.getById(id);
        if (record == null){
            return false;
        }
        record.setCardDeleteFlag(1);
        return reportUploadRecordService.updateById(record);
    }

    @Override
    public Boolean operateReportCard(ReportCardOperateDto dto) {
        if (dto == null){
            return false;
        }
        String id = dto.getId();
        if (StrUtil.isBlank(id)){
            log.error("缺少id");
            return false;
        }
        Integer operateType = dto.getOperateType();
        if (operateType == null){
            log.error("操作类型为空");
            return false;
        }
        if (!(operateType == 1 || operateType == 2)){
            log.error("操作类型错误");
            return false;
        }
        ReportUploadRecord record = reportUploadRecordService.getById(id);
        if (record == null){
            log.error("未查询到数据 id: {}" ,id);
            return false;
        }
        String operateRemark = dto.getOperateRemark();
        if (operateType == 1){
            record.setCardDeleteFlag(1);
            if (StrUtil.isNotBlank(operateRemark)){
                record.setCardDeleteRemark(operateRemark);
            }
        }
        if (operateType == 2){
            record.setCardExcludeFlag(1);
            if (StrUtil.isNotBlank(operateRemark)){
                record.setCardExcludeRemark(operateRemark);Remark();
            }
        }
        return null;
    }

    @Override
    public PageInfo<ReportUploadPrintVO> printList(ReportUploadPrintQueryDTO queryDTO, String loginUserId) {
        PageHelper.startPage(queryDTO.getPageIndex(),queryDTO.getPageSize());
        List<ReportUploadPrintVO> list = reportUploadPrintMapper.findList(queryDTO);
        return new PageInfo<>(list);
    }

    @Override
    public TbCdcmrExportTask exportSelectRecord(ReportUploadQueryDTO dto, String loginUserId) {
        // 根据id查询列表
        List<String> idList = dto.getIds();
        if (idList == null || idList.isEmpty()){
            throw new MedicalBusinessException("导出列表为空");
        }
        LambdaQueryWrapper<ReportUploadRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ReportUploadRecord::getId,idList);
        List<ReportUploadRecord> list = reportUploadRecordService.list(wrapper);
        if (list == null || list.isEmpty()){
            throw new MedicalBusinessException("导出列表为空");
        }
        List<ReportUploadRecordVO> voList = list.stream().map(item -> {
            ReportUploadRecordVO reportUploadRecordVO = new ReportUploadRecordVO();
            BeanUtils.copyProperties(item,reportUploadRecordVO);
            return reportUploadRecordVO;
        }).collect(Collectors.toList());
        String reportCardClassType = dto.getReportCardClassType();
        if (StrUtil.equals(reportCardClassType,"01")){
            List<InfectedReportCardExcelVO> retList = new ArrayList<>();
            boolean isDesensitized = threadLocal.get().getNormalFlag();
            voList.forEach(item -> {
                if (isDesensitized) {
                    DesensitizedUtils.desensitizedObject(item);
                }
                InfectedReportCardExcelVO infectedReportCardExcelVO = new InfectedReportCardExcelVO();
                BeanUtils.copyProperties(item,infectedReportCardExcelVO);
                retList.add(infectedReportCardExcelVO);
            });

            ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                    dto.getTaskName(),
                    dto.getTaskUrl(),
                    null,
                    dto.getModuleType(),
                    dto.getExportType(),
                    dto.getAppCode());

            return exportTaskService.addAndUploadFile(dto,
                    () -> retList,
                    () -> CollUtil.isEmpty(retList) ? 0 : retList.size(),
                    taskDTO,
                    InfectedReportCardExcelVO.class,
                    Function.identity());
        }
        if (StrUtil.equals(reportCardClassType,"02")){
            List<NonStatutoryReportCardExcelVO> retList = new ArrayList<>();
            boolean isDesensitized = threadLocal.get().getNormalFlag();
            voList.forEach(item -> {
                if (isDesensitized) {
                    DesensitizedUtils.desensitizedObject(item);
                }
                NonStatutoryReportCardExcelVO nonStatutoryReportCardExcelVO = new NonStatutoryReportCardExcelVO();
                BeanUtils.copyProperties(item,nonStatutoryReportCardExcelVO);
                retList.add(nonStatutoryReportCardExcelVO);
            });

            ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                    dto.getTaskName(),
                    dto.getTaskUrl(),
                    null,
                    dto.getModuleType(),
                    dto.getExportType(),
                    dto.getAppCode());

            return exportTaskService.addAndUploadFile(dto,
                    () -> retList,
                    () -> CollUtil.isEmpty(retList) ? 0 : retList.size(),
                    taskDTO,
                    NonStatutoryReportCardExcelVO.class,
                    Function.identity());
        }
        if (StrUtil.equals(reportCardClassType,"09")){
            List<OtherReportCardExcelVO> retList = new ArrayList<>();
            boolean isDesensitized = threadLocal.get().getNormalFlag();
            voList.forEach(item -> {
                if (isDesensitized) {
                    DesensitizedUtils.desensitizedObject(item);
                }
                OtherReportCardExcelVO otherReportCardExcelVO = new OtherReportCardExcelVO();
                BeanUtils.copyProperties(item,otherReportCardExcelVO);
                retList.add(otherReportCardExcelVO);
            });

            ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                    dto.getTaskName(),
                    dto.getTaskUrl(),
                    null,
                    dto.getModuleType(),
                    dto.getExportType(),
                    dto.getAppCode());

            return exportTaskService.addAndUploadFile(dto,
                    () -> retList,
                    () -> CollUtil.isEmpty(retList) ? 0 : retList.size(),
                    taskDTO,
                    OtherReportCardExcelVO.class,
                    Function.identity());
        }
        if (StrUtil.equals(reportCardClassType,"10")){
            List<OtherInfoReportCardExcelVO> retList = new ArrayList<>();
            boolean isDesensitized = threadLocal.get().getNormalFlag();
            voList.forEach(item -> {
                if (isDesensitized) {
                    DesensitizedUtils.desensitizedObject(item);
                }
                OtherInfoReportCardExcelVO otherInfoReportCardExcelVO = new OtherInfoReportCardExcelVO();
                BeanUtils.copyProperties(item,otherInfoReportCardExcelVO);
                retList.add(otherInfoReportCardExcelVO);
            });

            ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                    dto.getTaskName(),
                    dto.getTaskUrl(),
                    null,
                    dto.getModuleType(),
                    dto.getExportType(),
                    dto.getAppCode());

            return exportTaskService.addAndUploadFile(dto,
                    () -> retList,
                    () -> CollUtil.isEmpty(retList) ? 0 : retList.size(),
                    taskDTO,
                    OtherInfoReportCardExcelVO.class,
                    Function.identity());
        }
        return null;
    }
}

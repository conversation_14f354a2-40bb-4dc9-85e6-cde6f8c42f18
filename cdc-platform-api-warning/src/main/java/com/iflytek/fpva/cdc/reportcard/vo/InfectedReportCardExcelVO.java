package com.iflytek.fpva.cdc.reportcard.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@ContentStyle(
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment =  VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="报告卡信息填报导出结果VO", description="报告卡信息填报导出结果VO")
public class InfectedReportCardExcelVO {
    @ApiModelProperty("卡片id")
    @ExcelProperty("卡片ID")
    @ColumnWidth(23)
    private String reportCardId;

    @ApiModelProperty("卡片编号")
    @ExcelColumn(column = 2, name = "卡片编号")
    @ExcelProperty("卡片编号")
    @ColumnWidth(23)
    private String reportCardCode;

    @ApiModelProperty("卡片状态")
    @ExcelColumn(column = 3, name = "卡片状态", required = true)
    @ExcelProperty("卡片状态")
    @ColumnWidth(23)
    private String status;

    //报告类别
    @ApiModelProperty("报告类别")
    @ExcelColumn(column = 4, name = "报告类别")
    @ExcelProperty("报告类别")
    @ColumnWidth(23)
    private String reportClass;


    @ApiModelProperty("患者姓名")
    @ExcelColumn(column = 5, name = "*患者姓名", required = true)
    @ExcelProperty("*患者姓名")
    @ColumnWidth(23)
    private String name;

    @ApiModelProperty("患儿家长姓名")
    @ExcelColumn(column = 6, name = "患儿家长姓名")
    @ExcelProperty("患儿家长姓名")
    @ColumnWidth(23)
    private String parentName;

    //有效证件类型
    @ApiModelProperty("有效证件类型")
    @ExcelColumn(column = 7, name = "有效证件类型", required = true)
    @ExcelProperty("有效证件类型")
    @ColumnWidth(23)
    private String validCertType;


    @ApiModelProperty("有效证件号码")
    @ExcelColumn(column = 8, name = "有效证件号码", required = true, strip = {"'", "\""})
    @ExcelProperty("有效证件号码")
    @ColumnWidth(23)
    private String validCertNumber;

    @ApiModelProperty("性别")
    @ExcelColumn(column = 9, name = "性别", required = true)
    @ExcelProperty("性别")
    @ColumnWidth(23)
    private String sexDesc;

    @ApiModelProperty("出生日期")
    @ExcelColumn(column = 10, name = "出生日期", required = true)
    @ExcelProperty(value = "出生日期",converter = DateYYYYMMDDConverter.class)
    @ColumnWidth(23)
    private Date birthday;

    @ApiModelProperty("年龄")
    @ExcelColumn(column = 11, name = "年龄")
    @ExcelProperty("年龄")
    @ColumnWidth(23)
    private String age;

    //年龄单位
    @ApiModelProperty("年龄单位")
    @ExcelColumn(column = 12, name = "年龄单位")
    @ExcelProperty("年龄单位")
    @ColumnWidth(23)
    private String ageUnit;


    @ApiModelProperty("患者工作单位")
    @ExcelColumn(column = 13, name = "患者工作单位")
    @ExcelProperty("患者工作单位")
    @ColumnWidth(23)
    private String company;

    @ApiModelProperty("联系电话")
    @ExcelColumn(column = 14, name = "*联系电话", required = true, strip = {"'", "\""})
    @ExcelProperty("*联系电话")
    @ColumnWidth(23)
    private String phone;

    @ApiModelProperty("病人属于")
    @ExcelColumn(column = 15, name = "病人属于", required = true)
    @ExcelProperty("病人属于")
    @ColumnWidth(23)
    private String attribution;

    @ApiModelProperty("现住地址国标")
    @ExcelColumn(column = 16, name = "现住地址国标", required = true)
    @ExcelProperty("现住地址国标")
    @ColumnWidth(23)
    private String addressCode;

    @ApiModelProperty("现住详细地址")
    @ExcelColumn(column = 17, name = "现住详细地址", required = true)
    @ExcelProperty("现住详细地址")
    @ColumnWidth(23)
    private String addressName;

    @ApiModelProperty("人群分类")
    @ExcelColumn(column = 18, name = "人群分类", required = true)
    @ExcelProperty("人群分类")
    @ColumnWidth(23)
    private String humanCategory;

    @ApiModelProperty("症状体征")
    @ExcelColumn(column = 19, name = "症状体征", required = true)
    @ExcelProperty("症状体征")
    @ColumnWidth(23)
    private String symptomSign;

    @ApiModelProperty("病例分类")
    @ExcelColumn(column = 20, name = "病例分类", required = true)
    @ExcelProperty("病例分类")
    @ColumnWidth(23)
    private String casesCategory;

    @ApiModelProperty("病例分类2")
    @ExcelColumn(column = 21, name = "病例分类2")
    @ExcelProperty("病例分类2")
    @ColumnWidth(23)
    private String casesCategory2;

    @ApiModelProperty("发病日期")
    @ExcelColumn(column = 22, name = "发病日期", required = true)
    @ExcelProperty("发病日期")
    @ColumnWidth(23)
    private Date onsetDate;

    @ApiModelProperty("诊断时间")
    @ExcelColumn(column = 23, name = "诊断时间", required = true)
    @ExcelProperty("诊断时间")
    @ColumnWidth(23)
    private Date diagnoseTime;

    @ApiModelProperty("死亡日期")
    @ExcelColumn(column = 24, name = "死亡日期")
    @ExcelProperty("死亡日期")
    @ColumnWidth(23)
    private Date deathDate;

    @ApiModelProperty("疾病名称")
    @ExcelColumn(column = 25, name = "*疾病名称", required = true)
    @ExcelProperty("*疾病名称")
    @ColumnWidth(23)
    private String diseaseName;

    @ApiModelProperty("订正前病种")
    @ExcelColumn(column = 26, name = "订正前病种")
    @ExcelProperty("订正前病种")
    @ColumnWidth(23)
    private String revisedPreviousDisease;

    @ApiModelProperty("接触史")
    @ExcelColumn(column = 27, name = "接触史")
    @ExcelProperty("接触史")
    @ColumnWidth(23)
    private String contactHistory;
    @ApiModelProperty("是否隔离")
    @ExcelColumn(column = 28, name = "是否隔离")
    @ExcelProperty("是否隔离")
    @ColumnWidth(23)
    private String quarantineFlag;

    @ApiModelProperty("订正前诊断时间")
    @ExcelColumn(column = 29, name = "订正前诊断时间")
    @ExcelProperty("订正前诊断时间")
    @ColumnWidth(23)
    private Date revisedPreviousDiagnoseTime;

    @ApiModelProperty("订正前终审时间")
    @ExcelColumn(column = 30, name = "订正前终审时间")
    @ExcelProperty("订正前终审时间")
    @ColumnWidth(23)
    private Date revisedPreviousCheckTime;

    @ApiModelProperty("填卡医生")
    @ExcelColumn(column = 31, name = "填卡医生", required = true)
    @ExcelProperty("填卡医生")
    @ColumnWidth(23)
    private String fillDoctor;

    @ApiModelProperty("医生填卡日期")
    @ExcelColumn(column = 32, name = "医生填卡日期", required = true)
    @ExcelProperty("医生填卡日期")
    @ColumnWidth(23)
    private Date fillDate;

    @ApiModelProperty("报告单位地区编码")
    @ExcelColumn(column = 33, name = "报告单位地区编码")
    @ExcelProperty("报告单位地区编码")
    @ColumnWidth(23)
    private String unitCode;

    @ApiModelProperty("报告单位")
    @ExcelColumn(column = 34, name = "报告单位", required = true)
    @ExcelProperty("报告单位")
    @ColumnWidth(23)
    private String unitName;

    @ApiModelProperty("单位类型")
    @ExcelColumn(column = 35, name = "单位类型")
    @ExcelProperty("单位类型")
    @ColumnWidth(23)
    private String unitType;

    @ApiModelProperty("报告卡录入时间")
    @ExcelColumn(column = 36, name = "报告卡录入时间", required = true)
    @ExcelProperty("报告卡录入时间")
    @ColumnWidth(23)
    private Date recordTime;

    @ApiModelProperty("录卡用户")
    @ExcelColumn(column = 37, name = "录卡用户", required = true)
    @ExcelProperty("录卡用户")
    @ColumnWidth(23)
    private String recordUser;

    @ApiModelProperty("录卡用户所属单位")
    @ExcelColumn(column = 38, name = "录卡用户所属单位", required = true)
    @ExcelProperty("录卡用户所属单位")
    @ColumnWidth(23)
    private String recordUserCompany;

    @ApiModelProperty("县区审核时间")
    @ExcelColumn(column = 39, name = "县区审核时间")
    @ExcelProperty("县区审核时间")
    @ColumnWidth(23)
    private Date districtCheckTime;

    @ApiModelProperty("地市审核时间")
    @ExcelColumn(column = 40, name = "地市审核时间")
    @ExcelProperty("地市审核时间")
    @ColumnWidth(23)
    private Date cityCheckTime;

    @ApiModelProperty("省市审核时间")
    @ExcelColumn(column = 41, name = "省市审核时间")
    @ExcelProperty("省市审核时间")
    @ColumnWidth(23)
    private Date provinceCheckTime;

    @ApiModelProperty("审核状态") //必填
    @ExcelColumn(column = 42, name = "*审核状态", required = true)
    @ExcelProperty("*审核状态")
    @ColumnWidth(23)
    private String checkStatus;

    @ApiModelProperty("订正报告时间")
    @ExcelColumn(column = 43, name = "订正报告时间")
    @ExcelProperty("订正报告时间")
    @ColumnWidth(23)
    private Date revisedReportTime;

    @ApiModelProperty("订正终审时间")
    @ExcelColumn(column = 44, name = "订正终审时间")
    @ExcelProperty("订正终审时间")
    @ColumnWidth(23)
    private Date revisedFinalCheckTime;

    @ApiModelProperty("终审死亡时间")
    @ExcelColumn(column = 45, name = "终审死亡时间")
    @ExcelProperty("终审死亡时间")
    @ColumnWidth(23)
    private Date finalCheckDeathTime;

    @ApiModelProperty("订正用户")
    @ExcelColumn(column = 46, name = "订正用户")
    @ExcelProperty("订正用户")
    @ColumnWidth(23)
    private String revisedUser;

    @ApiModelProperty("订正用户所属单位")
    @ExcelColumn(column = 47, name = "订正用户所属单位")
    @ExcelProperty("订正用户所属单位")
    @ColumnWidth(23)
    private String revisedUserCompany;

    @ApiModelProperty("删除时间")
    @ExcelColumn(column = 48, name = "删除时间")
    @ExcelProperty("删除时间")
    @ColumnWidth(23)
    private Date deleteTime;

    @ApiModelProperty("删除用户")
    @ExcelColumn(column = 49, name = "删除用户")
    @ExcelProperty("删除用户")
    @ColumnWidth(23)
    private String deleteUser;

    @ApiModelProperty("删除用户所属单位")
    @ExcelColumn(column = 50, name = "删除用户所属单位")
    @ExcelProperty("删除用户所属单位")
    @ColumnWidth(23)
    private String deleteUserCompany;

    @ApiModelProperty("删除原因")
    @ExcelColumn(column = 51, name = "删除原因")
    @ExcelProperty("删除原因")
    @ColumnWidth(23)
    private String deleteReason;

    @ApiModelProperty("备注")
    @ExcelColumn(column = 52, name = "备注")
    @ExcelProperty("备注")
    @ColumnWidth(23)
    private String remark;
}

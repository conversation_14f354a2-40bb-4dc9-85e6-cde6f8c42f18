package com.iflytek.fpva.cdc.reportcard.controller;


import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.common.annotation.OperationLogAnnotation;
import com.iflytek.fpva.cdc.common.entity.TbCdcmrExportTask;
import com.iflytek.fpva.cdc.entity.TbCdcewFileUpload;
import com.iflytek.fpva.cdc.reportcard.dto.*;
import com.iflytek.fpva.cdc.reportcard.service.InfectedReportCardService;
import com.iflytek.fpva.cdc.reportcard.vo.DiagnosticCriteriaInfoVO;
import com.iflytek.fpva.cdc.reportcard.vo.MedicalRecordVO;
import com.iflytek.fpva.cdc.reportcard.vo.ReportUploadPrintVO;
import com.iflytek.fpva.cdc.reportcard.vo.ReportUploadRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/pt/{version}/infected/reportCard")
public class InfectedReportCardController {

    private InfectedReportCardService infectedReportCardService;

    @Autowired
    public void setInfectedReportCardService(InfectedReportCardService infectedReportCardService) {
        this.infectedReportCardService = infectedReportCardService;
    }

    /**
     * 新增法定传染病报告卡 非法定传染病 其他传染病
     * @return
     */
    @PostMapping("/create")
    public Boolean create(@RequestBody ReportCardInfoDataDTO dto,
                          @RequestParam String loginUserId){
        return infectedReportCardService.create(dto,loginUserId);
    }

    /**
     * 上传文件导入数据
     * @param file
     * @return
     */
    @PostMapping(value = "/uploadFileAndInsert")
    @OperationLogAnnotation(operationName = "报告卡信息填报-上传导入excel并插入记录")
    public TbCdcewFileUpload uploadFile(@RequestParam MultipartFile file){
        return infectedReportCardService.uploadFileAndInsert(file);
    }

    /**
     * 检查是否显示提示
     * @param loginUserId
     * @return
     */
    @GetMapping("/checkPop")
    public Boolean checkPop(@RequestParam String loginUserId){
        return infectedReportCardService.checkPop(loginUserId);
    }

    /**
     * 查询临床诊断信息
     * @param diseaseCode
     * @return
     */
    @GetMapping("/getDiagnosticCriteriaInfo")
    public DiagnosticCriteriaInfoVO getDiagnosticCriteriaInfo(@RequestParam String diseaseCode){
        return infectedReportCardService.getDiagnosticCriteriaInfo(diseaseCode);
    }

    /**
     * 查询病历详情 medical record
     */
    @GetMapping("/getMedicalRecordDetail")
    public MedicalRecordVO getMedicalRecordDetail(@RequestParam String id){
        return infectedReportCardService.getMedicalRecordDetail(id);
    }

    /**
     * 查询报告卡详情
     */
    @GetMapping("/getReportCardDetail")
    public ReportUploadRecordVO getReportCardDetail(@RequestParam String id){
        return infectedReportCardService.getReportCardDetail(id);
    }

    /**
     * 报告卡打印
     */
    @PostMapping("/addPrint")
    public String addPrint(@RequestParam String loginUserId,@RequestParam String id){
        return infectedReportCardService.addPrint(loginUserId,id);
    }
    /**
     * 删除报告卡
     */
    @PostMapping("/operateReportCard")
    public Boolean operateReportCard(@RequestBody ReportCardOperateDto dto){
        return infectedReportCardService.operateReportCard(dto);
    }

    /**
     * 获取打印记录列表
     */
    @PostMapping("/print/list")
    public PageInfo<ReportUploadPrintVO> printList(@RequestBody ReportUploadPrintQueryDTO queryDTO, @RequestParam String loginUserId) {
        return infectedReportCardService.printList(queryDTO,loginUserId);
    }

    /**
     * 导出选中报告卡列表
     */
    @PostMapping("/report/exportSelectRecord")
    @OperationLogAnnotation(operationName = "导出选中报告卡列表")
    public TbCdcmrExportTask exportSelectRecord(@RequestParam String loginUserId, @RequestBody ReportUploadQueryDTO dto) {
        return infectedReportCardService.exportSelectRecord(dto, loginUserId);
    }

    // TODO 列表展示


    // TODO 按查询条件导出全部报卡
}

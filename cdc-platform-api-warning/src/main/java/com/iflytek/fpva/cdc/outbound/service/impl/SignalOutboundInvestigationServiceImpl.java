package com.iflytek.fpva.cdc.outbound.service.impl;

import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.model.outbound.OutboundTemplate;
import com.iflytek.fpva.cdc.common.model.po.UapUserPo;
import com.iflytek.fpva.cdc.common.utils.SpringElUtils;
import com.iflytek.fpva.cdc.outbound.entity.TbCdcewOutboundInvestigation;
import com.iflytek.fpva.cdc.outbound.enums.OutboundBusinessTypeEnum;
import com.iflytek.fpva.cdc.outbound.service.OutboundInvestigationService;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewDiseaseWarningSignal;
import com.iflytek.fpva.cdc.warning.entity.TbCdcewWarningProcessTask;
import com.iflytek.fpva.cdc.warning.mapper.TbCdcewDiseaseWarningSignalMapper;
import com.iflytek.fpva.cdc.warning.service.WarningProcessTaskService;
import com.iflytek.fpva.cdc.outbound.model.dto.CommonOutboundInvestInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.iflytek.fpva.cdc.common.interceptor.UserInfoInterceptor.userInfo;

@Service
@Slf4j
public class SignalOutboundInvestigationServiceImpl implements OutboundInvestigationService {

    @Resource
    private WarningProcessTaskService warningProcessTaskService;

    @Resource
    private TbCdcewDiseaseWarningSignalMapper tbCdcewDiseaseWarningSignalMapper;

    @Resource
    private AdminServiceProApi adminServiceProApi;

    @Override
    public String getOutboundBusinessType() {

        return OutboundBusinessTypeEnum.WARNING_SIGNAL.getCode();
    }

    @Override
    public String getOutboundTemplateContent(String taskId, String templateId) {

        TbCdcewWarningProcessTask processTask = warningProcessTaskService.loadById(taskId);
        TbCdcewDiseaseWarningSignal signal = tbCdcewDiseaseWarningSignalMapper.selectById(processTask.getSignalId());
        OutboundTemplate outboundTemplate = adminServiceProApi.loadOutboundTemplate(templateId);
        return getTemplateContent(processTask, signal, outboundTemplate.getTemplateContent());
    }

    @Override
    public CommonOutboundInvestInfo getInvestInfoAndSetRefId(TbCdcewOutboundInvestigation entity) {

        CommonOutboundInvestInfo info = new CommonOutboundInvestInfo();

        TbCdcewWarningProcessTask processTask = warningProcessTaskService.loadById(entity.getTaskId());
        TbCdcewDiseaseWarningSignal signal = tbCdcewDiseaseWarningSignalMapper.selectById(processTask.getSignalId());
        entity.setRefId(signal.getSignalId());
        info.setEventTime(signal.getWarningTime());
        info.setStatDimName(signal.getStatDimName());
        info.setDiseaseName(signal.getDiseaseName());

        return info;
    }

    private String getTemplateContent(TbCdcewWarningProcessTask processTask, TbCdcewDiseaseWarningSignal signal, String templateContent){
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("task", processTask);
        contentMap.put("signal", signal);
        contentMap.put("warningTime", DateUtils.parseDate(signal.getWarningTime(), "yyyy-MM-dd") );
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        contentMap.put("currUser", uapUserPo);
        return SpringElUtils.parseTemplate(templateContent, contentMap);
    }

}

package com.iflytek.fpva.cdc.warning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("研判角色")
@Data
public class JudgementRoleVO {

    @ApiModelProperty("角色id")
    private String id;

    @ApiModelProperty("角色姓名")
    private String roleName;

    @ApiModelProperty("已配置专家数量")
    private int configuredExpertNumber;

    @ApiModelProperty("备注")
    private String notes;

    @ApiModelProperty("创建人姓名")
    private String creator;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改人姓名")
    private String updater;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("uap角色名称")
    private String uapRoleName;

}

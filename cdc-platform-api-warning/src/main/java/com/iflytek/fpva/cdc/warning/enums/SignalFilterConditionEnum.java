package com.iflytek.fpva.cdc.warning.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum SignalFilterConditionEnum {

    SYNDROME("syndrome", "症候群异常聚集", "warning_type"),

    INFECTED("infected", "传染病病例预警", "warning_type"),

    MULTICHANNEL("multichannel", "呼吸道传染病多渠道预警", "warning_type"),

    ;

    private final String code;
    private final String desc;
    private final String column;

    SignalFilterConditionEnum(String code, String desc, String column) {
        this.code = code;
        this.desc = desc;
        this.column = column;
    }

    public static String getDescByCode(String code){
        SignalFilterConditionEnum[] values = SignalFilterConditionEnum.values();
        for (SignalFilterConditionEnum value : values) {
            if(value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return null;
    }

    public static Map<String, Object> mapValues() {
        return Arrays.stream(values()).collect(Collectors.toMap(SignalFilterConditionEnum::name, s -> s));
    }
}

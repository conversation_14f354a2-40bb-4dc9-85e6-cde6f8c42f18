package com.iflytek.fpva.cdc.warning.enums;

import com.iflytek.fpva.cdc.common.enums.BaseEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TaskResultTypeEnum implements BaseEnum {

    EXCLUDED("excluded", "已排除"),

    SUSPECTED_INCIDENT("suspectedIncident", "疑似事件"),

    RULE_OUT_OUTBREAKS("ruleOutOutbreaks", "排除暴发/流行"),

    CONFIRMATION_OF_OUTBREAKS("confirmationOfOutbreaks", "确认暴发/流行"),

    SUSPECTED_OUTBREAK("suspectedOutbreak", "疑似暴发/流行")

    ;

    private final String code;

    private final String desc;


    TaskResultTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<String, Object> mapValues() {
        return Arrays.stream(values()).collect(Collectors.toMap(TaskResultTypeEnum::name, s -> s));
    }

    public static TaskResultTypeEnum getByCode(String taskResult){

        TaskResultTypeEnum[] values = TaskResultTypeEnum.values();
        return Arrays.stream(values).filter(e -> e.getCode().equals(taskResult)).findFirst().orElse(null);
    }

    public static String getNameByCode(String code){
        TaskResultTypeEnum byCode = getByCode(code);
        if (byCode == null){
            return null;
        }
        return byCode.getDesc();
    }
}

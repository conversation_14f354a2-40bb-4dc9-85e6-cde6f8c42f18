package com.iflytek.fpva.cdc.warning.vo.stats;

import com.iflytek.fpva.cdc.common.annotation.IndicatorProperty;
import com.iflytek.fpva.cdc.warning.constant.IndicatorIdConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("信号类型预警统计")
public class WarningScenarioStatVO {

    @ApiModelProperty("重点地区预警信号数")
    @IndicatorProperty(id = IndicatorIdConstant.SignalStat.TYPE_AREA, code = SignalOverviewVO.INDICATOR_CODE_TYPE_AREA, name = "重点地区预警信号数")
    private Integer typeAreaNum;

    @ApiModelProperty("重点人群预警信号数")
    @IndicatorProperty(id = IndicatorIdConstant.SignalStat.TYPE_POPULATION, code = SignalOverviewVO.INDICATOR_CODE_TYPE_POPULATION, name = "重点人群预警信号数")
    private Integer typePopulationNum;

    @ApiModelProperty("重点机构或场所预警信号数")
    @IndicatorProperty(id = IndicatorIdConstant.SignalStat.TYPE_VENUE, code = SignalOverviewVO.INDICATOR_CODE_TYPE_VENUE, name = "重点机构或场所预警信号数")
    private Integer typeVenueNum;

}

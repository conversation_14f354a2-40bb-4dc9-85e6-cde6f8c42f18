package com.iflytek.fpva.cdc.warning.service.impl;

import cn.hutool.core.util.StrUtil;
import com.iflytek.fpva.cdc.common.apiService.DataServiceProApi;
import com.iflytek.fpva.cdc.common.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.fpva.cdc.common.model.vo.GroupSumIntVO;
import com.iflytek.fpva.cdc.common.model.vo.GroupSumPairVO;
import com.iflytek.fpva.cdc.common.model.vo.IndicatorDataVO;
import com.iflytek.fpva.cdc.common.model.vo.MedCntIndicatorVO;
import com.iflytek.fpva.cdc.common.model.vo.MsPatientInfoVO;
import com.iflytek.fpva.cdc.common.model.vo.MsProcessInfoAddressVO;
import com.iflytek.fpva.cdc.common.model.dto.SourceKeyDTO;
import com.iflytek.fpva.cdc.common.model.vo.TimeTrendVO;
import com.iflytek.fpva.cdc.common.utils.MathUtil;
import com.iflytek.fpva.cdc.util.DateUtils;
import com.iflytek.fpva.cdc.warning.dto.SignalDetailQueryDTO;
import com.iflytek.fpva.cdc.warning.enums.FlagEnum;
import com.iflytek.fpva.cdc.warning.enums.LoOutcomeStatusEnum;
import com.iflytek.fpva.cdc.warning.enums.MapAddressTypeEnum;
import com.iflytek.fpva.cdc.warning.service.TricycleDistributionService;
import com.iflytek.fpva.cdc.warning.vo.AddressPointDataVO;
import com.iflytek.fpva.cdc.warning.vo.stats.PopulationStatVO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TricycleDistributionServiceImpl implements TricycleDistributionService {

    @Resource
    private DataServiceProApi dataServiceProApi;

    @Override
    public List<TimeTrendVO> caseTimeTrend(SignalDetailQueryDTO queryDTO, List<SourceKeyDTO> sourceKeyDTOS) {
        MsProcessSimpleInfoQueryDTO processSimpleInfoQueryDTO = new MsProcessSimpleInfoQueryDTO();
        BeanUtils.copyProperties(queryDTO, processSimpleInfoQueryDTO);
        processSimpleInfoQueryDTO.setIds(sourceKeyDTOS.stream().map(SourceKeyDTO::getSourceKey).collect(Collectors.toList()));
        List<MedCntIndicatorVO> medCntIndicatorVOS = dataServiceProApi.listMedCntIndicator(queryDTO.getWarningType(), processSimpleInfoQueryDTO);
        return medCntIndicatorVOS.stream().map(m -> {
            TimeTrendVO timeTrendVO = new TimeTrendVO();
            timeTrendVO.setStatDate(m.getStatDate());
            timeTrendVO.setValue(m.getMedCaseCnt());
            return timeTrendVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AddressPointDataVO> mapRelation(String warningType, String addressType, MsProcessSimpleInfoQueryDTO dto) {
        List<MsProcessInfoAddressVO> addressVos = dataServiceProApi.listAddressBySourceKeys(warningType, dto);
        return addressVos.stream().map(a -> {
            AddressPointDataVO addressPointDataVO = new AddressPointDataVO();
            addressPointDataVO.setBusinessId(a.getProcessId());
            addressPointDataVO.setLatitude(MapAddressTypeEnum.COMPANY.getCode().equals(addressType)
                                                   ? a.getLvCompanyLatitude() :
                                                   MapAddressTypeEnum.MEDICAL_ORG.getCode().equals(addressType)
                                                           ? a.getFvOrgLongitude() : a.getLvLivingAddrLatitude());
            addressPointDataVO.setLongitude(MapAddressTypeEnum.COMPANY.getCode().equals(addressType)
                                                    ? a.getLvCompanyLongitude() :
                                                    MapAddressTypeEnum.MEDICAL_ORG.getCode().equals(addressType)
                                                            ? a.getFvOrgLatitude() : a.getLvLivingAddrLongitude());
            addressPointDataVO.setAddressName(MapAddressTypeEnum.COMPANY.getCode().equals(addressType)
                                                      ? a.getLvCompany() :
                                                      MapAddressTypeEnum.MEDICAL_ORG.getCode().equals(addressType)
                                                              ? a.getFvOrgName() : a.getLvLivingAddrDetail());
            return addressPointDataVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<IndicatorDataVO> medCaseIndicator(SignalDetailQueryDTO queryDTO, List<SourceKeyDTO> sourceKeyDTOS) {

        String warningType = queryDTO.getWarningType();
        MsProcessSimpleInfoQueryDTO processSimpleInfoQueryDTO = new MsProcessSimpleInfoQueryDTO();
        BeanUtils.copyProperties(queryDTO, processSimpleInfoQueryDTO);
        processSimpleInfoQueryDTO.setIds(sourceKeyDTOS.stream().map(SourceKeyDTO::getSourceKey).collect(Collectors.toList()));
        List<MedCntIndicatorVO> totalCntIndicatorVOS = dataServiceProApi.listMedCntIndicator(warningType, processSimpleInfoQueryDTO);
        int totalExisting = totalCntIndicatorVOS.stream().mapToInt(MedCntIndicatorVO::getExistingCaseCnt).sum();
        int totalRecover = totalCntIndicatorVOS.stream().mapToInt(MedCntIndicatorVO::getRecoverCnt).sum();


        processSimpleInfoQueryDTO.setStartDate(new Date());
        processSimpleInfoQueryDTO.setEndDate(new Date());
        List<MedCntIndicatorVO> todayData = dataServiceProApi.listMedCntIndicator(warningType, processSimpleInfoQueryDTO);
        int todayExisting = todayData.stream().mapToInt(MedCntIndicatorVO::getExistingCaseCnt).sum();
        int todayRecover = todayData.stream().mapToInt(MedCntIndicatorVO::getRecoverCnt).sum();


        processSimpleInfoQueryDTO.setStartDate(DateUtils.addDays(new Date(), -1));
        processSimpleInfoQueryDTO.setEndDate(processSimpleInfoQueryDTO.getStartDate());
        List<MedCntIndicatorVO> yesterdayData = dataServiceProApi.listMedCntIndicator(warningType, processSimpleInfoQueryDTO);
        int yesterdayExisting = yesterdayData.stream().mapToInt(MedCntIndicatorVO::getExistingCaseCnt).sum();
        int yesterdayRecover = yesterdayData.stream().mapToInt(MedCntIndicatorVO::getRecoverCnt).sum();

        List<IndicatorDataVO> indicatorDataVOS = new ArrayList<>();
        IndicatorDataVO medDataVO = new IndicatorDataVO("现存病例数", totalExisting,
                MathUtil.getGrowthRate(yesterdayExisting, todayExisting));

        IndicatorDataVO recoverDataVO = new IndicatorDataVO("累计治愈病例数",
                totalRecover,
                MathUtil.getGrowthRate(yesterdayRecover, todayRecover));
        indicatorDataVOS.add(medDataVO);
        indicatorDataVOS.add(recoverDataVO);
        return indicatorDataVOS;
    }

    @Override
    public List<TimeTrendVO> existingMedCaseLastWeek(SignalDetailQueryDTO queryDTO,
                                                     List<SourceKeyDTO> sourceKeyDTOS) {
        MsProcessSimpleInfoQueryDTO processSimpleInfoQueryDTO = new MsProcessSimpleInfoQueryDTO();
        BeanUtils.copyProperties(queryDTO, processSimpleInfoQueryDTO);
        processSimpleInfoQueryDTO.setIds(sourceKeyDTOS.stream().map(SourceKeyDTO::getSourceKey).collect(Collectors.toList()));
        processSimpleInfoQueryDTO.setEndDate(queryDTO.getEndDate() == null ? new Date() : queryDTO.getEndDate());
        processSimpleInfoQueryDTO.setStartDate(DateUtils.addDays(processSimpleInfoQueryDTO.getEndDate(), -6));
        List<MedCntIndicatorVO> medCntIndicatorVOS = dataServiceProApi.listMedCntIndicator(queryDTO.getWarningType(), processSimpleInfoQueryDTO);
        List<TimeTrendVO> results = new ArrayList<>();
        int value = 0;
        for (MedCntIndicatorVO c : medCntIndicatorVOS) {
            value += c.getExistingCaseCnt();
            TimeTrendVO timeTrendVO = new TimeTrendVO();
            timeTrendVO.setStatDate(c.getStatDate());
            timeTrendVO.setValue(value);
            results.add(timeTrendVO);
        }
        return results;
    }

    @Override
    public PopulationStatVO populationStat(SignalDetailQueryDTO queryDTO, List<SourceKeyDTO> sourceKeyDTOS) {

        MsProcessSimpleInfoQueryDTO dto = new MsProcessSimpleInfoQueryDTO();
        BeanUtils.copyProperties(queryDTO, dto);
        dto.setIds(sourceKeyDTOS.stream().map(SourceKeyDTO::getSourceKey).collect(Collectors.toList()));
        List<MsPatientInfoVO> msPatientInfoVOS = dataServiceProApi.listPatientInfoByProcessIds(queryDTO.getWarningType(), dto);
        msPatientInfoVOS.sort(Comparator.comparing(m -> m.getPatientAge() == null ? 0 : m.getPatientAge()));

        PopulationStatVO populationStatVO = new PopulationStatVO();
        populationStatVO.setAgeGroup(GroupSumIntVO.groupSumIntVOS(msPatientInfoVOS, this::decorateAge, p -> 1));
        populationStatVO.setSexGroup(GroupSumIntVO.groupSumIntVOS(msPatientInfoVOS, MsPatientInfoVO::getPatientSexName, p -> 1));
        populationStatVO.setJobGroup(GroupSumIntVO.groupSumIntVOS(msPatientInfoVOS, MsPatientInfoVO::getJob, p -> 1));

        populationStatVO.setAgeAndSexGroup(GroupSumPairVO.group(msPatientInfoVOS, this::decorateAge,
                this::countFemale, this::countMale, () -> "女性发病人数", () -> "男性发病人数"));

        populationStatVO.setMedTypeGroup(GroupSumIntVO.groupSumIntVOS(msPatientInfoVOS,
                m -> {
                    if (FlagEnum.NO.getCode().equals(m.getLiSevereFlag())) {
                        return "非重症病例";
                    } else if (FlagEnum.YES.getCode().equals(m.getLiSevereFlag()) && !LoOutcomeStatusEnum.DEATH.getLabel().equals(m.getLoOutcomeStatus())) {
                        return "重症未死亡病例";
                    } else if (LoOutcomeStatusEnum.DEATH.getLabel().equals(m.getLoOutcomeStatus())) {
                        return "死亡病例";
                    } else {
                        return null;
                    }
                }, p -> 1));

        populationStatVO.setMedGroupTypeGroup(GroupSumIntVO.groupSumIntVOS(msPatientInfoVOS.stream().filter(m -> StringUtils.isNotEmpty(m.getLiSubgroup())).flatMap(m ->
                        Arrays.stream(m.getLiSubgroup().split("\\|"))).collect(Collectors.toList()),
                m -> m, p -> 1));


        return populationStatVO;
    }

    private String decorateAge(MsPatientInfoVO vo) {
        return vo.getPatientAge() == null ? null : String.valueOf(Math.round(vo.getPatientAge()));
    }

    private int countFemale(MsPatientInfoVO vo) {
        return StrUtil.contains(vo.getPatientSexName(), "女") ? 1 : 0;
    }

    private int countMale(MsPatientInfoVO vo) {
        return StrUtil.contains(vo.getPatientSexName(), "男") ? 1 : 0;
    }
}

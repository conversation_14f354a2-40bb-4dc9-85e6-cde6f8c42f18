package com.iflytek.fpva.cdc.reportcard.dto.qcs;

import com.iflytek.fpva.cdc.warning.dto.CommonQueryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReportQcsAnalysisStatsDTO extends CommonQueryDTO {

    @ApiModelProperty("区域查询类型 病例现住址-livingAddress;监测单位-orgAddress")
    private String addressType;

    @ApiModelProperty("时间查询类型  系统报告时间-identifyTime ;报告审核时间-approveTime ;病例就诊时间-visitTime; 病例发病时间-onsetTime; 死亡时间-deathTime")
    private String timeType;

    @ApiModelProperty(value = "传染病类别（法定传染病、其它传染病）")
    private String infectClass;

    @ApiModelProperty(value = "传染病类别（甲类传染病、乙类传染病、丙类传染病、其它法定管理以及重点监测传染病）")
    private String infectType;

    @ApiModelProperty("传染病类型")
    private List<String> infectTypes;

    @ApiModelProperty(value = "传染病编码")
    private String infectCode;

    @ApiModelProperty(value = "传染病传播途径（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病）")
    private String infectTransmitType;

    @ApiModelProperty(value = "医疗机构名称")
    private List<String> orgNameList;


    private String provinceName;

    private String cityName;

    private String districtName;
}

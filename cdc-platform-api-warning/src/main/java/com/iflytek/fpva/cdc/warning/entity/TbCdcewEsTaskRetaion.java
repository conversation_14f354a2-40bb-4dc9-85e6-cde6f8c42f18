package com.iflytek.fpva.cdc.warning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 流调任务关联表
 */
@Data
@TableName("tb_cdcew_es_task_retaion")
@ApiModel(value = "流调任务关联表")
public class TbCdcewEsTaskRetaion implements Serializable {
    public static final String TABLE_NAME = "tb_cdcew_es_task_retaion";
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "流调任务ID")
    private String esTaskCode;

    @ApiModelProperty(value = "任务id,任务类型对应表的主键id")
    private String taskId;

    @ApiModelProperty(value = "任务类型1-app.tb_cdcew_emergency_event_process_task ;2-app.tb_cdcew_warning_process_task ;3-ads.ads_ms_syndrome_process_info")
    private String taskType;

    @ApiModelProperty(value = "信号、事件id")
    private String eventId;

    @ApiModelProperty(value = "任务创建人")
    private String creator;

    @ApiModelProperty(value = "任务创建人id")
    private String creatorId;

    @ApiModelProperty(value = "任务创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新人id")
    private String updaterId;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识： 0-未删除；1-已删除")
    private String deleteFlag;
} 
package com.iflytek.fpva.cdc.warning.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum TableNameWhiteListEnum {

    SUS_REPORT("tb_cdcew_warning_suspect_report", "信号异常信息表"),

    SIGNAL_INVEST_REPORT("tb_cdcew_warning_invest_report", "信号现场任务调查表"),

    EVENT_SUSPECT("tb_cdcew_event_suspect_report", "事件核实信息卡"),

    INVEST_REPORT("tb_cdcew_event_invest_report", "事件现场任务调查表"),

    EMERGENCY_EVENT_REPORT("tb_cdcew_emergency_event_report", "突发公共卫生事件报告卡"),

    ;

    private final String tableName;
    private final String tableDesc;

    TableNameWhiteListEnum(String tableName, String tableDesc) {
        this.tableName = tableName;
        this.tableDesc = tableDesc;
    }

    public static List<String> allTableNameWhiteList(){

        TableNameWhiteListEnum[] values = TableNameWhiteListEnum.values();
        return Arrays.stream(values).map(TableNameWhiteListEnum::getTableName).collect(Collectors.toList());
    }
}

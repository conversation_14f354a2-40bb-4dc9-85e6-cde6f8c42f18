package com.iflytek.fpva.cdc.warning.dto;

import com.iflytek.fpva.cdc.warning.enums.TimeLineMomentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SignalTimeDTO {
    /**
     * 信号id
     */
    private String signalId;

    /**
     * 目标信号id
     */
    private String targetSignalId;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 处置时间
     */
    private Date disposalTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 信号状态
     */
    private TimeLineMomentTypeEnum momentTypeEnum;

    /**
     * 调查/研判结论
     */
    private String conclusion;

    /**
     * 信号处理状态
     */
    private String processStatus;

    /**
     * 备注
     */
    private String notes;

    /**
     * 描述拼接信息
     * */
    private String content;

}

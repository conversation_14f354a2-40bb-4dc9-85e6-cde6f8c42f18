package com.iflytek.fpva.cdc.warning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("信号数据")
public class WarningSignalExportVO {
    @ApiModelProperty("信号id")
    private String signalId;

    @ApiModelProperty("信号num")
    private String signalNum;
    @ApiModelProperty("所属辖区")
    private String area;


    @ApiModelProperty("信号生成时间")
    private String warningTime;

    @ApiModelProperty("疾病类型编码")
    private String diseaseTypeCode;

    @ApiModelProperty("疾病类型名称")
    private String diseaseTypeName;

    @ApiModelProperty("疾病编码")
    private String diseaseCode;

    @ApiModelProperty("疾病名称")
    private String diseaseName;


    @ApiModelProperty("预警地点id")
    private String statDimId;

    @ApiModelProperty("预警地点名称")
    private String statDimName;

    @ApiModelProperty("风险等级id")
    private String riskLevelId;

    @ApiModelProperty("风险等级id")
    private String riskLevelDetailId;

    @ApiModelProperty("风险等级")
    private String riskLevelDesc;

    @ApiModelProperty("风险等级颜色")
    private String riskLevelColor;

    @ApiModelProperty("预警地址类型")
    private String warningLocationType;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("县区编码")
    private String districtCode;

    @ApiModelProperty("县区名称")
    private String districtName;

    @ApiModelProperty("街道编码")
    private String streetCode;

    @ApiModelProperty("街道名称")
    private String streetName;

    @ApiModelProperty("预警方法")
    private String warningMethodName;

    @ApiModelProperty("病例数")
    private Integer medicalCaseCnt;

    @ApiModelProperty("死亡人数")
    private Integer deathCnt;

    @ApiModelProperty("预警线")
    private BigDecimal warningThreshold;

    @ApiModelProperty("信号状态")
    private String  processingStatus;

    @ApiModelProperty("当前流程剩余时长")
    private String remainingHours;

    @ApiModelProperty("当前截止日期")
    private String processingLatestTime;

    @ApiModelProperty("当前流程责任人id")
    private String warningChargePersonId;

    @ApiModelProperty("当前流程责任人名称")
    private String warningChargePersonName;

    @ApiModelProperty("预警规则id")
    private String warningRuleId;

    @ApiModelProperty("预警规则描述")
    private String warningRuleDesc;

    @ApiModelProperty("首例病例时间")
    private String firstCaseTime;

    @ApiModelProperty("信号截止日期")
    private String endDate;

    @ApiModelProperty("核实状态")
    private String checkStatus;
    @ApiModelProperty("核实最晚完成时间")
    private String checkLatestTime;
    @ApiModelProperty("核实实际完成时间")
    private String checkFinishTime;
    @ApiModelProperty("核实结果")
    private String checkResult;

    @ApiModelProperty("调查状态")
    private String investStatus;
    @ApiModelProperty("调查最晚完成时间")
    private String investLatestTime;
    @ApiModelProperty("调查实际完成时间")
    private String investFinishTime;
    @ApiModelProperty("调查结果")
    private String investResult;

    @ApiModelProperty("研判状态")
    private String judgeStatus;
    @ApiModelProperty("研判最晚完成时间")
    private String judgeLatestTime;
    @ApiModelProperty("研判实际完成时间")
    private String judgeFinishTime;
    @ApiModelProperty("研判结果")
    private String judgeResult;

    @ApiModelProperty("预警类型")
    private String warningType;

    @ApiModelProperty(value = "多渠道信号类型")
    private String signalType;
    @ApiModelProperty(value = "多渠道专题id")
    private String topicId;
    @ApiModelProperty(value = "多渠道信号-病原预警-实验室")
    private String testingLabName;
    @ApiModelProperty(value = "多渠道信号-病原预警-样本类型")
    private String sampleTypeName;

    @ApiModelProperty("经度")
    private Long longitude;
    @ApiModelProperty("纬度")
    private Long latitude;

    @ApiModelProperty("是否已发起流调")
    private boolean isEpiInvest;

    @ApiModelProperty(value = "信号变化")
    private String signalChangeLog;
    
    @ApiModelProperty(value = "信号更新时间")
    private String updateTime;

    @ApiModelProperty("智能风险分级")
    private String eventLevel;
    @ApiModelProperty("预警原因")
    private String warningReason;
    @ApiModelProperty("预警场景")
    private String warningScenario;
    @ApiModelProperty("预警场景")
    private String warningScenarioType;

}

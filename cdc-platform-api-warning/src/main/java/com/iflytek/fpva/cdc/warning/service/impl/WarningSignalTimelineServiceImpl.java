package com.iflytek.fpva.cdc.warning.service.impl;

import com.iflytek.fpva.cdc.warning.entity.TbCdcewWarningSignalTimelineDetail;
import com.iflytek.fpva.cdc.warning.entity.WarningSignalTimeline;
import com.iflytek.fpva.cdc.warning.enums.TimeLineMomentTypeEnum;
import com.iflytek.fpva.cdc.warning.mapper.TbCdcewWarningSignalTimelineMapper;
import com.iflytek.fpva.cdc.warning.service.WarningSignalTimelineService;
import com.iflytek.fpva.cdc.warning.vo.MedChangeLogVO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class WarningSignalTimelineServiceImpl implements WarningSignalTimelineService {

    private static final String TABLE_NAME = "tb_cdcew_warning_signal_timeline";

    private static final String DETAIL_TABLE_NAME = "tb_cdcew_warning_signal_timeline_detail";

    @Resource
    private TbCdcewWarningSignalTimelineMapper tbCdcewWarningSignalTimelineMapper;

    @Resource
    private BatchUidService batchUidService;


    @Override
    public List<MedChangeLogVO> groupByDateAndType(List<String> signalIds, String warningType) {
        return tbCdcewWarningSignalTimelineMapper.groupByDateAndType(signalIds,warningType);
    }

    @Override
    public List<WarningSignalTimeline> listBySignalIds(List<String> signalIds, String warningType) {
        return tbCdcewWarningSignalTimelineMapper.listBySignalIds(signalIds, warningType, TimeLineMomentTypeEnum.signalTimeLine);
    }

    @Override
    public List<String> listSourceKey(String timelineId) {
        return tbCdcewWarningSignalTimelineMapper.listSourceKey(timelineId);
    }

    @Override
    public void save(List<WarningSignalTimeline> list) {
        List<TbCdcewWarningSignalTimelineDetail> details = new ArrayList<>();
        list.forEach(l -> {
            l.setId(String.valueOf(batchUidService.getUid(TABLE_NAME)));
            l.setCreateTime(new Date());
            l.getCurrentSourceKeyList().forEach(sourceKey -> {
                TbCdcewWarningSignalTimelineDetail warningSignalTimelineDetail  = new TbCdcewWarningSignalTimelineDetail();
                warningSignalTimelineDetail.setId(String.valueOf(batchUidService.getUid(DETAIL_TABLE_NAME)));
                warningSignalTimelineDetail.setSourceKey(sourceKey);
                warningSignalTimelineDetail.setTimelineId(l.getId());
                warningSignalTimelineDetail.setCreateTime(new Date());
                details.add(warningSignalTimelineDetail);
            });
        });
        tbCdcewWarningSignalTimelineMapper.batchInsert(list);
        tbCdcewWarningSignalTimelineMapper.batchInsertDetail(details);
    }
}

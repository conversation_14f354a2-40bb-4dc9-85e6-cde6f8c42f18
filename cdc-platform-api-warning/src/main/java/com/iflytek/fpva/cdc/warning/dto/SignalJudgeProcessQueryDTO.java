package com.iflytek.fpva.cdc.warning.dto;

import java.util.Date;

import com.iflytek.fpva.cdc.common.model.dto.CommonQueryDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("信号研判流程查询dto")
@Data
public class SignalJudgeProcessQueryDTO extends CommonQueryDTO {

    /**
     * 研判流程id
     */
    @ApiModelProperty("研判流程id")
    private String id;
    /**
     * 研判流程名称
     */
    @ApiModelProperty("研判流程名称")
    private String judgeProcessName;

    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private Date createTimeStart;
    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private Date createTimeEnd;
    /**
     * 最后修改时间-开始
     */
    @ApiModelProperty("最后修改时间-开始")
    private Date updateTimeStart;
    /**
     * 最后修改时间-结束
     */
    @ApiModelProperty("最后修改时间-结束")
    private Date updateTimeEnd;

    @ApiModelProperty("初步诊断")
    private String initDiagnose;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("预警类型")
    private String warningType;

}

package com.iflytek.fpva.cdc.emergency.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.dto.SignalPushRuleDTO;
import com.iflytek.fpva.cdc.common.dto.message.MessageAddDTO;
import com.iflytek.fpva.cdc.common.dto.message.MessageConfig;
import com.iflytek.fpva.cdc.common.entity.TbCdcmrExportTask;
import com.iflytek.fpva.cdc.common.enums.BooleanEnum;
import com.iflytek.fpva.cdc.common.model.dto.ExportTaskDTO;
import com.iflytek.fpva.cdc.common.service.ExportTaskService;
import com.iflytek.fpva.cdc.common.utils.CdcDateUtil;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEvent;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEventProcessTask;
import com.iflytek.fpva.cdc.emergency.enums.TaskResultEnum;
import com.iflytek.fpva.cdc.emergency.enums.TaskStatusEnum;
import com.iflytek.fpva.cdc.emergency.enums.TaskTypeEnum;
import com.iflytek.fpva.cdc.emergency.event.TaskCreateEvent;
import com.iflytek.fpva.cdc.emergency.event.TaskResultEvent;
import com.iflytek.fpva.cdc.emergency.mapper.TbCdcewEmergencyEventMapper;
import com.iflytek.fpva.cdc.emergency.mapper.TbCdcewEmergencyEventProcessTaskMapper;
import com.iflytek.fpva.cdc.emergency.model.dto.EventTaskQueryDTO;
import com.iflytek.fpva.cdc.emergency.model.dto.TaskUpdateDTO;
import com.iflytek.fpva.cdc.emergency.model.vo.EventTaskVO;
import com.iflytek.fpva.cdc.emergency.model.vo.InvestedTaskVO;
import com.iflytek.fpva.cdc.emergency.model.vo.JudgedTaskVO;
import com.iflytek.fpva.cdc.emergency.service.EmergencyEventProcessTaskService;
import com.iflytek.fpva.cdc.outbound.enums.OutboundBusinessTypeEnum;
import com.iflytek.fpva.cdc.outbound.model.dto.JudgeTaskInfo;
import com.iflytek.fpva.cdc.outbound.service.OutboundInvestigationCommonService;
import com.iflytek.fpva.cdc.service.common.impl.CdcServiceBaseImpl;
import com.iflytek.fpva.cdc.util.BeanUtils;
import com.iflytek.fpva.cdc.util.StringUtils;
import com.iflytek.fpva.cdc.warning.dto.DealUserDTO;
import com.iflytek.fpva.cdc.warning.dto.LogDTO;
import com.iflytek.fpva.cdc.warning.dto.TaskTransferDTO;
import com.iflytek.fpva.cdc.warning.enums.FlagEnum;
import com.iflytek.fpva.cdc.warning.enums.LogTypeEnum;
import com.iflytek.fpva.cdc.warning.enums.MessageConfigEnum;
import com.iflytek.fpva.cdc.warning.enums.TaskStatusTypeEnum;
import com.iflytek.fpva.cdc.warning.service.LogService;
import com.iflytek.fpva.cdc.warning.service.WarningSignalService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

import static com.iflytek.fpva.cdc.common.interceptor.UserInfoInterceptor.userInfo;

@Service
@Slf4j
public class EmergencyEventProcessTaskServiceImpl extends CdcServiceBaseImpl<TbCdcewEmergencyEventProcessTaskMapper, TbCdcewEmergencyEventProcessTask> implements EmergencyEventProcessTaskService {

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private TbCdcewEmergencyEventProcessTaskMapper tbCdcewEmergencyEventProcessTaskMapper;

    @Resource
    private AdminServiceProApi adminServiceProApi;

    @Resource
    private OutboundInvestigationCommonService outboundInvestigationCommonService;

    @Resource
    private LogService logService;

    @Resource
    private WarningSignalService warningSignalService;


    @Resource
    private TbCdcewEmergencyEventMapper emergencyEventMapper;


    @Resource
    private ExportTaskService exportTaskService;


    @Override
    @Transactional
    public void createByEvent(TbCdcewEmergencyEvent event, TbCdcewEmergencyEventProcessTask task) {
        fulfillTask(event, task);
        create(task);
    }

    @Override
    public PageInfo<EventTaskVO> pageList(EventTaskQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        queryDTO.setLoginUserId(userInfo.get().getId());
        return new PageInfo<>(tbCdcewEmergencyEventProcessTaskMapper.getEventTaskList(queryDTO));
    }

    @Override
    @Transactional
    public void updateTaskResult(TaskUpdateDTO dto) {
        TbCdcewEmergencyEventProcessTask existed = getById(dto.getTaskId());
        existed.setTaskResult(dto.getTaskResult());
        existed.setFinishedTime(new Date());
        existed.setTaskStatus(dto.getTaskStatus());
        existed.setNotes(dto.getBasic());
        existed.setAttachmentJson(dto.getAttachmentJson());
        update(existed);
        applicationEventPublisher.publishEvent(new TaskResultEvent(this, existed));
    }

    @Override
    @Transactional
    public void createCheckTask(TbCdcewEmergencyEvent event) {
        TbCdcewEmergencyEventProcessTask task = new TbCdcewEmergencyEventProcessTask();
        task.setEventId(event.getEventId());
        task.setTaskType(TaskTypeEnum.CHECKED_TASK.getCode());
        task.setStartTime(new Date());
        task.setTaskName(event.getDistrictName() + event.getDiseaseName());
        createByEvent(event, task);
    }

    @Override
    @Transactional
    public void addJudgeTasks(TaskUpdateDTO dto) {
        if (StringUtils.isEmpty(dto.getEventId())){
            throw new MedicalBusinessException("信号id 不能为空");
        }
        TbCdcewEmergencyEvent event = emergencyEventMapper.selectById(dto.getEventId());
        if (event == null){
            throw new MedicalBusinessException("事件不存在");
        }


        if (dto.getChargePersons() == null){
            throw new MedicalBusinessException("专家不能为空");
        }
        if (dto.getChargePersons().size() % 2 == 0){
            throw new MedicalBusinessException("所选专家的人数必须是奇数");
        }
        for (DealUserDTO userDTO : dto.getChargePersons()){
            TbCdcewEmergencyEventProcessTask task = new TbCdcewEmergencyEventProcessTask();
            task.setEventId(dto.getEventId());
            task.setTaskType(TaskTypeEnum.JUDGED_TASK.getCode());
            task.setChargePersonId(userDTO.getId());
            task.setChargePersonName(userDTO.getName());
            task.setTaskName(event.getDistrictName() + event.getDiseaseName());
            create(task);
        }
        //判断是否需要进行发送短信提醒
        if (BooleanEnum.TRUE.getStrVal().equals(dto.getSmsNoticeFlag())) {

            JudgeTaskInfo info = new JudgeTaskInfo();
            info.setRefId(dto.getEventId());
            info.setBusinessTypeName(OutboundBusinessTypeEnum.getDescByCode(dto.getBusinessType()));
            String recordId = outboundInvestigationCommonService.sendJudgeExpertsMsg(dto.getExpertInfo(), dto.getTemplateId(), info);
            if (StringUtils.isNotBlank(recordId)) {
                LogDTO logDTO = LogDTO.builder().operateContent("发起研判").build().add("eventId", dto.getEventId());
                logService.insertLogInto(dto.getEventId(), LogTypeEnum.INIT_JUDGMENT.getCode(), logDTO);
            }
        }

    }

    @Override
    @Transactional
    public void updateEpiFlag(String taskId) {
        TbCdcewEmergencyEventProcessTask byId = getById(taskId);
        byId.setEpiInvestFlag(FlagEnum.YES.getCode());
        update(byId);
    }

    @Override
    public Pair<Boolean, Boolean> judgeConfirmed(String eventId) {
        LambdaQueryWrapper<TbCdcewEmergencyEventProcessTask> queryWrapper = lambdaQueryWrapper();
        queryWrapper.eq(TbCdcewEmergencyEventProcessTask::getEventId, eventId);
        queryWrapper.eq(TbCdcewEmergencyEventProcessTask::getTaskType, TaskTypeEnum.JUDGED_TASK.getCode());
        List<TbCdcewEmergencyEventProcessTask> tasks = list(queryWrapper);
        long total = tasks.size();
        long judged = tasks.stream().filter(s -> s.getTaskStatus().equals(TaskStatusTypeEnum.JUDGED.getCode())).count();
        long confirmed = tasks.stream().filter(s -> TaskResultEnum.CONFIRMATION_OF_OUTBREAKS.getCode().equals(s.getTaskResult())).count();
        //判断是否超过一半的专家
        return Pair.of(total == judged, confirmed > total / 2);
    }

    @Override
    public void sendMessage(String senderId, String senderName, TbCdcewEmergencyEventProcessTask task) {
        MessageConfigEnum messageConfigEnum = getMessageConfigEnum(task.getTaskType());
        if (messageConfigEnum == null){
            return;
        }
        MessageAddDTO addDTO = new MessageAddDTO();
        try {
            MessageConfig messageConfig = adminServiceProApi.getMessageConfigById(messageConfigEnum.getId());
            if (messageConfig != null){
                addDTO = MessageAddDTO.generateVO(messageConfig);
                addDTO.setMessageContent(messageConfig.getMessageName()+" 事件ID："+task.getEventId());
                addDTO.setSenderId(senderId);
                addDTO.setSender(senderName);
                addDTO.setReceiverId(task.getChargePersonId());
                addDTO.setReceiver(task.getChargePersonName());
                addDTO.setRequestParam(MessageAddDTO.buildParam(task.getEventId()));
                addDTO.setMessageConfigId(messageConfig.getId());
                adminServiceProApi.saveMessage(addDTO);
            }
        } catch (Exception e){
            log.error("发生消息提醒失败参数为#{}", JSONObject.toJSONString(addDTO), e);
        }
    }

    @Override
    public void stopMessageRemind(TbCdcewEmergencyEventProcessTask task) {
        warningSignalService.stopSignalPushMessage(
                task.getEventId(),
                task.getChargePersonId(),
                null,
                null,
                getMessageConfigEnum(task.getTaskType()),
                false
        );
    }

    @Override
    @Transactional
    public void transferTask(TaskTransferDTO taskTransferDTO) {
        TbCdcewEmergencyEventProcessTask processTask = getById(taskTransferDTO.getTaskId());
        if (processTask == null){
            throw new MedicalBusinessException("未查询到任务， 请检查任务id是否正确");
        }
        if (TaskStatusTypeEnum.TRANSFERRED.getCode().equals(processTask.getTaskStatus())){
            throw new MedicalBusinessException("任务已转交，无法再次转交");
        }
        if (TaskStatusTypeEnum.TIME_OUT.getCode().equals(processTask.getTaskStatus())){
            throw new MedicalBusinessException("当前任务已超时，无法转交");
        }
        if (TaskStatusTypeEnum.finishedStatus().contains(processTask.getTaskStatus())){
            throw new MedicalBusinessException("任务已完成，无法转交");
        }
        TbCdcewEmergencyEventProcessTask newProcessTask = new TbCdcewEmergencyEventProcessTask();
        BeanUtils.copyProperties(processTask, newProcessTask);
        newProcessTask.setId(null);
        newProcessTask.setChargePersonId(taskTransferDTO.getTransferToUserId());
        newProcessTask.setChargePersonName(taskTransferDTO.getTransferToUserName());
        create(newProcessTask);
        processTask.setTaskStatus(TaskStatusTypeEnum.TRANSFERRED.getCode());
        update(processTask);
        sendTransferMessage(taskTransferDTO, processTask);
        stopMessageRemind(processTask);
    }

    @Override
    public String hasEpiInvest(String eventId) {
        LambdaQueryWrapper<TbCdcewEmergencyEventProcessTask> queryWrapper = lambdaQueryWrapper();
        queryWrapper.eq(TbCdcewEmergencyEventProcessTask::getEventId, eventId);
        queryWrapper.eq(TbCdcewEmergencyEventProcessTask::getEpiInvestFlag, FlagEnum.YES.getCode());
        int epiInvested = count(queryWrapper);
        return epiInvested > 0 ? FlagEnum.YES.getCode() : FlagEnum.NO.getCode();
    }

    @Override
    public void beforeCreate(TbCdcewEmergencyEventProcessTask entity) {
        super.beforeCreate(entity);
        entity.setTaskStatus(TaskStatusEnum.getInitialStatusByType(entity.getTaskType()));
        entity.setStartTime(new Date());
    }

    @Override
    public void afterCreate(TbCdcewEmergencyEventProcessTask entity) {
        super.afterCreate(entity);
        applicationEventPublisher.publishEvent(new TaskCreateEvent(this, entity));
    }

    /**
     * 设置责任人
     */
    private void fulfillTask(TbCdcewEmergencyEvent event, TbCdcewEmergencyEventProcessTask task){
        if (StringUtils.isEmpty(task.getChargePersonId())){
            task.setChargePersonId(event.getChargePersonId());
            task.setChargePersonName(event.getChargePersonName());
        }
    }

    /**
     * 获取消息提醒的配置
     */
    private MessageConfigEnum getMessageConfigEnum(String taskType){
        TaskTypeEnum byCode = TaskTypeEnum.getByCode(taskType);
        MessageConfigEnum messageConfigEnum = null;
        switch (byCode){
            case CHECKED_TASK:
                messageConfigEnum = MessageConfigEnum.EVENT_CHECK;
                break;
            case INVESTED_TASK:
                messageConfigEnum = MessageConfigEnum.EVENT_INVEST;
                break;
            case JUDGED_TASK:
                messageConfigEnum = MessageConfigEnum.EVENT_JUDGE;
                break;
        }
        return messageConfigEnum;
    }

    /**
     * 消息转交
     */
    private void sendTransferMessage(TaskTransferDTO taskTransferDTO,
                                     TbCdcewEmergencyEventProcessTask old) {
        MessageConfigEnum messageConfigEnum = getMessageConfigEnum(old.getTaskType());
        if (messageConfigEnum == null) {
            return;
        }

        SignalPushRuleDTO pushRuleDTO = new SignalPushRuleDTO();
        BeanUtils.copyProperties(taskTransferDTO, pushRuleDTO);

        pushRuleDTO.setRepeatStartTime(taskTransferDTO.getRepeatStartTime() != null ?
                CdcDateUtil.concatTimestamp(DateUtil.today(), taskTransferDTO.getRepeatStartTime()) : null);
        pushRuleDTO.setRepeatEndTime(taskTransferDTO.getRepeatEndTime() != null ?
                CdcDateUtil.concatTimestamp(DateUtil.today(), taskTransferDTO.getRepeatEndTime()) : null);

        warningSignalService.sendSignalPushMessage(pushRuleDTO,
                old.getChargePersonId(), old.getChargePersonName(),
                taskTransferDTO.getTransferToUserId(), taskTransferDTO.getTransferToUserName(),
                old.getEventId(), messageConfigEnum.getId());
    }

    @Override
    public TbCdcewEmergencyEventProcessTask selectByEventIdAndTaskType(String eventId, String taskType) {
        LambdaQueryWrapper<TbCdcewEmergencyEventProcessTask> queryWrapper = lambdaQueryWrapper();
        queryWrapper.eq(TbCdcewEmergencyEventProcessTask::getEventId, eventId);
        queryWrapper.eq(TbCdcewEmergencyEventProcessTask::getTaskType, taskType);
        return getOne(queryWrapper);
    }


    @Override
    public TbCdcmrExportTask exportTaskList(EventTaskQueryDTO dto) {
        if (CollUtil.isEmpty(dto.getIds()) && StringUtils.isNotEmpty(dto.getId())) {
            List<String> ids = Lists.newArrayList();
            ids.add(dto.getId());
            dto.setIds(ids);
        }
        List<EventTaskVO> dataList = tbCdcewEmergencyEventProcessTaskMapper.selectTaskList(dto);
        dto.setAppCode("cdc-platform-api");
        String taskType = dto.getTaskType();
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getByCode(taskType);
        if (taskTypeEnum == null) {
            throw new MedicalBusinessException("任务类型未知: " + taskType);
        }
        switch (taskTypeEnum) {
            case JUDGED_TASK:
                dto.setExportType("judged-task");
                List<JudgedTaskVO> judgedTaskVOList = new ArrayList<>();
                dataList.forEach(item -> {
                    JudgedTaskVO judgedTaskVO = new JudgedTaskVO();
                    BeanUtils.copyProperties(item, judgedTaskVO);
                    judgedTaskVOList.add(judgedTaskVO);
                });
                return doExport(dto, judgedTaskVOList, JudgedTaskVO.class);
            case INVESTED_TASK:
                dto.setExportType("invested-task");
                List<InvestedTaskVO> investedTaskVOList = new ArrayList<>();
                dataList.forEach(item -> {
                    InvestedTaskVO investedTaskVO = new InvestedTaskVO();
                    BeanUtils.copyProperties(item, investedTaskVO);
                    investedTaskVOList.add(investedTaskVO);
                });
                return doExport(dto, investedTaskVOList, InvestedTaskVO.class);
            default:
                log.warn("{}: 未知导出类型!", taskTypeEnum.getCode());
                break;
        }

        return new TbCdcmrExportTask();
    }


    /**
     * 执行导出
     * @param <T>
     */
    private <T> TbCdcmrExportTask doExport(EventTaskQueryDTO dto, List<T> dataList, Class<T> clazz) {
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                null,
                dto.getModuleType(),
                dto.getExportType(),
                dto.getAppCode());

        return exportTaskService.addAndUploadFile(dto,
                () -> dataList,
                () -> CollUtil.isEmpty(dataList) ? 0 : dataList.size(),
                taskDTO,
                clazz,
                Function.identity());
    }



}

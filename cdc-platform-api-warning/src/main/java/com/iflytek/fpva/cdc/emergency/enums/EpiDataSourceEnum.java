package com.iflytek.fpva.cdc.emergency.enums;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum EpiDataSourceEnum {
    MASS_UNEXPLAINED_DISEASE("5", "massUnexplainedDisease","症侯群预警"),
    SYNDROME("5", "SYNDROME","症侯群预警"),
    OTHER("6", "OTHER","其他"),
    SCHOOL ("7", "SCHOOL", "学校预警"),

    LIVING_ADDR("8","LIVING_ADDR", "居住地预警"),

    COMPANY("9","COMPANY", "单位预警"),

    INFECTIOUS_WARNING ("11","infected", "传染病预警"),

    SYNDROME_WARNING("12","syndrome", "症候群异常预警"),

    PATHOGEN_WARNING("13","pathogen_warning", "病原预警"),

    EPIDEMIC_CLUES("14","epidemic_clues", "疫情线索");



    private final String code;

    private final String signalType;
    private final String desc;

    EpiDataSourceEnum(String code, String signalType, String desc) {
        this.code = code;
        this.signalType = signalType;
        this.desc = desc;
    }

    public static String getCodeBySignalType(String signalType){
        return Arrays.stream(EpiDataSourceEnum.values()).filter(c -> c.getSignalType().equals(signalType)).findFirst()
                .orElse(OTHER).getCode();
    }

}

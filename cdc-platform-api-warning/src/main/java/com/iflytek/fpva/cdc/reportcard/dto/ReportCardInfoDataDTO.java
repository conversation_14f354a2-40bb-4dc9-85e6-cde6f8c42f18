package com.iflytek.fpva.cdc.reportcard.dto;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import lombok.Data;

import java.util.Date;

@Data
public class ReportCardInfoDataDTO {

    /**
     * 卡片ID
     */

    private String reportCardId;

    /**
     * 卡片编号
     */

    private String reportCardCode;

    /**
     * 卡片状态
     */

    private String status;

    /**
     * 报告类别
     */

    private String reportClass;


    /**
     * 患者姓名
     */

    private String name;

    /**
     * 患儿家长姓名
     */

    private String parentName;

    /**
     * 有效证件类型
     */

    private String validCertType;

    /**
     * 有效证件号码
     */

    private String validCertNumber;

    /**
     * 性别
     */

    private String sexDesc;

    /**
     * 出生日期
     */

    private Date birthday;

    /**
     * 年龄
     */

    private String age;

    /**
     * 年龄单位
     */

    private String ageUnit;


    /**
     * 患者工作单位
     */

    private String company;

    /**
     * 联系电话
     */

    private String phone;

    /**
     * 病人属于
     */

    private String attribution;

    /**
     * 现住地址国标
     */

    private String addressCode;

    /**
     * 现住详细地址
     */

    private String addressName;

    /**
     * 人群分类
     */

    private String humanCategory;

    /**
     * 症状体征
     */

    private String symptomSign;

    /**
     * 病例分类
     */

    private String casesCategory;

    /**
     * 病例分类2
     */

    private String casesCategory2;

    /**
     * 发病日期
     */

    private Date onsetDate;

    /**
     * 诊断时间
     */

    private Date diagnoseTime;

    /**
     * 死亡日期
     */

    private Date deathDate;

    /**
     * 疾病名称
     */

    private String diseaseName;

    /**
     * 接触史
     */

    private String contactHistory;



    private String quarantineFlag;

    /**
     * 订正前病种
     */

    private String revisedPreviousDisease;

    /**
     * 订正前诊断时间
     */

    private Date revisedPreviousDiagnoseTime;

    /**
     * 订正前终审时间
     */

    private Date revisedPreviousCheckTime;

    /**
     * 填卡医生
     */

    private String fillDoctor;

    /**
     * 医生填卡日期
     */

    private Date fillDate;

    /**
     * 报告单位地区编码
     */

    private String unitCode;

    /**
     * 报告单位
     */

    private String unitName;

    /**
     * 单位类型
     */

    private String unitType;

    /**
     * 报告卡录入时间
     */

    private Date recordTime;

    /**
     * 录卡用户
     */

    private String recordUser;

    /**
     * 录卡用户所属单位
     */

    private String recordUserCompany;

    /**
     * 县区审核时间
     */

    private Date districtCheckTime;

    /**
     * 地市审核时间
     */

    private Date cityCheckTime;

    /**
     * 省市审核时间
     */

    private Date provinceCheckTime;

    /**
     * 审核状态
     */

    private String checkStatus;

    /**
     * 订正报告时间
     */

    private Date revisedReportTime;

    /**
     * 订正终审时间
     */

    private Date revisedFinalCheckTime;

    /**
     * 终审死亡时间
     */

    private Date finalCheckDeathTime;

    /**
     * 订正用户
     */

    private String revisedUser;

    /**
     * 订正用户所属单位
     */

    private String revisedUserCompany;

    /**
     * 删除时间
     */

    private Date deleteTime;

    /**
     * 删除用户
     */

    private String deleteUser;

    /**
     * 删除用户所属单位
     */

    private String deleteUserCompany;

    /**
     * 删除原因
     */

    private String deleteReason;

    /**
     * 备注
     */

    private String remark;

    /**
     * 疾病编码
     */
    private String diseaseCode;


    /**
     * 报告卡类型区分 01 法定传染病 02 非法定传染病 09 其他疾病 10 其他信息报送
     */
    private String reportCardClassType;


    private String sentinelTitle;

    private String sentinelCode;

    private String sentinelRegionName;

    private String sentinelRegionCode;

    private String sentinelAddressDetail;

}

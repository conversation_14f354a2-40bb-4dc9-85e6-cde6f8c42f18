package com.iflytek.fpva.cdc.reportcard.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.reportcard.dto.qcs.ReportQcsAnalysisDTO;
import com.iflytek.fpva.cdc.reportcard.vo.qcs.DelayedReportVO;
import com.iflytek.fpva.cdc.reportcard.vo.qcs.LeakReportsVO;
import com.iflytek.fpva.cdc.reportcard.vo.qcs.QcsReportVO;
import com.iflytek.fpva.cdc.reportcard.vo.qcs.StatisticalAnalysisVO;

import java.util.List;

public interface ReportQcsAnalysisService {

    List<StatisticalAnalysisVO> diseaseName(ReportQcsAnalysisDTO dto);

    List<StatisticalAnalysisVO> fillDate(ReportQcsAnalysisDTO dto);

    List<StatisticalAnalysisVO> district(ReportQcsAnalysisDTO dto);

    List<StatisticalAnalysisVO> personType(ReportQcsAnalysisDTO dto);

    byte[] leakReportsExport(ReportQcsAnalysisDTO dto);

    byte[] delayedReportsExport(ReportQcsAnalysisDTO dto);

    PageInfo<LeakReportsVO> leakReport(ReportQcsAnalysisDTO dto);

    PageInfo<DelayedReportVO> delayedReport(ReportQcsAnalysisDTO dto);

    List<QcsReportVO> getRegionLeakReportList(ReportQcsAnalysisDTO dto);

    List<QcsReportVO> getOrgLeakReportList(ReportQcsAnalysisDTO dto);

    byte[] exportRegionLeakReportList(ReportQcsAnalysisDTO dto);

    byte[] exportOrgLeakReportList(ReportQcsAnalysisDTO dto);

    List<StatisticalAnalysisVO> ageType(ReportQcsAnalysisDTO dto);

    List<StatisticalAnalysisVO> sexNameType(ReportQcsAnalysisDTO dto);

    List<QcsReportVO> getTopTenRegionLeakReports(ReportQcsAnalysisDTO dto);

    List<QcsReportVO> getTopTenOrgLeakReports(ReportQcsAnalysisDTO dto);
}

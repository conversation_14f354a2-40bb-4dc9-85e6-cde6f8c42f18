package com.iflytek.fpva.cdc.warning.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "信号处理记录")
@ExcelIgnoreUnannotated
public class WarningProcessTaskVO {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @ExcelProperty(value = "ID", order = 1)
    private String id ;

    /**
     * 信号id
     */
    @ApiModelProperty(value = "信号id")
    @ExcelProperty(value = "信号ID", order = 4)
    private String signalId ;

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private String chargePersonId ;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @ExcelProperty(value = "处理人", order = 3)
    private String chargePersonName ;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName ;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private String taskStatus ;

    @ExcelProperty(value = "状态", order = 10)
    private String taskStatusText;

    /**
     * 任务结果
     */
    @ApiModelProperty(value = "任务结果")
    @ExcelProperty(value = "核实结果", order = 11)
    private String taskResult ;

    /**
     * 任务截止日期
     */
    @ApiModelProperty(value = "任务截止日期")
    @ExcelProperty(value = "处理时间", order = 2)

    private Date endTime ;

    /**
     * 任务开始日期
     */
    @ApiModelProperty(value = "任务开始日期")
    private Date startTime ;

    /**
     * 任务完成时间
     */
    @ApiModelProperty(value = "任务完成时间")
    private Date finishedTime ;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private String taskType ;

    /**
     * 信号预警类型,syndrome或infected
     */
    @ApiModelProperty(value = "信号预警类型,syndrome或infected")
    private String warningType ;

    /**
     * 任务备注
     */
    @ApiModelProperty(value = "任务备注")
    private String notes ;

    /**
     * 任务创建人
     */
    @ApiModelProperty(value = "任务创建人")
    private String creator ;

    /**
     * 任务创建人id
     */
    @ApiModelProperty(value = "任务创建人id")
    private String creatorId ;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime ;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

    /**
     * 信号相关字段冗余
     * */
    @ApiModelProperty(value = "信号发生地")
    @ExcelProperty(value = "预警地点", order = 5)
    private String statDimName;

    @ApiModelProperty(value = "信号所在市")
    @ExcelProperty(value = "所在市", order = 6)
    private String cityName;

    @ApiModelProperty(value = "信号所在县区")
    @ExcelProperty(value = "所在区", order = 7)
    private String districtName;

    @ApiModelProperty(value = "信号所造乡镇街道")
    @ExcelProperty(value = "所在街道", order = 8)
    private String streetName;


    @ApiModelProperty(value = "病种")
    @ExcelProperty(value = "病种", order = 9)
    private String diseaseName;

    @ApiModelProperty(value = "病种编码")
    private String diseaseCode;


    @ApiModelProperty(value = "是否已处理: 1: 已处理 0: 未处理")
    private String hasDealt;


}

package com.iflytek.fpva.cdc.warning.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 多渠道预警信号明细表;
 * <AUTHOR> dingyuan
 * @date : 2024-9-20
 */
@ApiModel(value = "多渠道预警信号明细表")
@Data
public class TbCdcewMultichannelWarningSignalDetail implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id ;

    /**
     * 信号ID
     */
    @ApiModelProperty(value = "信号ID")
    private String signalId ;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private Date fullDate ;

    /**
     * 预警地点
     */
    @ApiModelProperty(value = "预警地点")
    private String statLocGroupId ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String statLocGroupName ;

    /**
     * 疾病编码
     */
    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode ;

    /**
     * 疾病名称
     */
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName ;

    /**
     * 死亡数
     */
    @ApiModelProperty(value = "死亡数")
    private Integer deathCnt ;

    /**
     * 病例数
     */
    @ApiModelProperty(value = "病例数")
    private Integer medicalCaseCnt ;

    /**
     * 治愈数
     */
    @ApiModelProperty(value = "治愈数")
    private Integer recoverCnt ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

}

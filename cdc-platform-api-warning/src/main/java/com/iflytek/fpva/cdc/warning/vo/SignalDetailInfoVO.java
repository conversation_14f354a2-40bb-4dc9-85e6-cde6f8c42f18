package com.iflytek.fpva.cdc.warning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SignalDetailInfoVO {

    @ApiModelProperty(value = "信号id")
    private String signalId;

    @ApiModelProperty(value = "信号编码")
    private String signalNum;

    @ApiModelProperty(value = "预警地点")
    private String statDimName;
    
    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;
    
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;
    
    @ApiModelProperty(value = "病例数")
    private Integer caseCount;

    @ApiModelProperty(value = "预警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warningTime;
    
    @ApiModelProperty(value = "风险等级编码")
    private String riskLevelId;
    
    @ApiModelProperty(value = "风险等级名称")
    private String riskLevelDesc;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

}

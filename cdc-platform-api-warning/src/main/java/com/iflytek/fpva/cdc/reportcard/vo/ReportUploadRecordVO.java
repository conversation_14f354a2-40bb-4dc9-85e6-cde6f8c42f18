package com.iflytek.fpva.cdc.reportcard.vo;

import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.fpva.cdc.common.annotation.Sensitive;
import com.iflytek.fpva.cdc.common.enums.SensitiveTypeEnum;
import com.iflytek.fpva.cdc.reportcard.entity.ReportUploadRecord;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ReflectionUtils;

/**
 * <p>
 * 上传报告卡记录（报告卡管理和重卡识别工具共用）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ReportUploadRecord对象", description = "上传报告卡记录")
@Slf4j
public class ReportUploadRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TB_NAME = "tb_cdcew_report_upload_record";

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "附件ID")
    private String attachmentId;

    @ApiModelProperty(value = "卡片id")
    private String reportCardId;

    @ApiModelProperty(value = "卡片状态")
    private String status;

    @ApiModelProperty(value = "患者姓名")
    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    private String name;

    @ApiModelProperty(value = "患儿家长姓名")
    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    private String parentName;

    @ApiModelProperty(value = "有效证件类型")
    private String validCertType;

    @ApiModelProperty(value = "有效证件号")
    @Sensitive(type = SensitiveTypeEnum.ID_NUM)
    private String validCertNumber;

    @ApiModelProperty(value = "患者工作单位")
    @Sensitive(type = SensitiveTypeEnum.COMPANY)
    private String company;

    @Sensitive(type = SensitiveTypeEnum.PHONE)
    private String phone;

    @ApiModelProperty(value = "病人属于")
    private String attribution;

    private String addressCode;

    @Sensitive(type = SensitiveTypeEnum.ADDRESS)
    @ApiModelProperty(value = "现住详细地址")
    private String addressName;

    @ApiModelProperty(value = "人群分类")
    private String humanCategory;

    @ApiModelProperty(value = "病例分类")
    private String casesCategory;

    private String casesCategory2;

    @ApiModelProperty(value = "发病日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date onsetDate;

    @ApiModelProperty(value = "诊断时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date diagnoseTime;

    @ApiModelProperty(value = "死亡日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date deathDate;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    private String diseaseCode;

    @ApiModelProperty(value = "订正前病种")
    private String revisedPreviousDisease;

    @ApiModelProperty(value = "订正前诊断时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date revisedPreviousDiagnoseTime;

    @ApiModelProperty(value = "订正前终审时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date revisedPreviousCheckTime;

    @ApiModelProperty(value = "填卡医生")
    private String fillDoctor;

    @ApiModelProperty(value = "医生填卡日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date fillDate;

    @ApiModelProperty(value = "报告单位地区编码")
    private String unitCode;

    @ApiModelProperty(value = "报告单位")
    private String unitName;

    @ApiModelProperty(value = "单位类型")
    private String unitType;

    @ApiModelProperty(value = "报告卡录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    @ApiModelProperty(value = "录卡用户")
    private String recordUser;

    @ApiModelProperty(value = "录卡用户所属单位")
    private String recordUserCompany;

    @ApiModelProperty(value = "县区审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date districtCheckTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cityCheckTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date provinceCheckTime;

    @ApiModelProperty(value = "审核状态")
    private String checkStatus;

    @ApiModelProperty(value = "订正报告时间")
    private Date revisedReportTime;

    @ApiModelProperty(value = "订正终审时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date revisedFinalCheckTime;

    @ApiModelProperty(value = "终审死亡时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finalCheckDeathTime;

    @ApiModelProperty(value = "订正用户")
    private String revisedUser;

    @ApiModelProperty(value = "订正用户所属单位")
    private String revisedUserCompany;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;

    private String deleteUser;

    private String deleteUserCompany;

    private String deleteReason;

    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String creatorId;

    private String creator;
    private String updaterId;

    private String updater;
    @ApiModelProperty(value = "主索引id")
    private String globalPersonId;

    @ApiModelProperty(value = "卡片编号")
    private String reportCardCode;

    @ApiModelProperty(value = "性别")
    private String sexDesc;

    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "年龄单位")
    private String ageUnit;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    @ApiModelProperty(value = "重卡id")
    private String repeatHash;

    @ApiModelProperty(value = "重卡处理状态 0-未处理；1-已处理")
    private Integer repeatDisposeStatus;

    @ApiModelProperty(value = "重卡扫描识别时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime repeatScanTime;

    @ApiModelProperty(value = "用户处理重卡时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime repeatDisposeTime;

    @ApiModelProperty(value = "重卡规则id")
    private String ruleId;

    @ApiModelProperty(value = "报告类别")
    private String reportClass;
    @ApiModelProperty(value = "处理删除 0未删除 1删除")
    private Integer disposeDelete;

    @ApiModelProperty(value = "消息发送状态 0-未发送；1-已发送")
    private Integer messageFlag;


    @ApiModelProperty(value = "疑似重复报告卡ID")
    private String reportCardIds;
    @ApiModelProperty(value = "加工字段：报告单位地址-省编码")
    private String reportOrgAddrProvinceCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-省名称")
    private String reportOrgAddrProvince;

    @ApiModelProperty(value = "加工字段：报告单位地址-市编码")
    private String reportOrgAddrCityCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-市名称")
    private String reportOrgAddrCity;

    @ApiModelProperty(value = "加工字段：报告单位地址-区编码")
    private String reportOrgAddrDistrictCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-区名称")
    private String reportOrgAddrDistrict;

    @ApiModelProperty(value = "EDR生命周期ID")
    private String lifeId;

    @ApiModelProperty(value = "EDR档案ID")
    private String archiveId;

    /**
     * 重卡识别主记录ID
     */
    private String mainRecordId;

    public static ReportUploadRecordVO fromEntity(ReportUploadRecord record) {
        ReportUploadRecordVO vo = new ReportUploadRecordVO();
        BeanUtils.copyProperties(record, vo);
        return vo;
    }

    /**
     * 获取字段拼接值的Hash值
     * @param filedNames
     * @return
     */
    public String repeatBy(List<String> filedNames) {
        String ret = "";
        try {
            for (String filedName : filedNames) {
                Field field = ReflectionUtils.findField(ReportUploadRecordVO.class, filedName);
                field.setAccessible(true);
                Object value = ReflectionUtils.getField(field, this);
                if (value != null) {
                    ret += value;
                }
            }
            if (StrUtil.isNotBlank(ret)){
                ret = String.valueOf(ret.hashCode()) ;
            }
        }catch (Exception e){
            log.error("获取字段拼接值的Hash值失败",e);
        }

        return ret;
    }

    public String getRepeatId() {
        return repeatHash;
    }


    /**
     * 删除标志：0-未删除，1-已删除,河南定制字段(应标)
     */
    private Integer cardDeleteFlag;

    /**
     * 删除备注 河南定制字段(应标)
     */
    private String cardDeleteRemark;

    /**
     * 排除标志：0-未排除，1-已排除,河南定制字段(应标)
     */
    private Integer cardExcludeFlag;

    /**
     * 排除备注 河南定制字段(应标)
     */
    private String cardExcludeRemark;


    /**
     * 症状体征
     */
    private String symptomSign;

    /**
     * 接触史
     */
    private String contactHistory;

    /**
     * 是否隔离
     */
    private String quarantineFlag;

    private String getQuarantineFlag(){
        if (StrUtil.equals(quarantineFlag,"1")){
            return "是";
        } else {
            return "否";
        }
    }
    /**
     * 报告卡类型区分
     */
    private String reportCardClassType;

    private String sentinelTitle;

    private String sentinelCode;

    private String sentinelRegionName;

    private String sentinelRegionCode;

    private String sentinelAddressDetail;
}

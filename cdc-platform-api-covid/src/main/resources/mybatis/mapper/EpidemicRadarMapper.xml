<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.covid.mapper.EpidemicRadarMapper">

    <select id="queryNewsInfoPage" resultType="com.iflytek.fpva.cdc.covid.dto.CovidNewsInfoSource">
        select *
        from covid_news_info_source
        <where>
            <if test="minDate != null">
                and release_time <![CDATA[>=]]> #{minDate}
            </if>
            <if test="maxDate != null">
                and release_time <![CDATA[<=]]> #{maxDate}
            </if>
            <if test="province != null and province != ''">
                and province like concat('%',#{province,jdbcType=VARCHAR},'%')
            </if>
            <if test="city != null and city != ''">
                and city like concat('%',#{city,jdbcType=VARCHAR},'%')
            </if>
            <if test="keyword != null and keyword != ''">
                and (label like concat('%',#{keyword,jdbcType=VARCHAR},'%')
                or title like concat('%',#{keyword,jdbcType=VARCHAR},'%')
                or text like concat('%',#{keyword,jdbcType=VARCHAR},'%'))
            </if>
        </where>
        order by release_time desc
    </select>

    <select id="querySpaceTimeAreaStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.SpaceTimeStatistics">
        select COALESCE(count(DISTINCT province||city),0)as exist_case_city_num,
        (select COALESCE(sum(aa.num),0) from (select count(DISTINCT province||city) as num from covid_region GROUP BY province,city) aa)as all_city_num,
        (select min(date) from covid_info_day_source) as start_date
        from covid_detail_city_source
        where  (COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)) > 0 or COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)) > 0)
        <choose>
            <when test="endTime != null">
                and date = #{endTime}
            </when>
            <otherwise>
                and date = (select max(date) from covid_info_day_source)
            </otherwise>
        </choose>
    </select>

    <select id="querySpaceTimeStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.SpaceTimeStatistics">
        select date as end_date,
        coalesce(COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)),0)as new_confirm_cnt,
        coalesce(COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)),0)as new_asymptomatic_cnt,
        coalesce(COALESCE(new_native_to_confirm_cnt,new_to_confirm_cnt),0)as new_to_confirm_cnt,
        coalesce(now_confirm_cnt,0) as now_confirm_cnt
        from covid_info_day_source ds
        <where>
            <if test="endTime != null">
                date = #{endTime}
            </if>
        </where>
        ORDER BY date desc limit 1
    </select>

    <select id="querySpaceTimeDistribution" resultType="com.iflytek.fpva.cdc.covid.dto.SpaceTimeDistribution">
        select id,province,city,coalesce(COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)),0)as new_confirm_cnt,
        coalesce(COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)),0)as new_asymptomatic_cnt,
        (select coalesce(sum(aa.num),0) from (select count(DISTINCT province||city) num from covid_risk_area_source
        where date = ds.date and risk_level in('高风险','中风险') and province = ds.province and city = ds.city)aa)as risk_area_temp_cnt,
        (select count(DISTINCT province||city) num from covid_risk_area_source
        where date = ds.date)as risk_area_all_cnt
        from covid_detail_city_source ds
        where (COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)) > 0 or COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)) > 0)
        <choose>
            <when test="endTime != null">
                and date = #{endTime}
            </when>
            <otherwise>
                and date = (select max(date) from covid_info_day_source)
            </otherwise>
        </choose>
    </select>

    <select id="queryInfectStatisticsTime" resultType="com.iflytek.fpva.cdc.covid.dto.InfectStatisticsDto">
        select max(date) as end_time,
        (select max(date_time) from covid_risk_area_source)as risk_time
        from covid_detail_province_source_province
        <where>
            <if test="province != null and province != ''">
                province = #{province}
            </if>
        </where>
    </select>

    <select id="queryInfectStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.InfectStatistics">
        select rr.province,rr.city,#{endTime} as date,
        (select max(id) from covid_detail_province_source_province where date = #{endTime} and province = rr.province)as id,
        COALESCE(cs.new_confirm_cnt,0)as new_confirm_cnt,
        COALESCE(cs.new_asymptomatic_cnt,0)as new_asymptomatic_cnt,
        COALESCE(cs.now_confirm_cnt,0)as now_confirm_cnt,
        COALESCE(cs.now_asymptomatic_cnt,0)as now_asymptomatic_cnt,
        COALESCE(ra.high_risk_area,0)as high_risk_area,
        COALESCE(ra.mid_risk_area,0)as mid_risk_area,
        COALESCE((COALESCE(cs.new_confirm_cnt,0)+COALESCE(cs.new_asymptomatic_cnt,0)),0)as new_infected_cnt,
        (select max(date) from covid_detail_city_source where province = rr.province and city = rr.city and (COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)) > 0 or COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)) > 0))as last_confirm_cnt_date,
        (select COALESCE(sum(COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0))+COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0))),0) from covid_detail_city_source where province = rr.province and city = rr.city and (COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)) > 0 or COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)) > 0)
        and date between #{sevenTime} and #{endTime}
        )as seven_confirm_cnt,
        (select COALESCE(sum(COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0))+COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0))),0) from covid_detail_city_source where province = rr.province and city = rr.city and (COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)) > 0 or COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)) > 0)
        and date between #{fourteenTime} and #{endTime}
        )as fourteen_confirm_cnt
        from (select province,city from covid_region group by province,city) rr
        left join (select province,city,date,
        COALESCE(new_confirm_native_cnt,COALESCE(new_confirm_cnt,0)-COALESCE(new_confirm_inport_cnt,0)) as new_confirm_cnt,
        COALESCE(new_asymptomatic_native_cnt,COALESCE(new_asymptomatic_cnt,0)-COALESCE(new_asymptomatic_inport_cnt,0)) as new_asymptomatic_cnt,
        COALESCE(now_confirm_native_cnt,now_confirm_cnt) as now_confirm_cnt,
        COALESCE(now_asymptomatic_native_cnt,now_asymptomatic_cnt) as now_asymptomatic_cnt from covid_detail_city_source where date = #{endTime}) cs on cs.province = rr.province and cs.city = rr.city
        left join(select province,city,
        sum(case when trim(risk_level)= '高风险' then 1 else 0 end) as high_risk_area,
        sum(case when trim(risk_level)= '中风险' then 1 else 0 end) as mid_risk_area
        from  covid_risk_area_source where
        date_time = #{riskTime}
        GROUP BY province,city)ra on ra.province = rr.province and ra.city = rr.city
        <where>
            <if test="province != null and province != ''">
                rr.province = #{province}
            </if>
        </where>
        order by last_confirm_cnt_date desc nulls last,new_infected_cnt desc
    </select>

    <select id="queryInfectNewStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.InfectCount">
        select COALESCE(COALESCE(ss.new_confirm_native_cnt,COALESCE(ss.new_confirm_cnt,0)-COALESCE(ss.new_confirm_inport_cnt,0)),0) as first_num,pp.date from
        covid_info_day_source pp
        left join covid_detail_city_source ss on ss.date = pp.date
            <if test="province != null and province != ''">
                and ss.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and ss.city = #{city}
            </if>
        order by pp.date
    </select>

    <select id="queryInfectNowStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.InfectCount">
        select COALESCE(COALESCE(ss.new_asymptomatic_native_cnt,COALESCE(ss.new_asymptomatic_cnt,0)-COALESCE(ss.new_asymptomatic_inport_cnt,0)),0) as first_num,pp.date from
        covid_info_day_source pp
        left join covid_detail_city_source ss on ss.date = pp.date
            <if test="province != null and province != ''">
                and ss.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and ss.city = #{city}
            </if>
        order by pp.date
    </select>

    <select id="queryInfectDeadStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.InfectCount">
        select COALESCE(ss.new_dead_cnt,0) as first_num,pp.date from
        covid_info_day_source pp
        left join covid_detail_city_source ss on ss.date = pp.date
        <if test="province != null and province != ''">
            and ss.province = #{province}
        </if>
        <if test="city != null and city != ''">
            and ss.city = #{city}
        </if>
        order by pp.date
    </select>

    <select id="queryInfectAreaStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.InfectCount">
        select pp.date,
        count(DISTINCT case when trim(ss.risk_level) = '高风险' then ss.area else null end)as first_num,
        count(DISTINCT case when trim(ss.risk_level) = '中风险' then ss.area else null end)as second_num,
        count(DISTINCT case when trim(ss.risk_level) = '低风险' then ss.area else null end)as third_num
        from
        (select date from covid_risk_area_source GROUP BY date) pp
        left join covid_risk_area_source ss on ss.date = pp.date
        <if test="province != null and province != ''">
            and ss.province = #{province}
        </if>
        <if test="city != null and city != ''">
            and ss.city = #{city}
        </if>
        GROUP BY ss.province,ss.city,pp.date
        order by pp.date
    </select>

    <select id="queryRiskAreaStatistics" resultType="com.iflytek.fpva.cdc.covid.dto.RiskArea">
        select date_time,province,city,district,area,risk_level from covid_risk_area_source where date_time = (select max(date_time) from covid_risk_area_source)
    </select>

    <select id="queryRegionList" resultType="com.iflytek.fpva.cdc.covid.dto.Region">
        select province,city from covid_region group by province,city ORDER BY province,city
    </select>


</mapper>
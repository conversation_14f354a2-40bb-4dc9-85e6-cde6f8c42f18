package com.iflytek.fpva.cdc.covid.service;


import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.covid.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 疫情雷达
 * @date 2022/8/29 14:25
 */
public interface EpidemicRadarService {

    PageInfo<CovidNewsInfoSource> queryNewsInfoPage(NewsInfoQueryParam newsInfoQueryParam);

    SpaceTimeStatistics querySpaceTimeStatistics(String endTime);

    List<SpaceTimeDistribution> querySpaceTimeDistribution(String endTime);

    InfectStatisticsDto queryInfectStatistics(String province);

    List<InfectCount> queryInfectNewStatistics(String province, String city);

    List<InfectCount> queryInfectNowStatistics(String province, String city);

    List<InfectCount> queryInfectDeadStatistics(String province, String city);

    List<InfectCount> queryInfectAreaStatistics(String province, String city);

    List<RiskAreaDto> queryRiskAreaStatistics();

    List<RegionDto> queryRegionList();

    AddressDto getAddress();

    AddressDto getCurrUserAddress(String loginUserName);

}

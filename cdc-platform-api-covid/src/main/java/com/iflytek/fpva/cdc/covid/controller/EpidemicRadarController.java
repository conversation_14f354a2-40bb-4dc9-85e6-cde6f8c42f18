package com.iflytek.fpva.cdc.covid.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.covid.config.AppConfig;
import com.iflytek.fpva.cdc.covid.dto.*;
import com.iflytek.fpva.cdc.covid.service.EpidemicRadarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 疫情雷达
 * @author: shenghuang
 * @create: 2022-08-29 16:20
 **/
@Api(tags = "疫情雷达")
@RestController
public class EpidemicRadarController {

    @Resource
    private EpidemicRadarService epidemicRadarService;

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("新冠舆情追踪分页")
    @PostMapping("/{version}/pt/epidemicRadar/newsInfo/page")
    public PageInfo<CovidNewsInfoSource> queryNewsInfoPage(@PathVariable String version, @RequestBody NewsInfoQueryParam newsInfoQueryParam) {
        return epidemicRadarService.queryNewsInfoPage(newsInfoQueryParam);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("本土疫情时空分布统计")
    @GetMapping("/{version}/pt/epidemicRadar/spaceTime/statistics")
    public SpaceTimeStatistics querySpaceTimeStatistics(@PathVariable String version, @RequestParam(value = "endTime", required = false) String endTime) {
        return epidemicRadarService.querySpaceTimeStatistics(endTime);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("本土疫情时空分布城市分布")
    @GetMapping("/{version}/pt/epidemicRadar/spaceTime/distribution")
    public List<SpaceTimeDistribution> querySpaceTimeDistribution(@PathVariable String version, @RequestParam(value = "endTime", required = false) String endTime) {
        return epidemicRadarService.querySpaceTimeDistribution(endTime);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("各省市感染者统计列表")
    @GetMapping("/{version}/pt/epidemicRadar/infect/statistics")
    public InfectStatisticsDto queryInfectStatistics(@PathVariable String version, @RequestParam(value = "province", required = false) String province) {
        return epidemicRadarService.queryInfectStatistics(province);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("新增人数统计列表")
    @GetMapping("/{version}/pt/epidemicRadar/infect/newStatistics")
    public List<InfectCount> queryInfectNewStatistics(@PathVariable String version,
                                                      @RequestParam(value = "province", required = false) String province,
                                                      @RequestParam(value = "city", required = false) String city) {
        return epidemicRadarService.queryInfectNewStatistics(province, city);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("现有人数统计列表")
    @GetMapping("/{version}/pt/epidemicRadar/infect/nowStatistics")
    public List<InfectCount> queryInfectNowStatistics(@PathVariable String version,
                                                      @RequestParam(value = "province", required = false) String province,
                                                      @RequestParam(value = "city", required = false) String city) {
        return epidemicRadarService.queryInfectNowStatistics(province, city);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("死亡人数统计列表")
    @GetMapping("/{version}/pt/epidemicRadar/infect/deadStatistics")
    public List<InfectCount> queryInfectDeadStatistics(@PathVariable String version,
                                                       @RequestParam(value = "province", required = false) String province,
                                                       @RequestParam(value = "city", required = false) String city) {
        return epidemicRadarService.queryInfectDeadStatistics(province, city);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("风险地区统计列表")
    @GetMapping("/{version}/pt/epidemicRadar/infect/areaStatistics")
    public List<InfectCount> queryInfectAreaStatistics(@PathVariable String version,
                                                       @RequestParam(value = "province", required = false) String province,
                                                       @RequestParam(value = "city", required = false) String city) {
        return epidemicRadarService.queryInfectAreaStatistics(province, city);
    }


    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("全国高中风险地区查询")
    @GetMapping("/{version}/pt/epidemicRadar/riskArea/statistics")
    public List<RiskAreaDto> queryRiskAreaStatistics(@PathVariable String version) {
        return epidemicRadarService.queryRiskAreaStatistics();
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("全国省市查询")
    @GetMapping("/{version}/pt/epidemicRadar/region")
    public List<RegionDto> queryRegionList(@PathVariable String version) {
        return epidemicRadarService.queryRegionList();
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("获取当前省市地址")
    @GetMapping("/{version}/pt/epidemicRadar/address")
    public AddressDto getAddress(@PathVariable String version) {
        return epidemicRadarService.getAddress();
    }


    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = AppConfig.COMPATIBLE_VERSION, required = true)
    @ApiOperation("获取当前用户的省市地址")
    @GetMapping("/{version}/pt/epidemicRadar/getCurrUserAddress")
    public AddressDto getCurrUserAddress(@PathVariable String version, @RequestParam String loginUserName) {
        return epidemicRadarService.getCurrUserAddress(loginUserName);
    }


}

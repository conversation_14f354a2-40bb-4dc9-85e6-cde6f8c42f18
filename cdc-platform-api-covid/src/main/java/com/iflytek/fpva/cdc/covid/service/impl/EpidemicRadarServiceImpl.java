package com.iflytek.fpva.cdc.covid.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.apiService.UapServiceApi;
import com.iflytek.fpva.cdc.covid.config.QueryParamConstants;
import com.iflytek.fpva.cdc.covid.dto.*;
import com.iflytek.fpva.cdc.covid.mapper.EpidemicRadarMapper;
import com.iflytek.fpva.cdc.covid.service.EpidemicRadarService;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.sec.uap.client.rest.context.UapServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 疫情雷达
 * @author: shenghuang
 * @create: 2022-08-29 16:29
 **/
@Slf4j
@Service
public class EpidemicRadarServiceImpl implements EpidemicRadarService {

    @Autowired
    private EpidemicRadarMapper epidemicRadarMapper;

    @Value("${fpva.covid.addressUrl:http://pv.sohu.com/cityjson?ie=utf-8}")
    private String addressUrl;

    @Value("${fpva.covid.province:安徽省}")
    private String province;

    @Value("${fpva.covid.city:合肥市}")
    private String city;

    @Autowired
    private UapServiceApi uapServiceApi;

    @Override
    public PageInfo<CovidNewsInfoSource> queryNewsInfoPage(NewsInfoQueryParam newsInfoQueryParam) {
        if (newsInfoQueryParam.getPageIndex() < QueryParamConstants.MIN_PAGE_INDEX || newsInfoQueryParam.getPageSize() > QueryParamConstants.MAX_PAGE_SIZE) {
            throw new MedicalBusinessException("分页参数不正确");
        }
        try {
            PageHelper.startPage(newsInfoQueryParam.getPageIndex(), newsInfoQueryParam.getPageSize());
            LocalDateTime startTime = !StringUtil.isEmpty(newsInfoQueryParam.getStartTime()) ? LocalDateTime.parse(newsInfoQueryParam.getStartTime() + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
            LocalDateTime endTime = newsInfoQueryParam.getEndTime() != null ? LocalDateTime.parse(newsInfoQueryParam.getEndTime() + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
            // 协办任务或符合检索条件的其他任务（原始任务、下级任务）
            List<CovidNewsInfoSource> newsInfoSourceList = epidemicRadarMapper.queryNewsInfoPage(startTime, endTime, newsInfoQueryParam.getProvince(), newsInfoQueryParam.getCity(),newsInfoQueryParam.getKeyword());
            return new PageInfo<>(newsInfoSourceList);
        } catch (Exception e) {
            log.error("queryNewsInfoPage报错", e);
            throw new MedicalBusinessException("查询新冠舆情追踪分页接口报错");
        }
    }

    @Override
    public SpaceTimeStatistics querySpaceTimeStatistics(String endTime) {
        SpaceTimeStatistics dto = new SpaceTimeStatistics();
        LocalDate time = !StringUtil.isEmpty(endTime) ? LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;
        SpaceTimeStatistics spaceTimeAreaStatistics = epidemicRadarMapper.querySpaceTimeAreaStatistics(time);
        if(spaceTimeAreaStatistics != null){
            BeanUtils.copyProperties(spaceTimeAreaStatistics,dto);
        }
        SpaceTimeStatistics spaceTimeStatistics = epidemicRadarMapper.querySpaceTimeStatistics(time);
        if(spaceTimeStatistics != null){
            dto.setEndDate(spaceTimeStatistics.getEndDate());
            dto.setNewConfirmCnt(spaceTimeStatistics.getNewConfirmCnt());
            dto.setNewAsymptomaticCnt(spaceTimeStatistics.getNewAsymptomaticCnt());
            dto.setNowConfirmCnt(spaceTimeStatistics.getNowConfirmCnt());
            dto.setNewToConfirmCnt(spaceTimeStatistics.getNewToConfirmCnt());
        }
        if(dto.getEndDate() == null){
            dto.setEndDate(endTime);
        }
        return dto;
    }

    @Override
    public List<SpaceTimeDistribution> querySpaceTimeDistribution(String endTime) {
        List<SpaceTimeDistribution> result = new ArrayList<>();
        LocalDate time = !StringUtil.isEmpty(endTime) ? LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;
        List<SpaceTimeDistribution> list = epidemicRadarMapper.querySpaceTimeDistribution(time);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        Map<String, List<SpaceTimeDistribution>> collect = list.stream().collect(Collectors.groupingBy(g -> g.getProvince()));
        collect.entrySet().forEach(f -> {
            SpaceTimeDistribution distribution = new SpaceTimeDistribution();
            distribution.setProvince(f.getKey());
            List<SpaceTimeDistribution> value = f.getValue();
            if (!CollectionUtils.isEmpty(value)) {
                Long newConfirmCnt = value.stream().collect(Collectors.summingLong(SpaceTimeDistribution::getNewConfirmCnt));
                distribution.setNewConfirmCnt(newConfirmCnt);
                Long newAsymptomaticCnt = value.stream().collect(Collectors.summingLong(SpaceTimeDistribution::getNewAsymptomaticCnt));
                distribution.setNewAsymptomaticCnt(newAsymptomaticCnt);
                Long riskAreaAllCnt = value.stream().collect(Collectors.summingLong(SpaceTimeDistribution::getRiskAreaAllCnt));
                if(riskAreaAllCnt != null && riskAreaAllCnt > 0){
                    Long riskAreaCnt = value.stream().filter(g->g.getRiskAreaTempCnt() != null).collect(Collectors.summingLong(SpaceTimeDistribution::getRiskAreaTempCnt));
                    distribution.setRiskAreaCnt(riskAreaCnt);
                }
                value.sort(Comparator.comparing(SpaceTimeDistribution::getNewConfirmCnt,Comparator.reverseOrder()).thenComparing(SpaceTimeDistribution::getNewAsymptomaticCnt,Comparator.reverseOrder())
                        .thenComparing(SpaceTimeDistribution::getRiskAreaTempCnt,Comparator.reverseOrder()));
                distribution.setChildren(value);
                result.add(distribution);
            }
        });
        result.sort(Comparator.comparing(SpaceTimeDistribution::getNewConfirmCnt,Comparator.reverseOrder()).thenComparing(SpaceTimeDistribution::getNewAsymptomaticCnt,Comparator.reverseOrder())
                .thenComparing(a->(a.getRiskAreaCnt() == null ? 0 : a.getRiskAreaCnt()),Comparator.reverseOrder()));
        return result;
    }

    @Override
    public InfectStatisticsDto queryInfectStatistics(String province) {
        //获取最新的截止时间和高风险时间
        InfectStatisticsDto dto = epidemicRadarMapper.queryInfectStatisticsTime(province);
        if (dto == null || dto.getEndTime() == null || dto.getRiskTime() == null) {
            return new InfectStatisticsDto();
        }
        dto.setProvince(province);
        dto.setSevenTime(dto.getEndTime().minusDays(7));
        dto.setFourteenTime(dto.getEndTime().minusDays(14));
        List<InfectStatistics> list = epidemicRadarMapper.queryInfectStatistics(dto);
        dto.setList(list);
        return dto;
    }

    @Override
    public List<InfectCount> queryInfectNewStatistics(String province, String city) {
        return epidemicRadarMapper.queryInfectNewStatistics(province, city);
    }

    @Override
    public List<InfectCount> queryInfectNowStatistics(String province, String city) {
        return epidemicRadarMapper.queryInfectNowStatistics(province, city);
    }

    @Override
    public List<InfectCount> queryInfectDeadStatistics(String province, String city) {
        return epidemicRadarMapper.queryInfectDeadStatistics(province, city);
    }

    @Override
    public List<InfectCount> queryInfectAreaStatistics(String province, String city) {
        return epidemicRadarMapper.queryInfectAreaStatistics(province, city);
    }

    @Override
    public List<RiskAreaDto> queryRiskAreaStatistics() {
        List<RiskAreaDto> result = new ArrayList<>();
        List<RiskArea> list = epidemicRadarMapper.queryRiskAreaStatistics();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<RiskArea> highRiskAreaList = list.stream().filter(f -> "高风险".equals(f.getRiskLevel())).collect(Collectors.toList());
        RiskAreaDto highRiskArea = new RiskAreaDto();
        highRiskArea.setName("高风险地区");
        highRiskArea.setDateTime(list.get(0).getDateTime());
        highRiskArea.setNum(highRiskAreaList.size());
        highRiskArea.setProvinceList(getRiskAreaProvince(highRiskAreaList));
        result.add(highRiskArea);
        List<RiskArea> midRiskAreaList = list.stream().filter(f -> "中风险".equals(f.getRiskLevel())).collect(Collectors.toList());
        RiskAreaDto midRiskArea = new RiskAreaDto();
        midRiskArea.setName("中风险地区");
        midRiskArea.setDateTime(list.get(0).getDateTime());
        midRiskArea.setNum(midRiskAreaList.size());
        midRiskArea.setProvinceList(getRiskAreaProvince(midRiskAreaList));
        result.add(midRiskArea);
        List<RiskArea> lowRiskAreaList = list.stream().filter(f -> "低风险".equals(f.getRiskLevel())).collect(Collectors.toList());
        RiskAreaDto lowRiskArea = new RiskAreaDto();
        lowRiskArea.setName("低风险地区");
        lowRiskArea.setDateTime(list.get(0).getDateTime());
        lowRiskArea.setNum(lowRiskAreaList.size());
        lowRiskArea.setProvinceList(getRiskAreaProvince(lowRiskAreaList));
        result.add(lowRiskArea);
        return result;
    }

    @Override
    public List<RegionDto> queryRegionList() {
        List<RegionDto> result = new ArrayList<>();
        List<Region> list = epidemicRadarMapper.queryRegionList();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        Map<String, List<Region>> collect = list.stream().collect(Collectors.groupingBy(Region::getProvince));
        collect.entrySet().forEach(f -> {
            RegionDto dto = new RegionDto();
            dto.setProvince(f.getKey());
            List<String> cityList = f.getValue().stream().map(Region::getCity).distinct().collect(Collectors.toList());
            dto.setCityList(cityList);
            result.add(dto);
        });
        return result;
    }

    @Override
    public AddressDto getAddress() {
        AddressDto dto = new AddressDto();
        dto.setProvince(province);
        dto.setCity(city);
        return dto;
    }

    @Override
    public AddressDto getCurrUserAddress(String loginUserName) {
        UapOrgPo userOrg = uapServiceApi.getUserOrg(loginUserName);
        AddressDto dto = new AddressDto();
        dto.setProvince(userOrg.getProvince());
        dto.setCity(userOrg.getCity());
        return dto;
    }

    /**
     * 获取高中风险数据
     *
     * @param highRiskAreaList
     * @return
     */
    private List<RiskAreaProvince> getRiskAreaProvince(List<RiskArea> highRiskAreaList) {
        List<RiskAreaProvince> provinceList = new ArrayList<>();
        if (CollectionUtils.isEmpty(highRiskAreaList)) {
            return provinceList;
        }
        Map<String, List<RiskArea>> province = highRiskAreaList.stream().collect(Collectors.groupingBy(RiskArea::getProvince));
        province.entrySet().forEach(f -> {
            RiskAreaProvince riskAreaProvince = new RiskAreaProvince();
            riskAreaProvince.setName(f.getKey());
            riskAreaProvince.setNum(f.getValue().size());
            Map<String, List<RiskArea>> city = f.getValue().stream().collect(Collectors.groupingBy(RiskArea::getCity));
            List<RiskAreaCity> cityList = new ArrayList<>();
            city.entrySet().forEach(h -> {
                RiskAreaCity riskAreaCity = new RiskAreaCity();
                riskAreaCity.setName(h.getKey());
                riskAreaCity.setNum(h.getValue().size());
                List<String> areaList = h.getValue().stream().map(m -> m.getDistrict() + " " + m.getArea()).collect(Collectors.toList());
                riskAreaCity.setAreaList(areaList);
                cityList.add(riskAreaCity);
            });
            cityList.sort(Comparator.comparing(RiskAreaCity::getNum).reversed());
            riskAreaProvince.setCityList(cityList);
            provinceList.add(riskAreaProvince);
        });
        provinceList.sort(Comparator.comparing(RiskAreaProvince::getNum).reversed());
        return provinceList;
    }
}

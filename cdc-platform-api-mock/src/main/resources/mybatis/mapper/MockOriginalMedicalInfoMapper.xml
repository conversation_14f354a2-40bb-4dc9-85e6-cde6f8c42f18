<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.mock.mapper.MockOriginalMedicalInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.mock.entity.MockOriginalMedicalInfo">
    <id column="source_key" jdbcType="VARCHAR" property="sourceKey" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="etl_date" jdbcType="TIMESTAMP" property="etlDate" />
    <result column="full_date" jdbcType="DATE" property="fullDate" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="med_type" jdbcType="VARCHAR" property="medType" />
    <result column="main_suit" jdbcType="VARCHAR" property="mainSuit" />
    <result column="pulse" jdbcType="VARCHAR" property="pulse" />
    <result column="illness_history" jdbcType="VARCHAR" property="illnessHistory" />
    <result column="previous_history" jdbcType="VARCHAR" property="previousHistory" />
    <result column="family_history" jdbcType="VARCHAR" property="familyHistory" />
    <result column="irritability_history" jdbcType="VARCHAR" property="irritabilityHistory" />
    <result column="aux_exam" jdbcType="VARCHAR" property="auxExam" />
    <result column="diastolic_pressure" jdbcType="VARCHAR" property="diastolicPressure" />
    <result column="mean_pressure" jdbcType="VARCHAR" property="meanPressure" />
    <result column="systolic_pressure" jdbcType="VARCHAR" property="systolicPressure" />
    <result column="temperature" jdbcType="VARCHAR" property="temperature" />
    <result column="respiratory_rate" jdbcType="VARCHAR" property="respiratoryRate" />
    <result column="blood_oxygen" jdbcType="VARCHAR" property="bloodOxygen" />
    <result column="heart_rate" jdbcType="VARCHAR" property="heartRate" />
    <result column="fasting_glucose" jdbcType="VARCHAR" property="fastingGlucose" />
    <result column="postprandial_glucose" jdbcType="VARCHAR" property="postprandialGlucose" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="height" jdbcType="VARCHAR" property="height" />
    <result column="checkup_other" jdbcType="VARCHAR" property="checkupOther" />
    <result column="assistant_diagnose_result" jdbcType="VARCHAR" property="assistantDiagnoseResult" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
    <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="sex_desc" jdbcType="VARCHAR" property="sexDesc" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="idcard_no" jdbcType="VARCHAR" property="idcardNo" />
    <result column="residential_address" jdbcType="VARCHAR" property="residentialAddress" />
    <result column="outpatient_time" jdbcType="TIMESTAMP" property="outpatientTime" />
    <result column="diagnose_age" jdbcType="VARCHAR" property="diagnoseAge" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="diagnostic_code1" jdbcType="VARCHAR" property="diagnosticCode1" />
    <result column="diagnostic_name1" jdbcType="VARCHAR" property="diagnosticName1" />
    <result column="diagnostic_code2" jdbcType="VARCHAR" property="diagnosticCode2" />
    <result column="diagnostic_name2" jdbcType="VARCHAR" property="diagnosticName2" />
    <result column="diagnostic_code3" jdbcType="VARCHAR" property="diagnosticCode3" />
    <result column="diagnostic_name3" jdbcType="VARCHAR" property="diagnosticName3" />
    <result column="onset_time" jdbcType="VARCHAR" property="onsetTime" />
    <result column="onsetdays" jdbcType="INTEGER" property="onsetdays" />
    <result column="data_source_code" jdbcType="VARCHAR" property="dataSourceCode" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="street_longitude" jdbcType="VARCHAR" property="streetLongitude" />
    <result column="street_latitude" jdbcType="VARCHAR" property="streetLatitude" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_longitude" jdbcType="VARCHAR" property="companyLongitude" />
    <result column="company_latitude" jdbcType="VARCHAR" property="companyLatitude" />
    <result column="address_name" jdbcType="VARCHAR" property="addressName" />
    <result column="address_longitude" jdbcType="VARCHAR" property="addressLongitude" />
    <result column="address_latitude" jdbcType="VARCHAR" property="addressLatitude" />
    <result column="medical_distribution" jdbcType="VARCHAR" property="medicalDistribution" />
    <result column="today_symptoms" jdbcType="VARCHAR" property="todaySymptoms" />
    <result column="medical_symptom_id" jdbcType="INTEGER" property="medicalSymptomId" />
    <result column="street_type" jdbcType="INTEGER" property="streetType" />
    <result column="medical_type" jdbcType="INTEGER" property="medicalType" />
    <result column="abnormal_cause" jdbcType="VARCHAR" property="abnormalCause" />
    <result column="purchased" jdbcType="VARCHAR" property="purchased" />
    <result column="medical_records" jdbcType="VARCHAR" property="medicalRecords" />
    <result column="activity_track" jdbcType="VARCHAR" property="activityTrack" />
    <result column="quantity" jdbcType="VARCHAR" property="quantity" />
    <result column="symptom" jdbcType="VARCHAR" property="symptom" />
    <result column="ai_diagnose" jdbcType="VARCHAR" property="aiDiagnose" />
    <result column="purchased_type" jdbcType="VARCHAR" property="purchasedType" />
    <result column="data_type_code" jdbcType="INTEGER" property="dataTypeCode" />
    <result column="stage" jdbcType="INTEGER" property="stage" />
    <result column="event_id" jdbcType="INTEGER" property="eventId" />
  </resultMap>
  <sql id="Base_Column_List">
    source_key, create_time, etl_date, full_date, update_time, med_type, main_suit, pulse, 
    illness_history, previous_history, family_history, irritability_history, aux_exam, 
    diastolic_pressure, mean_pressure, systolic_pressure, temperature, respiratory_rate, 
    blood_oxygen, heart_rate, fasting_glucose, postprandial_glucose, weight, height, 
    checkup_other, assistant_diagnose_result, province_code, province_name, city_code, 
    city_name, district_code, district_name, org_id, org_name, doctor_id, doctor_name, 
    patient_id, patient_name, sex_desc, phone, idcard_no, residential_address, outpatient_time, 
    diagnose_age, birthday, diagnostic_code1, diagnostic_name1, diagnostic_code2, diagnostic_name2, 
    diagnostic_code3, diagnostic_name3, onset_time, onsetdays, data_source_code, street_code, 
    street_name, street_longitude, street_latitude, company_code, company_name, company_longitude, 
    company_latitude, address_name, address_longitude, address_latitude, medical_distribution, 
    today_symptoms, medical_symptom_id, street_type, medical_type, abnormal_cause, purchased,
    medical_records, activity_track, quantity, symptom, ai_diagnose, purchased_type,data_type_code,
    stage, event_id
  </sql>
  <select id="findAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcew_mock_original_medical_info
  </select>
  <select id="findBySourceKeyList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcew_mock_original_medical_info
    where source_key in
    <foreach item="item" index="index" collection="list"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
  <select id="findByEventId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcew_mock_original_medical_info
    where event_id = #{eventId, jdbcType=INTEGER}
  </select>
</mapper>
package com.iflytek.fpva.cdc.mock.event.listener;

import com.alibaba.fastjson.JSON;
import com.iflytek.fpva.cdc.common.enums.BooleanEnum;
import com.iflytek.fpva.cdc.constant.enums.CallResultEnum;
import com.iflytek.fpva.cdc.constant.enums.MedDistributionEnum;
import com.iflytek.fpva.cdc.constant.enums.TrueOrFalseEnum;
import com.iflytek.fpva.cdc.mock.constant.MockConstant;
import com.iflytek.fpva.cdc.mock.constant.enums.MockCallResultEnum;
import com.iflytek.fpva.cdc.mock.constant.enums.MockMedicalCallStatusEnum;
import com.iflytek.fpva.cdc.mock.entity.MockMedInfo;
import com.iflytek.fpva.cdc.mock.event.MockCallResultEvent;
import com.iflytek.fpva.cdc.mock.mapper.MockDoctorMapper;
import com.iflytek.fpva.cdc.mock.mapper.MockMedInfoMapper;
import com.iflytek.fpva.cdc.mock.model.vo.MockDoctorVo;
import com.iflytek.fpva.cdc.outbound.constant.enums.ConnectStatusEnum;
import com.iflytek.fpva.cdc.outbound.model.dto.output.BatchListRs;
import com.iflytek.fpva.cdc.outbound.model.dto.output.Record;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 存储病例扩展数据和外呼明细数据
 *
 * <AUTHOR>
 * @date 2020/11/17 17:11
 */
@Slf4j
@Service
public class MockMedicalDetailListener {

    @Resource
    private MockMedInfoMapper mockMedInfoMapper;

    @Resource
    private MockDoctorMapper mockDoctorMapper;

    @Resource
    private BatchUidService batchUidService;

    @EventListener
    @Async
    public void storeMockOutCallResult(MockCallResultEvent event) {
        try {
            MockMedInfo mockMedInfo = event.getMockMedInfo();
            BatchListRs batchListRs = event.getBatchListRs();
            MockMedInfo mockMedInfo1 = new MockMedInfo();
            mockMedInfo1.setSourceKey(mockMedInfo.getSourceKey());
            mockMedInfo1.setOutcallStatus(String.valueOf(MockMedicalCallStatusEnum.CALL_FINISH.getCode()));
            String eventId = mockMedInfo.getEventId();

            List<Record> data = batchListRs.getData();
            //外呼对象：学生家长，肺炎&系统随机事件
            List<MockDoctorVo> doctorInfoList = mockDoctorMapper.getDoctorInfoList(eventId, 4);
            List<String> sourceKeys = doctorInfoList.stream().map(MockDoctorVo::getMedicalId).collect(Collectors.toList());
            boolean isSchool = sourceKeys.contains(mockMedInfo1.getSourceKey());
            boolean answer = false;

            //外呼对象：患者, 鼠疫案例，外呼牧民
            boolean isPatient = "3".equals(eventId);

            //外呼对象：家属
            List<MockDoctorVo> familyList = mockDoctorMapper.getDoctorInfoList(eventId, 6);
            List<String> familySourceKeys = familyList.stream().map(MockDoctorVo::getMedicalId).collect(Collectors.toList());
            boolean isFamily = familySourceKeys.contains(mockMedInfo1.getSourceKey());

            for (int i = 0; i < data.size(); i++) {
                Record record = data.get(i);
                try {
                    boolean success = ConnectStatusEnum.NORMAL_ANSWER.equals(record.getConnectStatus());
                    LinkedHashMap<String, String> callResult = new LinkedHashMap<>(1);
                    if (success) {
                        if (isSchool) {
                            MockMedInfo medInfo = mockMedInfoMapper.selectByPrimaryKey(mockMedInfo1.getSourceKey());
                            answer = TrueOrFalseEnum.YES.getDesc1().equals(record.getWetherDiarrhea());
                            callResult.put("外呼状态", record.getCallResult());
                            callResult.put("症状问询", record.getWetherDiarrhea() + Optional.ofNullable(medInfo.getSymptom()).orElse("") + "相似症状");
                            if ("2".equals(eventId) && !TrueOrFalseEnum.YES.getDesc1().equals(record.getWetherDiarrhea())) {
                                List<MockMedInfo> mockMedInfoList2 = mockMedInfoMapper.selectByPatientId(mockMedInfo.getPatientId());
                                mockMedInfoList2.stream().forEach(mockMedInfo2 -> {
                                    MockMedInfo tempMockMedInfo = new MockMedInfo();
                                    tempMockMedInfo.setSourceKey(mockMedInfo2.getSourceKey());
                                    tempMockMedInfo.setPatientName(mockMedInfo.getTeacher());
                                    mockMedInfoMapper.updateByPrimaryKeySelective(tempMockMedInfo);
                                });
                            }
                            if ("2".equals(eventId) && answer) {
                                mockMedInfo1.setOutcallResult(String.valueOf(MockCallResultEnum.TRUE.getCode()));
                                mockMedInfo1.setStage(MockConstant.STAGE_3);
                                mockMedInfo1.setMedicalDistribution(MedDistributionEnum.OUTCALL.getCode());
                            }
                        } else if (isFamily) {
                            MockMedInfo medInfo = mockMedInfoMapper.selectByPrimaryKey(mockMedInfo1.getSourceKey());
                            answer = TrueOrFalseEnum.YES.getDesc1().equals(record.getWetherDiarrhea());
                            callResult.put("外呼状态", record.getCallResult());
                            callResult.put("症状问询", record.getWetherDiarrhea() + Optional.ofNullable(medInfo.getSymptom()).orElse("") + "相似症状");
                        } else if (isPatient) {
                            callResult.put("外呼状态", record.getCallResult());
                            callResult.put("其他症状问询", record.getExtractText1());
                            callResult.put("职业问询", record.getExtractText2());
                            callResult.put("流行病学史", record.getExtractText3());
                        } else {
                            callResult.put("外呼状态", record.getCallResult());
                            callResult.put("患者是否有传染病风险", record.getWetherDiarrhea());
                            callResult.put("患者病历内容是否真实", getBusinessDiarrheaCrowd(record));
                        }
                        String callResultStr = JSON.toJSONString(callResult);
                        mockMedInfo1.setOutcallContent(callResultStr);
                    }
                    else {
                        //外呼话术改变，临时获取接通信息
                        callResult.put("外呼结果", record.getCallResult());
                        String callResultStr = JSON.toJSONString(callResult);
                        mockMedInfo1.setOutcallContent(callResultStr);
                    }
                } catch (Exception e) {
                    log.error("组装接通用户外呼结果数据错误！！", e);
                }
            }

            if ("4".equals(eventId) || "5".equals(eventId)) {
                //外呼为学生，并且有相似症状，则需要新增病例
                if (isSchool || isFamily) {
                    int type = ("4".equals(eventId) ? 4 : 6);
                    processCallResult(eventId, mockMedInfo1, answer, type);
                    //外呼结果更新到新增病例，不能更新到当前病例，所以需要清空结果
                    mockMedInfo1.setOutcallStatus("");
                    mockMedInfo1.setOutcallContent("");
                }
            }
            mockMedInfoMapper.updateByPrimaryKeySelective(mockMedInfo1);
            log.info("更新患者外呼数据成功，患者source key：{}", mockMedInfo.getSourceKey());
        } catch (Exception e) {
            log.error("更新患者外呼数据报错：", e);
        }
    }

    private void processCallResult(String eventId, MockMedInfo mockMedInfo1, boolean answer, int type) {
        if (!answer) {
            return;
        }

        //1, 新增病例
        MockMedInfo mockMedInfo = mockMedInfoMapper.selectByPrimaryKey(mockMedInfo1.getSourceKey());
        mockMedInfo.setOutcallResult(mockMedInfo1.getOutcallResult());
        mockMedInfo.setOutcallContent(mockMedInfo1.getOutcallContent());

        List<MockDoctorVo> doctorInfoList = mockDoctorMapper.getDoctorInfoList(eventId, type);
        Map<String, List<MockDoctorVo>> doctorInfoMap = doctorInfoList.stream().filter(mockDoctorVo -> StringUtils.isNoneBlank(mockDoctorVo.getMedicalId())).collect(Collectors.groupingBy(MockDoctorVo::getMedicalId));
        List<MockDoctorVo> mockDoctorVos = doctorInfoMap.computeIfAbsent(mockMedInfo.getSourceKey(), v -> new ArrayList<>());
        if (CollectionUtils.isNotEmpty(mockDoctorVos)) {
            MockDoctorVo mockDoctorVo = mockDoctorVos.get(0);
            //插入新病例
            MockMedInfo mockMedInfo2 = new MockMedInfo();
            mockMedInfo2.setSourceKey(batchUidService.getUid("tb_cdcew_mock_medical_info") + "");
            mockMedInfo2.setEventId(eventId);
            mockMedInfo2.setPatientName(mockDoctorVo.getParentName());
            mockMedInfo2.setPhone(mockDoctorVo.getTelephone());
            mockMedInfo2.setDiagnoseAge(mockDoctorVo.getParentAge() + "");
            mockMedInfo2.setSexDesc(mockDoctorVo.getParentSex());
            mockMedInfo2.setProvinceName(mockMedInfo.getProvinceName());
            mockMedInfo2.setProvinceCode(mockMedInfo.getProvinceCode());
            mockMedInfo2.setCityName(mockMedInfo.getCityName());
            mockMedInfo2.setCityCode(mockMedInfo.getCityCode());
            mockMedInfo2.setDistrictName(mockMedInfo.getDistrictName());
            mockMedInfo2.setDistrictCode(mockMedInfo.getDistrictCode());
            mockMedInfo2.setResidentialAddress(mockMedInfo.getAddressName());
            mockMedInfo2.setAddressName(mockMedInfo.getAddressName());
            mockMedInfo2.setAddressLatitude(mockMedInfo.getAddressLatitude());
            mockMedInfo2.setAddressLongitude(mockMedInfo.getAddressLongitude());
            mockMedInfo2.setTodaySymptoms(mockMedInfo.getTodaySymptoms());
            mockMedInfo2.setSymptom(mockMedInfo.getSymptom());
            mockMedInfo2.setStage(MockConstant.STAGE_3);
            mockMedInfo2.setMedicalDistribution(MedDistributionEnum.OUTCALL.getCode());
            mockMedInfo2.setFullDate(mockMedInfo.getFullDate());
            mockMedInfo2.setOutcallStatus(String.valueOf(MockMedicalCallStatusEnum.CALL_FINISH.getCode()));
            mockMedInfo2.setOutcallContent(mockMedInfo.getOutcallContent());
            mockMedInfo2.setOutcallResult(MockCallResultEnum.TRUE.getCode() + "");
            mockMedInfo2.setPatientId(mockMedInfo.getSourceKey());
            mockMedInfo2.setNoteOutcallSent(BooleanEnum.TRUE.getIntVal());
            mockMedInfo2.setNoteOutcallRec(BooleanEnum.TRUE.getIntVal());
            mockMedInfo2.setMedicalDistribution(MedDistributionEnum.OUTCALL.getCode());
            mockMedInfo2.setSort(0);
            mockMedInfoMapper.insert(mockMedInfo2);
        }
    }

    /**
     * 业务显示转换
     * @return
     */
    public String getBusinessDiarrheaCrowd(Record record){
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.YES.getDesc1().equals(record.getDiarrheaCrowd())) {
            return com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.YES.getDesc();
        }
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.NO.getDesc1().equals(record.getDiarrheaCrowd())) {
            return com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.NO.getDesc();
        }
        return record.getDiarrheaCrowd();
    }

    /**
     * 业务显示转换
     * @return
     */
    public CallResultEnum getBusinessDiarrheaCrowdEnum(Record record){
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.YES.getDesc1().equals(record.getDiarrheaCrowd())) {
            return CallResultEnum.TRUE;
        }
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.NO.getDesc1().equals(record.getDiarrheaCrowd())) {
            return CallResultEnum.NOT_TRUE;
        }
        return CallResultEnum.UNKNOWN;
    }

    /**
     * 业务显示转换
     * @return
     */
    public CallResultEnum getBusinessWetherDiarrheaEnum(Record record){
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.YES.getDesc1().equals(record.getWetherDiarrhea())) {
            return CallResultEnum.TRUE;
        }
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.NO.getDesc1().equals(record.getWetherDiarrhea())) {
            return CallResultEnum.NOT_TRUE;
        }
        return CallResultEnum.UNKNOWN;
    }

    /**
     * 业务显示转换
     * @return
     */
    public String getBusinessWetherDiarrhea(Record record){
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.YES.getDesc1().equals(record.getWetherDiarrhea())) {
            return com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.YES.getDesc();
        }
        if (com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.NO.getDesc1().equals(record.getWetherDiarrhea())) {
            return com.iflytek.fpva.cdc.outbound.constant.enums.TrueOrFalseEnum.NO.getDesc();
        }
        return record.getWetherDiarrhea();
    }
}

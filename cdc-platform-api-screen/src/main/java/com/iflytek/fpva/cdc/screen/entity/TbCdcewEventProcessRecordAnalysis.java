package com.iflytek.fpva.cdc.screen.entity;

import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.model.vo.EventAnalysisResultVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * tb_cdcew_event_process_record_analysis
 * <AUTHOR>
@Data
public class TbCdcewEventProcessRecordAnalysis implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 记录id
     */
    private String processRecordId;

    /**
     * 过程结论
     */
    private String conclusion;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表")
    private List<TbCdcAttachment> attachmentList;

    /**
     * 创建者id
     */
    private String creatorId;

    /**
     * 创建者name
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 事件id
     */
    private String eventId;

    private String processDesc;

    private static final long serialVersionUID = 1L;

    public static EventAnalysisResultVO transform(TbCdcewEventProcessRecordAnalysis tbCdcewEventProcessRecordAnalysis){
         EventAnalysisResultVO resultVO = new EventAnalysisResultVO();

         resultVO.setId(tbCdcewEventProcessRecordAnalysis.getId());
         resultVO.setProcessConclusion(tbCdcewEventProcessRecordAnalysis.getConclusion());
         resultVO.setAttachmentList(tbCdcewEventProcessRecordAnalysis.getAttachmentList());
         resultVO.setCreatorId(tbCdcewEventProcessRecordAnalysis.getCreatorId());
         resultVO.setCreateTime(tbCdcewEventProcessRecordAnalysis.getCreateTime());
         resultVO.setCreatorName(tbCdcewEventProcessRecordAnalysis.getCreatorName());
         resultVO.setEventId(tbCdcewEventProcessRecordAnalysis.getEventId());
         resultVO.setType("过程结论");
         resultVO.setProcessDesc("过程结论");
         return resultVO;
    }
}
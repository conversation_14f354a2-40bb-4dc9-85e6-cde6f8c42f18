package com.iflytek.fpva.cdc.screen.param;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.Collection;
import java.util.List;

@Data
public class StatQueryParam {
    String provinceCode;
    String cityCode;
    String districtCode;
    List<Integer> processingStatusCollection;
    @ApiParam("DJYY-等级医院；JCYL-基层医疗；XXDW-学校单位；JDQY-街道区域")
    String eventType;

    @ApiModelProperty("症状")
    private Collection<String> symptom;

}

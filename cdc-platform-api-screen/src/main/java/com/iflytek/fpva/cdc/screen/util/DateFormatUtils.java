package com.iflytek.fpva.cdc.screen.util;

import com.iflytek.fpva.cdc.constant.TimeConstant;
import com.iflytek.fpva.cdc.util.StringUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class DateFormatUtils {

    public static Date formatDate(String date, String pattern) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            log.error("日期转换错误！", e);
        }
        return null;
    }

    public static Date formatDate(String date) {
        return formatDate(date, TimeConstant.NORM_DATE_PATTERN);
    }

    public static Date formatDateNecessary(String date, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            log.error("日期转换错误！", e);
            throw new MedicalBusinessException("日期格式错误,应该是" + pattern);
        }
    }


    /**
     * 常量类不需要public构造方法
     */
    private DateFormatUtils() {
        throw new IllegalStateException("常量类");
    }

    public static String parseDate(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }

    //将java.util.Date 转换为java8 的java.time.LocalDateTime,默认时区为东8区
    public static LocalDateTime dateConvertToLocalDateTime(Date date) {
        return date.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
    }

    //将java8 的 java.time.LocalDateTime 转换为 java.util.Date，默认时区为东8区
    public static Date localDateTimeConvertToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.toInstant(ZoneOffset.of("+8")));
    }

}

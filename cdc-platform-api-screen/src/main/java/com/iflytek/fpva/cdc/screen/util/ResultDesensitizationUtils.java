package com.iflytek.fpva.cdc.screen.util;

import org.springframework.util.StringUtils;

/**
 * 脱敏工具类
 * 用于  省、市、区、机构、患者、医生名称脱敏
 */
public class ResultDesensitizationUtils {


    /**
     * 地址信息脱敏
     */
    public static String replaceLivingAddressNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            String result;
            if (str.length() >= 3) {
                result = org.apache.commons.lang.StringUtils.left(str, 3);
            } else {
                result = org.apache.commons.lang.StringUtils.left(str, 1);
            }
            return result + "*****";
        }
        return str;
    }

    /**
     * 地址集合长字符串脱敏(逗号分隔)
     */
    public static String replaceLivingAddressList(String str) {
        if (!StringUtils.isEmpty(str)) {
            String[] split = str.split(",");
            StringBuilder result = new StringBuilder();
            for (String s : split) {
                result.append(replaceLivingAddressNameX(s) + ",");
            }
            if (StringUtils.isEmpty(result.toString())) {
                return "";
            }
            return result.substring(0, result.length() - 1);
        }
        return str;
    }


    /**
     * 省、市、区脱敏
     */
    public static String replaceAdministrativeAreaNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            if (str.contains("省")) {
                return "**省";
            } else if (str.contains("市")) {
                return "**市";
            } else if (str.contains("区")) {
                return "**区";
            } else if (str.contains("县")) {
                return "**县";
            } else if (str.contains("镇")) {
                return "**镇";
            } else if (str.contains("街道")) {
                return "**街道";
            } else if (str.contains("办事处")) {
                return "**办事处";
            } else if (str.contains("乡")) {
                return "**乡";
            } else if (str.contains("村")) {
                return "**村";
            }
        }
        return str;
    }

    /**
     * 机构名称脱敏
     */
    public static String replaceOrganizationNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            if (str.contains("卫生室")) {
                return "**卫生室";
            } else if (str.contains("分院")) {
                return "**分院";
            } else if (str.contains("卫生院")) {
                return "**卫生院";
            } else if (str.contains("卫生站")) {
                return "**卫生站";
            } else if (str.contains("卫生服务站")) {
                return "**卫生服务站";
            } else if (str.contains("服务中心")) {
                return "**服务中心";
            } else if (str.contains("卫健委")) {
                return "**卫健委";
            } else if (str.contains("分室")) {
                return "**分室";
            } else if (str.contains("医院")) {
                return "**医院";
            } else if (str.contains("机构")) {
                return "**机构";
            } else if (str.contains("项目")) {
                return "**项目";
            } else if (str.contains("社区")) {
                return "**社区";
            } else if (str.contains("室")) {
                return "**室";
            } else if (str.contains("保健院")) {
                return "**保健院";
            } else if (str.contains("店")) {
                return "**店";
            } else if (str.contains("公司")) {
                return "**公司";
            } else if (str.contains("班")) {
                return "**班";
            } else if (str.contains("年级")) {
                return "**年级";
            } else if (str.contains("学")) {
                return "**学";
            } else if (str.contains("幼儿园")) {
                return "**幼儿园";
            } else if (str.contains("校区")) {
                return "**校区";
            } else if (str.contains("药房")) {
                return "**药房";
            } else if (str.contains("看护点")) {
                return "**看护点";
            } else if (str.contains("镇")) {
                return "**镇";
            } else if (str.contains("乡")) {
                return "**乡";
            } else if (str.contains("场")) {
                return "**场";
            } else if (str.contains("园区")) {
                return "**园区";
            } else if (str.contains("街道")) {
                return "**街道";
            } else if (str.contains("区")) {
                return "**区";
            } else if (str.contains("单位")) {
                return "**单位";
            } else if (str.contains("园")) {
                return "**园";
            } else if (str.contains("局")) {
                return "**局";
            } else if (str.contains("场")) {
                return "**场";
            } else if (str.contains("监狱")) {
                return "**监狱";
            } else if (str.contains("会")) {
                return "**会";
            } else if (str.contains("中心")) {
                return "**中心";
            } else if (str.contains("基地")) {
                return "**基地";
            } else if (str.contains("处")) {
                return "**处";
            } else if (str.contains("居")) {
                return "**居";
            } else if (str.contains("站")) {
                return "**站";
            }
        }
        return str;
    }

    /**
     * 公司名称脱敏
     */
    public static String replaceCompanyNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            if (str.length() >= 2) {
                String name = str.substring(str.length() - 2);
                return "***" + name;
            } else {
                return "***" + str;
            }

        }
        return str;
    }

    /**
     * 学校名称脱敏
     */
    public static String replaceSchoolNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            if (str.contains("班")) {
                return "**班";
            } else if (str.contains("年级")) {
                return "**年级";
            } else if (str.contains("学校")) {
                return "**学校";
            } else if (str.contains("小学")) {
                return "**小学";
            } else if (str.contains("中学")) {
                return "**中学";
            } else if (str.contains("大学")) {
                return "**大学";
            } else if (str.contains("初中")) {
                return "**初中";
            } else if (str.contains("高中")) {
                return "**高中";
            } else if (str.contains("幼儿园")) {
                return "**幼儿园";
            } else if (str.contains("校区")) {
                return "**校区";
            }
        }
        return str;
    }

    /**
     * 机构名称简称脱敏
     */
    public static String replaceOrgAbbreviationName(String str) {
        if (!StringUtils.isEmpty(str)) {
            return str.charAt(0) + "**";
        }
        return str;
    }

    /**
     * 患者姓名脱敏
     */
    public static String replacePatientNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            String name = org.apache.commons.lang.StringUtils.left(str, 1);
            return org.apache.commons.lang.StringUtils.rightPad(name, org.apache.commons.lang.StringUtils.length(str), "*");
        }
        return str;
    }

    /**
     * 医生姓名脱敏
     */
    public static String replaceDoctorNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            String name = org.apache.commons.lang.StringUtils.left(str, 1);
            return name + "**";
        }
        return str;
    }

    /**
     * 驳回原因脱敏
     */
    public static String replaceReasonNameX(String str) {
        if (!StringUtils.isEmpty(str)) {
            String name = org.apache.commons.lang.StringUtils.left(str, 2);
            return name + "**";
        }
        return str;
    }

    /**
     * 区县名称集合脱敏
     */
    public static String replaceDistrictNameList(String str) {
        if (!StringUtils.isEmpty(str)) {
            String[] split = str.split(",");
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < split.length; i++) {
                if (!StringUtils.isEmpty(split[i])) {
                    result.append("**市**区,");
                } else {
                    result.append(" ,");
                }
            }
            if (StringUtils.isEmpty(result.toString())) {
                return "";
            }
            return result.substring(0, result.length() - 1);
        }
        return str;
    }


    /**
     * 电话号码脱敏
     */
    public static String replacePhone(String str) {
        if (!StringUtils.isEmpty(str)) {
            String string = str.trim();
            if ("无".equals(string)) {
                return str;
            }
            if (string.length() == 11) {
                return string.substring(0, 3) + "****" + string.substring(7, 11);
            } else {
                return org.apache.commons.lang.StringUtils.left(string, 1) + "**";
            }
        }
        return str;
    }

    /**
     * 身份证脱敏
     */
    public static String replaceIdCard(String str) {
        if (!StringUtils.isEmpty(str)) {
            return "******";
        }
        return str;
    }
}

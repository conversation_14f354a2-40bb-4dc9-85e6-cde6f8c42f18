package com.iflytek.fpva.cdc.screen.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * tb_cdcew_window_period_med_relation
 * <AUTHOR>
@Data
public class TbCdcewWindowPeriodMedRelation implements Serializable {
    /**
     * id
     */
    private String id;

    /**
     * 统计日期
     */
    private Date fullDate;

    /**
     * 症状
     */
    private String symptomType;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 信号统计维度编码：市区/机构/单位/学校等
     */
    private String statDimId;

    /**
     * 信号统计维度名称：市区/机构/单位/学校等
     */
    private String statDimName;

    /**
     * 病历ID
     */
    private String sourceKey;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 信号ID
     */
    private String eventId;

    /**
     * 病历来源，0智医助理，1等级医院,2学校
     */
    private Short dataSourceTypeCode;

    /**
     * kie症状list，|分隔
     */
    private String symptomTagList;

    /**
     * 主诉症状list, |分割
     */
    private String mainSuitSymptomTagList;

    /**
     * 主诉症状发病时间list, |分割, 与主诉症状一一对应
     */
    private String onsetTimeList;

    /**
     * 关键症状(判别症状)标签list，|分割
     */
    private String keySymptomTagList;

    /**
     * 其他症状标签list，|分割
     */
    private String otherSymptomTagList;

    /**
     * 检查检验list，|分割
     */
    private String examineList;

    /**
     * 体征list，|分割
     */
    private String physicalSignList;

    /**
     * 流行病学史list，|分割
     */
    private String epidemiologyHistoryList;

    /**
     * 诊断
     */
    private String diagnoseName;

    /**
     * 诱因list, |分割
     */
    private String causeList;

    /**
     * 增强条件匹配结果，结果依|间隔
     */
    private String enhanceConditionList;

    /**
     * 病历创建时间
     */
    private Date medCreateTime;

    /**
     * 病历创建日期
     */
    private Date medFullDate;

    private static final long serialVersionUID = 1L;
}
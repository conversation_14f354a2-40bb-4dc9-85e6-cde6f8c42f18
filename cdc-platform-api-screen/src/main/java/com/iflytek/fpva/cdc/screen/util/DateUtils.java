package com.iflytek.fpva.cdc.screen.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class DateUtils {

    public static final String SHORT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String HOUR_DATE_FORMAT = "HH";

    public static String parseDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(SHORT_DATE_FORMAT);
        if (date != null) {
            return sdf.format(date);
        }
        return "";
    }

    public static String parseDate(Date date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        if (date != null) {
            return sdf.format(date);
        }
        return "";
    }

    public static List<Date> getDatesBetween(Date startDate, Date endDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);

        List<Date> dates = new ArrayList<>();
        Date currDate = cal.getTime();
        while (currDate.before(endDate) || currDate.equals(endDate)) {
            dates.add(currDate);
            cal.add(Calendar.DATE, 1);
            currDate = cal.getTime();
        }

        return dates;
    }

    public static List<String> getDatesBetweenAsStr(Date startDate, Date endDate) {
        List<Date> datesBetween = getDatesBetween(startDate, endDate);
        SimpleDateFormat sdf = new SimpleDateFormat(SHORT_DATE_FORMAT);
        return datesBetween.stream().map(sdf::format).collect(Collectors.toList());
    }

    public static Date addDays(Date date, Integer day) {
        return org.apache.commons.lang3.time.DateUtils.addDays(date, day);
    }

    public static Date getFullDate(Date date) {
        String s = parseDate(date);
        return DateFormatUtils.formatDate(s);
    }
}

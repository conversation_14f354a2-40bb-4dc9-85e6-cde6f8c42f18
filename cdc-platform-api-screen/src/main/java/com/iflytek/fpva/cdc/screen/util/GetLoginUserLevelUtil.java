package com.iflytek.fpva.cdc.screen.util;

import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.screen.dto.LoginUserLevelDto;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.util.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
@Component
public class GetLoginUserLevelUtil {

    @Resource
    RestService restService;
    public LoginUserLevelDto getUserLevel(String loginUserName){

        UapOrgPo org = restService.getUserOrg(loginUserName);
        if (!StringUtils.isEmpty(org.getDistrictCode())) {
            return LoginUserLevelDto.builder()
                    .areaLevel(3)
                    .areaCode(org.getDistrictCode())
                    .provinceCode(org.getProvinceCode())
                    .cityCode(org.getCityCode())
                    .districtCode(org.getDistrictCode())
                    .build();
        }

        if (!StringUtils.isEmpty(org.getCityCode())) {
            return LoginUserLevelDto.builder()
                    .areaLevel(2)
                    .areaCode(org.getCityCode())
                    .provinceCode(org.getProvinceCode())
                    .cityCode(org.getCityCode())
                    .districtCode(org.getDistrictCode())
                    .build();
        }

        if (!StringUtils.isEmpty(org.getProvinceCode())) {
            return LoginUserLevelDto.builder()
                    .areaLevel(1)
                    .areaCode(org.getProvinceCode())
                    .provinceCode(org.getProvinceCode())
                    .cityCode(org.getCityCode())
                    .districtCode(org.getDistrictCode())
                    .build();
        }

        return new LoginUserLevelDto();
    }
}

package com.iflytek.fpva.cdc.screen.mapper;

import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.screen.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface InfectedEventMapper {

    Integer getTotalReportCardCountBy(@Param("areaLevel") Integer areaLevel,
                                      @Param("areaCode") String areaCode);

    Integer getTotalWatchingCount(@Param("areaLevel") Integer areaLevel,
                                      @Param("areaCode") String areaCode);

    WarningCountData getStatusEventCountBy(@Param("areaLevel") Integer areaLevel,
                                           @Param("areaCode") String areaCode);

    List<CaseCountEveryDayDto> getCasesCountEveryday(@Param("areaLevel") Integer areaLevel,
                                                     @Param("areaCode") String areaCode,
                                                     @Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);

    List<CountRanking> getInfectedRanking(@Param("areaLevel") Integer areaLevel,
                                          @Param("areaCode") String areaCode,
                                          @Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate);

    /**
     * 根据区域编码以及时间获取区域对象下的信号情况
     *
     * @param areaCode  上级区划代码
     * @param areaLevel 上级区划代码
     * @param startTime 查询开始时间
     * @param endTime   查询结束时间
     * @return 区域对象下的信号情况
     */
    List<RegionDistribution> getInfectedDistributionByRegion(@Param("areaCode") String areaCode,
                                                             @Param("areaLevel") Integer areaLevel,
                                                             @Param("startTime") Date startTime,
                                                             @Param("endTime") Date endTime);

    /**
     * 查询各数据来源的信号分布情况
     *
     * @param areaCode  区域编码
     * @param areaLevel 区域级别
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 在 startTime 到 endTime 时间段症候群预警数
     */
    List<SourceTypeDto> getSourceTypeByDate(@Param("areaCode") String areaCode,
                                            @Param("areaLevel") Integer areaLevel,
                                            @Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);

    /**
     * 查询置顶信号
     *
     * @param areaCode  区域编码
     * @param areaLevel 区域级别
     * @return 置顶信号
     */
    List<TopWarningEvent> getTopWarningEvent(@Param("areaLevel") Integer areaLevel,
                                             @Param("areaCode") String areaCode);

    List<EventInfoResult> findByParameterMap(Map<String, Object> paraMap);


    WarningCountData getWarningCountByQueryDto(EventCriteria dto);

    List<CaseCountEveryDayDto> getCaseCountEvery(EventCriteria dto);
}

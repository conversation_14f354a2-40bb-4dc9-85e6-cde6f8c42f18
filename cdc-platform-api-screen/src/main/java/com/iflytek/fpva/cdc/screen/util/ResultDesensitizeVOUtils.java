package com.iflytek.fpva.cdc.screen.util;

import com.iflytek.fpva.cdc.screen.dto.EventInfoResult;
import com.iflytek.fpva.cdc.screen.dto.TopWarningEvent;
import com.iflytek.fpva.cdc.screen.vo.InfectTimeLineDetailVO;
import com.iflytek.fpva.cdc.screen.vo.SyndromeTimeLineDetailVO;
import com.iflytek.fpva.cdc.util.DesensitizationUtils;

import java.util.Objects;

/**
 * VO脱敏工具类
 * <AUTHOR>
public class ResultDesensitizeVOUtils {

    public static void desensitizeTopWarningEventVO(TopWarningEvent topWarningEvent) {
        if (Objects.nonNull(topWarningEvent)) {
            topWarningEvent.setEventAddress(ResultDesensitizationUtils.replaceOrganizationNameX(topWarningEvent.getEventAddress()));
        }
    }

    public static void desensitizeEventInfoResult(EventInfoResult eventInfoResult) {
        if (Objects.nonNull(eventInfoResult)) {
            eventInfoResult.setEventAddress(ResultDesensitizationUtils.replaceOrganizationNameX(eventInfoResult.getEventAddress()));
            eventInfoResult.setStatDimName(ResultDesensitizationUtils.replaceOrganizationNameX(eventInfoResult.getStatDimName()));
        }
    }

    public static void desensitizeSyndromeTimeLineDetailVO(SyndromeTimeLineDetailVO syndromeTimeLineDetailVO) {
        if (Objects.nonNull(syndromeTimeLineDetailVO)) {
            syndromeTimeLineDetailVO.setStatDimName(DesensitizationUtils.replaceOrganizationNameX(syndromeTimeLineDetailVO.getStatDimName()));
            syndromeTimeLineDetailVO.setNoteOrgName(DesensitizationUtils.replaceOrganizationNameX(syndromeTimeLineDetailVO.getNoteOrgName()));
            syndromeTimeLineDetailVO.setEsOrgName(DesensitizationUtils.replaceOrganizationNameX(syndromeTimeLineDetailVO.getEsOrgName()));
            syndromeTimeLineDetailVO.setOrgName(DesensitizationUtils.replaceOrganizationNameX(syndromeTimeLineDetailVO.getOrgName()));
        }
    }

    public static void desensitizeInfectTimeLineDetailVO(InfectTimeLineDetailVO infectTimeLineDetailVO) {
        if (Objects.nonNull(infectTimeLineDetailVO)) {
            infectTimeLineDetailVO.setStatDimName(DesensitizationUtils.replaceOrganizationNameX(infectTimeLineDetailVO.getStatDimName()));
            infectTimeLineDetailVO.setPatientName(DesensitizationUtils.replacePatientNameX(infectTimeLineDetailVO.getPatientName()));
            infectTimeLineDetailVO.setReportOrgName(DesensitizationUtils.replaceOrganizationNameX(infectTimeLineDetailVO.getReportOrgName()));
            infectTimeLineDetailVO.setNoteOrgName(DesensitizationUtils.replaceOrganizationNameX(infectTimeLineDetailVO.getNoteOrgName()));
            infectTimeLineDetailVO.setEsOrgName(DesensitizationUtils.replaceOrganizationNameX(infectTimeLineDetailVO.getEsOrgName()));
            infectTimeLineDetailVO.setOrgName(DesensitizationUtils.replaceOrganizationNameX(infectTimeLineDetailVO.getOrgName()));
        }
    }
}

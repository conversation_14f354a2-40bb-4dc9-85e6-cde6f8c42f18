package com.iflytek.fpva.cdc.screen.mapper;

import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.screen.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
public interface SyndromeScreenEventMapper {

    /**
     * 查询地区的信号统计
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @param areaLevel   区域级别
     * @param areaCode    区域编码
     * @return
    */
    WarningCountData getWarningCountByRegion(@Param("areaLevel") Integer areaLevel,
                                             @Param("areaCode") String areaCode,
                                             @Param("startDate") Date startDate,
                                             @Param("endDate") Date endDate);

    /**
     * 查询地区的累计病例数以及累计监测病例
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @param areaLevel   区域级别
     * @param areaCode    区域编码
     * @return
     */
    Integer getCasesCountByArea(@Param("areaLevel") Integer areaLevel,
                                @Param("areaCode") String areaCode,
                                @Param("startDate") Date startDate,
                                @Param("endDate") Date endDate);

    Integer getTotalWatchingCount(@Param("areaLevel") Integer areaLevel,
                                @Param("areaCode") String areaCode,
                                @Param("startDate") Date startDate,
                                @Param("endDate") Date endDate);

    /**
     * 获取配置的值
     *
     * @param configKey 配置的key
     * @return 该配置的值
     */
    Integer getConfigValue(String configKey);

    /**
     * 查询每天病历数情况
     *
     * @param areaLevel 区域级别
     * @param areaCode  区域编码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 该区域的在 startTime 到 endTime 时间段的每天的预警病例数
     */
    List<CaseCountEveryDayDto> getCasesCountEveryday(@Param("areaLevel") Integer areaLevel,
                                                     @Param("areaCode") String areaCode,
                                                     @Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);

    /**
     * 查询给定时间内症候群排名
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 在 startTime 到 endTime 时间段症候群预警数
     */
    List<CountRanking> getSyndromeRanking(@Param("areaLevel") Integer areaLevel,
                                          @Param("areaCode") String areaCode,
                                          @Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate);

    /**
     * 根据区域编码以及时间获取区域对象下的信号情况
     *
     * @param areaCode 上级区划代码
     * @param areaLevel 上级区划代码
     * @param startTime 查询开始时间
     * @param endTime 查询结束时间
     * @return 区域对象下的信号情况
     */
    List<RegionDistribution> getRegionEventDistribution(@Param("areaCode") String areaCode,
                                                        @Param("areaLevel") Integer areaLevel,
                                                        @Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime);

    /**
     * 查询各数据来源的信号分布情况
     *
     * @param areaCode 区域编码
     * @param areaLevel 区域级别
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param sourceType 数据来源
     * @return 在 startTime 到 endTime 时间段症候群预警数
     */
    SourceTypeDto getSourceTypeByDate(@Param("areaCode") String areaCode,
                                      @Param("areaLevel") Integer areaLevel,
                                      @Param("startTime") Date startTime,
                                      @Param("endTime") Date endTime,
                                      @Param("sourceType") String sourceType);

    List<SourceTypeDto> getEventTypeByDate(@Param("areaCode") String areaCode,
                                           @Param("areaLevel") Integer areaLevel,
                                           @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime,
                                           @Param("eventTypes") List<String> eventTypes);

    /**
     * 查询置顶信号
     *
     * @param areaCode 区域编码
     * @param areaLevel 区域级别
     *
     * @return 置顶信号
     */
    List<TopWarningEvent> getTopWarningEvent(@Param("areaLevel") Integer areaLevel,
                                             @Param("areaCode") String areaCode);

    List<EventInfoResult> findByParameterMap(Map<String, Object> paraMap);

    WarningCountData getWarningCountByQueryDto(EventCriteria dto);

    List<CaseCountEveryDayDto> getCaseCountEvery(EventCriteria dto);

    String getDistrictCode(String orgId);
}

package com.iflytek.fpva.cdc.screen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

/**
 * <AUTHOR>

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginUserLevelDto {

    private Integer areaLevel;

    private String areaCode;

    private String provinceCode;

    private String cityCode;

    private String districtCode;

    @ApiModelProperty("症状")
    private Collection<String> symptom;
}

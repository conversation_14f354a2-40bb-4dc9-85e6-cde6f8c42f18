package com.iflytek.fpva.cdc.screen.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InfectTimeLineDetailVO {


    private String statDimId;

    @ApiModelProperty("监测预警地点")
    private String statDimName;

    @ApiModelProperty("传染病编码")
    private String infectedSubCode;

    @ApiModelProperty("传染病名称")
    private String infectedSubName;

    @ApiModelProperty("病例数")
    private Integer medicalCount;

    @ApiModelProperty("事件类型")
    private String eventType;

    // --------------- 第N例病例相关  -------------------
    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("性别")
    private String sexDesc;

    @ApiModelProperty("年龄")
    private String diagnoseAge;

    @ApiModelProperty("疾病诊断")
    private String diagnoseName;

    @ApiModelProperty("报告单位")
    private String reportOrgName;



    // --------------- 主动排查相关  -------------------
    @ApiModelProperty("主动排查执行方式")
    private String noteType;

    private String noteOrgId;

    @ApiModelProperty("主动排查通知对象")
    private String noteOrgName;

    // -------------   流调指派相关  --------------------
    @ApiModelProperty("指派单位")
    private String esOrgName;

    @ApiModelProperty("流调状态 1-待处理 2-处理中 3=已完成")
    private String esStatus;

    @ApiModelProperty("流调报告 true:已出 false:未出")
    private Boolean esReport;

    //  -------------   信号评估相关 ------------------
    @ApiModelProperty("信号评估 -- 填报单位id")
    private String orgId;

    @ApiModelProperty("信号评估 -- 填报单位")
    private String orgName;

    /**
     * 调查时间
     */
    @ApiModelProperty(value = "信号评估 -- 调查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date investigationTime;

    /**
     * 调查方式：1.电话调查、2.现场调查、3.监测数据分析、4.其他方式
     */
    @ApiModelProperty(value = "信号评估 -- 调查方式：1.电话调查、2.现场调查、3.监测数据分析、4.其他方式")
    private Integer investigationMethod;

    /**
     * 研判结论
     */
    @ApiModelProperty(value = "信号评估 -- 研判结论: 0-爆发或流行, 1-排除, 2-持续关注")
    private Integer conclusions;

}

package com.iflytek.fpva.cdc.screen.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
@Data
public class EventInfoResult {

    @ApiModelProperty(value = "信号id")
    private String eventId;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    @ApiModelProperty(value = "发生地点")
    private String statDimName;

    private String eventNum;

    private String eventName;

    @ApiModelProperty(value = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    private Date beginDate;

    private String sourceType;

    private String eventAddress;

    private String caseCount;

    private String processingStatus;

    private String infectedTypeCode;

    private Date updateTime;

    private Date processTime;

    private Double orgLongitude;

    private Double orgLatitude;

    private Double warningAttentionValue;

    @ApiModelProperty(value = "响应超时状态:0.未超时 1.响应超时")
    private Integer responseTimeOutStatus;

    @ApiModelProperty(value = "处置超时状态:0.未超时 1.处置超时")
    private Integer processingTimeOutStatus;

    @ApiModelProperty(value = "最晚响应时间")
    private Date responseLatestTime;

    @ApiModelProperty(value = "最晚处理时间")
    private Date processingLatestTime;

    @ApiModelProperty(value = "值为9时代表置顶")
    private Integer attentionLevel;

    private Long leftTime;

    private String eventType;
}

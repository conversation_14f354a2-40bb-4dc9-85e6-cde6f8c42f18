package com.iflytek.fpva.cdc.screen.entity;

import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.model.vo.EventAnalysisResultVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * tb_cdcew_infected_event_record_analysis
 * <AUTHOR>
@Data
public class TbCdcewInfectedEventRecordAnalysis implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 记录id
     */
    private String processRecordId;

    /**
     * 过程结论
     */
    private String conclusion;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件列表")
    private List<TbCdcAttachment> attachmentList;


    /**
     * 创建者id
     */
    private String creatorId;

    /**
     * 创建者name
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 事件id
     */
    private String eventId;

    private String processDesc;


    private static final long serialVersionUID = 1L;

    public static EventAnalysisResultVO  transform(TbCdcewInfectedEventRecordAnalysis tbCdcewInfectedEventRecordAnalysis) {
        EventAnalysisResultVO resultVO = new EventAnalysisResultVO();

        resultVO.setId(tbCdcewInfectedEventRecordAnalysis.getId());
        resultVO.setProcessConclusion(tbCdcewInfectedEventRecordAnalysis.getConclusion());
        resultVO.setAttachmentList(tbCdcewInfectedEventRecordAnalysis.getAttachmentList());
        resultVO.setCreatorId(tbCdcewInfectedEventRecordAnalysis.getCreatorId());
        resultVO.setCreateTime(tbCdcewInfectedEventRecordAnalysis.getCreateTime());
        resultVO.setCreatorName(tbCdcewInfectedEventRecordAnalysis.getCreatorName());
        resultVO.setEventId(tbCdcewInfectedEventRecordAnalysis.getEventId());
        resultVO.setType("过程结论");
        resultVO.setProcessDesc("过程结论");
        return resultVO;
    }
}
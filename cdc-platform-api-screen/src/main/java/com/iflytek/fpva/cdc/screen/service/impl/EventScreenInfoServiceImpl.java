package com.iflytek.fpva.cdc.screen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.constant.enums.EventAnalysisConclusionsEnum;
import com.iflytek.fpva.cdc.constant.enums.EventTypeEnum;
import com.iflytek.fpva.cdc.controller.infected.InfectedEventDetailController;
import com.iflytek.fpva.cdc.entity.TbCdcInfectedEventAnalysis;
import com.iflytek.fpva.cdc.entity.TbCdcInfectedWarningEvent;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcInfectedEventAnalysisMapper;
import com.iflytek.fpva.cdc.mapper.infected.TbCdcInfectedWarningEventMapper;
import com.iflytek.fpva.cdc.model.dto.PageParam;
import com.iflytek.fpva.cdc.model.dto.RegionQueryListDto;
import com.iflytek.fpva.cdc.model.dto.infected.InfectedEventListQueryDTO;
import com.iflytek.fpva.cdc.model.po.UapOrgPo;
import com.iflytek.fpva.cdc.model.vo.EventCriteria;
import com.iflytek.fpva.cdc.model.vo.EventVO;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.model.vo.area.CascadeVO;
import com.iflytek.fpva.cdc.screen.param.StatQueryParam;
import com.iflytek.fpva.cdc.screen.service.EventScreenInfoService;
import com.iflytek.fpva.cdc.screen.vo.DisposalNoticeVO;
import com.iflytek.fpva.cdc.screen.vo.ScreenEventVO;
import com.iflytek.fpva.cdc.screen.vo.ScreenStatResult;
import com.iflytek.fpva.cdc.service.common.BusinessPersonService;
import com.iflytek.fpva.cdc.service.infected.CdcInfectedEventService;
import com.iflytek.fpva.cdc.service.common.OrgService;
import com.iflytek.fpva.cdc.service.common.TbCdcConfigService;
import com.iflytek.fpva.cdc.service.common.RestService;
import com.iflytek.fpva.cdc.util.CollectionUtil;
import com.iflytek.fpva.cdc.util.UapAccessUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
public class EventScreenInfoServiceImpl implements EventScreenInfoService {
    @Resource
    private TbCdcInfectedWarningEventMapper tbCdcInfectedWarningEventMapper;

    @Resource
    TbCdcInfectedEventAnalysisMapper tbCdcInfectedEventAnalysisMapper;
    @Resource
    TbCdcConfigService tbCdcConfigService;
    @Resource
    BusinessPersonService businessPersonService;
    @Resource
    RestService restService;
    @Resource
    OrgService orgService;
    @Resource
    CdcInfectedEventService cdcInfectedEventService;
    @Resource
    InfectedEventDetailController infectedEventDetailController;

    @Override
    public ScreenStatResult getStat(StatQueryParam param) {
        EventCriteria eventCriteria = EventCriteria.builder().provinceCode(CollectionUtil.getSplitList(param.getProvinceCode()))
                .cityCode((CollectionUtil.getSplitList(param.getCityCode())))
                .districtCode(CollectionUtil.getSplitList(param.getDistrictCode()))
                .processingStatusCollection(param.getProcessingStatusCollection())
                .eventType(param.getEventType())
                .symptomTypeCollection(param.getSymptom())
                .showAiScreen(tbCdcConfigService.isInfectedEnableAiScreen())
                .build();
        List<TbCdcInfectedWarningEvent> events = tbCdcInfectedWarningEventMapper.findByParameterMap(eventCriteria.getParameterMap());
        ScreenStatResult result = new ScreenStatResult();
        result.setCaseTotal(events.stream().mapToInt(TbCdcInfectedWarningEvent::getReportCaseCnt).sum());
        result.setEventTotal(events.size());
        return result;
    }

    @Override
    public PageData<DisposalNoticeVO> getDisposalNotice(PageParam pageParam) {

        EventCriteria eventCriteria = EventCriteria.builder().provinceCode(CollectionUtil.getSplitList(pageParam.getProvinceCode()))
                .cityCode((CollectionUtil.getSplitList(pageParam.getCityCode())))
                .districtCode(CollectionUtil.getSplitList(pageParam.getDistrictCode()))
                .eventType(pageParam.getEventType())
                .symptomTypeCollection(pageParam.getSymptom())
                .build();
        PageMethod.startPage(pageParam.getPageIndex(), pageParam.getPageSize());
        PageInfo<TbCdcInfectedEventAnalysis> pageInfo;
        List<TbCdcInfectedEventAnalysis> list = tbCdcInfectedEventAnalysisMapper.findByParameterMap(eventCriteria.getParameterMap());
        pageInfo = new PageInfo<>(list);
        PageData<DisposalNoticeVO> pageData = PageData.fromPageInfo(pageInfo);
        List<DisposalNoticeVO> result = new ArrayList<>();
        list.forEach(tbCdcInfectedEventAnalysis -> {
            DisposalNoticeVO disposalNoticeVO = new DisposalNoticeVO();

            disposalNoticeVO.setInfectedName(tbCdcInfectedEventAnalysis.getInfectedName());
            disposalNoticeVO.setCreateTime(tbCdcInfectedEventAnalysis.getCreateTime());
            disposalNoticeVO.setEventId(tbCdcInfectedEventAnalysis.getEventId());
            disposalNoticeVO.setAnalysisResult(EventAnalysisConclusionsEnum.getDescByCode(tbCdcInfectedEventAnalysis.getConclusions()));
            result.add(disposalNoticeVO);
        });
        pageData.setData(result);
        return pageData;
    }

    @Override
    public PageData<ScreenEventVO> getEventList(InfectedEventListQueryDTO dto,
                                                String loginUserName,
                                                String loginUserId) {
        //crud权限控制
        UapOrgPo orgPo = restService.getUserOrg(loginUserName);
        dto.getRegionInfoList().forEach(e -> {
            UapAccessUtil.hasAccess(orgPo, e.getProvinceCode(), e.getCityCode(), e.getDistrictCode());
        });

        //是否是普通用户
        boolean isOrdinaryUser = tbCdcConfigService.isOrdinaryUser(loginUserId);
        RegionQueryListDto queryListDTO = new RegionQueryListDto();
        queryListDTO.setPageIndex(dto.getPageIndex());
        queryListDTO.setPageSize(dto.getPageSize());
        queryListDTO.setStatusCollection(dto.getStatusCollection());
        queryListDTO.setResponseTimeOutStatus(dto.getResponseTimeOutStatus());
        queryListDTO.setProcessingTimeOutStatus(dto.getProcessingTimeOutStatus());
        queryListDTO.setDateType(dto.getDateType());
        queryListDTO.setConclusions(dto.getConclusions());
        queryListDTO.setEventType(dto.getEventType());
        // 研判大屏特殊排序方式  待处理前面 处理中后面  然后再是发生时间倒序排
        queryListDTO.setSortType(-1);

        Collection<String> symptom = dto.getSymptom();
//        //预警管理员需要做数据权限
//        PersonDataAuthPo personDataAuthPo = businessPersonService.getInfectedDataAuthByLoginUserId(loginUserId);
//        if (personDataAuthPo.getIsPlatformAdmin() && CollectionUtils.isEmpty(symptom)) {
//            // 传染病数据权限过滤  过滤第二层级的数据
//            List<TbCdcewPersonDataAuth> infectedDataAuthByLoginUserId = personDataAuthPo.getList();
//            List<String> collect = infectedDataAuthByLoginUserId.stream().map(TbCdcewPersonDataAuth::getSymptomId).collect(Collectors.toList());
//            Collection<String> codeList = cdcInfectedEventService.getInfectedCodeList(collect);
//            dto.setSymptom(codeList);
//        }


        dto.setSymptom(cdcInfectedEventService.buildInfectedCodes(CollectionUtils.isEmpty(symptom)?new ArrayList<>():new ArrayList<>(symptom) ,loginUserId));
        if (!StringUtils.isEmpty(dto.getOrgId())) {
            if (EventTypeEnum.JDQY.getName().equals(dto.getEventType())
                    || EventTypeEnum.XXDW.getName().equals(dto.getEventType())) {
                List<String> orgIds = new ArrayList<>();
                orgIds.add(dto.getOrgId());
                queryListDTO.setOrgIds(orgIds);
            } else {
                List<CascadeVO> orgTreeList = new ArrayList<>();
                CascadeVO cascadeVO = orgService.buildCascadeVOByOrgId(dto.getOrgId());
                orgTreeList.add(cascadeVO);
                queryListDTO.setOrgIds(orgService.getAllOrgId(orgTreeList));
            }
        }

        queryListDTO.setSymptom(dto.getSymptom());
        queryListDTO.setRegionInfoList(dto.getRegionInfoList());
        queryListDTO.setMaxFullDate(dto.getMaxFullDate());
        queryListDTO.setMinFullDate(dto.getMinFullDate());

        //是否脱敏显示
        boolean isDesensitization = tbCdcConfigService.isDesensitization(loginUserId);

        PageData<EventVO> eventVOList = cdcInfectedEventService.getEventList(queryListDTO, tbCdcConfigService.isInfectedEnableAiScreen(), isOrdinaryUser, isDesensitization, true);
        List<ScreenEventVO> screenEventVOList = new ArrayList<>();
        eventVOList.getData().forEach(e -> {
            ScreenEventVO screenEventVO = new ScreenEventVO();
            BeanUtil.copyProperties(e, screenEventVO);
            screenEventVO.setMapRelation(infectedEventDetailController.mapRelation(screenEventVO.getId(), null, null, loginUserId));
            screenEventVOList.add(screenEventVO);
        });
        PageData<ScreenEventVO> result = new PageData<>();
        result.setData(screenEventVOList);
        result.setPageIndex(eventVOList.getPageIndex());
        result.setPageSize(eventVOList.getPageSize());
        result.setTotal(eventVOList.getTotal());
        result.setPages(eventVOList.getPages());
        return result;
    }
}

package com.iflytek.fpva.cdc.screen.service;

import com.iflytek.fpva.cdc.constant.enums.MonitorCategoryEnum;
import com.iflytek.fpva.cdc.entity.CdcEventProcessRecord;
import com.iflytek.fpva.cdc.entity.TbCdcewWarningEventProcessRecord;
import com.iflytek.fpva.cdc.model.vo.EventAnalysisResultVO;
import com.iflytek.fpva.cdc.model.vo.RegionVO;
import com.iflytek.fpva.cdc.screen.entity.TbCdcewEventProcessRecordAnalysis;
import com.iflytek.fpva.cdc.screen.vo.EventMapVO;
import com.iflytek.fpva.cdc.screen.vo.RecordAnalysisResultVO;
import com.iflytek.fpva.cdc.screen.vo.SyndromeTimeLineDetailVO;

import java.util.List;

public interface EventProcessRecordService {
    SyndromeTimeLineDetailVO eventTimeLineDetail(String eventId, String processRecordId,String loginUserId);

    TbCdcewEventProcessRecordAnalysis createRecordAnalysisResult(RecordAnalysisResultVO recordAnalysisResultVO, String loginUserId, String loginUserName);

    List<EventAnalysisResultVO> getRecordAnalysisResultList(String eventId);

    List<List<RegionVO>> getEventMapVO(String eventId, String loginUserId);

    List<TbCdcewWarningEventProcessRecord> getEventRecordTimeLine(String eventId);
}

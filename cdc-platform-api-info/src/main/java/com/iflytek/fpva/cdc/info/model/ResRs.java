package com.iflytek.fpva.cdc.info.model;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class ResRs {

    private boolean IsSuccess;
    private int Result;
    private int TotalCount;
    private String DataType;
    private String Message;
    private String ErrorFieldList;
    private String ErrorTableList;
    private String TableName;
    private String Data;
    private String ExceptionMessage;
    private String ErrorServiceId;
    private List<DataList> DataList;
    private int ErrorCode;

    public static ResRs Empty(){
        return ResRs.builder().IsSuccess(false).ErrorCode(0).Data("返回结果异常").build();
    }
}
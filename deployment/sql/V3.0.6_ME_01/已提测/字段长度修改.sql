ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN first_visit_org_id TYPE varchar(100) USING first_visit_org_id::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN first_visit_org_name TYPE varchar(100) USING first_visit_org_name::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN id TYPE varchar(64) USING id::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN disease_name TYPE varchar(64) USING disease_name::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN disease_code TYPE varchar(64) USING disease_code::varchar;

ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN disease_type TYPE varchar(64) USING disease_type::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN outcome_status TYPE varchar(64) USING outcome_status::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN process_type TYPE varchar(64) USING process_type::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN creator TYPE varchar(64) USING creator::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN updater TYPE varchar(64) USING updater::varchar;
ALTER TABLE app.tb_cdcew_emergency_event_process_info ALTER COLUMN job TYPE varchar(64) USING job::varchar;

ALTER TABLE app.tb_cdcew_event_suspect_report ADD if not exists delete_flag varchar(2) NULL;
COMMENT ON COLUMN app.tb_cdcew_event_suspect_report.delete_flag IS '删除标识';

ALTER TABLE app.tb_cdcew_event_invest_report ADD if not exists delete_flag varchar(2) NULL;
COMMENT ON COLUMN app.tb_cdcew_event_invest_report.delete_flag IS '删除标识';
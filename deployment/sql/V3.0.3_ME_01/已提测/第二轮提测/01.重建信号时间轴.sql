-- app.tb_cdcew_warning_signal_timeline definition

-- Drop table

DROP TABLE if exists app.tb_cdcew_warning_signal_timeline;

CREATE TABLE app.tb_cdcew_warning_signal_timeline (
	id varchar(32) NOT NULL, -- 主键
	signal_id varchar(32) NULL, -- 信号ID
	moment_type_code varchar(32) NULL, -- 信号事件类型代码
	moment_type_name varchar(32) NULL, -- 信号事件类型名称
	moment_time timestamp NULL, -- 时间
	current_case_count int4 NULL, -- 现存病例数
	change_case_count int4 NULL, -- 信号的变化数量
	current_source_key_list text NULL, -- 当前病例主键列表
	create_time timestamp NULL, -- 创建时间
	source_signal_id varchar(32) NULL,-- 源信号id
    warning_type varchar(20) NOT NULL, --预警类型，非空
	CONSTRAINT tb_cdcew_warning_signal_timeline_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_signal_timeline IS '信号时间线';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.signal_id IS '信号ID';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.moment_type_code IS '信号事件类型代码';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.moment_type_name IS '信号事件类型名称';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.moment_time IS '时间';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.current_case_count IS '现存病例数';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.change_case_count IS '信号的变化数量';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.current_source_key_list IS '当前病例主键列表';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.warning_type IS '预警类型';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.source_signal_id IS '源信号id';


-- app.tb_cdcew_warning_signal_timeline_detail definition

-- Drop table

DROP TABLE if exists app.tb_cdcew_warning_signal_timeline_detail;

CREATE TABLE app.tb_cdcew_warning_signal_timeline_detail (
	id varchar NOT NULL, -- 主键id
	timeline_id varchar NULL, -- 时间线id
	source_key varchar NULL, -- source_key
	create_time timestamp NULL, -- 创建时间
	CONSTRAINT tb_cdcew_warning_signal_timeline_detail_pk PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_signal_timeline_detail IS '信号时间线详情';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline_detail.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline_detail.timeline_id IS '时间线id';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline_detail.source_key IS 'source_key';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline_detail.create_time IS '创建时间';
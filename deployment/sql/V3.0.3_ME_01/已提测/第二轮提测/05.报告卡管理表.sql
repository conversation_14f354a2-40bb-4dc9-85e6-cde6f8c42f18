DROP TABLE IF EXISTS app.tb_cdcew_report_upload_record;
CREATE TABLE app.tb_cdcew_report_upload_record (
	id varchar(100) NOT NULL, -- 主键
	attachment_id varchar(100) NOT NULL, -- 附件id
	report_card_id varchar(100) NULL, -- 卡片id
	status varchar(100) NULL, -- 卡片状态
	"name" varchar(100) NULL, -- 患者姓名
	parent_name varchar(100) NULL,-- 患儿家长姓名
	valid_cert_type  varchar(64) null, -- 有效证件类型
	valid_cert_number varchar(100) NULL,-- 有效证件号
	company varchar(100) NULL, -- 患者工作单位
	phone varchar(100) NULL,
	attribution varchar(100) NULL, -- 病人属于
	address_code varchar(100) NULL,
	address_name varchar(100) NULL,
	human_category varchar(100) NULL, -- 人群分类
	cases_category varchar(100) NULL, -- 病例分类
	cases_category2 varchar(100) NULL, -- 病例分类2
	onset_date timestamp(6) NULL,    -- 发病日期
	diagnose_time timestamp(6) NULL, -- 诊断时间
	death_date timestamp(6) NULL, -- 死亡日期
	disease_name varchar(100) NULL,-- 疾病名称
	disease_code varchar(100) NULL,-- 疾病编码
	revised_previous_disease varchar(100) NULL,-- 订正前病种
	revised_previous_diagnose_time timestamp(6) NULL,-- 订正前诊断时间
	revised_previous_check_time timestamp(6) NULL, -- 订正前终审时间
	fill_doctor varchar(100) NULL, -- 填卡医生
	fill_date timestamp(6) NULL, -- 医生填卡日期
	unit_code varchar(100) NULL, -- 报告单位地区编码
	unit_name varchar(100) NULL, -- 报告单位
	unit_type varchar(100) NULL, -- 单位类型
	record_time timestamp(6) NULL, -- 报告卡录入时间
	record_user varchar(100) NULL,  -- 录卡用户
	record_user_company varchar(100) NULL, -- 录卡用户所属单位
	district_check_time timestamp(6) NULL, -- 县区审核时间
	city_check_time timestamp(6) NULL, -- 地市审核时间
	province_check_time timestamp(6) NULL,-- 省市审核时间
	check_status varchar(100) NULL,  -- 审核状态
	revised_report_time timestamp(6) NULL, -- 订正报告时间
	revised_final_check_time timestamp(6) NULL, -- 订正终审时间
	final_check_death_time timestamp(6) NULL, -- 终审死亡时间
	revised_user varchar(100) NULL, -- 订正用户
	revised_user_company varchar(100) NULL, -- 订正用户所属单位
	delete_time timestamp(6) NULL, -- 删除时间
	delete_user varchar(100) NULL, -- 删除用户
	delete_user_company varchar(100) NULL, -- 删除用户所属单位
	delete_reason varchar(100) NULL, -- 删除原因
	remark varchar(100) NULL,
	create_time timestamp(6) NULL, -- 创建时间
	update_time timestamp(6) NULL, -- 更新时间
	creator_id varchar(64) NULL, -- 创建人ID
	creator varchar(64) NULL, -- 创建人
	global_person_id varchar(100) NULL, -- 主索引id
	report_card_code varchar NULL, -- 卡片编号
	sex_desc varchar NULL, -- 性别
	birthday timestamp(6) NULL, -- 出生日期
	age varchar NULL, -- 年龄
	age_unit  varchar(64) null, -- 年龄单位
	province_code varchar(32) NULL, -- 省编码
	province_name varchar(20) NULL, -- 省名称
	city_code varchar(20) NULL, -- 市编码
	city_name varchar(20) NULL, -- 市名称
	district_code varchar(20) NULL, -- 区县编码
	district_name varchar(20) NULL, -- 区县名称
	report_class  varchar(64) null, -- 报告类别
	CONSTRAINT tb_cdcew_report_upload_record_card_id UNIQUE (report_card_id),
	CONSTRAINT tb_cdcew_report_upload_record_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_report_upload_record IS '上传报告卡记录';
-- Column comments


COMMENT ON COLUMN app.tb_cdcew_report_upload_record.report_class IS '报告类别';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.valid_cert_type IS '有效证件类型';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.age_unit IS '年龄单位';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.attachment_id IS '附件id';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.report_card_id IS '卡片id';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.status IS '卡片状态';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record."name" IS '患者姓名';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record."update_time" IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.global_person_id IS '主索引id';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.report_card_code IS '卡片编号';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.sex_desc IS '性别';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.birthday IS '出生日期';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.age IS '年龄';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.parent_name IS '患儿家长姓名';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.valid_cert_number IS '有效证件号';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.company IS '患者工作单位';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.attribution IS '病人属于';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.human_category IS '人群分类';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.cases_category IS '病例分类';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.onset_date IS '发病日期';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.diagnose_time IS '诊断时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.death_date IS '死亡日期';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_previous_disease IS '订正前病种';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_previous_diagnose_time IS '订正前诊断时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_previous_check_time IS '订正前终审时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.fill_doctor IS '填卡医生';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.fill_date IS '医生填卡日期';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.unit_code IS '报告单位地区编码';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.unit_name IS '报告单位';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.unit_type IS '单位类型';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.record_time IS '报告卡录入时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.record_user IS '录卡用户';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.record_user_company IS '录卡用户所属单位';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.district_check_time IS '县区审核时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.check_status IS '审核状态';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_report_time IS '订正报告时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_final_check_time IS '订正终审时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.final_check_death_time IS '终审死亡时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_user IS '订正用户';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.revised_user_company IS '订正用户所属单位';
COMMENT ON COLUMN app.tb_cdcew_report_upload_record.address_name IS '现住详细地址';






DROP TABLE IF EXISTS app.tb_cdcew_report_upload_relation;
CREATE TABLE app.tb_cdcew_report_upload_relation (
	id varchar(64) NOT NULL, -- 主键
	repeat_id varchar(64) NULL, -- 重卡关联id
	report_upload_record_id varchar(64) NULL, -- tb_cdcew_report_upload_record.id
	report_card_id varchar(64) NULL, -- tb_cdcew_report_upload_record.report_card_id
	message_flag  int4 null, -- 消息发送状态 0-未发送；1-已发送
	repeat_dispose_status  int4 null, -- 重卡处理状态 0-未处理；1-已处理	
	repeat_scan_time timestamp(6) NULL, -- 重卡扫描识别时间
	repeat_dispose_time timestamp(6) NULL, -- 用户处理重卡时间
	in_time int4 null, -- 处理及时 0-否；1-是
	dispose_delete  int4  NULL ,-- 处理删除 0未删除 1删除
	rule_id  varchar(64) null, -- 重卡规则id
	rule_item_id  varchar(64) null, -- 重卡规则id
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,-- 是否删除 0未删除 1删除
	updater_id varchar(64) NULL, -- 修改人ID
	updater varchar(64) NULL, -- 修改人
	update_time timestamp(6) NULL, -- 更新时间
	creator_id varchar(64) NULL, -- 创建人ID
	creator varchar(64) NULL, -- 创建人
	create_time timestamp(6) NULL, -- 创建时间
	CONSTRAINT tb_cdcew_report_upload_relation_pk PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_report_upload_relation IS '报告卡重卡关联表';
CREATE INDEX tb_cdcew_report_upload_relation_record_id ON app.tb_cdcew_report_upload_relation (report_upload_record_id);
CREATE INDEX tb_cdcew_report_upload_relation_repeat_id_idx ON app.tb_cdcew_report_upload_relation (repeat_id,report_upload_record_id);
CREATE INDEX tb_cdcew_report_upload_relation_rule_id_idx ON app.tb_cdcew_report_upload_relation (rule_id);

-- Column comments
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.in_time IS '处理及时 0-否；1-是';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.repeat_dispose_status IS '重卡处理状态 0-未处理；1-已处理';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.repeat_scan_time IS '重卡扫描识别时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.repeat_dispose_time IS '用户处理重卡时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.rule_id IS '重卡规则id';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.dispose_delete IS '处理删除 0未删除 1删除';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.repeat_id IS '重卡关联id';;
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.report_upload_record_id IS 'tb_cdcew_report_upload_record.id';;
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.report_card_id IS 'tb_cdcew_report_upload_record.report_card_id';;
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.creator_id IS '创建人ID';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.message_flag IS '消息发送状态 0-未发送；1-已发送';
COMMENT ON COLUMN app.tb_cdcew_report_upload_relation.delete_flag IS '是否删除 0未删除 1删除';





DROP TABLE IF EXISTS app.tb_cdcew_report_upload_print;
CREATE TABLE app.tb_cdcew_report_upload_print (
	id varchar(64) NOT NULL, -- 主键
	report_upload_record_id varchar(64) NULL, -- tb_cdcew_report_upload_record.id
	creator_id varchar(64) NULL, -- 创建人ID
	creator varchar(64) NULL, -- 创建人
	create_time timestamp(6) NULL, -- 创建时间
	CONSTRAINT tb_cdcew_report_upload_print_pk PRIMARY KEY (id)
);
CREATE INDEX tb_cdcew_report_upload_print_record_id ON app.tb_cdcew_report_upload_print (report_upload_record_id);
COMMENT ON TABLE app.tb_cdcew_report_upload_print IS '报告卡打印记录';
-- Column comments

COMMENT ON COLUMN app.tb_cdcew_report_upload_print.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_upload_print.report_upload_record_id IS 'tb_cdcew_report_upload_record.id';;
COMMENT ON COLUMN app.tb_cdcew_report_upload_print.creator_id IS '创建人ID';
COMMENT ON COLUMN app.tb_cdcew_report_upload_print.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_report_upload_print.create_time IS '创建时间';




DROP TABLE IF EXISTS app.tb_cdcew_report_upload_repeat_rule;
CREATE TABLE app.tb_cdcew_report_upload_repeat_rule (
	id varchar(64) NOT NULL, -- 主键
	rule_name varchar(256) NULL,-- 规则名称
	disease_name varchar(100) NULL,-- 疾病名称
	disease_code varchar(100) NULL,-- 疾病编码
	time_type  int4 null, -- 时间区间类型 1-年度内（1月1日至12月31日）；2-累计（截止到当前时期所有累计）；3-跨年度
	time_value  int4 null, -- 时间区间类型 为3-跨年度时保存
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,-- 是否删除 0未删除 1删除
	creator_id varchar(64) NULL, -- 创建人ID
	creator varchar(64) NULL, -- 创建人
	create_time timestamp(6) NULL, -- 创建时间
	updater_id varchar(64) NULL, -- 修改人ID
	updater varchar(64) NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	CONSTRAINT tb_cdcew_report_upload_repeat_rule_pk PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_report_upload_repeat_rule IS '报告卡规则表';
-- Column comments

COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.rule_name IS '规则名称';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.disease_code IS '疾病编码';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.time_type IS '时间区间类型 1-年度内（1月1日至12月31日）；2-累计（截止到当前时期所有累计）；3-跨年度';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.time_value IS '时间区间类型 为3-跨年度时保存';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.creator_id IS '创建人ID';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule.delete_flag IS '是否删除 0未删除 1删除';


DROP TABLE IF EXISTS app.tb_cdcew_report_upload_repeat_rule_item;
CREATE TABLE app.tb_cdcew_report_upload_repeat_rule_item (
	id varchar(64) NOT NULL, -- 主键
	rule_id varchar(64) NOT NULL, -- tb_cdcew_report_upload_repeat_rule.id
	item_context text NULL,-- 规则内容 json
	creator_id varchar(64) NULL, -- 创建人ID
	creator varchar(64) NULL, -- 创建人
	create_time timestamp(6) NULL, -- 创建时间
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,-- 是否删除 0未删除 1删除
	CONSTRAINT tb_cdcew_report_upload_repeat_rule_item_pk PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_report_upload_repeat_rule_item IS '报告卡规则表';
-- Column comments
CREATE INDEX tb_cdcew_report_upload_repeat_rule_id ON app.tb_cdcew_report_upload_repeat_rule_item (rule_id);
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.rule_id IS 'tb_cdcew_report_upload_repeat_rule.id';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.item_context IS '规则内容 json 如[{"code": "validCertNumber","desc": "有效证件号"}]';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.creator_id IS '创建人ID';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_upload_repeat_rule_item.delete_flag IS '是否删除 0未删除 1删除';

ALTER TABLE app.tb_cdcew_awareness_result ADD new_deaths int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD cum_deaths int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD infectious_rate int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD death_rate int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD calibrate_new_deaths int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD calibrate_cum_deaths int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD calibrate_infectious_rate int4 NULL;
ALTER TABLE app.tb_cdcew_awareness_result ADD calibrate_death_rate int4 NULL;

COMMENT ON COLUMN app.tb_cdcew_awareness_result.new_deaths IS '新增死亡数';
COMMENT ON COLUMN app.tb_cdcew_awareness_result.cum_deaths IS '累计死亡数';
COMMENT ON COLUMN app.tb_cdcew_awareness_result.infectious_rate IS '发病率';
COMMENT ON COLUMN app.tb_cdcew_awareness_result.death_rate IS '死亡率';

COMMENT ON COLUMN app.tb_cdcew_awareness_result.calibrate_new_deaths IS '校准参数-新增死亡数';
COMMENT ON COLUMN app.tb_cdcew_awareness_result.calibrate_cum_deaths IS '校准参数-累计死亡数';
COMMENT ON COLUMN app.tb_cdcew_awareness_result.calibrate_infectious_rate IS '校准参数-发病率';
COMMENT ON COLUMN app.tb_cdcew_awareness_result.calibrate_death_rate IS '校准参数-死亡率';
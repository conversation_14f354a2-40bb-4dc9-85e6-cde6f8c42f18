UPDATE app.tb_cdcew_system_default_config
SET config_type='algorithm_awareness_param', config_name='态势感知请求参数', config_key='algorithm_awareness_param', config_json='{"beta":"0.016","asympFactor":"1","latency":4.5,"mySympProb":0.75,"mySevereProb":0.1,"quarPeriod":14,"myDeathProb":0.01}', creator_id=NULL, creator=NULL, create_time=NULL, updater_id=NULL, update_time=NULL, updater=NULL
WHERE id='1';


INSERT INTO app.tb_cdcew_system_default_config
(id, config_type, config_name, config_key, config_json, creator_id, creator, create_time, updater_id, update_time, updater)
VALUES('2', 'algorithm_awareness_seir_param', '态势感知请求参数', 'algorithm_awareness_seir_param', '{"beta":"0.016","asympFactor":"1"}', NULL, NULL, NULL, NULL, NULL, NULL);

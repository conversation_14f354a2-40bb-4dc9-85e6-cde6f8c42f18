drop view if exists  v_warning_signal;
-- app.v_warning_signal source

-- app.v_warning_signal source

CREATE OR REPLACE VIEW app.v_warning_signal
AS SELECT tciws.signal_id,
    tciws.signal_num,
    tciws.warning_time,
    tciws.disease_code,
    tciws.disease_name,
    NULL::text AS warning_scenario,
    'infected'::text AS warning_type,
    tciws.stat_dim_id,
    tciws.stat_dim_name,
    tciws.province_code,
    tciws.province_name,
    tciws.city_code,
    tciws.city_name,
    tciws.district_code,
    tciws.district_name,
    tciws.street_code,
    tciws.street_name,
    tciws.medical_case_cnt,
    tciws.risk_level_id,
    tciws.risk_level_detail_id,
    NULL::text AS event_level,
    tciws.processing_status,
    tciws.processing_latest_time,
    tciws.warning_charge_person_id,
    tciws.warning_charge_person_name,
    tciws.warning_method_name,
    tciws.death_cnt,
    tciws.warning_threshold,
    tciws.warning_rule_id,
    tciws.warning_rule_desc,
    tciws.first_case_time,
    tciws.check_status,
    tciws.check_latest_time,
    tciws.check_finish_time,
    tciws.check_result,
    tciws.invest_status,
    tciws.invest_latest_time,
    tciws.invest_finish_time,
    tciws.invest_result,
    tciws.judge_status,
    tciws.judge_latest_time,
    tciws.judge_finish_time,
    tciws.judge_result,
    tciws.signal_change_log,
    tciws.warning_location_type,
    tciws.end_date,
    tciws.update_time,
    tciws.create_time,
    'infectious_warning' as signal_class,
    tciws.disease_name as warning_reason
   FROM app.tb_cdcew_infected_warning_signal tciws
UNION ALL
 SELECT tcsws.signal_id,
    tcsws.signal_num,
    tcsws.warning_time,
    tcsws.disease_code,
    tcsws.disease_name,
    NULL::text AS warning_scenario,
    'syndrome'::text AS warning_type,
    tcsws.stat_dim_id,
    tcsws.stat_dim_name,
    tcsws.province_code,
    tcsws.province_name,
    tcsws.city_code,
    tcsws.city_name,
    tcsws.district_code,
    tcsws.district_name,
    tcsws.street_code,
    tcsws.street_name,
    tcsws.medical_case_cnt,
    tcsws.risk_level_id,
    tcsws.risk_level_detail_id,
    NULL::text AS event_level,
    tcsws.processing_status,
    tcsws.processing_latest_time,
    tcsws.warning_charge_person_id,
    tcsws.warning_charge_person_name,
    tcsws.warning_method_name,
    tcsws.death_cnt,
    tcsws.warning_threshold,
    tcsws.warning_rule_id,
    tcsws.warning_rule_desc,
    tcsws.first_case_time,
    tcsws.check_status,
    tcsws.check_latest_time,
    tcsws.check_finish_time,
    tcsws.check_result,
    tcsws.invest_status,
    tcsws.invest_latest_time,
    tcsws.invest_finish_time,
    tcsws.invest_result,
    tcsws.judge_status,
    tcsws.judge_latest_time,
    tcsws.judge_finish_time,
    tcsws.judge_result,
    tcsws.signal_change_log,
    tcsws.warning_location_type,
    tcsws.end_date,
    tcsws.update_time,
    tcsws.create_time,
    'syndrome_warning' as signal_class,
    tcsws.disease_name as warning_reason
   FROM app.tb_cdcew_syndrome_warning_signal tcsws
UNION ALL
 SELECT tcmws.signal_id,
    tcmws.signal_num,
    tcmws.warning_time,
    tcmws.disease_code,
    tcmws.disease_name,
    NULL::text AS warning_scenario,
    'multichannel'::text AS warning_type,
    tcmws.stat_loc_group_id AS stat_dim_id,
    tcmws.stat_loc_group_name AS stat_dim_name,
    tcmws.province_code,
    tcmws.province_name,
    tcmws.city_code,
    tcmws.city_name,
    tcmws.district_code,
    tcmws.district_name,
    tcmws.street_code,
    tcmws.street_name,
    tcmws.medical_case_cnt,
    tcmws.risk_level_id,
    tcmws.risk_level_detail_id,
    ''::text AS event_level,
    tcmws.processing_status,
    tcmws.processing_latest_time,
    tcmws.warning_charge_person_id,
    tcmws.warning_charge_person_name,
    tcmws.warning_method_name,
    tcmws.death_cnt,
    tcmws.warning_threshold,
    tcmws.warning_rule_id,
    tcmws.warning_rule_desc,
    tcmws.first_case_time,
    tcmws.check_status,
    tcmws.check_latest_time,
    tcmws.check_finish_time,
    tcmws.check_result,
    tcmws.invest_status,
    tcmws.invest_latest_time,
    tcmws.invest_finish_time,
    tcmws.invest_result,
    tcmws.judge_status,
    tcmws.judge_latest_time,
    tcmws.judge_finish_time,
    tcmws.judge_result,
    tcmws.signal_change_log,
    warning_location_types AS warning_location_type,
    tcmws.end_date,
    tcmws.update_time,
    tcmws.create_time,
    case
        when tcmws.signal_type = 'infectious_warning' then 'infectious_warning'
        when tcmws.signal_type = 'syndrome_warning' then 'syndrome_warning'
        when tcmws.signal_type = 'pathogen_warning' then 'pathogen_warning'
        when tcmws.signal_type = 'epidemic_clues' then 'epidemic_clues'
    end as signal_class,
    tcmws.warning_reason
   FROM app.tb_cdcew_multichannel_warning_signal tcmws;
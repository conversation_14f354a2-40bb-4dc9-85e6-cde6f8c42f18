-- 创建表 tb_cdcew_warning_process_task_stats
CREATE TABLE tb_cdcew_warning_process_task_stats (
    -- 主键
    id VARCHAR(255) PRIMARY KEY,
    -- 省编码
    province_code VARCHAR(255),
    -- 省名称
    province_name VARCHAR(255),
    -- 市编码
    city_code VARCHAR(255),
    -- 市名称
    city_name VARCHAR(255),
    -- 区编码
    district_code VARCHAR(255),
    -- 区名称
    district_name VARCHAR(255),
    -- 疾病类型编码
    disease_type_code VARCHAR(255),
    -- 疾病类型
    disease_type VARCHAR(255),
    -- 疾病编码
    disease_code VARCHAR(255),
    -- 疾病名称
    disease_name VARCHAR(255),
    -- 风险等级
    risk_level_id VARCHAR(255),
    -- 预警地址类型
    warning_location_type VARCHAR(255),
    -- 任务类型
    task_type VARCHAR(255),
    -- 任务状态
    task_status VARCHAR(255),
    -- 任务结果
    task_result VARCHAR(255),
    -- 数量
    signal_num INTEGER,
    -- 统计日期
    stat_date TIMESTAMP,
    -- 创建日期
    create_time TIMESTAMP,
    -- 信号类型
    signal_type VARCHAR(255),
    -- 主题id
    topic_id VARCHAR(255),
    -- 主题名称
    topic_name VARCHAR(255),
    -- 预警类型
    warning_type VARCHAR(255)
);

-- 添加列注释
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.id IS '主键';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.province_code IS '省编码';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.province_name IS '省名称';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.city_code IS '市编码';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.city_name IS '市名称';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.district_code IS '区编码';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.district_name IS '区名称';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.disease_type_code IS '疾病类型编码';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.disease_type IS '疾病类型';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.disease_code IS '疾病编码';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.disease_name IS '疾病名称';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.risk_level_id IS '风险等级';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.warning_location_type IS '预警地址类型';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.task_type IS '任务类型';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.task_status IS '任务状态';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.task_result IS '任务结果';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.signal_num IS '数量';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.stat_date IS '统计日期';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.create_time IS '创建日期';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.signal_type IS '信号类型';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.topic_id IS '主题id';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.topic_name IS '主题名称';
COMMENT ON COLUMN tb_cdcew_warning_process_task_stats.warning_type IS '预警类型';
-- 创建表 tb_cdcew_warning_signal_stats
DROP TABLE if exists app.tb_cdcew_warning_signal_stats;
CREATE TABLE app.tb_cdcew_warning_signal_stats (
	id varchar(255) NOT NULL, -- 主键
	province_code varchar(255) NULL, -- 省编码
	province_name varchar(255) NULL, -- 省名称
	city_code varchar(255) NULL, -- 市编码
	city_name varchar(255) NULL, -- 市名称
	district_code varchar(255) NULL, -- 区编码
	district_name varchar(255) NULL, -- 区名称
	disease_type varchar(255) NULL, -- 疾病类型
	disease_name varchar(255) NULL, -- 疾病名称
	risk_level_id varchar(255) NULL, -- 风险等级
	warning_location_type varchar(255) NULL, -- 预警地址类型
	signal_num int4 NULL, -- 信号数量
	stat_date date NULL, -- 统计日期
	processing_status varchar(255) NULL, -- 信号状态
	create_time timestamp NULL, -- 创建日期
	disease_type_code varchar(255) NULL, -- 疾病类型编码
	disease_code varchar(255) NULL, -- 疾病编码
	topic_id varchar(255) NULL, -- 主题id
	topic_name varchar(255) NULL, -- 主题名称
	signal_type varchar(255) NULL, -- 信号类型
	warning_type varchar(255) NULL, -- 预警类型
	CONSTRAINT tb_cdcew_warning_signal_stats_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.province_code IS '省编码';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.province_name IS '省名称';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.city_code IS '市编码';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.city_name IS '市名称';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.district_code IS '区编码';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.district_name IS '区名称';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.disease_type IS '疾病类型';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.risk_level_id IS '风险等级';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.warning_location_type IS '预警地址类型';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.signal_num IS '信号数量';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.stat_date IS '统计日期';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.processing_status IS '信号状态';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.create_time IS '创建日期';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.disease_type_code IS '疾病类型编码';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.disease_code IS '疾病编码';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.topic_id IS '主题id';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.topic_name IS '主题名称';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.signal_type IS '信号类型';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_stats.warning_type IS '预警类型';
create table app.tb_cdcew_infected_warning_signal_stats_bak20250217
as select * from app.tb_cdcew_infected_warning_signal_stats;

create table app.tb_cdcew_syndrome_warning_signal_stats_bak20250217
as select * from app.tb_cdcew_syndrome_warning_signal_stats;

create table app.tb_cdcew_multichannel_warning_signal_stats_bak20250217
as select * from app.tb_cdcew_multichannel_warning_signal_stats;

create table app.tb_cdcew_infected_processing_task_stats_bak20250217
as select * from app.tb_cdcew_infected_processing_task_stats;

create table app.tb_cdcew_syndrome_processing_task_stats_bak20250217
as select * from app.tb_cdcew_syndrome_processing_task_stats;

create table app.tb_cdcew_multichannel_processing_task_stats_bak20250217
as select * from app.tb_cdcew_multichannel_processing_task_stats;

drop table if exists app.tb_cdcew_infected_warning_signal_stats;
drop table if exists app.tb_cdcew_syndrome_warning_signal_stats;
drop table if exists app.tb_cdcew_multichannel_warning_signal_stats;

drop table if exists app.tb_cdcew_infected_processing_task_stats;
drop table if exists app.tb_cdcew_syndrome_processing_task_stats;
drop table if exists app.tb_cdcew_multichannel_processing_task_stats;

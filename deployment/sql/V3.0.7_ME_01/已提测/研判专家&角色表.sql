
drop table if exists app.tb_cdcew_judgement_expert;
CREATE TABLE app.tb_cdcew_judgement_expert (
	id varchar(64) NOT NULL PRIMARY KEY,
	role_id varchar(64) NOT NULL,
	expert_id varchar(64) NOT NULL,
	expert_name varchar(64) NOT NULL,
	province_code varchar(64) NOT NULL,
	province_name varchar(64) NOT NULL,
	city_code varchar(64) NULL,
	city_name varchar(64) NULL,
	district_code varchar(64) NULL,
	district_name varchar(64) NULL,
	creator_id varchar(64) NULL,
	creator varchar(64) NULL,
	create_time timestamp NULL,
	updater varchar(64) NULL,
	updater_id varchar(64) NULL,
	update_time timestamp NULL,
	delete_flag varchar(2) NULL
);

-- 添加表注释
COMMENT ON TABLE app.tb_cdcew_judgement_expert IS '研判专家表';

-- 添加字段注释
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.id IS 'id';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.role_id IS '研判角色id';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.expert_id IS '研判专家id';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.expert_name IS '研判专家姓名';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.province_code IS '省code';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.province_name IS '省';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.city_code IS '市code';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.city_name IS '市';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.district_code IS '县区code';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.district_code IS '县区';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.updater IS '更新人';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.delete_flag IS '删除标识:0-未删除,1-已删除';

ALTER TABLE app.tb_cdcew_judgement_expert ADD expert_phone varchar(18) null;
COMMENT ON COLUMN app.tb_cdcew_judgement_expert.expert_phone IS '专家电话';


--角色
drop table if exists app.tb_cdcew_judgement_role;
CREATE TABLE app.tb_cdcew_judgement_role (
	id varchar(64) NOT NULL PRIMARY KEY,
	role_name varchar(64) NOT NULL,
	notes text NULL,
	creator_id varchar(64) NULL,
	creator varchar(64) NULL,
	create_time timestamp NULL,
	updater varchar(64) NULL,
	updater_id varchar(64) NULL,
	update_time timestamp NULL,
	delete_flag varchar(2) NULL
);

-- 添加表注释
COMMENT ON TABLE app.tb_cdcew_judgement_role IS '研判角色表';

-- 添加字段注释
COMMENT ON COLUMN app.tb_cdcew_judgement_role.id IS '研判角色id';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.role_name IS '研判角色名称';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.notes IS '备注';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.updater IS '更新人';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_judgement_role.delete_flag IS '删除标识:0-未删除,1-已删除';
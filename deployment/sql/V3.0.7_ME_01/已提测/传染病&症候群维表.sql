drop table if exists app.tb_cdcew_infected_disease_info;
create table if not exists app.tb_cdcew_infected_disease_info
(
    id                     varchar(64) not null primary key,
    infected_class_code    varchar(64),
    infected_class_name    varchar(64),
    disease_type_code      varchar(64),
    disease_type_name      varchar(64),
    parent_disease_id      varchar(64),
    parent_disease_code    varchar(64),
    parent_disease_name    varchar(64),
    level_type             varchar(64),
    disease_code           varchar(64),
    disease_name           varchar(64),
    order_flag             integer,
    transmission_type_code varchar(64),
    transmission_type_name varchar(64),
    management_type_code   varchar(64),
    management_type_name   varchar(64),
    alias                  varchar(64),
    notes                  text,
    status                 smallint    default 1,
    delete_flag            varchar(10) default '0'::character varying,
    create_time            timestamp,
    update_time            timestamp,
    creator_id             varchar(100),
    creator                varchar(100),
    updater_id             varchar(100),
    updater                varchar(100)
);
comment on table app.tb_cdcew_infected_disease_info is '传染病维表';

comment on column app.tb_cdcew_infected_disease_info.id is '传染病信息表主键ID';
comment on column app.tb_cdcew_infected_disease_info.infected_class_code is '传染病类型代码 01. 法定；  09.其他(包含自定义的)';
comment on column app.tb_cdcew_infected_disease_info.infected_class_name is '传染病类型名称';
comment on column app.tb_cdcew_infected_disease_info.disease_type_code is '传染病病种类型 甲乙丙其他';
comment on column app.tb_cdcew_infected_disease_info.disease_type_name is '传染病病种类型名称';
comment on column app.tb_cdcew_infected_disease_info.parent_disease_id is '父级病种id';
comment on column app.tb_cdcew_infected_disease_info.parent_disease_code is '父级病种code';
comment on column app.tb_cdcew_infected_disease_info.parent_disease_name is '父级病种name';
comment on column app.tb_cdcew_infected_disease_info.level_type is '类型: 传染病病种;传染病;传染病亚型';
comment on column app.tb_cdcew_infected_disease_info.disease_code is '病种code';
comment on column app.tb_cdcew_infected_disease_info.disease_name is '病种name';
comment on column app.tb_cdcew_infected_disease_info.order_flag is '排序字段';
comment on column app.tb_cdcew_infected_disease_info.transmission_type_code is '传播途径分类code';
comment on column app.tb_cdcew_infected_disease_info.transmission_type_name is '传播途径分类name';
comment on column app.tb_cdcew_infected_disease_info.management_type_code is '传染病类别管理代码';
comment on column app.tb_cdcew_infected_disease_info.management_type_name is '传染病类别管理名称';
comment on column app.tb_cdcew_infected_disease_info.alias is '别名';
comment on column app.tb_cdcew_infected_disease_info.notes is '备注';
comment on column app.tb_cdcew_infected_disease_info.status is '状态（启用状态 1启用；0未启用）';
comment on column app.tb_cdcew_infected_disease_info.delete_flag is '删除标识： 0-未删除，1-已删除';
comment on column app.tb_cdcew_infected_disease_info.create_time is '创建时间';
comment on column app.tb_cdcew_infected_disease_info.update_time is '更新时间';
comment on column app.tb_cdcew_infected_disease_info.creator_id is '创建人id';
comment on column app.tb_cdcew_infected_disease_info.creator is '创建人姓名';
comment on column app.tb_cdcew_infected_disease_info.updater_id is '更新人id';
comment on column app.tb_cdcew_infected_disease_info.updater is '更新人name';


-- auto-generated definition
drop table if exists app.tb_cdcew_syndrome_disease_info;
create table app.tb_cdcew_syndrome_disease_info
(
    id                varchar(255) not null primary key,
    disease_code      varchar(255),
    disease_name      varchar(255),
    disease_parent_id varchar(255),
    notes             varchar(255),
    status            smallint,
    delete_flag       varchar(10),
    create_time       timestamp,
    creator           varchar(255),
    creator_id        varchar(255),
    update_time       timestamp,
    updater           varchar(255),
    updater_id        varchar(255),
    order_flag        integer
);

comment on table app.tb_cdcew_syndrome_disease_info is '症候群疾病分类信息表';

comment on column app.tb_cdcew_syndrome_disease_info.id is 'uuid';
comment on column app.tb_cdcew_syndrome_disease_info.disease_code is '疾病编码';
comment on column app.tb_cdcew_syndrome_disease_info.disease_name is '疾病名称';
comment on column app.tb_cdcew_syndrome_disease_info.disease_parent_id is '疾病父类ID';
comment on column app.tb_cdcew_syndrome_disease_info.notes is '备注信息';
comment on column app.tb_cdcew_syndrome_disease_info.status is '状态（0：关闭；1开启）';
comment on column app.tb_cdcew_syndrome_disease_info.delete_flag is '删除标识（0：未删除；1：已删除）';
comment on column app.tb_cdcew_syndrome_disease_info.create_time is '创建时间';
comment on column app.tb_cdcew_syndrome_disease_info.creator is '创建人';
comment on column app.tb_cdcew_syndrome_disease_info.creator_id is '创建人ID';
comment on column app.tb_cdcew_syndrome_disease_info.update_time is '更新时间';
comment on column app.tb_cdcew_syndrome_disease_info.updater is '更新人';
comment on column app.tb_cdcew_syndrome_disease_info.updater_id is '更新人ID';
comment on column app.tb_cdcew_syndrome_disease_info.order_flag is '排序';


insert into app.tb_cdcew_syndrome_disease_info
select *
from app.tb_cdcmr_syndrome_disease_info
where delete_flag = '0';

insert into app.tb_cdcew_infected_disease_info
select *
from app.tb_cdcmr_infected_disease_info
where delete_flag = '0';



INSERT INTO app.tb_cdcmr_param_config (id, config_group, config_code, config_name, config_value, config_remark, create_user, update_user, create_time, update_time, is_delete, update_user_name) VALUES('183841038182383736', '1', 'cdc-platform-api:pushEmergencySwitch', '推送突发公共卫生事件开关', '1', '1:开启；0:关闭', '100001', '100001', '2025-06-04 15:38:33.994', '2025-06-04 15:38:33.994', '1', '超级管理员');


-- 创建信号研判流程表
drop table if exists app.tb_cdcew_signal_judge_process;
CREATE TABLE app.tb_cdcew_signal_judge_process (
    id VARCHAR(64) PRIMARY KEY,
    judge_process_name VARCHAR(64),
    expert_category_codes VARCHAR(64),
    expert_categories VARCHAR(64),
    judge_expert_codes VA<PERSON><PERSON><PERSON>(255),
    judge_experts VARCHAR(255),
    init_diagnose VARCHAR(100),
    risk_level VARCHAR(64),
    warning_type VARCHAR(32),
    creator_id VARCHAR(64),
    creator VARCHAR(64),
    create_time TIMESTAMP,
    updater VARCHAR(64),
    updater_id VARCHAR(64),
    update_time TIMESTAMP,
    delete_flag VARCHAR(2)
);

-- 添加表注释
COMMENT ON TABLE app.tb_cdcew_signal_judge_process IS '信号研判流程';

-- 添加字段注释
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.id IS 'uuid';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.judge_process_name IS '研判流程名称';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.expert_category_codes IS '研判角色code';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.expert_categories IS '研判角色名称';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.judge_expert_codes IS '研判专家code';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.judge_experts IS '研判专家名称';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.init_diagnose IS '初步诊断';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.risk_level IS '风险等级';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.warning_type IS '预警类型';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.creator IS '创建人姓名';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.updater IS '更新人姓名';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.delete_flag IS '删除标识:0-未删除,1-已删除';


ALTER TABLE app.tb_cdcew_emergency_event ADD init_diagnose_other varchar(512) NULL;
COMMENT ON COLUMN app.tb_cdcew_emergency_event.init_diagnose_other IS '初步诊断_其它';
ALTER TABLE app.tb_cdcew_emergency_event ADD cause_factor_other varchar(512) NULL;
COMMENT ON COLUMN app.tb_cdcew_emergency_event.cause_factor_other IS '致病因素_其它';

ALTER TABLE app.tb_cdcew_emergency_event_process_info ADD patient_identity_type varchar(32) NULL;
COMMENT ON COLUMN app.tb_cdcew_emergency_event_process_info.patient_identity_type IS '身份证件类型';
ALTER TABLE app.tb_cdcew_emergency_event_process_info ADD patient_identity_no varchar(32) NULL;
COMMENT ON COLUMN app.tb_cdcew_emergency_event_process_info.patient_identity_no IS '身份证号';

--新建风险评估问卷模板表
CREATE TABLE app.tb_cdcew_risk_assessment_question_template (
    id VARCHAR(64) PRIMARY KEY,
    name VARCHAR(64) NOT NULL,
    content TEXT,
    status VARCHAR(2),
    notes TEXT,
    create_time TIMESTAMP,
    creator VA<PERSON><PERSON><PERSON>(64),
    creator_id VARCHAR(64),
    update_time TIMESTAMP,
    updater VARCHAR(64),
    updater_id VARCHAR(64),
    delete_flag VARCHAR(2)
);

COMMENT ON TABLE app.tb_cdcew_risk_assessment_question_template IS '风险评估问卷模板表';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.name IS '问卷名称';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.content IS '问卷内容';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.status IS '状态';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.notes IS '备注';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.updater IS '更新人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_template.delete_flag IS '删除标识';

--新建风险评估问卷填写结果表
CREATE TABLE app.tb_cdcew_risk_assessment_question_result (
    id VARCHAR(64) PRIMARY KEY,
    assessment_event_id VARCHAR(64) NOT NULL,
    name VARCHAR(255),
    content TEXT,
    personal_opinion TEXT,
    round VARCHAR(2),
    create_time TIMESTAMP,
    creator VARCHAR(64),
    creator_id VARCHAR(64),
    update_time TIMESTAMP,
    updater VARCHAR(64),
    updater_id VARCHAR(64),
    delete_flag VARCHAR(2)
);

COMMENT ON TABLE app.tb_cdcew_risk_assessment_question_result IS '风险评估问卷填写结果表';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.assessment_event_id IS '评估事件id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.name IS '问卷名称';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.content IS '问卷填写结果';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.personal_opinion IS '个人意见';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.round IS '轮次';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.updater IS '更新人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_question_result.delete_flag IS '删除标识';

CREATE TABLE app.tb_cdcew_risk_assessment_questionnaire_round (
    id VARCHAR(64) PRIMARY KEY,
    assessment_event_id VARCHAR(64),
    round VARCHAR(2),
    is_last_round VARCHAR(2),
    question_template_id VARCHAR(64),
    create_time TIMESTAMP,
    creator VARCHAR(64),
    creator_id VARCHAR(64),
    update_time TIMESTAMP,
    updater VARCHAR(64),
    updater_id VARCHAR(64),
    delete_flag VARCHAR(2)
);

COMMENT ON TABLE app.tb_cdcew_risk_assessment_questionnaire_round IS '风险评估问卷轮次表';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.assessment_event_id IS '关联评估事件';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.round IS '当前轮次';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.is_last_round IS '是否为最后一轮';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.question_template_id IS '所用问卷模板id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.updater IS '更新人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_questionnaire_round.delete_flag IS '删除标识';

--风险矩阵法结果表
CREATE TABLE app.tb_cdcew_risk_assessment_factor_result (
    id VARCHAR(64) PRIMARY KEY,
    assessment_event_id VARCHAR(64),
    content TEXT,
    management_advice TEXT,
    create_time TIMESTAMP,
    creator VARCHAR(64),
    creator_id VARCHAR(64),
    update_time TIMESTAMP,
    updater VARCHAR(64),
    updater_id VARCHAR(64),
    delete_flag VARCHAR(2)
);

COMMENT ON TABLE app.tb_cdcew_risk_assessment_factor_result IS '风险评估风险因素结果表';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.assessment_event_id IS '评估事件id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.content IS '风险因素填写结果';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.management_advice IS '管理建议';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.creator_id IS '创建人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.updater IS '更新人';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.updater_id IS '更新人id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_factor_result.delete_flag IS '删除标识';


ALTER TABLE app.tb_cdcew_risk_assessment_event
ADD COLUMN if not exists risk_level VARCHAR(64),
ADD COLUMN if not exists is_pushed VARCHAR(2) DEFAULT '0';

COMMENT ON COLUMN app.tb_cdcew_risk_assessment_event.risk_level IS '风险等级';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_event.is_pushed IS '是否推送至突发公共卫生事件系统';

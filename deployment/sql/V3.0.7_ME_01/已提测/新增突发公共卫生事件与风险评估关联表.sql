-- 新增突发公共卫生事件与风险评估关联表
CREATE TABLE app.tb_cdcew_emergency_event_risk_assessment_relation (
    id                     VARCHAR(64) PRIMARY KEY,
    event_id               VARCHAR(64),
    event_type             VARCHAR(64),
    assessment_event_id    VARCHAR(64),
    assessment_type  VARCHAR(64),
    create_time            TIM<PERSON><PERSON><PERSON>,
    creator                <PERSON><PERSON><PERSON><PERSON>(64),
    creator_id             VARCHAR(64),
    update_time            TIMESTAMP,
    updater                VARCHAR(64),
    updater_id             VARCHAR(64),
    delete_flag            VARCHAR(2)
);

COMMENT ON TABLE app.tb_cdcew_emergency_event_risk_assessment_relation IS '应急事件与风险评估事件关联表';

COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.id IS '主键ID';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.event_id IS '突发公共卫生事件ID';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.event_type IS '突发公共卫生事件类型';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.assessment_event_id IS '风险评估ID';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.assessment_type IS '风险评估类型';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.creator IS '创建人名称';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.creator_id IS '创建人ID';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.updater IS '更新人名称';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.updater_id IS '更新人ID';
COMMENT ON COLUMN app.tb_cdcew_emergency_event_risk_assessment_relation.delete_flag IS '删除标志，0：未删除，1：已删除';

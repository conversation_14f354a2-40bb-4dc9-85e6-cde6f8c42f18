
ALTER TABLE app.tb_cdcew_judge_process ADD if not exists expert_info text NULL;
COMMENT ON COLUMN app.tb_cdcew_judge_process.expert_info IS '专家信息';

ALTER TABLE app.tb_cdcew_signal_judge_process ADD if not exists expert_info text NULL;
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.expert_info IS '专家信息';

ALTER TABLE app.tb_cdcew_signal_judge_process ADD if not exists judge_deadline numeric NULL;
COMMENT ON COLUMN app.tb_cdcew_signal_judge_process.judge_deadline IS '研判处理时限';
ALTER TABLE app.tb_cdcew_signal_judge_process ALTER COLUMN judge_deadline TYPE int4 USING judge_deadline::int4;


INSERT INTO app.tb_cdcmr_outbound_template (id, "name", template_content, "type", category, speech_id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag) VALUES('3', '专家研判短信模板', '您好，您有一条待研判任务，#{task.businessTypeName}ID为#{task.refId}，请及时处理。', 'sms', 'expert-outbound', '12107', NULL, NULL, NULL, NULL, NULL, NULL, '0');

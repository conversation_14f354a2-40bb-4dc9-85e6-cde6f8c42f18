delete from app.tb_cdcdm_data_model_form
where model_id in ('154150725981569182', '154149113221349534');
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154809182050320546', '154149113221349534', '154809182049009826', '信号主数据', 0, 1, NULL, '[{"type":"group","field":"7ax23ddb54","title":"信号特征信息","repeat":0,"props":{"max":1,"rules":[{"placeholder":"请输入","type":"input","field":"1xj660ft5os","title":"信号ID","databaseOption":{"label":"signal_id","value":"tb_cdcew_disease_warning_signal:signal_id","desc":"主键"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:signal_id"],"databaseName":["tb_cdcew_disease_warning_signal","signal_id"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}","format":"YYYY-MM-DD HH:mm:ss","placeholder":"请选择","type":"datePicker","field":"26otu43y20t","title":"信号生成时间","databaseOption":{"label":"warning_time","value":"tb_cdcew_disease_warning_signal:warning_time","desc":"预警时间"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:warning_time"],"databaseName":["tb_cdcew_disease_warning_signal","warning_time"]},{"placeholder":"请选择","showOptions":true,"type":"cascader","field":"295g2j16y9h","title":"预警病种","databaseOption":{"label":"disease_name","value":"tb_cdcew_disease_warning_signal:disease_name","desc":"疾病名称"},"optionField":"155377925913313400","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:disease_name"],"databaseName":["tb_cdcew_disease_warning_signal","disease_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"input","field":"21v4z7zg3u8","title":"预警地点","databaseOption":{"label":"stat_dim_name","value":"tb_cdcew_disease_warning_signal:stat_dim_name","desc":"预警地区名称：地点/区划/单位/学校等"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:stat_dim_name"],"databaseName":["tb_cdcew_disease_warning_signal","stat_dim_name"],"desensitizedType":"ADDRESS","props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"29u9c32kv9p","title":"风险分级","databaseOption":{"label":"risk_level_id","value":"tb_cdcew_disease_warning_signal:risk_level_id","desc":"风险等级id"},"optionField":"155384152542150776","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:risk_level_id"],"databaseName":["tb_cdcew_disease_warning_signal","risk_level_id"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"cascader","field":"1al96sfhy6a","title":"所在辖区","databaseOption":{"label":"district_name","value":"tb_cdcew_disease_warning_signal:district_name","desc":"区县名称"},"optionField":"155923923630817407","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:district_name"],"databaseName":["tb_cdcew_disease_warning_signal","district_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"syua2yfaq","title":"病例数","databaseOption":{"label":"medical_case_cnt","value":"tb_cdcew_disease_warning_signal:medical_case_cnt","desc":"病例数"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:medical_case_cnt"],"databaseName":["tb_cdcew_disease_warning_signal","medical_case_cnt"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"1p9ezc8xo4s","title":"死亡数","databaseOption":{"label":"death_cnt","value":"tb_cdcew_disease_warning_signal:death_cnt","desc":"死亡数量"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:death_cnt"],"databaseName":["tb_cdcew_disease_warning_signal","death_cnt"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"29qpq5cyhj5","title":"预警线","databaseOption":{"label":"warning_threshold","value":"tb_cdcew_disease_warning_signal:warning_threshold","desc":"预警阈值"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:warning_threshold"],"databaseName":["tb_cdcew_disease_warning_signal","warning_threshold"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"}]}},{"type":"group","field":"203c0d4o48j","title":"信号处理情况","repeat":0,"props":{"max":1,"rules":[{"placeholder":"请选择","showOptions":true,"type":"radio","field":"1fm19k016v9","title":"信号状态","databaseOption":{"label":"processing_status","value":"tb_cdcew_disease_warning_signal:processing_status","desc":"信号当前状态"},"optionField":"154151240303902878","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:processing_status"],"databaseName":["tb_cdcew_disease_warning_signal","processing_status"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"input","field":"y6mes44avk","title":"信号责任人","databaseOption":{"label":"warning_charge_person_name","value":"tb_cdcew_disease_warning_signal:warning_charge_person_name","desc":"责任人姓名"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:warning_charge_person_name"],"databaseName":["tb_cdcew_disease_warning_signal","warning_charge_person_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"26e70b05035","title":"核实结果","databaseOption":{"label":"check_result","value":"tb_cdcew_disease_warning_signal:check_result","desc":"核实结果"},"optionField":"154151519476777118","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:check_result"],"databaseName":["tb_cdcew_disease_warning_signal","check_result"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"8vmt2vz15i","title":"调查结论","databaseOption":{"label":"invest_result","value":"tb_cdcew_disease_warning_signal:invest_result","desc":"调查结果"},"optionField":"154151586048770206","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:invest_result"],"databaseName":["tb_cdcew_disease_warning_signal","invest_result"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"type":"input","field":"2fneace4zsv","title":"调查结论研判依据","placeholder":"请输入","databaseField":[],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"1oroxnop7df","title":"专家研判结论","databaseOption":{"label":"judge_result","value":"tb_cdcew_disease_warning_signal:judge_result","desc":"研判结果"},"optionField":"154151640809603230","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:judge_result"],"databaseName":["tb_cdcew_disease_warning_signal","judge_result"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"type":"input","field":"1tsh0f8wooi","title":"研判专家","placeholder":"请输入","props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"}]}}]', 0, '2024-07-26 17:14:46.604', 'admin', '2025-03-27 09:36:43.677', 'admin', 6, NULL);
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154547593844883618', '154150725981569182', '154547593844621474', '信号主数据', 0, 1, NULL, '[{"type":"group","field":"8z6f0lgx6o","title":"信号特征信息","repeat":0,"props":{"max":1,"rules":[{"placeholder":"请输入","type":"input","field":"1ck5dis5i5u","title":"信号ID","databaseOption":{"label":"signal_id","value":"tb_cdcew_disease_warning_signal:signal_id","desc":"主键"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:signal_id"],"databaseName":["tb_cdcew_disease_warning_signal","signal_id"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}","format":"YYYY-MM-DD HH:mm:ss","placeholder":"请选择","type":"datePicker","field":"2o0p2ybfwyo","title":"信号生成时间","databaseOption":{"label":"warning_time","value":"tb_cdcew_disease_warning_signal:warning_time","desc":"预警时间"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:warning_time"],"databaseName":["tb_cdcew_disease_warning_signal","warning_time"]},{"placeholder":"请选择","showOptions":true,"type":"cascader","field":"22cv0wlrjve","title":"预警病种","databaseOption":{"label":"disease_name","value":"tb_cdcew_disease_warning_signal:disease_name","desc":"疾病名称"},"optionField":"156012680203731071","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:disease_name"],"databaseName":["tb_cdcew_disease_warning_signal","disease_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"input","field":"1bdm2a6gz9a","title":"预警地点","databaseOption":{"label":"stat_dim_name","value":"tb_cdcew_disease_warning_signal:stat_dim_name","desc":"预警地区名称：地点/区划/单位/学校等"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:stat_dim_name"],"databaseName":["tb_cdcew_disease_warning_signal","stat_dim_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"2htdoy5knds","title":"风险分级","databaseOption":{"label":"risk_level_id","value":"tb_cdcew_disease_warning_signal:risk_level_id","desc":"风险等级id"},"optionField":"155384152542150776","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:risk_level_id"],"databaseName":["tb_cdcew_disease_warning_signal","risk_level_id"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"cascader","field":"1ahpkuq7msg","title":"所在辖区","databaseOption":{"label":"district_name","value":"tb_cdcew_disease_warning_signal:district_name","desc":"区县名称"},"optionField":"155923923630817407","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:district_name"],"databaseName":["tb_cdcew_disease_warning_signal","district_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"1sie38x0k0m","title":"病例数","databaseOption":{"label":"medical_case_cnt","value":"tb_cdcew_disease_warning_signal:medical_case_cnt","desc":"病例数"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:medical_case_cnt"],"databaseName":["tb_cdcew_disease_warning_signal","medical_case_cnt"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"1eml66u52as","title":"死亡数","databaseOption":{"label":"death_cnt","value":"tb_cdcew_disease_warning_signal:death_cnt","desc":"死亡数量"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:death_cnt"],"databaseName":["tb_cdcew_disease_warning_signal","death_cnt"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"2m8w43exj3e","title":"预警线","databaseOption":{"label":"warning_threshold","value":"tb_cdcew_disease_warning_signal:warning_threshold","desc":"预警阈值"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:warning_threshold"],"databaseName":["tb_cdcew_disease_warning_signal","warning_threshold"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"}]}},{"type":"group","field":"12f1m1xynki","title":"信号处理情况","repeat":0,"props":{"max":1,"rules":[{"placeholder":"请选择","showOptions":true,"type":"radio","field":"1v2jxlk1gz1","title":"信号状态","databaseOption":{"label":"processing_status","value":"tb_cdcew_disease_warning_signal:processing_status","desc":"信号当前状态"},"optionField":"154151240303902878","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:processing_status"],"databaseName":["tb_cdcew_disease_warning_signal","processing_status"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"input","field":"2lnmgh43xzc","title":"信号责任人","databaseOption":{"label":"warning_charge_person_name","value":"tb_cdcew_disease_warning_signal:warning_charge_person_name","desc":"责任人姓名"},"databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:warning_charge_person_name"],"databaseName":["tb_cdcew_disease_warning_signal","warning_charge_person_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"1st0x22gynr","title":"核实结果","databaseOption":{"label":"check_result","value":"tb_cdcew_disease_warning_signal:check_result","desc":"核实结果"},"optionField":"154151519476777118","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:check_result"],"databaseName":["tb_cdcew_disease_warning_signal","check_result"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"1naislx8yzp","title":"调查结论","databaseOption":{"label":"invest_result","value":"tb_cdcew_disease_warning_signal:invest_result","desc":"调查结果"},"optionField":"154151586048770206","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:invest_result"],"databaseName":["tb_cdcew_disease_warning_signal","invest_result"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"input","field":"1sie38x22ha","title":"调查结论判断依据","props":"{\"isWordLimit\":false,\"wordLimit\":[],\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"2c7bykam343","title":"专家研判结论","databaseOption":{"label":"judge_result","value":"tb_cdcew_disease_warning_signal:judge_result","desc":"研判结果"},"optionField":"154151640809603230","databaseField":["tb_cdcew_disease_warning_signal","tb_cdcew_disease_warning_signal:judge_result"],"databaseName":["tb_cdcew_disease_warning_signal","judge_result"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"input","field":"h8xlotei62","title":"研判专家","props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":32,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"}]}}]', 0, '2024-07-23 21:34:23.092', 'admin', '2025-03-27 09:29:57.732', 'admin', 5, NULL);
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154547593845145762', '154150725981569182', '154547593844621474', '异常信息卡', NULL, NULL, NULL, NULL, 0, '2024-07-23 21:34:23.092', 'admin', '2025-02-13 15:14:16.441', 'zhuangli5', 2, 'SUSPECT_REPORT,154146260289323166');
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154547593845407906', '154150725981569182', '154547593844621474', '现场调查记录', NULL, NULL, NULL, NULL, 0, '2024-07-23 21:34:23.092', 'admin', '2025-02-13 15:14:16.441', 'zhuangli5', 1, 'INVEST_REPORT,154147841911029918');
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154809182049009826', '154149113221349534', '154809182049009826', '异常信息卡', NULL, NULL, NULL, NULL, 0, '2024-07-26 17:14:46.604', 'admin', '2025-02-20 19:09:01.468', 'admin', 2, 'SUSPECT_REPORT,154146260289323166');
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154809182049796258', '154149113221349534', '154809182049009826', '现场调查表', NULL, NULL, NULL, NULL, 0, '2024-07-26 17:14:46.604', 'admin', '2025-02-20 19:09:01.468', 'admin', 1, 'INVEST_REPORT,154147841911029918');
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154809182049271970', '154149113221349534', '154809182049009826', '预警依据', 0, 1, NULL, '[{"type":"group","field":"10cltdwm1hg","title":"预警方法","repeat":0,"props":{"max":1,"rules":[{"type":"radio","field":"evuz7mkukv","title":"预警方法","placeholder":"请选择","databaseOption":{"label":"warning_method","value":"tb_cdcew_syndrome_signal_base:warning_method"},"optionField":"154151727782690974","databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:warning_method"],"databaseName":["tb_cdcew_syndrome_signal_base","warning_method"],"props":"{}"},{"type":"radio","field":"2kdjjat7qfk","title":"统计指标","placeholder":"请选择","databaseOption":{"label":"stat_indicator","value":"tb_cdcew_syndrome_signal_base:stat_indicator"},"optionField":"154151755699978398","databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:stat_indicator"],"databaseName":["tb_cdcew_syndrome_signal_base","stat_indicator"],"props":"{}"},{"type":"radio","field":"oxf4qqzfew","title":"病例范围_纳排","placeholder":"请选择","databaseOption":{"label":"has_excluded_case","value":"tb_cdcew_syndrome_signal_base:has_excluded_case"},"optionField":"154151786838491294","databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:has_excluded_case"],"databaseName":["tb_cdcew_syndrome_signal_base","has_excluded_case"],"props":"{}"},{"placeholder":"请选择","showOptions":true,"type":"checkbox","field":"kathq267bk","title":"病例范围_重症","databaseOption":{"label":"severe_scope","value":"tb_cdcew_syndrome_signal_base:severe_scope"},"databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:severe_scope"],"databaseName":["tb_cdcew_syndrome_signal_base","severe_scope"],"optionField":"155378833225154680","props":"{}"},{"type":"radio","field":"tk0rrg0zd1","title":"病例范围_亚组","placeholder":"请选择","databaseOption":{"label":"subgroup_type","value":"tb_cdcew_syndrome_signal_base:subgroup_type"},"optionField":"154151942531055774","databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:subgroup_type"],"databaseName":["tb_cdcew_syndrome_signal_base","subgroup_type"],"props":"{}"},{"type":"input","field":"hxqv8uqoag","title":"病例范围_具体亚组","placeholder":"请输入","databaseOption":{"label":"subgroup_name","value":"tb_cdcew_syndrome_signal_base:subgroup_name"},"databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:subgroup_name"],"databaseName":["tb_cdcew_syndrome_signal_base","subgroup_name"],"props":"{}"},{"type":"radio","field":"1886kb9eml7","title":"统计区域","placeholder":"请选择","databaseOption":{"label":"stat_region","value":"tb_cdcew_syndrome_signal_base:stat_region"},"optionField":"154152019840467102","databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:stat_region"],"databaseName":["tb_cdcew_syndrome_signal_base","stat_region"],"props":"{}"},{"type":"number","field":"fh4mtxlukp","title":"统计区域数","placeholder":"请输入","databaseOption":{"label":"stat_region_count","value":"tb_cdcew_syndrome_signal_base:stat_region_count"},"databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:stat_region_count"],"databaseName":["tb_cdcew_syndrome_signal_base","stat_region_count"],"props":"{}"},{"type":"number","field":"24th9bjseer","title":"时间窗口","placeholder":"请输入","databaseOption":{"label":"time_window","value":"tb_cdcew_syndrome_signal_base:time_window"},"databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:time_window"],"databaseName":["tb_cdcew_syndrome_signal_base","time_window"],"props":"{}"},{"type":"radio","field":"1uha9za0v85","title":"时间窗口单位","placeholder":"请选择","databaseOption":{"label":"time_window_unit","value":"tb_cdcew_syndrome_signal_base:time_window_unit"},"optionField":"154152073527558302","databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:time_window_unit"],"databaseName":["tb_cdcew_syndrome_signal_base","time_window_unit"],"props":"{}"},{"type":"input","field":"2hwxavw9iyy","title":"基线数据","placeholder":"请输入","databaseOption":{"label":"baseline","value":"tb_cdcew_syndrome_signal_base:baseline"},"databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:baseline"],"databaseName":["tb_cdcew_syndrome_signal_base","baseline"],"props":"{}"},{"type":"input","field":"ldt70y5ed1","title":"预警阈值","placeholder":"请输入","databaseOption":{"label":"warning_threshold","value":"tb_cdcew_syndrome_signal_base:warning_threshold"},"databaseField":["tb_cdcew_syndrome_signal_base","tb_cdcew_syndrome_signal_base:warning_threshold"],"databaseName":["tb_cdcew_syndrome_signal_base","warning_threshold"],"props":"{}"}]}}]', 0, '2024-07-26 17:14:46.604', 'admin', '2025-02-20 19:09:01.468', 'admin', 5, NULL);
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154809182050058402', '154149113221349534', '154809182049009826', '信号处置记录', 0, 1, NULL, '[{"type":"group","field":"1u6ng639m4a","title":"信号推送记录","repeat":1,"props":{"max":50,"rules":[{"type":"datePicker","field":"1z0cb27w6ij","title":"推送时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"push_time","value":"tb_cdcew_warning_push_record:push_time"},"databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:push_time"],"databaseName":["tb_cdcew_warning_push_record","push_time"],"props":"{}"},{"type":"input","field":"26zgnxabdho","title":"被推送人","placeholder":"请输入","databaseOption":{"label":"receiver","value":"tb_cdcew_warning_push_record:receiver"},"databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:receiver"],"databaseName":["tb_cdcew_warning_push_record","receiver"],"props":"{}"},{"type":"input","field":"lvmenfeck","title":"推送人","placeholder":"请输入","databaseOption":{"label":"pusher","value":"tb_cdcew_warning_push_record:pusher"},"databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:pusher"],"databaseName":["tb_cdcew_warning_push_record","pusher"],"props":"{}"},{"type":"radio","field":"1ebycdoajfm","title":"推送结果","placeholder":"请选择","databaseOption":{"label":"push_result","value":"tb_cdcew_warning_push_record:push_result"},"optionField":"154154233896108190","databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:push_result"],"databaseName":["tb_cdcew_warning_push_record","push_result"],"props":"{}"}]}},{"type":"group","field":"zrc5rkptir","title":"信号核实记录","repeat":0,"props":{"max":1,"rules":[{"type":"datePicker","field":"la9l3726ng","title":"完成信号核实时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"finished_time","value":"tb_cdcew_warning_process_task_checked:finished_time"},"databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:finished_time"],"databaseName":["tb_cdcew_warning_process_task","finished_time"],"props":"{}"},{"type":"input","field":"1eq4s4jk13u","title":"核实人","placeholder":"请输入","databaseOption":{"label":"charge_person_name","value":"tb_cdcew_warning_process_task_checked:charge_person_name"},"databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:charge_person_name"],"databaseName":["tb_cdcew_warning_process_task","charge_person_name"],"props":"{}"},{"type":"radio","field":"1813cfsn1ry","title":"核实状态","placeholder":"请选择","databaseOption":{"label":"task_status","value":"tb_cdcew_warning_process_task_checked:task_status"},"optionField":"154154289730683038","databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:task_status"],"databaseName":["tb_cdcew_warning_process_task","task_status"],"props":"{}"},{"type":"radio","field":"1puomyjtnmq","title":"核实结果","placeholder":"请选择","databaseOption":{"label":"task_result","value":"tb_cdcew_warning_process_task_checked:task_result"},"optionField":"154151519476777118","databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:task_result"],"databaseName":["tb_cdcew_warning_process_task","task_result"],"props":"{}"}]}},{"type":"group","field":"gyarvngphx","title":"现场调查记录","repeat":0,"props":{"max":1,"rules":[{"type":"datePicker","field":"1pgi77om6oc","title":"完成现场调查时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"finished_time","value":"tb_cdcew_warning_process_task_invested:finished_time"},"databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:finished_time"],"databaseName":["tb_cdcew_warning_process_task","finished_time"],"props":"{}"},{"type":"input","field":"ay2lqxam4a","title":"现场调查人","placeholder":"请输入","databaseOption":{"label":"charge_person_name","value":"tb_cdcew_warning_process_task_invested:charge_person_name"},"databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:charge_person_name"],"databaseName":["tb_cdcew_warning_process_task","charge_person_name"],"props":"{}"},{"type":"radio","field":"vtjsawiz4j","title":"现场调查状态","placeholder":"请选择","databaseOption":{"label":"task_status","value":"tb_cdcew_warning_process_task_invested:task_status"},"optionField":"154155092889567390","databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:task_status"],"databaseName":["tb_cdcew_warning_process_task","task_status"],"props":"{}"},{"type":"radio","field":"2k9zxd2f948","title":"现场调查结果","placeholder":"请选择","databaseOption":{"label":"task_result","value":"tb_cdcew_warning_process_task_invested:task_result"},"optionField":"154151586048770206","databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:task_result"],"databaseName":["tb_cdcew_warning_process_task","task_result"],"props":"{}"}]}},{"type":"group","field":"1mi5x46arot","title":"发起研判记录","repeat":0,"props":{"max":1,"rules":[{"type":"datePicker","field":"2lr62etk8x4","title":"发起研判时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"create_time","value":"tb_cdcew_warning_process_task_judged:create_time"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:create_time"],"databaseName":["tb_cdcew_warning_process_task","create_time"],"props":"{}"},{"type":"input","field":"2amm7ktasbw","title":"发起研判人","placeholder":"请输入","databaseOption":{"label":"creator","value":"tb_cdcew_warning_process_task_judged:creator"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:creator"],"databaseName":["tb_cdcew_warning_process_task","creator"],"props":"{}"}]}},{"type":"group","field":"10225imy7z","title":"专家研判记录","repeat":1,"props":{"max":50,"rules":[{"type":"datePicker","field":"t5uc0jr77l","title":"完成研判时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"finished_time","value":"tb_cdcew_warning_process_task_judged:finished_time"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:finished_time"],"databaseName":["tb_cdcew_warning_process_task","finished_time"],"props":"{}"},{"type":"input","field":"2ipa6dljefk","title":"研判专家","placeholder":"请输入","databaseOption":{"label":"charge_person_name","value":"tb_cdcew_warning_process_task_judged:charge_person_name"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:charge_person_name"],"databaseName":["tb_cdcew_warning_process_task","charge_person_name"],"props":"{}"},{"type":"radio","field":"184mydiium0","title":"研判状态","placeholder":"请选择","databaseOption":{"label":"task_status","value":"tb_cdcew_warning_process_task_judged:task_status"},"optionField":"154154363818868894","databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:task_status"],"databaseName":["tb_cdcew_warning_process_task","task_status"],"props":"{}"},{"type":"radio","field":"1jga12yehiw","title":"研判结果","placeholder":"请选择","databaseOption":{"label":"task_result","value":"tb_cdcew_warning_process_task_judged:task_result"},"optionField":"154151640809603230","databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:task_result"],"databaseName":["tb_cdcew_warning_process_task","task_result"],"props":"{}"}]},"max":50}]', 0, '2024-07-26 17:14:46.604', 'admin', '2025-02-20 19:17:13.099', 'admin', 4, NULL);
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154809182049534114', '154149113221349534', '154809182049009826', '信号变化追溯', 0, 1, NULL, '[{"type":"group","field":"cpvklwfblv","title":"信号变化记录","repeat":1,"props":{"max":100,"rules":[{"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}","format":"YYYY-MM-DD HH:mm:ss","placeholder":"请选择","type":"datePicker","field":"1xj660jfytd","title":"信号变化时间","databaseOption":{"label":"moment_time","value":"tb_cdcew_warning_signal_timeline:moment_time","desc":"时间"},"databaseField":["tb_cdcew_warning_signal_timeline","tb_cdcew_warning_signal_timeline:moment_time"],"databaseName":["tb_cdcew_warning_signal_timeline","moment_time"]},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"d7lmahhkcq","title":"信号变化节点","databaseOption":{"label":"moment_type_name","value":"tb_cdcew_warning_signal_timeline:moment_type_name","desc":"信号事件类型名称"},"optionField":"154154414284734622","databaseField":["tb_cdcew_warning_signal_timeline","tb_cdcew_warning_signal_timeline:moment_type_name"],"databaseName":["tb_cdcew_warning_signal_timeline","moment_type_name"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"kzmra4hvg7","title":"现存病例数","databaseOption":{"label":"current_case_count","value":"tb_cdcew_warning_signal_timeline:current_case_count","desc":"现存病例数"},"databaseField":["tb_cdcew_warning_signal_timeline","tb_cdcew_warning_signal_timeline:current_case_count"],"databaseName":["tb_cdcew_warning_signal_timeline","current_case_count"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"},{"placeholder":"请输入","type":"number","field":"v16wt8w1cn","title":"节点变化病例数","databaseOption":{"label":"change_case_count","value":"tb_cdcew_warning_signal_timeline:change_case_count","desc":"信号的变化数量"},"databaseField":["tb_cdcew_warning_signal_timeline","tb_cdcew_warning_signal_timeline:change_case_count"],"databaseName":["tb_cdcew_warning_signal_timeline","change_case_count"],"props":"{\"isWordLimit\":false,\"wordLimit\":[],\"minLength\":0,\"maxLength\":0,\"supportDecimal\":false,\"supportNegative\":false,\"supportFuture\":false}"}]},"max":100}]', 0, '2024-07-26 17:14:46.604', 'admin', '2025-03-29 15:31:54.799', 'admin', 3, NULL);
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154547593845670050', '154150725981569182', '154547593844621474', '预警依据', 0, 1, NULL, '[{"type":"group","field":"2ntlv2vq604","title":"预警方法","repeat":0,"props":{"max":1,"rules":[{"type":"radio","field":"29u9c331rq1","title":"预警方法","placeholder":"请选择","databaseOption":{"label":"warning_method","value":"tb_cdcew_infected_signal_base:warning_method"},"optionField":"157028167238811796","databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:warning_method"],"databaseName":["tb_cdcew_infected_signal_base","warning_method"],"props":"{}"},{"type":"radio","field":"9v2wg1nsqd","title":"预警周期","placeholder":"请选择","databaseOption":{"label":"warning_period","value":"tb_cdcew_infected_signal_base:warning_period"},"optionField":"157129803344904342","databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:warning_period"],"databaseName":["tb_cdcew_infected_signal_base","warning_period"],"props":"{}"},{"type":"radio","field":"s9xul3ovr7","title":"统计指标","placeholder":"请选择","databaseOption":{"label":"stat_indicator","value":"tb_cdcew_infected_signal_base:stat_indicator"},"optionField":"154151755699978398","databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:stat_indicator"],"databaseName":["tb_cdcew_infected_signal_base","stat_indicator"],"props":"{}"},{"placeholder":"请选择","showOptions":true,"type":"checkbox","field":"7ego14h0jb","title":"病例范围","databaseOption":{"label":"process_scope","value":"tb_cdcew_infected_signal_base:process_scope"},"optionField":"157871341259718806","databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:process_scope"],"databaseName":["tb_cdcew_infected_signal_base","process_scope"],"props":"{}"},{"type":"textarea","field":"l36d7rqlxw","title":"人群范围","placeholder":"请输入","databaseOption":{"label":"person_scope","value":"tb_cdcew_infected_signal_base:person_scope"},"databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:person_scope"],"databaseName":["tb_cdcew_infected_signal_base","person_scope"],"props":"{}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"1odihwszw91","title":"统计区域","databaseOption":{"label":"stat_region","value":"tb_cdcew_infected_signal_base:stat_region"},"optionField":"154152019840467102","databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:stat_region"],"databaseName":["tb_cdcew_infected_signal_base","stat_region"],"props":"{}"},{"placeholder":"请输入","type":"number","field":"1jjtn0ofc0v","title":"统计区域数","databaseOption":{"label":"stat_region_count","value":"tb_cdcew_infected_signal_base:stat_region_count"},"databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:stat_region_count"],"databaseName":["tb_cdcew_infected_signal_base","stat_region_count"],"props":"{}"},{"type":"number","field":"2czou20wzxd","title":"时间窗口","placeholder":"请输入","databaseOption":{"label":"time_window","value":"tb_cdcew_infected_signal_base:time_window"},"databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:time_window"],"databaseName":["tb_cdcew_infected_signal_base","time_window"],"props":"{}"},{"placeholder":"请选择","showOptions":true,"type":"radio","field":"fyuoihrar9","title":"时间窗口单位","databaseOption":{"label":"time_window_unit","value":"tb_cdcew_infected_signal_base:time_window_unit"},"optionField":"154152073527558302","databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:time_window_unit"],"databaseName":["tb_cdcew_infected_signal_base","time_window_unit"],"props":"{}"},{"type":"input","field":"2mu5rppoufs","title":"基线数据","placeholder":"请输入","databaseOption":{"label":"baseline","value":"tb_cdcew_infected_signal_base:baseline"},"databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:baseline"],"databaseName":["tb_cdcew_infected_signal_base","baseline"],"props":"{}"},{"type":"input","field":"114yovmeog6","title":"预警阈值","placeholder":"请输入","databaseOption":{"label":"warning_threshold","value":"tb_cdcew_infected_signal_base:warning_threshold"},"databaseField":["tb_cdcew_infected_signal_base","tb_cdcew_infected_signal_base:warning_threshold"],"databaseName":["tb_cdcew_infected_signal_base","warning_threshold"],"props":"{}"}]}}]', 0, '2024-07-23 21:34:23.092', 'admin', '2025-02-20 19:20:57.994', 'admin', 4, NULL);
INSERT INTO app.tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model) VALUES('154547593844621474', '154150725981569182', '154547593844621474', '信号处置记录', 0, 1, NULL, '[{"type":"group","field":"1u6ng639m4a","title":"信号推送记录","repeat":1,"props":{"max":50,"rules":[{"type":"datePicker","field":"1z0cb27w6ij","title":"推送时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"push_time","value":"tb_cdcew_warning_push_record:push_time"},"databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:push_time"],"databaseName":["tb_cdcew_warning_push_record","push_time"],"props":"{}"},{"type":"input","field":"26zgnxabdho","title":"被推送人","placeholder":"请输入","databaseOption":{"label":"receiver","value":"tb_cdcew_warning_push_record:receiver"},"databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:receiver"],"databaseName":["tb_cdcew_warning_push_record","receiver"],"props":"{}"},{"type":"input","field":"lvmenfeck","title":"推送人","placeholder":"请输入","databaseOption":{"label":"pusher","value":"tb_cdcew_warning_push_record:pusher"},"databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:pusher"],"databaseName":["tb_cdcew_warning_push_record","pusher"],"props":"{}"},{"type":"radio","field":"1ebycdoajfm","title":"推送结果","placeholder":"请选择","databaseOption":{"label":"push_result","value":"tb_cdcew_warning_push_record:push_result"},"optionField":"154154233896108190","databaseField":["tb_cdcew_warning_push_record","tb_cdcew_warning_push_record:push_result"],"databaseName":["tb_cdcew_warning_push_record","push_result"],"props":"{}"}]},"max":50},{"type":"group","field":"zrc5rkptir","title":"信号核实记录","repeat":0,"props":{"max":1,"rules":[{"type":"datePicker","field":"la9l3726ng","title":"完成信号核实时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"finished_time","value":"tb_cdcew_warning_process_task_checked:finished_time"},"databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:finished_time"],"databaseName":["tb_cdcew_warning_process_task","finished_time"],"props":"{}"},{"type":"input","field":"1eq4s4jk13u","title":"核实人","placeholder":"请输入","databaseOption":{"label":"charge_person_name","value":"tb_cdcew_warning_process_task_checked:charge_person_name"},"databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:charge_person_name"],"databaseName":["tb_cdcew_warning_process_task","charge_person_name"],"props":"{\"maxLength\":32}"},{"type":"radio","field":"1813cfsn1ry","title":"核实状态","placeholder":"请选择","databaseOption":{"label":"task_status","value":"tb_cdcew_warning_process_task_checked:task_status"},"optionField":"154154289730683038","databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:task_status"],"databaseName":["tb_cdcew_warning_process_task","task_status"],"props":"{}"},{"type":"radio","field":"1puomyjtnmq","title":"核实结果","placeholder":"请选择","databaseOption":{"label":"task_result","value":"tb_cdcew_warning_process_task_checked:task_result"},"optionField":"154151519476777118","databaseField":["tb_cdcew_warning_process_task_checked","tb_cdcew_warning_process_task_checked:task_result"],"databaseName":["tb_cdcew_warning_process_task","task_result"],"props":"{}"}]}},{"type":"group","field":"gyarvngphx","title":"现场调查记录","repeat":0,"props":{"max":1,"rules":[{"type":"datePicker","field":"1pgi77om6oc","title":"完成现场调查时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"finished_time","value":"tb_cdcew_warning_process_task_invested:finished_time"},"databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:finished_time"],"databaseName":["tb_cdcew_warning_process_task","finished_time"],"props":"{}"},{"type":"input","field":"ay2lqxam4a","title":"现场调查人","placeholder":"请输入","databaseOption":{"label":"charge_person_name","value":"tb_cdcew_warning_process_task_invested:charge_person_name"},"databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:charge_person_name"],"databaseName":["tb_cdcew_warning_process_task","charge_person_name"],"props":"{}"},{"type":"radio","field":"vtjsawiz4j","title":"现场调查状态","placeholder":"请选择","databaseOption":{"label":"task_status","value":"tb_cdcew_warning_process_task_invested:task_status"},"optionField":"154155092889567390","databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:task_status"],"databaseName":["tb_cdcew_warning_process_task","task_status"],"props":"{}"},{"type":"radio","field":"2k9zxd2f948","title":"现场调查结果","placeholder":"请选择","databaseOption":{"label":"task_result","value":"tb_cdcew_warning_process_task_invested:task_result"},"optionField":"154151586048770206","databaseField":["tb_cdcew_warning_process_task_invested","tb_cdcew_warning_process_task_invested:task_result"],"databaseName":["tb_cdcew_warning_process_task","task_result"],"props":"{}"}]}},{"type":"group","field":"1mi5x46arot","title":"发起研判记录","repeat":0,"props":{"max":1,"rules":[{"type":"datePicker","field":"2lr62etk8x4","title":"发起研判时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"create_time","value":"tb_cdcew_warning_process_task_judged:create_time"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:create_time"],"databaseName":["tb_cdcew_warning_process_task","create_time"],"props":"{}"},{"type":"input","field":"2amm7ktasbw","title":"发起研判人","placeholder":"请输入","databaseOption":{"label":"creator","value":"tb_cdcew_warning_process_task_judged:creator"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:creator"],"databaseName":["tb_cdcew_warning_process_task","creator"],"props":"{}"}]}},{"type":"group","field":"10225imy7z","title":"专家研判记录","repeat":1,"props":{"max":50,"rules":[{"type":"datePicker","field":"t5uc0jr77l","title":"完成研判时间","placeholder":"请选择","format":"YYYY-MM-DD HH:mm:ss","databaseOption":{"label":"finished_time","value":"tb_cdcew_warning_process_task_judged:finished_time"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:finished_time"],"databaseName":["tb_cdcew_warning_process_task","finished_time"],"props":"{}"},{"type":"input","field":"2ipa6dljefk","title":"研判专家","placeholder":"请输入","databaseOption":{"label":"charge_person_name","value":"tb_cdcew_warning_process_task_judged:charge_person_name"},"databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:charge_person_name"],"databaseName":["tb_cdcew_warning_process_task","charge_person_name"],"props":"{}"},{"type":"radio","field":"184mydiium0","title":"研判状态","placeholder":"请选择","databaseOption":{"label":"task_status","value":"tb_cdcew_warning_process_task_judged:task_status"},"optionField":"154154363818868894","databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:task_status"],"databaseName":["tb_cdcew_warning_process_task","task_status"],"props":"{}"},{"type":"radio","field":"1jga12yehiw","title":"研判结果","placeholder":"请选择","databaseOption":{"label":"task_result","value":"tb_cdcew_warning_process_task_judged:task_result"},"optionField":"154151640809603230","databaseField":["tb_cdcew_warning_process_task_judged","tb_cdcew_warning_process_task_judged:task_result"],"databaseName":["tb_cdcew_warning_process_task","task_result"],"props":"{}"}]},"max":50}]', 0, '2024-07-23 21:34:23.092', 'admin', '2025-02-20 19:21:31.099', 'admin', 3, NULL);


delete from app.tb_cdcdm_data_form_template
where model_id in ('154150725981569182', '154149113221349534');
INSERT INTO app.tb_cdcdm_data_form_template (form_template_detail_id, form_template_code, form_template_name, config_info, config_json, model_id, model_name, model_version_id, is_enable, is_deleted, create_time, creator, update_time, updater, key_field, master_table_id, master_table, fields_config, model_group, web_config, related_disease, business_key, retrieve_table, es_index_name, es_index_template) VALUES('154150725981569182', NULL, NULL, 'INFECTED_SIGNAL', NULL, '154150725981569182', '传染病预警信号', '154547593844621474', 1, 0, '2024-07-19 14:54:11.414', 'admin', '2025-03-27 09:37:38.334', 'admin', 'signalId', 'tb_cdcew_disease_warning_signal', 'tb_cdcew_disease_warning_signal', '{"filterFields":[{"itemId":"398309c4-be67-4cef-8310-ff10b8aea20f","type":"condition","item":"tb_cdcew_disease_warning_signal:warning_type","operator":"=","value":"infected","parentItemId":"250bf54f-fc2c-4065-8147-0b314bf45ab6","unit":"","valueType":null},{"itemId":"250bf54f-fc2c-4065-8147-0b314bf45ab6","type":"logic","item":"或","operator":"","code":"","value":"","parentItemId":""}],"retrieveFilter":null}', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_data_form_template (form_template_detail_id, form_template_code, form_template_name, config_info, config_json, model_id, model_name, model_version_id, is_enable, is_deleted, create_time, creator, update_time, updater, key_field, master_table_id, master_table, fields_config, model_group, web_config, related_disease, business_key, retrieve_table, es_index_name, es_index_template) VALUES('154149113221349534', NULL, NULL, 'SYNDROME_SIGNAL', NULL, '154149113221349534', '症候群预警信号', '154809182049009826', 1, 0, '2024-07-19 14:29:09.783', 'admin', '2025-03-27 09:38:03.844', 'admin', 'signalId', 'tb_cdcew_disease_warning_signal', 'tb_cdcew_disease_warning_signal', '{"filterFields":[{"itemId":"1dace776-d2b5-45e2-9286-befeaf1ab06e","type":"condition","item":"tb_cdcew_disease_warning_signal:warning_type","operator":"=","value":"syndrome","parentItemId":"40a47333-ad3a-4578-9758-bb2e610c1bd6","unit":"","valueType":null},{"itemId":"40a47333-ad3a-4578-9758-bb2e610c1bd6","type":"logic","item":"或","operator":"","code":"","value":"","parentItemId":""}],"retrieveFilter":null}', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO app.tb_cdcdm_metadata_table_info (id, table_code, "schema", table_name, table_type, table_desc, alias, memo, create_time, update_time, creator, updater, delete_flag, filter_condition, creator_id, updater_id, ref_id, table_status) VALUES('177415174402080962', 'tb_cdcew_disease_warning_signal', 'app', 'tb_cdcew_disease_warning_signal', 'fact', '疾病信号表', 'tcdws', NULL, '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, '2');
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174402080962', '177415174402080962', 'tb_cdcew_disease_warning_signal:signal_id', 'signal_id', NULL, '主键', 'varchar', 'signalId', '主键', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174402343106', '177415174402080962', 'tb_cdcew_disease_warning_signal:signal_num', 'signal_num', NULL, '预警信号编号', 'varchar', 'signalNum', '预警信号编号', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174402605250', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_time', 'warning_time', NULL, '预警时间', 'timestamp', 'warningTime', '预警时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174402867394', '177415174402080962', 'tb_cdcew_disease_warning_signal:first_case_time', 'first_case_time', NULL, '首例病例时间', 'timestamp', 'firstCaseTime', '首例病例时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174403129538', '177415174402080962', 'tb_cdcew_disease_warning_signal:begin_date', 'begin_date', NULL, '开始日期', 'date', 'beginDate', '开始日期', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174403391682', '177415174402080962', 'tb_cdcew_disease_warning_signal:end_date', 'end_date', NULL, '结束日期', 'date', 'endDate', '结束日期', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174403653826', '177415174402080962', 'tb_cdcew_disease_warning_signal:province_code', 'province_code', NULL, '省编码', 'varchar', 'provinceCode', '省编码', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174403915970', '177415174402080962', 'tb_cdcew_disease_warning_signal:province_name', 'province_name', NULL, '省名称', 'varchar', 'provinceName', '省名称', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174404178114', '177415174402080962', 'tb_cdcew_disease_warning_signal:city_code', 'city_code', NULL, '市编码', 'varchar', 'cityCode', '市编码', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174404440258', '177415174402080962', 'tb_cdcew_disease_warning_signal:city_name', 'city_name', NULL, '市名称', 'varchar', 'cityName', '市名称', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174404702402', '177415174402080962', 'tb_cdcew_disease_warning_signal:district_code', 'district_code', NULL, '区县编码', 'varchar', 'districtCode', '区县编码', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174404964546', '177415174402080962', 'tb_cdcew_disease_warning_signal:district_name', 'district_name', NULL, '区县名称', 'varchar', 'districtName', '区县名称', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174405226690', '177415174402080962', 'tb_cdcew_disease_warning_signal:street_code', 'street_code', NULL, '街道编码', 'varchar', 'streetCode', '街道编码', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174405488834', '177415174402080962', 'tb_cdcew_disease_warning_signal:street_name', 'street_name', NULL, '街道名称', 'varchar', 'streetName', '街道名称', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174405750978', '177415174402080962', 'tb_cdcew_disease_warning_signal:stat_dim_id', 'stat_dim_id', NULL, '预警地区编码：地点/区划/单位/学校等', 'varchar', 'statDimId', '预警地区编码：地点/区划/单位/学校等', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174406013122', '177415174402080962', 'tb_cdcew_disease_warning_signal:stat_dim_name', 'stat_dim_name', NULL, '预警地区名称：地点/区划/单位/学校等', 'varchar', 'statDimName', '预警地区名称：地点/区划/单位/学校等', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174406275266', '177415174402080962', 'tb_cdcew_disease_warning_signal:topic_id', 'topic_id', NULL, '专题id', 'varchar', 'topicId', '专题id', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174406537410', '177415174402080962', 'tb_cdcew_disease_warning_signal:topic_name', 'topic_name', NULL, '专题name', 'varchar', 'topicName', '专题name', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174406799554', '177415174402080962', 'tb_cdcew_disease_warning_signal:disease_code', 'disease_code', NULL, '疾病编码', 'varchar', 'diseaseCode', '疾病编码', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174407061698', '177415174402080962', 'tb_cdcew_disease_warning_signal:disease_name', 'disease_name', NULL, '疾病名称', 'varchar', 'diseaseName', '疾病名称', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174407323842', '177415174402080962', 'tb_cdcew_disease_warning_signal:death_cnt', 'death_cnt', NULL, '死亡数量', 'int4', 'deathCnt', '死亡数量', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174407585986', '177415174402080962', 'tb_cdcew_disease_warning_signal:medical_case_cnt', 'medical_case_cnt', NULL, '病例数', 'int4', 'medicalCaseCnt', '病例数', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174407848130', '177415174402080962', 'tb_cdcew_disease_warning_signal:processing_status', 'processing_status', NULL, '信号当前状态', 'varchar', 'processingStatus', '信号当前状态', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174408110274', '177415174402080962', 'tb_cdcew_disease_warning_signal:processing_latest_time', 'processing_latest_time', NULL, '当前流程最晚处理时间', 'timestamp', 'processingLatestTime', '当前流程最晚处理时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174408372418', '177415174402080962', 'tb_cdcew_disease_warning_signal:risk_level_id', 'risk_level_id', NULL, '风险等级id', 'varchar', 'riskLevelId', '风险等级id', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174408634562', '177415174402080962', 'tb_cdcew_disease_warning_signal:risk_level_detail_id', 'risk_level_detail_id', NULL, '风险等级ID', 'varchar', 'riskLevelDetailId', '风险等级ID', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174408896706', '177415174402080962', 'tb_cdcew_disease_warning_signal:event_level', 'event_level', NULL, '事件等级', 'varchar', 'eventLevel', '事件等级', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174409158850', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_scenario', 'warning_scenario', NULL, '预警场景', 'varchar', 'warningScenario', '预警场景', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174409420994', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_scenario_type', 'warning_scenario_type', NULL, '预警场景类型', 'varchar', 'warningScenarioType', '预警场景类型', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174409683138', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_reason', 'warning_reason', NULL, '预警原因', 'varchar', 'warningReason', '预警原因', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174409945282', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_rule_id', 'warning_rule_id', NULL, '预警规则ID', 'varchar', 'warningRuleId', '预警规则ID', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174410207426', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_rule_desc', 'warning_rule_desc', NULL, '预警规则描述', 'varchar', 'warningRuleDesc', '预警规则描述', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174410469570', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_method_name', 'warning_method_name', NULL, '预警方法名称', 'varchar', 'warningMethodName', '预警方法名称', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174410731714', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_threshold', 'warning_threshold', NULL, '预警阈值', 'numeric', 'warningThreshold', '预警阈值', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174410993858', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_charge_person_id', 'warning_charge_person_id', NULL, '责任人ID', 'varchar', 'warningChargePersonId', '责任人ID', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174411256002', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_charge_person_name', 'warning_charge_person_name', NULL, '责任人姓名', 'varchar', 'warningChargePersonName', '责任人姓名', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174411518146', '177415174402080962', 'tb_cdcew_disease_warning_signal:warning_type', 'warning_type', NULL, '预警类型 infected、syndrome、multichannel', 'varchar', 'warningType', '预警类型 infected、syndrome、multichannel', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174411780290', '177415174402080962', 'tb_cdcew_disease_warning_signal:signal_class', 'signal_class', NULL, '信号类型 infectious_warning、syndrome_warning、pathogen_warning、epidemic_clues', 'varchar', 'signalClass', '信号类型 infectious_warning、syndrome_warning、pathogen_warning、epidemic_clues', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174412042434', '177415174402080962', 'tb_cdcew_disease_warning_signal:check_status', 'check_status', NULL, '核实状态', 'varchar', 'checkStatus', '核实状态', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174412304578', '177415174402080962', 'tb_cdcew_disease_warning_signal:check_latest_time', 'check_latest_time', NULL, '核实最晚完成时间', 'timestamp', 'checkLatestTime', '核实最晚完成时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174412566722', '177415174402080962', 'tb_cdcew_disease_warning_signal:check_finish_time', 'check_finish_time', NULL, '核实实际完成时间', 'timestamp', 'checkFinishTime', '核实实际完成时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174412828866', '177415174402080962', 'tb_cdcew_disease_warning_signal:check_result', 'check_result', NULL, '核实结果', 'varchar', 'checkResult', '核实结果', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174413091010', '177415174402080962', 'tb_cdcew_disease_warning_signal:invest_status', 'invest_status', NULL, '调查状态', 'varchar', 'investStatus', '调查状态', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174413353154', '177415174402080962', 'tb_cdcew_disease_warning_signal:invest_latest_time', 'invest_latest_time', NULL, '调查最晚完成时间', 'timestamp', 'investLatestTime', '调查最晚完成时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174413615298', '177415174402080962', 'tb_cdcew_disease_warning_signal:invest_finish_time', 'invest_finish_time', NULL, '调查实际完成时间', 'timestamp', 'investFinishTime', '调查实际完成时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174413877442', '177415174402080962', 'tb_cdcew_disease_warning_signal:invest_result', 'invest_result', NULL, '调查结果', 'varchar', 'investResult', '调查结果', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174414139586', '177415174402080962', 'tb_cdcew_disease_warning_signal:judge_status', 'judge_status', NULL, '研判状态', 'varchar', 'judgeStatus', '研判状态', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174414401730', '177415174402080962', 'tb_cdcew_disease_warning_signal:judge_latest_time', 'judge_latest_time', NULL, '研判最晚完成时间', 'timestamp', 'judgeLatestTime', '研判最晚完成时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174414663874', '177415174402080962', 'tb_cdcew_disease_warning_signal:judge_finish_time', 'judge_finish_time', NULL, '研判实际完成时间', 'timestamp', 'judgeFinishTime', '研判实际完成时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174414926018', '177415174402080962', 'tb_cdcew_disease_warning_signal:judge_result', 'judge_result', NULL, '研判结果', 'varchar', 'judgeResult', '研判结果', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174415188162', '177415174402080962', 'tb_cdcew_disease_warning_signal:create_time', 'create_time', NULL, '创建时间', 'timestamp', 'createTime', '创建时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174415450306', '177415174402080962', 'tb_cdcew_disease_warning_signal:update_time', 'update_time', NULL, '更新时间', 'timestamp', 'updateTime', '更新时间', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174415712450', '177415174402080962', 'tb_cdcew_disease_warning_signal:signal_change_log', 'signal_change_log', NULL, '信号变化日志', 'varchar', 'signalChangeLog', '信号变化日志', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174415974594', '177415174402080962', 'tb_cdcew_disease_warning_signal:longitude', 'longitude', NULL, '信号发生地经度', 'numeric', 'longitude', '信号发生地经度', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177415174416236738', '177415174402080962', 'tb_cdcew_disease_warning_signal:latitude', 'latitude', NULL, '信号发生地纬度', 'numeric', 'latitude', '信号发生地纬度', '2025-03-27 09:25:57.361', '2025-03-27 09:25:57.361', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_disease_warning_signal', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO app.tb_cdcdm_metadata_table_info (id, table_code, "schema", table_name, table_type, table_desc, alias, memo, create_time, update_time, creator, updater, delete_flag, filter_condition, creator_id, updater_id, ref_id, table_status) VALUES('174939356610429099', 'tb_cdcew_warning_signal_timeline', 'app', 'tb_cdcew_warning_signal_timeline', 'fact', '信号时间线', 'tcwst', NULL, '2025-02-28 16:56:12.305', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, '2');
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962426278067', '174939356610429099', 'tb_cdcew_warning_signal_timeline:signal_id', 'signal_id', NULL, '信号ID', 'varchar', 'signalId', '信号ID', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962426540211', '174939356610429099', 'tb_cdcew_warning_signal_timeline:moment_type_code', 'moment_type_code', NULL, '信号事件类型代码', 'varchar', 'momentTypeCode', '信号事件类型代码', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962426802355', '174939356610429099', 'tb_cdcew_warning_signal_timeline:moment_type_name', 'moment_type_name', NULL, '信号事件类型名称', 'varchar', 'momentTypeName', '信号事件类型名称', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962427064499', '174939356610429099', 'tb_cdcew_warning_signal_timeline:moment_time', 'moment_time', NULL, '时间', 'timestamp', 'momentTime', '时间', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962427326643', '174939356610429099', 'tb_cdcew_warning_signal_timeline:current_case_count', 'current_case_count', NULL, '现存病例数', 'int4', 'currentCaseCount', '现存病例数', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962427588787', '174939356610429099', 'tb_cdcew_warning_signal_timeline:change_case_count', 'change_case_count', NULL, '信号的变化数量', 'int4', 'changeCaseCount', '信号的变化数量', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962427850931', '174939356610429099', 'tb_cdcew_warning_signal_timeline:current_source_key_list', 'current_source_key_list', NULL, '当前病例主键列表', 'text', 'currentSourceKeyList', '当前病例主键列表', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962428113075', '174939356610429099', 'tb_cdcew_warning_signal_timeline:create_time', 'create_time', NULL, '创建时间', 'timestamp', 'createTime', '创建时间', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962428375219', '174939356610429099', 'tb_cdcew_warning_signal_timeline:source_signal_id', 'source_signal_id', NULL, '源信号id', 'varchar', 'sourceSignalId', '源信号id', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962428637363', '174939356610429099', 'tb_cdcew_warning_signal_timeline:warning_type', 'warning_type', NULL, '预警类型', 'varchar', 'warningType', '预警类型', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962428899507', '174939356610429099', 'tb_cdcew_warning_signal_timeline:creator', 'creator', NULL, '创建人', 'varchar', 'creator', '创建人', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962429161651', '174939356610429099', 'tb_cdcew_warning_signal_timeline:notes', 'notes', NULL, '备注', 'text', 'notes', '备注', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962429423795', '174939356610429099', 'tb_cdcew_warning_signal_timeline:target_signal_id', 'target_signal_id', NULL, '目标信号id', 'varchar', 'targetSignalId', '目标信号id', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO app.tb_cdcdm_metadata_table_column_info (id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort) VALUES('177623962426015923', '174939356610429099', 'tb_cdcew_warning_signal_timeline:id', 'id', NULL, '主键', 'varchar', 'id', '主键', '2025-03-29 15:26:46.159', '2025-03-29 15:26:46.159', NULL, NULL, '0', NULL, NULL, NULL, NULL, NULL, '1', 'tb_cdcew_warning_signal_timeline', NULL, NULL, NULL, NULL, NULL, NULL);


ALTER TABLE app.tb_cdcew_syndrome_signal_base ADD if not exists process_scope varchar NULL;
COMMENT ON COLUMN app.tb_cdcew_syndrome_signal_base.process_scope IS '病例范围：剔除非危重症病例、剔除排除病例、剔除明确诊断病例';
ALTER TABLE app.tb_cdcew_syndrome_signal_base ADD if not exists person_scope varchar NULL;
COMMENT ON COLUMN app.tb_cdcew_syndrome_signal_base.person_scope IS '人群分类';

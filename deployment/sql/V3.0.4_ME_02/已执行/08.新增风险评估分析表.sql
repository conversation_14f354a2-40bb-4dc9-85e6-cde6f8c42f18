drop table IF EXISTS app.tb_cdcew_risk_assessment_analysis;
create TABLE app.tb_cdcew_risk_assessment_analysis(
    id varchar (255) primary key NOT NULL,
    risk_assessment_category varchar (255) NULL,
	disease varchar (255) NULL,
	time_type varchar (255) NULL,
	start_time timestamp,
	end_time timestamp,
	address_type varchar (255) NULL,
	report_id varchar (255) NULL,
	province_code varchar (255) NULL,
	province_name varchar (255) NULL,
	city_code varchar (255) NULL,
	city_name varchar (255) NULL,
	district_code varchar (255) NULL,
    district_name varchar (255) NULL,
	creator_id varchar (255) NULL,
	creator varchar (255) NULL,
	create_time timestamp NULL,
	updater_id varchar (255) NULL,
	updater varchar (255) NULL,
	update_time timestamp,
	delete_flag varchar (255) NULL
);

comment on column app.tb_cdcew_risk_assessment_analysis.id is '主键id';
comment on column app.tb_cdcew_risk_assessment_analysis.risk_assessment_category is '风险评估类别';
comment on column app.tb_cdcew_risk_assessment_analysis.disease is '疾病';
comment on column app.tb_cdcew_risk_assessment_analysis.time_type is '时间类型';
comment on column app.tb_cdcew_risk_assessment_analysis.start_time is '统计开始时间';
comment on column app.tb_cdcew_risk_assessment_analysis.end_time is '统计结束时间';
comment on column app.tb_cdcew_risk_assessment_analysis.address_type is '地区类型';
comment on column app.tb_cdcew_risk_assessment_analysis.report_id is '统计报告id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.province_code IS '省编码';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.province_name IS '省名称';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.city_code IS '市编码';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.city_name IS '市名称';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.district_code IS '区编码';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.district_name IS '区名称';
comment on column app.tb_cdcew_risk_assessment_analysis.creator_id is '创建者id';
comment on column app.tb_cdcew_risk_assessment_analysis.creator is '创建者';
comment on column app.tb_cdcew_risk_assessment_analysis.create_time is '创建时间';
comment on column app.tb_cdcew_risk_assessment_analysis.updater_id is '修改者id';
comment on column app.tb_cdcew_risk_assessment_analysis.updater is '修改者';
comment on column app.tb_cdcew_risk_assessment_analysis.update_time is '修改时间';
comment on column app.tb_cdcew_risk_assessment_analysis.delete_flag is '删除标记';


CREATE TABLE app.tb_cdcew_report_qcs_main_record (
	id varchar(64) NOT NULL,
	creator_id varchar(100) NULL,
	creator varchar(128) NULL,
	create_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	updater_id varchar(100) NULL,
	updater varchar(128) NULL,
	update_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,
	memo varchar(200) NULL,
	title varchar(200) NULL,
	import_amount int4 NULL DEFAULT 0,
	compare_date_type varchar(20) NULL,
	compare_start_date date NULL,
	compare_start_end date NULL,
	compare_addr_type varchar(20) NULL,
	compare_province_code varchar(128) NULL,
	compare_province_name varchar(128) NULL,
	compare_city_code varchar(128) NULL,
	compare_city_name varchar(128) NULL,
	compare_district_code varchar(128) NULL,
	compare_district_name varchar(128) NULL,
	finish_time timestamp NULL,
	leak_amount int4 NULL DEFAULT 0,
	delay_amount int4 NULL DEFAULT 0,
	attachment_name varchar(300) NULL,
	attachment_id varchar(200) NULL,
	CONSTRAINT pk_cdcew_report_qcs_main_record PRIMARY KEY (id)
);

COMMENT ON TABLE app.tb_cdcew_report_qcs_main_record IS '报卡质控督导-主记录（比对任务）表';

COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.updater IS '更新者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.delete_flag IS '删除标志，''1'' 表示已删除，''0'' 表示未删除';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.memo IS '备注';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.title IS '数据名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.import_amount IS '导入数据量';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_date_type IS '比对日期类型 onsetTime 发病日期 diagTime 诊断日期 visitTime 首次就诊时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_start_date IS '比对数据时间范围-开始';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_start_end IS '比对数据时间范围-结束';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_addr_type IS '比对地址类型 livingAddress 现住地址 orgAddress 就诊机构地址';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_province_code IS '比对地区-省编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_province_name IS '比对地区-省名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_city_code IS '比对地区-市编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_city_name IS '比对地区-市名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_district_code IS '比对地区-区县编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.compare_district_name IS '比对地区-区县名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.finish_time IS '完成时间（比对后回填）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.leak_amount IS '漏报数（比对后回填）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.delay_amount IS '迟报数（比对后回填）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.attachment_name IS '漏报数据导出文件名';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_main_record.attachment_id IS '漏报数据导出文件附件ID';


CREATE TABLE app.tb_cdcew_report_qcs_upload_info (
	id varchar(64) NOT NULL,
	creator_id varchar(100) NULL,
	creator varchar(128) NULL,
	create_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	updater_id varchar(100) NULL,
	updater varchar(128) NULL,
	update_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,
	memo varchar(200) NULL,
	main_record_id varchar(64) NOT NULL,
	report_card_id varchar(100) NOT NULL,
	report_card_code varchar(100) NULL,
	report_card_status varchar(64) NULL,
	report_type_name varchar(64) NULL,
	patient_name varchar(32) NULL,
	parent_name varchar(100) NULL,
	identity_type_name varchar(64) NULL,
	identity_no varchar(100) NULL,
	sex_name varchar(16) NULL,
	birth_date timestamp NULL,
	age int2 NULL,
	age_unit varchar(4) NULL,
	company varchar(100) NULL,
	phone varchar(100) NULL,
	belong_name varchar(100) NULL,
	living_addr_area_code varchar(16) NULL,
	living_addr_detail varchar(300) NULL,
	person_type_name varchar(16) NULL,
	case_name1 varchar(100) NULL,
	case_name2 varchar(100) NULL,
	onset_time timestamp NULL,
	diag_time timestamp NULL,
	death_date date NULL,
	disease_name varchar(100) NULL,
	unrevised_disease_name varchar(100) NULL,
	unrevised_identify_time timestamp NULL,
	unrevised_final_check_time timestamp NULL,
	report_doctor_name varchar(100) NULL,
	report_fill_date date NULL,
	report_org_addr_area_code varchar(16) NULL,
	report_org_name varchar(300) NULL,
	report_org_type_code varchar(100) NULL,
	upload_record_time timestamp NULL,
	upload_doctor_name varchar(100) NULL,
	upload_org_name varchar(100) NULL,
	district_check_time timestamp NULL,
	city_check_time timestamp NULL,
	province_check_time timestamp NULL,
	check_status varchar(32) NULL,
	revised_identify_time timestamp NULL,
	revised_final_check_time timestamp NULL,
	revised_death_time timestamp NULL,
	revise_doctor_name varchar(100) NULL,
	revise_org_name varchar(100) NULL,
	delete_op_time timestamp NULL,
	delete_doctor_name varchar(100) NULL,
	delete_org_name varchar(100) NULL,
	delete_reason varchar(300) NULL,
	report_card_note varchar(300) NULL,
	empi_id varchar(100) NULL,
	living_addr_province_code varchar(16) NULL,
	living_addr_province varchar(32) NULL,
	living_addr_city_code varchar(16) NULL,
	living_addr_city varchar(32) NULL,
	living_addr_district_code varchar(16) NULL,
	living_addr_district varchar(32) NULL,
	living_addr_func_district_code varchar(16) NULL,
	living_addr_func_district varchar(32) NULL,
	living_addr_street_code varchar(16) NULL,
	living_addr_street varchar(32) NULL,
	report_org_addr_province_code varchar(16) NULL,
	report_org_addr_province varchar(32) NULL,
	report_org_addr_city_code varchar(16) NULL,
	report_org_addr_city varchar(32) NULL,
	report_org_addr_district_code varchar(16) NULL,
	report_org_addr_district varchar(32) NULL,
	report_org_addr_func_district_code varchar(16) NULL,
	report_org_addr_func_district varchar(32) NULL,
	report_org_addr_street_code varchar(16) NULL,
	report_org_addr_street varchar(32) NULL,
	age_group varchar(32) NULL,
	death_flag int2 NULL,
	delay_flag int2 NULL,
	disease_code varchar(100) NULL,
	infect_type varchar(100) NULL,
	CONSTRAINT pk_cdcew_report_qcs_upload_info PRIMARY KEY (id)
);
CREATE INDEX idx_cdcew_report_qcs_upload_info_main_id ON app.tb_cdcew_report_qcs_upload_info USING btree (main_record_id);

COMMENT ON TABLE app.tb_cdcew_report_qcs_upload_info IS '报卡质控督导-导入（报卡）记录表';

COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.updater IS '更新者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.delete_flag IS '删除标志，''1'' 表示已删除，''0'' 表示未删除';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.memo IS '备注';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.main_record_id IS '主记录ID（外键）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_card_id IS '报告卡ID';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_card_code IS '卡片编号';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_card_status IS '卡片状态';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_type_name IS '报告类别';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.patient_name IS '患者姓名';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.parent_name IS '患儿家长姓名';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.identity_type_name IS '有效证件类型';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.identity_no IS '有效证件号码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.sex_name IS '患者性别';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.birth_date IS '出生日期';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.age IS '年龄';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.age_unit IS '年龄单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.company IS '患者工作单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.phone IS '联系电话';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.belong_name IS '病人属于';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_area_code IS '现住地址国标';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_detail IS '现住详细地址';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.person_type_name IS '人群分类';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.case_name1 IS '病例分类';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.case_name2 IS '病例分类2';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.onset_time IS '发病日期';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.diag_time IS '诊断时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.death_date IS '死亡日期';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.unrevised_disease_name IS '订正前病种';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.unrevised_identify_time IS '订正前诊断时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.unrevised_final_check_time IS '订正前终审时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_doctor_name IS '填卡医生';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_fill_date IS '医生填卡日期';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_area_code IS '报告单位地区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_name IS '报告单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_type_code IS '单位类型';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.upload_record_time IS '报告卡录入时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.upload_doctor_name IS '录卡用户';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.upload_org_name IS '录卡用户所属单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.district_check_time IS '县区审核时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.city_check_time IS '地市审核时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.province_check_time IS '省市审核时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.check_status IS '审核状态';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.revised_identify_time IS '订正报告时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.revised_final_check_time IS '订正终审时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.revised_death_time IS '终审死亡时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.revise_doctor_name IS '订正用户';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.revise_org_name IS '订正用户所属单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.delete_op_time IS '删除时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.delete_doctor_name IS '删除用户';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.delete_org_name IS '删除用户所属单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.delete_reason IS '删除原因';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_card_note IS '报告卡备注';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.empi_id IS '加工字段：主索引标识';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_province_code IS '加工字段：现住地址-省编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_province IS '加工字段：现住地址-省名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_city_code IS '加工字段：现住地址-市编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_city IS '加工字段：现住地址-市名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_district_code IS '加工字段：现住地址-区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_district IS '加工字段：现住地址-区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_func_district_code IS '加工字段：现住地址-功能区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_func_district IS '加工字段：现住地址-功能区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_street_code IS '加工字段：现住地址-街道编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.living_addr_street IS '加工字段：现住地址-街道名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_province_code IS '加工字段：报告单位地址-省编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_province IS '加工字段：报告单位地址-省名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_city_code IS '加工字段：报告单位地址-市编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_city IS '加工字段：报告单位地址-市名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_district_code IS '加工字段：报告单位地址-区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_district IS '加工字段：报告单位地址-区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_func_district_code IS '加工字段：报告单位地址-功能区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_func_district IS '加工字段：报告单位地址-功能区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_street_code IS '加工字段：报告单位地址-街道编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.report_org_addr_street IS '加工字段：报告单位地址-街道名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.age_group IS '加工字段：年龄分组';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.death_flag IS '加工字段：死亡标识，1 表示是，0 表示否';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.delay_flag IS '加工字段：延迟标识，1 表示是，0 表示否';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.disease_code IS '加工字段：疾病编码（根据名称获取）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_upload_info.infect_type IS '加工字段：传染病分类';


CREATE TABLE app.tb_cdcew_report_qcs_card_info (
	id varchar(64) NOT NULL,
	creator_id varchar(100) NULL,
	creator varchar(128) NULL,
	create_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	updater_id varchar(100) NULL,
	updater varchar(128) NULL,
	update_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,
	memo varchar(200) NULL,
	main_record_id varchar(64) NOT NULL,
	process_id varchar(36) NULL,
	report_id varchar(36) NULL,
	patient_name varchar(32) NULL,
	sex_name varchar(16) NULL,
	age int2 NULL,
	age_unit varchar(4) NULL,
	living_addr_province_code varchar(16) NULL,
	living_addr_province varchar(32) NULL,
	living_addr_city_code varchar(16) NULL,
	living_addr_city varchar(32) NULL,
	living_addr_district_code varchar(16) NULL,
	living_addr_district varchar(32) NULL,
	living_addr_func_district_code varchar(16) NULL,
	living_addr_func_district varchar(32) NULL,
	living_addr_street_code varchar(16) NULL,
	living_addr_street varchar(32) NULL,
	onset_time timestamp NULL,
	first_visit_time timestamp NULL,
	org_id varchar(300) NULL,
	org_name varchar(300) NULL,
	org_addr_province_code varchar(16) NULL,
	org_addr_province varchar(32) NULL,
	org_addr_city_code varchar(16) NULL,
	org_addr_city varchar(32) NULL,
	org_addr_district_code varchar(16) NULL,
	org_addr_district varchar(32) NULL,
	org_addr_func_district_code varchar(16) NULL,
	org_addr_func_district varchar(32) NULL,
	org_addr_street_code varchar(16) NULL,
	org_addr_street varchar(32) NULL,
	disease_name varchar(100) NULL,
	disease_code varchar(100) NULL,
	diag_time timestamp NULL,
	patient_identity_type varchar(32) NULL,
	patient_identity_no varchar(20) NULL,
	patient_birth_day date NULL,
	dead_time timestamp NULL,
	company varchar(100) NULL,
	patient_phone varchar(100) NULL,
	living_addr_detail varchar(300) NULL,
	person_type varchar(16) NULL,
	identify_class varchar(32) NULL,
	visit_type_name varchar(32) NULL,
	first_diag_flag varchar(16) NULL,
	CONSTRAINT tb_cdcew_report_qcs_card_info_pkey PRIMARY KEY (id)
);
CREATE INDEX tb_cdcew_report_qcs_card_info_main_record_id_idx ON app.tb_cdcew_report_qcs_card_info USING btree (main_record_id);

COMMENT ON TABLE app.tb_cdcew_report_qcs_card_info IS '报卡质控督导-漏报（就诊）数据表';

COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.updater IS '更新者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.delete_flag IS '删除标志 ''1'' 已删除 ''0'' 未删除';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.memo IS '备注';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.main_record_id IS '主记录ID（外键）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.process_id IS '数仓的病程ID';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.report_id IS '数仓的报告卡ID';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.patient_name IS '患者姓名';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.sex_name IS '患者性别';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.age IS '年龄';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.age_unit IS '年龄单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_province_code IS '所属地区省级编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_province IS '所属地区省级名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_city_code IS '所属地区市级编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_city IS '所属地区市级名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_district_code IS '所属地区区级编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_district IS '所属地区区级名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_func_district_code IS '所属功能区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_func_district IS '所属功能区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_street_code IS '所属街道编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_street IS '所属街道名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.onset_time IS '发病日期';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.first_visit_time IS '首次就诊时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_id IS '首次就诊机构ID';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_name IS '首次就诊机构名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_province_code IS '就诊机构所在省级编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_province IS '就诊机构所在省级名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_city_code IS '就诊机构所在市级编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_city IS '就诊机构所在市级名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_district_code IS '就诊机构所在区级编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_district IS '就诊机构所在区级名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_func_district_code IS '就诊机构所在功能区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_func_district IS '就诊机构所在功能区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_street_code IS '就诊机构所在街道编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.org_addr_street IS '就诊机构所在街道名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.disease_code IS '疾病编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.diag_time IS '诊断时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.patient_identity_type IS '证件类型';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.patient_identity_no IS '证件号码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.patient_birth_day IS '出生日期';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.dead_time IS '死亡时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.company IS '工作单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.patient_phone IS '联系电话';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.living_addr_detail IS '现住地址';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.person_type IS '人群分类';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.identify_class IS '诊断状态';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.visit_type_name IS '就诊类型';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_card_info.first_diag_flag IS '初诊标识';


CREATE TABLE app.tb_cdcew_report_qcs_stats (
	id varchar(64) NOT NULL,
	creator_id varchar(100) NULL,
	creator varchar(128) NULL,
	create_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	updater_id varchar(100) NULL,
	updater varchar(128) NULL,
	update_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP,
	delete_flag varchar(1) NOT NULL DEFAULT '0'::character varying,
	memo varchar(200) NULL,
	main_record_id varchar(64) NOT NULL,
	report_org_name varchar(300) NULL,
	report_org_addr_province_code varchar(16) NULL,
	report_org_addr_province varchar(32) NULL,
	report_org_addr_city_code varchar(16) NULL,
	report_org_addr_city varchar(32) NULL,
	report_org_addr_district_code varchar(16) NULL,
	report_org_addr_district varchar(32) NULL,
	report_org_addr_func_district_code varchar(16) NULL,
	report_org_addr_func_district varchar(32) NULL,
	report_org_addr_street_code varchar(16) NULL,
	report_org_addr_street varchar(32) NULL,
	identify_cnt int4 NULL,
	report_cnt int4 NULL,
	leak_cnt int4 NULL,
	delay_cnt int4 NULL,
	CONSTRAINT pk_cdcew_report_qcs_stats PRIMARY KEY (id)
);
CREATE INDEX idx_cdcew_report_qcs_stats_main_id ON app.tb_cdcew_report_qcs_stats USING btree (main_record_id);

COMMENT ON TABLE app.tb_cdcew_report_qcs_stats IS '报卡质控督导-比对结果（统计）表';

COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.updater IS '更新者';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.delete_flag IS '删除标志，''1'' 已删除，''0'' 未删除';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.memo IS '备注';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.main_record_id IS '主记录ID（外键）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_name IS '报告单位';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_province_code IS '报告单位地址-省编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_province IS '报告单位地址-省名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_city_code IS '报告单位地址-市编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_city IS '报告单位地址-市名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_district_code IS '报告单位地址-区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_district IS '报告单位地址-区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_func_district_code IS '报告单位地址-功能区编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_func_district IS '报告单位地址-功能区名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_street_code IS '报告单位地址-街道编码';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_org_addr_street IS '报告单位地址-街道名称';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.identify_cnt IS '应报告数（就诊识别数）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.report_cnt IS '报告发病数（报卡报告数）';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.leak_cnt IS '漏报数';
COMMENT ON COLUMN app.tb_cdcew_report_qcs_stats.delay_cnt IS '迟报数';

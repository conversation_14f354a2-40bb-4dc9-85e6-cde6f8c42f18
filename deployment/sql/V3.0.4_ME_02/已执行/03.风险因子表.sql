-- app.tb_cdcew_predictive_factor definition

-- Drop table

DROP TABLE if exists tb_cdcew_predictive_factor;

CREATE TABLE app.tb_cdcew_predictive_factor (
	id varchar(100) NOT NULL, -- 主键id
	"name" varchar(100) NULL, -- 名称
	"type" varchar(100) NULL, -- 类型
	options_json text NULL, -- 选择的值（JSON格式存储）
	unit_name varchar(50) NULL, -- 单位
	research_id varchar(100) NULL, -- 研究id
	bind_field_name varchar(32) NULL, -- 字段映射
    bind_field_desc varchar(32) NULL, -- 字段映射
    value_mapping_json text NULL, -- 值映射（JSON格式存储）
	creator_id varchar(100) NULL, -- 创建者id
	creator varchar(100) NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(100) NULL, -- 修改者id
	updater varchar(100) NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(2) NULL, -- 删除标记：0-未删除，1-已删除
	CONSTRAINT tb_cdcew_predictive_factor_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_predictive_factor IS '预测因子配置表';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_predictive_factor.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor."type" IS '类型';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.options_json IS '选择的值（JSON格式存储）';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.unit_name IS '单位';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.research_id IS '研究id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.bind_field_name IS '字段映射';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.value_mapping_json IS '值映射（JSON格式存储）';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.delete_flag IS '删除标记：0-未删除，1-已删除';
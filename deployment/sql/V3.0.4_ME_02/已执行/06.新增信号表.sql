-- app.tb_cdcew_disease_warning_signal definition
drop table if exists app.tb_cdcew_disease_warning_signal;
CREATE TABLE app.tb_cdcew_disease_warning_signal (
	signal_id varchar(100) NOT NULL, -- 主键
	signal_num varchar(32) NULL, -- 预警信号编号
	warning_time timestamp NULL, -- 预警时间
	begin_date date NULL, -- 开始日期
	end_date date NULL, -- 结束日期
	province_code varchar(64) NULL, -- 省编码
	province_name varchar(64) NULL, -- 省名称
	city_code varchar(64) NULL, -- 市编码
	city_name varchar(64) NULL, -- 市名称
	district_code varchar(64) NULL, -- 区县编码
	district_name varchar(64) NULL, -- 区县名称
	street_code varchar(200) NULL, -- 街道编码
	street_name varchar(200) NULL, -- 街道名称
	stat_dim_id varchar(500) NULL, -- 预警地区编码：地点/区划/单位/学校等
	stat_dim_name varchar(500) NULL, -- 预警地区名称：地点/区划/单位/学校等
	topic_id varchar(64) NULL, -- 专题id
	topic_name varchar(64) NULL, -- 专题name
	disease_code varchar(64) NULL, -- 疾病编码
	disease_name varchar(64) NULL, -- 疾病名称
	death_cnt int4 NULL, -- 死亡数量
	medical_case_cnt int4 NULL, -- 病例数
	processing_status varchar(32) NULL, -- 信号当前状态
	processing_latest_time timestamp NULL, -- 当前流程最晚处理时间
	risk_level_id varchar(64) NULL, -- 风险等级id
	risk_level_detail_id varchar(64) NULL, -- 风险等级ID
	event_level varchar(32) NULL, -- 事件等级
	warning_scenario varchar(500) NULL, -- 预警场景
	warning_scenario_type varchar(500) NULL, -- 预警场景类型
	warning_reason varchar(64) NULL, -- 预警原因
	warning_rule_id varchar(64) NULL, -- 预警规则ID
	warning_rule_desc varchar(500) NULL, -- 预警规则描述
	warning_method_name varchar(200) NULL, -- 预警方法名称
	warning_threshold numeric(11, 4) NULL, -- 预警阈值
	warning_charge_person_id varchar(200) NULL, -- 责任人ID
	warning_charge_person_name varchar(200) NULL, -- 责任人姓名
	warning_type varchar(32) NULL, -- 预警类型 infected、syndrome、multichannel
	signal_class varchar(32) NULL, -- 信号类型 infectious_warning、syndrome_warning、pathogen_warning、epidemic_clues
	check_status varchar(32) NULL, -- 核实状态
	check_latest_time timestamp NULL, -- 核实最晚完成时间
	check_finish_time timestamp NULL, -- 核实实际完成时间
	check_result varchar(100) NULL, -- 核实结果
	invest_status varchar(32) NULL, -- 调查状态
	invest_latest_time timestamp NULL, -- 调查最晚完成时间
	invest_finish_time timestamp NULL, -- 调查实际完成时间
	invest_result varchar(100) NULL, -- 调查结果
	judge_status varchar(32) NULL, -- 研判状态
	judge_latest_time timestamp NULL, -- 研判最晚完成时间
	judge_finish_time timestamp NULL, -- 研判实际完成时间
	judge_result varchar(100) NULL, -- 研判结果
	create_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 创建时间
	update_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 更新时间
	signal_change_log varchar(255) NULL, -- 信号变化日志
	longitude numeric NULL, -- 信号发生地经度
	latitude numeric NULL, -- 信号发生地纬度
	CONSTRAINT tb_cdcew_disease_warning_signal_pkey PRIMARY KEY (signal_id)
);
COMMENT ON TABLE app.tb_cdcew_disease_warning_signal IS '疾病信号表';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_num IS '预警信号编号';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_time IS '预警时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.begin_date IS '开始日期';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.end_date IS '结束日期';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.province_code IS '省编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.province_name IS '省名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.city_code IS '市编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.city_name IS '市名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.district_code IS '区县编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.district_name IS '区县名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.street_code IS '街道编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.street_name IS '街道名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.stat_dim_id IS '预警地区编码：地点/区划/单位/学校等';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.stat_dim_name IS '预警地区名称：地点/区划/单位/学校等';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.topic_id is '专题id';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.topic_name is '专题name';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.disease_code IS '疾病编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.death_cnt IS '死亡数量';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.medical_case_cnt IS '病例数';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.processing_status IS '信号当前状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.processing_latest_time IS '当前流程最晚处理时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.risk_level_id IS '风险等级id';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.risk_level_detail_id IS '风险等级ID';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.event_level IS '事件等级';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_scenario IS '预警场景';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_scenario_type IS '预警场景类型';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_reason IS '预警原因';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_rule_id IS '预警规则ID';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_rule_desc IS '预警规则描述';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_method_name IS '预警方法名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_threshold IS '预警阈值';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_charge_person_id IS '责任人ID';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_charge_person_name IS '责任人姓名';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_type IS '预警类型 infected、syndrome、multichannel';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_class IS '信号类型 infectious_warning、syndrome_warning、pathogen_warning、epidemic_clues';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_status IS '核实状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_latest_time IS '核实最晚完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_finish_time IS '核实实际完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_result IS '核实结果';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_status IS '调查状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_latest_time IS '调查最晚完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_finish_time IS '调查实际完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_result IS '调查结果';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_status IS '研判状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_latest_time IS '研判最晚完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_finish_time IS '研判实际完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_result IS '研判结果';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_change_log IS '信号变化日志';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.longitude IS '信号发生地经度';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.latitude IS '信号发生地纬度';
ALTER TABLE app.tb_cdcew_warning_signal_timeline
ADD COLUMN if not exists creator varchar(255) NULL,
ADD COLUMN if not exists notes text NULL,
ADD COLUMN if not exists target_signal_id varchar(255) NULL;

COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.notes IS '备注';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.target_signal_id IS '目标信号id';



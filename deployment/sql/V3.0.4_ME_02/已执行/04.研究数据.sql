-- app.tb_cdcew_warning_research_data definition

-- Drop table

DROP TABLE if exists tb_cdcew_warning_research_data;

CREATE TABLE tb_cdcew_warning_research_data (
	id varchar(100) NOT NULL, -- 主键id
	creator_id varchar(100) NULL, -- 创建者id
	creator varchar(64) NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(100) NULL, -- 修改者id
	updater varchar(64) NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(2) NULL, -- 删除标记
	research_id varchar(100) NULL, -- 研究id
	process_id varchar(100) NULL, -- 病程id
	process_type varchar(20) NULL, -- 病程类型
	exclude_flag varchar(2) NULL, -- 排除标识
	data_json jsonb NULL, -- 数据
	match_dw_flag varchar(2) NULL, -- 是否已匹配数仓
	CONSTRAINT tb_cdcew_warning_research_data_pkey PRIMARY KEY (id)
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN app.tb_cdcew_warning_research_data.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.delete_flag IS '删除标记';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.research_id IS '研究id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.process_id IS '病程id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.process_type IS '病程类型';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.exclude_flag IS '排除标识';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.data_json IS '数据';
COMMENT ON COLUMN app.tb_cdcew_warning_research_data.match_dw_flag IS '是否已匹配数仓';
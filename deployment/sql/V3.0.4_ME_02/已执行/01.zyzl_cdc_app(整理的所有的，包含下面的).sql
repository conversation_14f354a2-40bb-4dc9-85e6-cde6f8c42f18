-- app.tb_cdcew_warning_research_field_setting definition

-- Drop table

DROP TABLE if exists tb_cdcew_warning_research_field_setting;

CREATE TABLE app.tb_cdcew_warning_research_field_setting (
	"name" varchar(32) NULL, -- 名称
	identification varchar(32) NULL, -- 标识
	"type" varchar(32) NULL, -- 类型
	options_json text NULL, -- 选项值
	unit_name varchar(32) NULL, -- 单位
	source_type varchar(32) NULL, -- 所属类型，0-系统， 1-用户
	id varchar(100) NOT NULL, -- 主键id
	creator_id varchar(64) NULL, -- 创建者id
	creator varchar(100) NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(64) NULL, -- 修改者id
	updater varchar(100) NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(1) NULL, -- 删除标记，0 表示未删除，1 表示已删除
	process_type varchar(20) NULL, -- 病程类型
	CONSTRAINT tb_cdcew_warning_research_field_setting_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_research_field_setting IS '该表用于存储相关信息';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.identification IS '标识';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting."type" IS '类型';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.options_json IS '选项值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.unit_name IS '单位';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.source_type IS '所属类型，0-系统， 1-用户';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.delete_flag IS '删除标记，0 表示未删除，1 表示已删除';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.process_type IS '病程类型';


INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('死亡原因', 'deadReason', 'CATEGORIZATION', '["因该病死亡","其他"]', NULL, user, '176869752212029621', NULL, NULL, '2025-03-21 12:19:53.841', NULL, NULL, '2025-03-21 12:19:53.841', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病情转归', 'outCome', 'CATEGORIZATION', '["治愈","好转","稳定","恶化","死亡","其他"]', NULL, user, '176869752211767477', NULL, NULL, '2025-03-21 12:19:53.720', NULL, NULL, '2025-03-21 12:19:53.720', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('末次病原检测结果', 'lastPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176869752211505333', NULL, NULL, '2025-03-21 12:19:53.616', NULL, NULL, '2025-03-21 12:19:53.616', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('首次病原检测结果', 'firstPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176869752211243189', NULL, NULL, '2025-03-21 12:19:53.504', NULL, NULL, '2025-03-21 12:19:53.504', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('重症原因', 'severeReason', 'CATEGORIZATION', '["就诊期间死亡","病重病危通知","重症监护","其他"]', NULL, user, '176869752210981045', NULL, NULL, '2025-03-21 12:19:53.396', NULL, NULL, '2025-03-21 12:19:53.396', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例类型', 'caseType', 'CATEGORIZATION', '["非重症病例","重症未死亡病例","死亡病例"]', NULL, user, '176869752210718901', NULL, NULL, '2025-03-21 12:19:53.282', NULL, NULL, '2025-03-21 12:19:53.282', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('是否重症', 'isSevere', 'CATEGORIZATION', '["0","1"]', NULL, user, '176869752210456757', NULL, NULL, '2025-03-21 12:19:53.176', NULL, NULL, '2025-03-21 12:19:53.176', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('是否排除', 'isExcluded', 'CATEGORIZATION', '["0","1"]', NULL, user, '176869752210194613', NULL, NULL, '2025-03-21 12:19:53.061', NULL, NULL, '2025-03-21 12:19:53.061', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('确诊疾病', 'confirmedDisease', 'QUANTIFY', NULL, NULL, user, '176869751138549941', NULL, NULL, '2025-03-21 12:19:52.936', NULL, NULL, '2025-03-21 12:19:52.936', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('确诊疾病类型', 'confirmedDiseaseType', 'CATEGORIZATION', '["传染病","非传染病","不详"]', NULL, user, '176869751138287797', NULL, NULL, '2025-03-21 12:19:52.818', NULL, NULL, '2025-03-21 12:19:52.818', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('症候群', 'syndrome', 'CATEGORIZATION', '["发热呼吸道症候群","腹泻症候群","发热伴出疹症候群","发热伴出血症候群","脑炎脑膜炎症候群","流感样病例","严重急性呼吸道感染病例","不明原因肺炎","严重临床呼吸道症候群"]', NULL, user, '176869751138025653', NULL, NULL, '2025-03-21 12:19:52.706', NULL, NULL, '2025-03-21 12:19:52.706', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校地址', 'schoolAddress', 'QUANTIFY', NULL, NULL, user, '176869751137763509', NULL, NULL, '2025-03-21 12:19:52.567', NULL, NULL, '2025-03-21 12:19:52.567', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校类型', 'schoolType', 'CATEGORIZATION', '["学前教育","初等教育","中等教育","高等教育","特殊教育","其他教育"]', NULL, user, '176869751137501365', NULL, NULL, '2025-03-21 12:19:52.460', NULL, NULL, '2025-03-21 12:19:52.460', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('民族', 'ethnicity', 'QUANTIFY', NULL, NULL, user, '176869751137239221', NULL, NULL, '2025-03-21 12:19:52.342', NULL, NULL, '2025-03-21 12:19:52.342', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('人群分类', 'personType', 'CATEGORIZATION', '["幼托儿童","散居儿童","学生（大中小学）","教师","保育员及保姆","餐饮食品业","公共卫生服务员","商业服务","医务人员","工人","民工","农民","牧民","渔（船）民","干部职员","离退人员","家务及待业","其他","不详"]', NULL, user, '176869751136977077', NULL, NULL, '2025-03-21 12:19:52.226', NULL, NULL, '2025-03-21 12:19:52.226', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('现住址', 'currentAddress', 'QUANTIFY', NULL, NULL, user, '176869751136714933', NULL, NULL, '2025-03-21 12:19:52.127', NULL, NULL, '2025-03-21 12:19:52.127', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('性别', 'patientSexName', 'CATEGORIZATION', '["男性","女性","未知","未说明"]', NULL, user, '176869751136452789', NULL, NULL, '2025-03-21 12:19:52.014', NULL, NULL, '2025-03-21 12:19:52.014', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例ID', 'processId', 'QUANTIFY', NULL, NULL, user, '176869750062710965', NULL, NULL, '2025-03-21 12:19:51.802', NULL, NULL, '2025-03-21 12:19:51.802', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('死亡原因', 'deadReason', 'CATEGORIZATION', '["因该病死亡","其他"]', NULL, user, '176868898585444533', NULL, NULL, '2025-03-21 12:06:38.267', NULL, NULL, '2025-03-21 12:06:38.267', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病情转归', 'outCome', 'CATEGORIZATION', '["治愈","好转","稳定","恶化","死亡","其他"]', NULL, user, '176868897511702709', NULL, NULL, '2025-03-21 12:06:37.345', NULL, NULL, '2025-03-21 12:06:37.345', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('末次病原检测结果', 'lastPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176868896438223029', NULL, NULL, '2025-03-21 12:06:36.644', NULL, NULL, '2025-03-21 12:06:36.644', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('首次病原检测结果', 'firstPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176868896437960885', NULL, NULL, '2025-03-21 12:06:36.012', NULL, NULL, '2025-03-21 12:06:36.012', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('首诊单位', 'firstDiagnosisOrg', 'QUANTIFY', NULL, NULL, user, '176868895364219061', NULL, NULL, '2025-03-21 12:06:35.387', NULL, NULL, '2025-03-21 12:06:35.387', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例诊断状态', 'diagnosisStatus', 'CATEGORIZATION', '["疑似病例","临床诊断病例","实验室确诊病例","病原携带者"]', NULL, user, '176868894290739381', NULL, NULL, '2025-03-21 12:06:34.756', NULL, NULL, '2025-03-21 12:06:34.756', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('诊断时间（时）', 'diagnosisTime', 'QUANTIFY', NULL, NULL, user, '176868894290477237', NULL, NULL, '2025-03-21 12:06:34.138', NULL, NULL, '2025-03-21 12:06:34.138', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('诊断类型', 'diagnosisType', 'CATEGORIZATION', '["门急诊诊断","入院诊断","出院主要诊断","出院其他诊断","死亡诊断","病理诊断"]', NULL, user, '176868893216735413', NULL, NULL, '2025-03-21 12:06:33.485', NULL, NULL, '2025-03-21 12:06:33.485', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('法定报告传染病', 'diseaseName', 'QUANTIFY', NULL, NULL, user, '176868892143255733', NULL, NULL, '2025-03-21 12:06:32.843', NULL, NULL, '2025-03-21 12:06:32.843', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('疾病类型', 'diseaseType', 'CATEGORIZATION', '["传染病","非传染病"]', NULL, user, '176868892142993589', NULL, NULL, '2025-03-21 12:06:32.193', NULL, NULL, '2025-03-21 12:06:32.193', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校地址', 'schoolAddress', 'QUANTIFY', NULL, NULL, user, '176868891069251765', NULL, NULL, '2025-03-21 12:06:31.505', NULL, NULL, '2025-03-21 12:06:31.505', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校类型', 'schoolType', 'CATEGORIZATION', '["学前教育","初等教育","中等教育","高等教育","特殊教育","其他教育"]', NULL, user, '176868889995772085', NULL, NULL, '2025-03-21 12:06:30.809', NULL, NULL, '2025-03-21 12:06:30.809', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('人群分类', 'personType', 'CATEGORIZATION', '["幼托儿童","散居儿童","学生（大中小学）","教师","保育员及保姆","餐饮食品业","公共卫生服务员","商业服务","医务人员","工人","民工","农民","牧民","渔（船）民","干部职员","离退人员","家务及待业","其他","不详"]', NULL, user, '176868889995509941', NULL, NULL, '2025-03-21 12:06:30.132', NULL, NULL, '2025-03-21 12:06:30.132', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('现住址', 'currentAddress', 'QUANTIFY', NULL, NULL, user, '176868888921768117', NULL, NULL, '2025-03-21 12:06:29.455', NULL, NULL, '2025-03-21 12:06:29.455', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('性别', 'patientSexName', 'CATEGORIZATION', '["男性","女性","未知","未说明"]', NULL, user, '176868887848026293', NULL, NULL, '2025-03-21 12:06:28.750', NULL, NULL, '2025-03-21 12:06:28.750', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例ID', 'processId', 'QUANTIFY', NULL, NULL, user, '176868886774284469', NULL, NULL, '2025-03-21 12:06:27.419', NULL, NULL, '2025-03-21 12:06:27.419', '0', 'infected');

-- app.tb_cdcew_warning_research definition

-- Drop table

DROP TABLE if exists tb_cdcew_warning_research;

CREATE TABLE app.tb_cdcew_warning_research (
	"name" varchar(100) NULL, -- 名称
	remark text NULL, -- 描述
	inclusion_criteria text NULL, -- 纳入标准
	exclusion_criteria text NULL, -- 排除标准
	id varchar NOT NULL, -- 主键 id
	creator_id varchar NULL, -- 创建者 id
	creator varchar NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar NULL, -- 修改者 id
	updater varchar NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(2) NULL, -- 删除标记，0 表示未删除，1 表示已删除
	process_type varchar(20) NULL, -- 病程类型
	CONSTRAINT tb_cdcew_warning_research_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_research IS '关于预警研究的相关信息表';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_warning_research."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research.remark IS '描述';
COMMENT ON COLUMN app.tb_cdcew_warning_research.inclusion_criteria IS '纳入标准';
COMMENT ON COLUMN app.tb_cdcew_warning_research.exclusion_criteria IS '排除标准';
COMMENT ON COLUMN app.tb_cdcew_warning_research.id IS '主键 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.creator_id IS '创建者 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research.updater_id IS '修改者 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research.delete_flag IS '删除标记，0 表示未删除，1 表示已删除';
COMMENT ON COLUMN app.tb_cdcew_warning_research.process_type IS '病程类型';

-- app.tb_cdcew_predictive_factor definition

-- Drop table

DROP TABLE if exists tb_cdcew_predictive_factor;

CREATE TABLE app.tb_cdcew_predictive_factor (
	id varchar(100) NOT NULL, -- 主键id
	"name" varchar(100) NULL, -- 名称
	"type" varchar(100) NULL, -- 类型
	options_json text NULL, -- 选择的值（JSON格式存储）
	unit_name varchar(50) NULL, -- 单位
	research_id varchar(100) NULL, -- 研究id
	bind_field_name varchar(32) NULL, -- 字段映射
	value_mapping_json text NULL, -- 值映射（JSON格式存储）
	creator_id varchar(100) NULL, -- 创建者id
	creator varchar(100) NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(100) NULL, -- 修改者id
	updater varchar(100) NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(2) NULL, -- 删除标记：0-未删除，1-已删除
	CONSTRAINT tb_cdcew_predictive_factor_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_predictive_factor IS '预测因子配置表';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_predictive_factor.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor."type" IS '类型';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.options_json IS '选择的值（JSON格式存储）';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.unit_name IS '单位';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.research_id IS '研究id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.bind_field_name IS '字段映射';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.value_mapping_json IS '值映射（JSON格式存储）';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_predictive_factor.delete_flag IS '删除标记：0-未删除，1-已删除';

-- app.tb_cdcew_warning_research definition

-- Drop table

DROP TABLE if exists tb_cdcew_warning_research;

CREATE TABLE app.tb_cdcew_warning_research (
	"name" varchar(100) NULL, -- 名称
	remark text NULL, -- 描述
	inclusion_criteria text NULL, -- 纳入标准
	exclusion_criteria text NULL, -- 排除标准
	id varchar NOT NULL, -- 主键 id
	creator_id varchar NULL, -- 创建者 id
	creator varchar NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar NULL, -- 修改者 id
	updater varchar NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(2) NULL, -- 删除标记，0 表示未删除，1 表示已删除
	process_type varchar(20) NULL, -- 病程类型
	CONSTRAINT tb_cdcew_warning_research_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_research IS '关于预警研究的相关信息表';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_warning_research."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research.remark IS '描述';
COMMENT ON COLUMN app.tb_cdcew_warning_research.inclusion_criteria IS '纳入标准';
COMMENT ON COLUMN app.tb_cdcew_warning_research.exclusion_criteria IS '排除标准';
COMMENT ON COLUMN app.tb_cdcew_warning_research.id IS '主键 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.creator_id IS '创建者 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research.updater_id IS '修改者 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research.delete_flag IS '删除标记，0 表示未删除，1 表示已删除';
COMMENT ON COLUMN app.tb_cdcew_warning_research.process_type IS '病程类型';


drop TABLE if exists app.tb_cdcew_warning_research_indicator;
-- 建表语句 (2025-03-19 19:26 生成)
CREATE TABLE app.tb_cdcew_warning_research_indicator (
    id                  VARCHAR(100) PRIMARY KEY,          -- 主键id (UUID类型)
    creator_id          VARCHAR(50) ,               -- 创建者id
    creator             VARCHAR(100),              -- 创建者
    create_time         TIMESTAMP ,                 -- 创建时间
    updater_id          VARCHAR(50),                        -- 修改者id
    updater             VARCHAR(100),                       -- 修改者
    update_time         TIMESTAMP,                          -- 修改时间
    delete_flag         VARCHAR(1),    -- 删除标记 (0未删除/1已删除)
    research_id         VARCHAR(50),               -- 预警研究id
    name                VARCHAR(200),              -- 指标名称
    threshold_value     numeric(10, 5),          -- 阈值
    stated_value        numeric(10, 5),     -- 计算之后的值
    match_threshold_flag VARCHAR(1),               -- 是否达到阈值 (布尔语义)
    disposal_suggestion TEXT,                               -- 处置建议
    stat_strategy       VARCHAR(50),               -- 计算策略
    logics_json         TEXT                                -- 逻辑数json字段（隐藏）
);

-- 字段注释
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.id  IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.creator_id  IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.creator  IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.create_time  IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.updater_id  IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.updater  IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.update_time  IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.delete_flag  IS '删除标记（0未删除/1已删除）';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.research_id  IS '预警研究id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.name  IS '指标名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.threshold_value  IS '阈值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.stated_value  IS '计算之后的值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.match_threshold_flag  IS '是否达到阈值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.disposal_suggestion  IS '处置建议';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.stat_strategy  IS '计算策略';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.logics_json  IS '逻辑数json字段（内部存储）';

-- app.tb_cdcew_disease_warning_signal definition
drop table if exists app.tb_cdcew_disease_warning_signal;
CREATE TABLE app.tb_cdcew_disease_warning_signal (
	signal_id varchar(100) NOT NULL, -- 主键
	signal_num varchar(32) NULL, -- 预警信号编号
	warning_time timestamp NULL, -- 预警时间
	begin_date date NULL, -- 开始日期
	end_date date NULL, -- 结束日期
	province_code varchar(64) NULL, -- 省编码
	province_name varchar(64) NULL, -- 省名称
	city_code varchar(64) NULL, -- 市编码
	city_name varchar(64) NULL, -- 市名称
	district_code varchar(64) NULL, -- 区县编码
	district_name varchar(64) NULL, -- 区县名称
	street_code varchar(200) NULL, -- 街道编码
	street_name varchar(200) NULL, -- 街道名称
	stat_dim_id varchar(500) NULL, -- 预警地区编码：地点/区划/单位/学校等
	stat_dim_name varchar(500) NULL, -- 预警地区名称：地点/区划/单位/学校等
	topic_id varchar(64) NULL, -- 专题id
	topic_name varchar(64) NULL, -- 专题name
	disease_code varchar(64) NULL, -- 疾病编码
	disease_name varchar(64) NULL, -- 疾病名称
	death_cnt int4 NULL, -- 死亡数量
	medical_case_cnt int4 NULL, -- 病例数
	processing_status varchar(32) NULL, -- 信号当前状态
	processing_latest_time timestamp NULL, -- 当前流程最晚处理时间
	risk_level_id varchar(64) NULL, -- 风险等级id
	risk_level_detail_id varchar(64) NULL, -- 风险等级ID
	event_level varchar(32) NULL, -- 事件等级
	warning_scenario varchar(500) NULL, -- 预警场景
	warning_scenario_type varchar(500) NULL, -- 预警场景类型
	warning_reason varchar(64) NULL, -- 预警原因
	warning_rule_id varchar(64) NULL, -- 预警规则ID
	warning_rule_desc varchar(500) NULL, -- 预警规则描述
	warning_method_name varchar(200) NULL, -- 预警方法名称
	warning_threshold numeric(11, 4) NULL, -- 预警阈值
	warning_charge_person_id varchar(200) NULL, -- 责任人ID
	warning_charge_person_name varchar(200) NULL, -- 责任人姓名
	warning_type varchar(32) NULL, -- 预警类型 infected、syndrome、multichannel
	signal_class varchar(32) NULL, -- 信号类型 infectious_warning、syndrome_warning、pathogen_warning、epidemic_clues
	check_status varchar(32) NULL, -- 核实状态
	check_latest_time timestamp NULL, -- 核实最晚完成时间
	check_finish_time timestamp NULL, -- 核实实际完成时间
	check_result varchar(100) NULL, -- 核实结果
	invest_status varchar(32) NULL, -- 调查状态
	invest_latest_time timestamp NULL, -- 调查最晚完成时间
	invest_finish_time timestamp NULL, -- 调查实际完成时间
	invest_result varchar(100) NULL, -- 调查结果
	judge_status varchar(32) NULL, -- 研判状态
	judge_latest_time timestamp NULL, -- 研判最晚完成时间
	judge_finish_time timestamp NULL, -- 研判实际完成时间
	judge_result varchar(100) NULL, -- 研判结果
	create_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 创建时间
	update_time timestamp NOT NULL DEFAULT LOCALTIMESTAMP, -- 更新时间
	signal_change_log varchar(255) NULL, -- 信号变化日志
	longitude numeric NULL, -- 信号发生地经度
	latitude numeric NULL, -- 信号发生地纬度
	CONSTRAINT tb_cdcew_disease_warning_signal_pkey PRIMARY KEY (signal_id)
);
COMMENT ON TABLE app.tb_cdcew_disease_warning_signal IS '疾病信号表';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_num IS '预警信号编号';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_time IS '预警时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.begin_date IS '开始日期';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.end_date IS '结束日期';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.province_code IS '省编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.province_name IS '省名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.city_code IS '市编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.city_name IS '市名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.district_code IS '区县编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.district_name IS '区县名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.street_code IS '街道编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.street_name IS '街道名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.stat_dim_id IS '预警地区编码：地点/区划/单位/学校等';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.stat_dim_name IS '预警地区名称：地点/区划/单位/学校等';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.topic_id is '专题id';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.topic_name is '专题name';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.disease_code IS '疾病编码';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.disease_name IS '疾病名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.death_cnt IS '死亡数量';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.medical_case_cnt IS '病例数';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.processing_status IS '信号当前状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.processing_latest_time IS '当前流程最晚处理时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.risk_level_id IS '风险等级id';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.risk_level_detail_id IS '风险等级ID';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.event_level IS '事件等级';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_scenario IS '预警场景';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_scenario_type IS '预警场景类型';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_reason IS '预警原因';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_rule_id IS '预警规则ID';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_rule_desc IS '预警规则描述';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_method_name IS '预警方法名称';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_threshold IS '预警阈值';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_charge_person_id IS '责任人ID';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_charge_person_name IS '责任人姓名';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.warning_type IS '预警类型 infected、syndrome、multichannel';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_class IS '信号类型 infectious_warning、syndrome_warning、pathogen_warning、epidemic_clues';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_status IS '核实状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_latest_time IS '核实最晚完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_finish_time IS '核实实际完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.check_result IS '核实结果';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_status IS '调查状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_latest_time IS '调查最晚完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_finish_time IS '调查实际完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.invest_result IS '调查结果';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_status IS '研判状态';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_latest_time IS '研判最晚完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_finish_time IS '研判实际完成时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.judge_result IS '研判结果';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.update_time IS '更新时间';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.signal_change_log IS '信号变化日志';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.longitude IS '信号发生地经度';
COMMENT ON COLUMN app.tb_cdcew_disease_warning_signal.latitude IS '信号发生地纬度';

ALTER TABLE app.tb_cdcew_warning_signal_timeline
ADD COLUMN if not exists creator varchar(255) NULL,
ADD COLUMN if not exists notes text NULL;

COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.creator IS '创建人';
COMMENT ON COLUMN app.tb_cdcew_warning_signal_timeline.notes IS '备注';

drop table IF EXISTS app.tb_cdcew_risk_assessment_analysis;
create TABLE app.tb_cdcew_risk_assessment_analysis(
    id varchar (255) primary key NOT NULL,
    risk_assessment_category varchar (255) NULL,
	disease varchar (255) NULL,
	time_type varchar (255) NULL,
	start_time timestamp,
	end_time timestamp,
	adress_type varchar (255) NULL,
	report_id varchar (255) NULL,
	province_code varchar (255) NULL,
	province_name varchar (255) NULL,
	city_code varchar (255) NULL,
	city_name varchar (255) NULL,
	district_code varchar (255) NULL,
    district_name varchar (255) NULL,
	creator_id varchar (255) NULL,
	creator varchar (255) NULL,
	create_time timestamp NULL,
	updater_id varchar (255) NULL,
	updater varchar (255) NULL,
	update_time timestamp,
	delete_flag varchar (255) NULL
);

comment on column app.tb_cdcew_risk_assessment_analysis.id is '主键id';
comment on column app.tb_cdcew_risk_assessment_analysis.risk_assessment_category is '风险评估类别';
comment on column app.tb_cdcew_risk_assessment_analysis.disease is '疾病';
comment on column app.tb_cdcew_risk_assessment_analysis.time_type is '时间类型';
comment on column app.tb_cdcew_risk_assessment_analysis.start_time is '统计开始时间';
comment on column app.tb_cdcew_risk_assessment_analysis.end_time is '统计结束时间';
comment on column app.tb_cdcew_risk_assessment_analysis.adress_type is '地区类型';
comment on column app.tb_cdcew_risk_assessment_analysis.report_id is '统计报告id';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.province_code IS '省编码';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.province_name IS '省名称';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.city_code IS '市编码';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.city_name IS '市名称';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.district_code IS '区编码';
COMMENT ON COLUMN app.tb_cdcew_risk_assessment_analysis.district_name IS '区名称';
comment on column app.tb_cdcew_risk_assessment_analysis.creator_id is '创建者id';
comment on column app.tb_cdcew_risk_assessment_analysis.creator is '创建者';
comment on column app.tb_cdcew_risk_assessment_analysis.create_time is '创建时间';
comment on column app.tb_cdcew_risk_assessment_analysis.updater_id is '修改者id';
comment on column app.tb_cdcew_risk_assessment_analysis.updater is '修改者';
comment on column app.tb_cdcew_risk_assessment_analysis.update_time is '修改时间';
comment on column app.tb_cdcew_risk_assessment_analysis.delete_flag is '删除标记';







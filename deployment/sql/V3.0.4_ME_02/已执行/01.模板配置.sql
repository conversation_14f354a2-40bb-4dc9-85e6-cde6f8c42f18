-- app.tb_cdcew_warning_research_field_setting definition

-- Drop table

DROP TABLE if exists tb_cdcew_warning_research_field_setting;

CREATE TABLE app.tb_cdcew_warning_research_field_setting (
	"name" varchar(32) NULL, -- 名称
	identification varchar(32) NULL, -- 标识
	"type" varchar(32) NULL, -- 类型
	options_json text NULL, -- 选项值
	unit_name varchar(32) NULL, -- 单位
	source_type varchar(32) NULL, -- 所属类型，0-系统， 1-用户
	id varchar(100) NOT NULL, -- 主键id
	creator_id varchar(64) NULL, -- 创建者id
	creator varchar(100) NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(64) NULL, -- 修改者id
	updater varchar(100) NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(1) NULL, -- 删除标记，0 表示未删除，1 表示已删除
	process_type varchar(20) NULL, -- 病程类型
	CONSTRAINT tb_cdcew_warning_research_field_setting_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_research_field_setting IS '该表用于存储相关信息';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.identification IS '标识';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting."type" IS '类型';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.options_json IS '选项值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.unit_name IS '单位';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.source_type IS '所属类型，0-系统， 1-用户';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.delete_flag IS '删除标记，0 表示未删除，1 表示已删除';
COMMENT ON COLUMN app.tb_cdcew_warning_research_field_setting.process_type IS '病程类型';


INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('死亡原因', 'deadReason', 'CATEGORIZATION', '["因该病死亡","其他"]', NULL, user, '176869752212029621', NULL, NULL, '2025-03-21 12:19:53.841', NULL, NULL, '2025-03-21 12:19:53.841', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病情转归', 'outCome', 'CATEGORIZATION', '["治愈","好转","稳定","恶化","死亡","其他"]', NULL, user, '176869752211767477', NULL, NULL, '2025-03-21 12:19:53.720', NULL, NULL, '2025-03-21 12:19:53.720', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('末次病原检测结果', 'lastPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176869752211505333', NULL, NULL, '2025-03-21 12:19:53.616', NULL, NULL, '2025-03-21 12:19:53.616', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('首次病原检测结果', 'firstPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176869752211243189', NULL, NULL, '2025-03-21 12:19:53.504', NULL, NULL, '2025-03-21 12:19:53.504', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('重症原因', 'severeReason', 'CATEGORIZATION', '["就诊期间死亡","病重病危通知","重症监护","其他"]', NULL, user, '176869752210981045', NULL, NULL, '2025-03-21 12:19:53.396', NULL, NULL, '2025-03-21 12:19:53.396', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例类型', 'caseType', 'CATEGORIZATION', '["非重症病例","重症未死亡病例","死亡病例"]', NULL, user, '176869752210718901', NULL, NULL, '2025-03-21 12:19:53.282', NULL, NULL, '2025-03-21 12:19:53.282', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('是否重症', 'isSevere', 'CATEGORIZATION', '["0","1"]', NULL, user, '176869752210456757', NULL, NULL, '2025-03-21 12:19:53.176', NULL, NULL, '2025-03-21 12:19:53.176', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('是否排除', 'isExcluded', 'CATEGORIZATION', '["0","1"]', NULL, user, '176869752210194613', NULL, NULL, '2025-03-21 12:19:53.061', NULL, NULL, '2025-03-21 12:19:53.061', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('确诊疾病', 'confirmedDisease', 'QUANTIFY', NULL, NULL, user, '176869751138549941', NULL, NULL, '2025-03-21 12:19:52.936', NULL, NULL, '2025-03-21 12:19:52.936', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('确诊疾病类型', 'confirmedDiseaseType', 'CATEGORIZATION', '["传染病","非传染病","不详"]', NULL, user, '176869751138287797', NULL, NULL, '2025-03-21 12:19:52.818', NULL, NULL, '2025-03-21 12:19:52.818', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('症候群', 'syndrome', 'CATEGORIZATION', '["发热呼吸道症候群","腹泻症候群","发热伴出疹症候群","发热伴出血症候群","脑炎脑膜炎症候群","流感样病例","严重急性呼吸道感染病例","不明原因肺炎","严重临床呼吸道症候群"]', NULL, user, '176869751138025653', NULL, NULL, '2025-03-21 12:19:52.706', NULL, NULL, '2025-03-21 12:19:52.706', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校地址', 'schoolAddress', 'QUANTIFY', NULL, NULL, user, '176869751137763509', NULL, NULL, '2025-03-21 12:19:52.567', NULL, NULL, '2025-03-21 12:19:52.567', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校类型', 'schoolType', 'CATEGORIZATION', '["学前教育","初等教育","中等教育","高等教育","特殊教育","其他教育"]', NULL, user, '176869751137501365', NULL, NULL, '2025-03-21 12:19:52.460', NULL, NULL, '2025-03-21 12:19:52.460', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('民族', 'ethnicity', 'QUANTIFY', NULL, NULL, user, '176869751137239221', NULL, NULL, '2025-03-21 12:19:52.342', NULL, NULL, '2025-03-21 12:19:52.342', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('人群分类', 'personType', 'CATEGORIZATION', '["幼托儿童","散居儿童","学生（大中小学）","教师","保育员及保姆","餐饮食品业","公共卫生服务员","商业服务","医务人员","工人","民工","农民","牧民","渔（船）民","干部职员","离退人员","家务及待业","其他","不详"]', NULL, user, '176869751136977077', NULL, NULL, '2025-03-21 12:19:52.226', NULL, NULL, '2025-03-21 12:19:52.226', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('现住址', 'currentAddress', 'QUANTIFY', NULL, NULL, user, '176869751136714933', NULL, NULL, '2025-03-21 12:19:52.127', NULL, NULL, '2025-03-21 12:19:52.127', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('性别', 'patientSexName', 'CATEGORIZATION', '["男性","女性","未知","未说明"]', NULL, user, '176869751136452789', NULL, NULL, '2025-03-21 12:19:52.014', NULL, NULL, '2025-03-21 12:19:52.014', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例ID', 'processId', 'QUANTIFY', NULL, NULL, user, '176869750062710965', NULL, NULL, '2025-03-21 12:19:51.802', NULL, NULL, '2025-03-21 12:19:51.802', '0', 'syndrome');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('死亡原因', 'deadReason', 'CATEGORIZATION', '["因该病死亡","其他"]', NULL, user, '176868898585444533', NULL, NULL, '2025-03-21 12:06:38.267', NULL, NULL, '2025-03-21 12:06:38.267', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病情转归', 'outCome', 'CATEGORIZATION', '["治愈","好转","稳定","恶化","死亡","其他"]', NULL, user, '176868897511702709', NULL, NULL, '2025-03-21 12:06:37.345', NULL, NULL, '2025-03-21 12:06:37.345', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('末次病原检测结果', 'lastPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176868896438223029', NULL, NULL, '2025-03-21 12:06:36.644', NULL, NULL, '2025-03-21 12:06:36.644', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('首次病原检测结果', 'firstPathogenTestResult', 'CATEGORIZATION', '["正常","异常","不确定"]', NULL, user, '176868896437960885', NULL, NULL, '2025-03-21 12:06:36.012', NULL, NULL, '2025-03-21 12:06:36.012', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('首诊单位', 'firstDiagnosisOrg', 'QUANTIFY', NULL, NULL, user, '176868895364219061', NULL, NULL, '2025-03-21 12:06:35.387', NULL, NULL, '2025-03-21 12:06:35.387', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例诊断状态', 'diagnosisStatus', 'CATEGORIZATION', '["疑似病例","临床诊断病例","实验室确诊病例","病原携带者"]', NULL, user, '176868894290739381', NULL, NULL, '2025-03-21 12:06:34.756', NULL, NULL, '2025-03-21 12:06:34.756', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('诊断时间（时）', 'diagnosisTime', 'QUANTIFY', NULL, NULL, user, '176868894290477237', NULL, NULL, '2025-03-21 12:06:34.138', NULL, NULL, '2025-03-21 12:06:34.138', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('诊断类型', 'diagnosisType', 'CATEGORIZATION', '["门急诊诊断","入院诊断","出院主要诊断","出院其他诊断","死亡诊断","病理诊断"]', NULL, user, '176868893216735413', NULL, NULL, '2025-03-21 12:06:33.485', NULL, NULL, '2025-03-21 12:06:33.485', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('法定报告传染病', 'diseaseName', 'QUANTIFY', NULL, NULL, user, '176868892143255733', NULL, NULL, '2025-03-21 12:06:32.843', NULL, NULL, '2025-03-21 12:06:32.843', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('疾病类型', 'diseaseType', 'CATEGORIZATION', '["传染病","非传染病"]', NULL, user, '176868892142993589', NULL, NULL, '2025-03-21 12:06:32.193', NULL, NULL, '2025-03-21 12:06:32.193', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校地址', 'schoolAddress', 'QUANTIFY', NULL, NULL, user, '176868891069251765', NULL, NULL, '2025-03-21 12:06:31.505', NULL, NULL, '2025-03-21 12:06:31.505', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('学校类型', 'schoolType', 'CATEGORIZATION', '["学前教育","初等教育","中等教育","高等教育","特殊教育","其他教育"]', NULL, user, '176868889995772085', NULL, NULL, '2025-03-21 12:06:30.809', NULL, NULL, '2025-03-21 12:06:30.809', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('人群分类', 'personType', 'CATEGORIZATION', '["幼托儿童","散居儿童","学生（大中小学）","教师","保育员及保姆","餐饮食品业","公共卫生服务员","商业服务","医务人员","工人","民工","农民","牧民","渔（船）民","干部职员","离退人员","家务及待业","其他","不详"]', NULL, user, '176868889995509941', NULL, NULL, '2025-03-21 12:06:30.132', NULL, NULL, '2025-03-21 12:06:30.132', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('现住址', 'currentAddress', 'QUANTIFY', NULL, NULL, user, '176868888921768117', NULL, NULL, '2025-03-21 12:06:29.455', NULL, NULL, '2025-03-21 12:06:29.455', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('性别', 'patientSexName', 'CATEGORIZATION', '["男性","女性","未知","未说明"]', NULL, user, '176868887848026293', NULL, NULL, '2025-03-21 12:06:28.750', NULL, NULL, '2025-03-21 12:06:28.750', '0', 'infected');
INSERT INTO tb_cdcew_warning_research_field_setting ("name", identification, "type", options_json, unit_name, source_type, id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, process_type) VALUES('病例ID', 'processId', 'QUANTIFY', NULL, NULL, user, '176868886774284469', NULL, NULL, '2025-03-21 12:06:27.419', NULL, NULL, '2025-03-21 12:06:27.419', '0', 'infected');





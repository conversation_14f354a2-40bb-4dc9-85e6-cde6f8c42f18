drop TABLE if exists app.tb_cdcew_warning_research_indicator;
-- 建表语句 (2025-03-19 19:26 生成)
CREATE TABLE app.tb_cdcew_warning_research_indicator (
    id                  VARCHAR(100) PRIMARY KEY,          -- 主键id (UUID类型)
    creator_id          VARCHAR(50) ,               -- 创建者id
    creator             VARCHAR(100),              -- 创建者
    create_time         TIMESTAMP ,                 -- 创建时间
    updater_id          VARCHAR(50),                        -- 修改者id
    updater             VARCHAR(100),                       -- 修改者
    update_time         TIMESTAMP,                          -- 修改时间
    delete_flag         VARCHAR(1),    -- 删除标记 (0未删除/1已删除)
    research_id         VARCHAR(50),               -- 预警研究id
    name                VARCHAR(200),              -- 指标名称
    threshold_value     numeric(10, 5),          -- 阈值
    stated_value        numeric(10, 5),     -- 计算之后的值
    match_threshold_flag VARCHAR(1),               -- 是否达到阈值 (布尔语义)
    disposal_suggestion TEXT,                               -- 处置建议
    stat_strategy       VARCHAR(50),               -- 计算策略
    logics_json         TEXT                                -- 逻辑数json字段（隐藏）
);

-- 字段注释
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.id  IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.creator_id  IS '创建者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.creator  IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.create_time  IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.updater_id  IS '修改者id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.updater  IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.update_time  IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.delete_flag  IS '删除标记（0未删除/1已删除）';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.research_id  IS '预警研究id';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.name  IS '指标名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.threshold_value  IS '阈值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.stated_value  IS '计算之后的值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.match_threshold_flag  IS '是否达到阈值';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.disposal_suggestion  IS '处置建议';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.stat_strategy  IS '计算策略';
COMMENT ON COLUMN app.tb_cdcew_warning_research_indicator.logics_json  IS '逻辑数json字段（内部存储）';
-- app.tb_cdcew_warning_research definition

-- Drop table

DROP TABLE if exists tb_cdcew_warning_research;

CREATE TABLE app.tb_cdcew_warning_research (
	"name" varchar(100) NULL, -- 名称
	remark text NULL, -- 描述
	inclusion_criteria text NULL, -- 纳入标准
	exclusion_criteria text NULL, -- 排除标准
	id varchar NOT NULL, -- 主键 id
	creator_id varchar NULL, -- 创建者 id
	creator varchar NULL, -- 创建者
	create_time timestamp NULL, -- 创建时间
	updater_id varchar NULL, -- 修改者 id
	updater varchar NULL, -- 修改者
	update_time timestamp NULL, -- 修改时间
	delete_flag varchar(2) NULL, -- 删除标记，0 表示未删除，1 表示已删除
	process_type varchar(20) NULL, -- 病程类型
	CONSTRAINT tb_cdcew_warning_research_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_warning_research IS '关于预警研究的相关信息表';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN app.tb_cdcew_warning_research."name" IS '名称';
COMMENT ON COLUMN app.tb_cdcew_warning_research.remark IS '描述';
COMMENT ON COLUMN app.tb_cdcew_warning_research.inclusion_criteria IS '纳入标准';
COMMENT ON COLUMN app.tb_cdcew_warning_research.exclusion_criteria IS '排除标准';
COMMENT ON COLUMN app.tb_cdcew_warning_research.id IS '主键 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.creator_id IS '创建者 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcew_warning_research.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research.updater_id IS '修改者 id';
COMMENT ON COLUMN app.tb_cdcew_warning_research.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcew_warning_research.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcew_warning_research.delete_flag IS '删除标记，0 表示未删除，1 表示已删除';
COMMENT ON COLUMN app.tb_cdcew_warning_research.process_type IS '病程类型';
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170296667688403082', '154151240303902878', '7', '已合并', NULL, NULL, NULL, 1, '0', '2025-01-09 15:52:11.959', '2025-01-09 15:52:11.959', '100001', 'admin', '100001', 'admin');

INSERT INTO app.tb_cdcdm_data_dict (id, code, "name", attr_count, attr_value_count, "type", notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297004843335818', '任务状态', '任务状态', 1, 11, '1', NULL, 1, '0', '2025-01-09 15:57:25.341', '2025-01-09 15:57:25.341', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297061751652490', '170297004843335818', 'waitingChecked', '待核实', NULL, NULL, NULL, 1, '0', '2025-01-09 15:58:18.050', '2025-01-09 15:58:18.050', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297071415328906', '170297004843335818', 'checked', '已核实', NULL, NULL, NULL, 1, '0', '2025-01-09 15:58:27.735', '2025-01-09 15:58:27.735', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297080005263498', '170297004843335818', 'waitingInvest', '待调查', NULL, NULL, NULL, 1, '0', '2025-01-09 15:58:35.150', '2025-01-09 15:58:35.150', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297088595198090', '170297004843335818', 'invested', '已调查', NULL, NULL, NULL, 1, '0', '2025-01-09 15:58:43.263', '2025-01-09 15:58:43.263', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297102553841802', '170297004843335818', 'waitingJudge', '待研判', NULL, NULL, NULL, 1, '0', '2025-01-09 15:58:56.372', '2025-01-09 15:58:56.372', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297113291260042', '170297004843335818', 'judged', '已研判', NULL, NULL, NULL, 1, '0', '2025-01-09 15:59:06.352', '2025-01-09 15:59:06.352', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297122954936458', '170297004843335818', 'timeOut', '已超时', NULL, NULL, NULL, 1, '0', '2025-01-09 15:59:15.383', '2025-01-09 15:59:15.383', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297131544871050', '170297004843335818', 'transferred', '已转交', NULL, NULL, NULL, 1, '0', '2025-01-09 15:59:23.872', '2025-01-09 15:59:23.872', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297142282289290', '170297004843335818', 'waitingEmergency', '待启动应急响应', NULL, NULL, NULL, 1, '0', '2025-01-09 15:59:33.661', '2025-01-09 15:59:33.661', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297150872223882', '170297004843335818', 'emergencyInitiated', '已启动应急响应', NULL, NULL, NULL, 1, '0', '2025-01-09 15:59:41.979', '2025-01-09 15:59:41.979', '100001', 'admin', '100001', 'admin');
INSERT INTO app.tb_cdcdm_data_dict_value (id, data_dict_id, code, "name", description, parent_id, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater) VALUES('170297160535900298', '170297004843335818', 'isMerged', '已合并', NULL, NULL, NULL, 1, '0', '2025-01-09 15:59:50.020', '2025-01-09 15:59:50.020', '100001', 'admin', '100001', 'admin');


drop table if exists app.tb_cdcew_infected_warning_signal_timeline;
CREATE TABLE app.tb_cdcew_infected_warning_signal_timeline (
	id varchar(32) NOT NULL, -- 主键
	signal_id varchar(32) NULL, -- 信号ID
	moment_type_code varchar(32) NULL, -- 信号事件类型代码
	moment_type_name varchar(32) NULL, -- 信号事件类型名称
	moment_time timestamp NULL, -- 时间
	current_case_count int4 NULL, -- 现存病例数
	change_case_count int4 NULL, -- 信号的变化数量
	current_source_key_list text NULL, -- 当前病例主键列表
	create_time timestamp NULL, -- 创建时间
	CONSTRAINT tb_cdcew_infected_warning_signal_timeline_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.id IS '主键';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.signal_id IS '信号ID';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.moment_type_code IS '信号事件类型代码';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.moment_type_name IS '信号事件类型名称';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.moment_time IS '时间';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.current_case_count IS '现存病例数';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.change_case_count IS '信号的变化数量';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.current_source_key_list IS '当前病例主键列表';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.create_time IS '创建时间';


drop table if exists app.tb_cdcew_infected_warning_signal_timeline_detail;
CREATE TABLE app.tb_cdcew_infected_warning_signal_timeline_detail (
	id varchar NOT NULL, -- 主键id
	timeline_id varchar NULL, -- 时间线id
	source_key varchar NULL, -- source_key
	create_time timestamp NULL, -- 创建时间
	CONSTRAINT tb_cdcew_infected_warning_signal_timeline_detail_pk PRIMARY KEY (id)
);
COMMENT ON TABLE app.tb_cdcew_infected_warning_signal_timeline_detail IS '症候群时间线详情';

-- Column comments

COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline_detail.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline_detail.timeline_id IS '时间线id';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline_detail.source_key IS 'source_key';
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline_detail.create_time IS '创建时间';

alter table app.tb_cdcew_infected_warning_signal_timeline add column if not exists source_signal_id varchar(32);
COMMENT ON COLUMN app.tb_cdcew_infected_warning_signal_timeline.source_signal_id IS '源信号id';

alter table app.tb_cdcew_syndrome_warning_signal_timeline add column if not exists source_signal_id varchar(32);
COMMENT ON COLUMN app.tb_cdcew_syndrome_warning_signal_timeline.source_signal_id IS '源信号id';

drop table if exists app.tb_cdcew_outbound_investigation;
CREATE TABLE app.tb_cdcew_outbound_investigation (
    id VARCHAR(32),
    signal_id VARCHAR(32),
    task_id VARCHAR(32),
    target_id VARCHAR(32),
    target_name VARCHAR(32),
    execution_method VARCHAR(10),
    execution_time_type VARCHAR(10),
    specified_time TIMESTAMP,
    template_id VARCHAR(255),
    review_content TEXT,
    redial_count INTEGER,
    time_interval INTEGER,
    sms_reminder_type VARCHAR(255),
    outbound_record_id VARCHAR(255),
    user_org_id VARCHAR(64),
    user_org_name VARCHAR(100),
    target_province_code VARCHAR(20),
    target_province VARCHAR(50),
    target_city_code VARCHAR(20),
    target_city VARCHAR(50),
    target_district_code VARCHAR(20),
    target_district VARCHAR(50),
    creator_id VARCHAR(255), -- 创建者id
    creator VARCHAR(255), -- 创建者
    create_time TIMESTAMP, -- 创建时间
    updater_id VARCHAR(255), -- 修改者id
    updater VARCHAR(255), -- 修改者
    update_time TIMESTAMP, -- 修改时间
    delete_flag VARCHAR(10) -- 删除标记
);

COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.signal_id IS '信号id';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.task_id IS '任务id';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_id IS '通知对象ID';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_name IS '通知对象名称';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.execution_method IS '执行方式';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.execution_time_type IS '执行时间';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.specified_time IS '指定时间';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.template_id IS '电话模板';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.review_content IS '电话预览';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.redial_count IS '重拨次数';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.time_interval IS '时间间隔';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.sms_reminder_type IS '短信提醒';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.outbound_record_id IS '外呼记录id';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.user_org_id IS '用户所在机构id';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.user_org_name IS '用户所在机构名称';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_province_code IS '通知对象所在省编码';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_province IS '通知对象所在省';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_city_code IS '通知对象所在市编码';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_city IS '通知对象所在市';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_district_code IS '通知对象所在区县编码';
COMMENT ON COLUMN app.tb_cdcew_outbound_investigation.target_district IS '通知对象所在区县';
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.iflytek.fpva</groupId>
    <artifactId>cdc-platform-api</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>cdc-platform-api-core</module>
        <module>cdc-platform-api-outbound</module>
        <module>cdc-platform-api-coordination</module>
        <module>cdc-platform-api-app</module>
        <module>cdc-platform-api-multichannel</module>
        <module>cdc-platform-api-mock</module>
        <module>cdc-platform-api-front</module>
        <module>cdc-platform-api-info</module>
        <module>cdc-platform-api-form</module>
        <module>cdc-platform-api-screen</module>
        <module>cdc-platform-api-covid</module>
        <module>cdc-platform-api-pathogen</module>
        <module>cdc-platform-api-common</module>
        <module>cdc-platform-api-warning</module>
        <module>cdc-platform-api-risk</module>
    </modules>
    <properties>
        <common.version>1.1.6-SNAPSHOT</common.version>
        <build.id>1.0.4</build.id>
        <poi.version>5.2.2</poi.version>
        <poi-tl.version>1.12.2</poi-tl.version>
        <hutool.version>5.8.0</hutool.version>
    </properties>
    <parent>
        <groupId>com.iflytek.medicalboot</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

    <repositories>
        <repository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>https://artifacts.iflytek.com/artifactory/mvn-repo/</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.medicalboot</groupId>
            <artifactId>starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>${poi.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>${poi-tl.version}</version>
        </dependency>

        <!-- 读取大量excel数据时使用 -->
        <dependency>
            <groupId>com.monitorjbl</groupId>
            <artifactId>xlsx-streamer</artifactId>
            <version>2.1.0</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.iflytek.storage</groupId>
            <artifactId>storage-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>common</artifactId>
            <version>${common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-ldap-core</artifactId>
                    <groupId>org.springframework.ldap</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.oracle</groupId>
                    <artifactId>ojdbc6</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--            <version>42.2.20</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.highgo</groupId>
            <artifactId>hgdb-pgjdbc</artifactId>
            <version>42.5.0</version>
       </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.imrobin</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.3.70</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.8.1</version>
        </dependency>
        <!--整合Knife4j-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.4</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.commons</groupId>-->
<!--            <artifactId>commons-csv</artifactId>-->
<!--            <version>1.9.0</version>-->
<!--        </dependency>-->

    </dependencies>

    <profiles>
        <profile>
            <id>tomcat</id>
            <!-- 默认使用tomcat -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profiles.active>tomcat</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <!-- 东方通web容器 -->
            <id>tongWeb</id>
            <properties>
                <profiles.active>tongWeb</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.tongweb.springboot</groupId>
                    <artifactId>tongweb-spring-boot-starter-2.x</artifactId>
                    <version>7.0.E.6</version>
                </dependency>
            </dependencies>

            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>

        <profile>
            <!-- 中创web容器 -->
            <id>inforsuite</id>
            <properties>
                <profiles.active>inforsuite</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.cvicse.embedded</groupId>
                    <artifactId>spring-boot-starter-inforsuite</artifactId>
                    <version>********.IFP01</version>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>
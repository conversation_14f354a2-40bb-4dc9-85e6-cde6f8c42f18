package com.iflytek.fpva.cdc.pathogen.controller;

import com.iflytek.fpva.cdc.pathogen.constant.Constants;
import com.iflytek.fpva.cdc.pathogen.service.TbCdcewPathogenInfoService;
import com.iflytek.fpva.cdc.pathogen.util.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.apiguardian.api.API;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

@RestController
@Api(tags = "病原监测")
public class TbCdcewPathogenInfoController {
    @Resource
    private TbCdcewPathogenInfoService tbCdcewPathogenInfoService;

    @PostMapping("/pt/{version}/downloadTemplate")
    @ApiModelProperty("模板下载")
    public ResponseEntity<byte[]> downloadTemplate(@RequestParam String loginUserName){
        byte[] bytes = tbCdcewPathogenInfoService.downloadTemplate(loginUserName);
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(Constants.CLINICAL_PATHOGEN_INFO_EXCEL_NAME);
        return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.CREATED);
    }

    @PostMapping("/pt/{version}/importClinicalByFile")
    @ApiModelProperty("临床样本导入")
    public void importClinicalByFile(@RequestParam MultipartFile file, @RequestParam String loginUserName) throws IOException {
        tbCdcewPathogenInfoService.importClinicalByFile(file.getInputStream(), loginUserName);
    }
}


package com.iflytek.fpva.cdc.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("病例指标按区域统计")
@Data
public class AreaMedCntIndicatorVO extends MedCntIndicatorVO{
    @ApiModelProperty("区域级别")
    private String areaLevel;
    @ApiModelProperty("区域编码")
    private String areaCode;
    @ApiModelProperty("区域名称")
    private String areaName;
}

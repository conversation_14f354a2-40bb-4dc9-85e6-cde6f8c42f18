package com.iflytek.fpva.cdc.common.enums;

import com.google.common.collect.ImmutableList;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.List;


@Getter
public enum AreaLevelEnum {

    NONE(-1, ""),
    COUNTRY(0, "国家"),
    PROVINCE(1, "省"),
    CITY(2, "市"),
    DISTRICT(3, "区县"),
    STREET(4, "街道/乡镇"),
    VILLAGE(5, "社区/村"),
    GROUP(6, "小区/自然村"),
    ADDRESS(99, "详细地址");

    AreaLevelEnum(int level, String name) {
        this.level = level;
        this.name = name;
    }

    private final Integer level;
    private final String name;


    public boolean is(Integer level) {
        return this.level.equals(level);
    }

    public static final String REGION_ROOT_CODE = "1";

    public static final List<AreaLevelEnum> LEVEL_ENUMS = ImmutableList.of(COUNTRY, PROVINCE, CITY, DISTRICT, STREET, VILLAGE, GROUP, ADDRESS);

    public static AreaLevelEnum childLevel(int level) {
        if (level == NONE.level) {
            return NONE;
        }
        if (level == GROUP.level) {
            return ADDRESS;
        }

        return AreaLevelEnum.level(level + 1);
    }

    public static AreaLevelEnum level(int level) {
        switch (level) {
            case 0:
                return COUNTRY;
            case 1:
                return PROVINCE;
            case 2:
                return CITY;
            case 3:
                return DISTRICT;
            case 4:
                return STREET;
            case 5:
                return VILLAGE;
            case 6:
                return GROUP;
            case 99:
                return ADDRESS;
            default:
                return NONE;
        }
    }

    /**
     * @param level 输入level
     * @return 当前Level是否在输入level之下
     */
    public boolean isBelow(Integer level) {
        return level != null && level != NONE.getLevel() && this.getLevel() > level;
    }

    public boolean isAbove(Integer level) {
        return level != null && level != NONE.getLevel() && this.getLevel() < level;
    }

    public boolean beq(Integer level) {
        return (level != null && this.getLevel() == level) || isBelow(level);
    }

    public boolean aeq(Integer level) {
        return (level != null && this.getLevel() == level) || isAbove(level);
    }


    public static Integer arealLevel(String provinceCode, String cityCode, String districtCode) {
        if (StringUtils.isNotBlank(districtCode)) {
            return DISTRICT.getLevel();
        }
        if (StringUtils.isNotBlank(cityCode)) {
            return CITY.getLevel();
        }
        return StringUtils.isNotBlank(provinceCode) ? PROVINCE.getLevel() : NONE.getLevel();
    }
}

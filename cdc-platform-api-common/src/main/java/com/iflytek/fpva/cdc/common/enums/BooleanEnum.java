package com.iflytek.fpva.cdc.common.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public enum BooleanEnum {

    /**
     * True
     */
    TRUE(1, Boolean.TRUE, "True", "Y"),

    /**
     * False
     */
    FALSE(0, Boolean.FALSE, "False","N");

    private final int intVal;
    private final boolean boolVal;
    private final String textVal;
    private final String yesOrNo;

    BooleanEnum(int intVal, boolean boolVal, String textVal, String yesOrNo) {
        this.intVal = intVal;
        this.boolVal = boolVal;
        this.textVal = textVal;
        this.yesOrNo = yesOrNo;
    }

    public static boolean isTrue(Integer intVal){
        if (intVal == null){
            return false;
        }
        return Arrays.stream(values()).filter(v -> v.getIntVal() == intVal).findFirst().orElse(BooleanEnum.FALSE).isBoolVal();
    }
    public int getIntVal() {
        return intVal;
    }

    public String getStrVal() {
        return String.valueOf(intVal);
    }

    public boolean isBoolVal() {
        return boolVal;
    }

    public String getTextVal() {
        return textVal;
    }

    public String getYesOrNo() {
        return yesOrNo;
    }
}

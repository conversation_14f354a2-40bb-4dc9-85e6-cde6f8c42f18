package com.iflytek.fpva.cdc.common.deserializer;

import com.alibaba.nacos.shaded.com.google.gson.internal.bind.util.ISO8601Utils;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class LenientDateTypeAdapter implements JsonDeserializer<Date> {
    private static final String[] DATE_FORMATS = {
            "yyyy-MM-dd HH:mm:ss.SSS",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss'Z'"
    };
    @Override
    public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) {
        String dateStr = json.getAsString();

        // 1. 首先尝试使用 Gson 内置的 ISO 解析（更严格）
        try {
            return ISO8601Utils.parse(dateStr, new ParsePosition(0));
        } catch (ParseException e) {
            // 忽略，尝试自定义格式
        }

        // 2. 尝试多种自定义格式
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.US);
                sdf.setLenient(false); // 严格模式
                return sdf.parse(dateStr);
            } catch (java.text.ParseException e) {
                // 尝试下一个格式
            }
        }

        // 3. 最后尝试更宽松的解析（处理空格分隔符）
        try {
            // 替换空格为 T 使其符合 ISO 格式
            String isoFormatted = dateStr.replaceFirst(" ", "T");

            // 如果缺少时区，添加 UTC 时区
            if (!isoFormatted.contains("Z") && !isoFormatted.contains("+")) {
                isoFormatted += "Z";
            }

            return ISO8601Utils.parse(isoFormatted, new ParsePosition(0));
        } catch (ParseException e) {
            throw new JsonParseException("无法解析日期: " + dateStr, e);
        }
    }
}

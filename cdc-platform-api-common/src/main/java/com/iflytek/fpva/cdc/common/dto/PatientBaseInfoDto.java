//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.iflytek.fpva.cdc.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

public class PatientBaseInfoDto implements Serializable {
    private String localPatientId;
    private String hisPatientId;
    private String ehrId;
    private String identityTypeCode;
    private String otherIdCard;
    private String healthCard;
    private String insuranceCard;
    private String medicalCardType;
    private String medicalCard;
    private String passport;
    private String immuneCard;
    private String ruralCard;
    private String patientName;
    private String sexCode;
    @DateTimeFormat(
        pattern = "yyyy-MM-dd"
    )
    @JsonFormat(
        pattern = "yyyy-MM-dd",
        timezone = "GMT+8"
    )
    private Date birthday;
    private Date deathDate;
    private String nationality;
    private String ethnicity;
    private String maritalStatus;
    private Date weddingDate;
    private Integer obstetricalMark;
    private String careerType;
    private Date careerStartDate;
    private Date careerEndDate;
    private String bloodAbo;
    private String bloodRh;
    private String economicStatus;
    private String insuranceType;
    private String insuranceName;
    private String reliefSource;
    private String medicalPayWay;
    private String company;
    private String workAddrDetail;
    private Long workRegionCode;
    private String telephone;
    private String email;
    private String wechat;
    private String educationCode;
    private String educationName;
    private String photoUrl;
    private String contactRelationCode;
    private String contactName;
    private String contactTel;
    private String contactAddrDetail;
    private Integer residentMark;
    private String livingRegionCode;
    private String livingAddrDetail;
    private String livingPostcode;
    private String guardianRelationCode;
    private String guardianId;
    private String guardianName;
    private Short guardianAge;
    private String guardianNationality;
    private String guardianEthnicity;
    private String guardianTelephone;
    private String updateOrgCode;
    private String updateOrgName;
    private String updateId;
    private String updateName;
    private Integer fiveGuaranteesFlag;
    private Integer chronicDiseaseFlag;
    private String residentIdCard;
    private String nativePlace;
    private String identityTypeName;
    private String medicalCardTypeName;
    private String sexName;
    private String nationalityName;
    private String ethnicityName;
    private String maritalName;
    private String careerName;
    private String bloodAboName;
    private String bloodRhName;
    private String economicName;
    private String reliefSourceName;
    private String medicalPayName;
    private String contactRelationName;
    private String guardianRelationName;
    private String guardianNationalityName;
    private String guardianEthnicityName;
    private String workAddrProvince;
    private String workAddrCity;
    private String workAddrDistrict;
    private String workAddrTown;
    private String workAddrVillage;
    private String workAddrDoor;
    private String contactAddrProvince;
    private String contactAddrCity;
    private String contactAddrCounty;
    private String contactAddrTown;
    private String contactAddrVillage;
    private String contactAddrDoor;
    private String livingAddrProvince;
    private String livingAddrCity;
    private String livingAddrCounty;
    private String livingAddrTown;
    private String livingAddrVillage;
    private String livingAddrDoor;
    private Date collectionDatetime;
    private String registeredAddrProvince;
    private String registeredAddrCity;
    private String registeredAddrDistrict;
    private String registeredAddrTown;
    private String registeredAddrDetail;
    private String registeredPostcode;
    private String collectionRegionCode;
    private String collectionOrgCode;
    private String collectionOrgName;
    private String registeredAddrVillage;
    private String dataUapId;
    private String stdLivingAddrAreaCode;
    private String stdLivingAddrArea;
    private String stdWorkAddrAreaCode;
    private String stdWorkAddrArea;
    private String stdRegisteredAddrAreaCode;
    private String stdRegisteredAddrArea;
    private String stdContactAddrAreaCode;
    private String stdContactAddrArea;
    private String companyCode;
    private String livingAddrProvinceCode;
    private String livingAddrCityCode;
    private String livingAddrCountyCode;
    private String livingAddrTownCode;
    private String livingAddrVillageCode;
    private String personTypeCode;
    private String personTypeName;

    public String getLocalPatientId() {
        return this.localPatientId;
    }

    public void setLocalPatientId(String localPatientId) {
        this.localPatientId = localPatientId;
    }

    public String getHisPatientId() {
        return this.hisPatientId;
    }

    public void setHisPatientId(String hisPatientId) {
        this.hisPatientId = hisPatientId;
    }

    public String getEhrId() {
        return this.ehrId;
    }

    public void setEhrId(String ehrId) {
        this.ehrId = ehrId;
    }

    public String getIdentityTypeCode() {
        return this.identityTypeCode;
    }

    public void setIdentityTypeCode(String identityTypeCode) {
        this.identityTypeCode = identityTypeCode;
    }

    public String getOtherIdCard() {
        return this.otherIdCard;
    }

    public void setOtherIdCard(String otherIdCard) {
        this.otherIdCard = otherIdCard;
    }

    public String getHealthCard() {
        return this.healthCard;
    }

    public void setHealthCard(String healthCard) {
        this.healthCard = healthCard;
    }

    public String getInsuranceCard() {
        return this.insuranceCard;
    }

    public void setInsuranceCard(String insuranceCard) {
        this.insuranceCard = insuranceCard;
    }

    public String getMedicalCardType() {
        return this.medicalCardType;
    }

    public void setMedicalCardType(String medicalCardType) {
        this.medicalCardType = medicalCardType;
    }

    public String getMedicalCard() {
        return this.medicalCard;
    }

    public void setMedicalCard(String medicalCard) {
        this.medicalCard = medicalCard;
    }

    public String getPassport() {
        return this.passport;
    }

    public void setPassport(String passport) {
        this.passport = passport;
    }

    public String getImmuneCard() {
        return this.immuneCard;
    }

    public void setImmuneCard(String immuneCard) {
        this.immuneCard = immuneCard;
    }

    public String getRuralCard() {
        return this.ruralCard;
    }

    public void setRuralCard(String ruralCard) {
        this.ruralCard = ruralCard;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getSexCode() {
        return this.sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public Date getBirthday() {
        return this.birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Date getDeathDate() {
        return this.deathDate;
    }

    public void setDeathDate(Date deathDate) {
        this.deathDate = deathDate;
    }

    public String getNationality() {
        return this.nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getEthnicity() {
        return this.ethnicity;
    }

    public void setEthnicity(String ethnicity) {
        this.ethnicity = ethnicity;
    }

    public String getMaritalStatus() {
        return this.maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public Date getWeddingDate() {
        return this.weddingDate;
    }

    public void setWeddingDate(Date weddingDate) {
        this.weddingDate = weddingDate;
    }

    public Integer getObstetricalMark() {
        return this.obstetricalMark;
    }

    public void setObstetricalMark(Integer obstetricalMark) {
        this.obstetricalMark = obstetricalMark;
    }

    public String getCareerType() {
        return this.careerType;
    }

    public void setCareerType(String careerType) {
        this.careerType = careerType;
    }

    public Date getCareerStartDate() {
        return this.careerStartDate;
    }

    public void setCareerStartDate(Date careerStartDate) {
        this.careerStartDate = careerStartDate;
    }

    public Date getCareerEndDate() {
        return this.careerEndDate;
    }

    public void setCareerEndDate(Date careerEndDate) {
        this.careerEndDate = careerEndDate;
    }

    public String getBloodAbo() {
        return this.bloodAbo;
    }

    public void setBloodAbo(String bloodAbo) {
        this.bloodAbo = bloodAbo;
    }

    public String getBloodRh() {
        return this.bloodRh;
    }

    public void setBloodRh(String bloodRh) {
        this.bloodRh = bloodRh;
    }

    public String getEconomicStatus() {
        return this.economicStatus;
    }

    public void setEconomicStatus(String economicStatus) {
        this.economicStatus = economicStatus;
    }

    public String getInsuranceType() {
        return this.insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getInsuranceName() {
        return this.insuranceName;
    }

    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName;
    }

    public String getReliefSource() {
        return this.reliefSource;
    }

    public void setReliefSource(String reliefSource) {
        this.reliefSource = reliefSource;
    }

    public String getMedicalPayWay() {
        return this.medicalPayWay;
    }

    public void setMedicalPayWay(String medicalPayWay) {
        this.medicalPayWay = medicalPayWay;
    }

    public String getCompany() {
        return this.company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getWorkAddrDetail() {
        return this.workAddrDetail;
    }

    public void setWorkAddrDetail(String workAddrDetail) {
        this.workAddrDetail = workAddrDetail;
    }

    public Long getWorkRegionCode() {
        return this.workRegionCode;
    }

    public void setWorkRegionCode(Long workRegionCode) {
        this.workRegionCode = workRegionCode;
    }

    public String getTelephone() {
        return this.telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWechat() {
        return this.wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getEducationCode() {
        return this.educationCode;
    }

    public void setEducationCode(String educationCode) {
        this.educationCode = educationCode;
    }

    public String getEducationName() {
        return this.educationName;
    }

    public void setEducationName(String educationName) {
        this.educationName = educationName;
    }

    public String getPhotoUrl() {
        return this.photoUrl;
    }

    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }

    public String getContactRelationCode() {
        return this.contactRelationCode;
    }

    public void setContactRelationCode(String contactRelationCode) {
        this.contactRelationCode = contactRelationCode;
    }

    public String getContactName() {
        return this.contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactTel() {
        return this.contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public String getContactAddrDetail() {
        return this.contactAddrDetail;
    }

    public void setContactAddrDetail(String contactAddrDetail) {
        this.contactAddrDetail = contactAddrDetail;
    }

    public Integer getResidentMark() {
        return this.residentMark;
    }

    public void setResidentMark(Integer residentMark) {
        this.residentMark = residentMark;
    }

    public String getLivingRegionCode() {
        return this.livingRegionCode;
    }

    public void setLivingRegionCode(String livingRegionCode) {
        this.livingRegionCode = livingRegionCode;
    }

    public String getLivingAddrDetail() {
        return this.livingAddrDetail;
    }

    public void setLivingAddrDetail(String livingAddrDetail) {
        this.livingAddrDetail = livingAddrDetail;
    }

    public String getLivingPostcode() {
        return this.livingPostcode;
    }

    public void setLivingPostcode(String livingPostcode) {
        this.livingPostcode = livingPostcode;
    }

    public String getGuardianRelationCode() {
        return this.guardianRelationCode;
    }

    public void setGuardianRelationCode(String guardianRelationCode) {
        this.guardianRelationCode = guardianRelationCode;
    }

    public String getGuardianId() {
        return this.guardianId;
    }

    public void setGuardianId(String guardianId) {
        this.guardianId = guardianId;
    }

    public String getGuardianName() {
        return this.guardianName;
    }

    public void setGuardianName(String guardianName) {
        this.guardianName = guardianName;
    }

    public Short getGuardianAge() {
        return this.guardianAge;
    }

    public void setGuardianAge(Short guardianAge) {
        this.guardianAge = guardianAge;
    }

    public String getGuardianNationality() {
        return this.guardianNationality;
    }

    public void setGuardianNationality(String guardianNationality) {
        this.guardianNationality = guardianNationality;
    }

    public String getGuardianEthnicity() {
        return this.guardianEthnicity;
    }

    public void setGuardianEthnicity(String guardianEthnicity) {
        this.guardianEthnicity = guardianEthnicity;
    }

    public String getGuardianTelephone() {
        return this.guardianTelephone;
    }

    public void setGuardianTelephone(String guardianTelephone) {
        this.guardianTelephone = guardianTelephone;
    }

    public String getUpdateOrgCode() {
        return this.updateOrgCode;
    }

    public void setUpdateOrgCode(String updateOrgCode) {
        this.updateOrgCode = updateOrgCode;
    }

    public String getUpdateOrgName() {
        return this.updateOrgName;
    }

    public void setUpdateOrgName(String updateOrgName) {
        this.updateOrgName = updateOrgName;
    }

    public String getUpdateId() {
        return this.updateId;
    }

    public void setUpdateId(String updateId) {
        this.updateId = updateId;
    }

    public String getUpdateName() {
        return this.updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Integer getFiveGuaranteesFlag() {
        return this.fiveGuaranteesFlag;
    }

    public void setFiveGuaranteesFlag(Integer fiveGuaranteesFlag) {
        this.fiveGuaranteesFlag = fiveGuaranteesFlag;
    }

    public Integer getChronicDiseaseFlag() {
        return this.chronicDiseaseFlag;
    }

    public void setChronicDiseaseFlag(Integer chronicDiseaseFlag) {
        this.chronicDiseaseFlag = chronicDiseaseFlag;
    }

    public String getResidentIdCard() {
        return this.residentIdCard;
    }

    public void setResidentIdCard(String residentIdCard) {
        this.residentIdCard = residentIdCard;
    }

    public String getNativePlace() {
        return this.nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getIdentityTypeName() {
        return this.identityTypeName;
    }

    public void setIdentityTypeName(String identityTypeName) {
        this.identityTypeName = identityTypeName;
    }

    public String getMedicalCardTypeName() {
        return this.medicalCardTypeName;
    }

    public void setMedicalCardTypeName(String medicalCardTypeName) {
        this.medicalCardTypeName = medicalCardTypeName;
    }

    public String getSexName() {
        return this.sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getNationalityName() {
        return this.nationalityName;
    }

    public void setNationalityName(String nationalityName) {
        this.nationalityName = nationalityName;
    }

    public String getEthnicityName() {
        return this.ethnicityName;
    }

    public void setEthnicityName(String ethnicityName) {
        this.ethnicityName = ethnicityName;
    }

    public String getMaritalName() {
        return this.maritalName;
    }

    public void setMaritalName(String maritalName) {
        this.maritalName = maritalName;
    }

    public String getCareerName() {
        return this.careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getBloodAboName() {
        return this.bloodAboName;
    }

    public void setBloodAboName(String bloodAboName) {
        this.bloodAboName = bloodAboName;
    }

    public String getBloodRhName() {
        return this.bloodRhName;
    }

    public void setBloodRhName(String bloodRhName) {
        this.bloodRhName = bloodRhName;
    }

    public String getEconomicName() {
        return this.economicName;
    }

    public void setEconomicName(String economicName) {
        this.economicName = economicName;
    }

    public String getReliefSourceName() {
        return this.reliefSourceName;
    }

    public void setReliefSourceName(String reliefSourceName) {
        this.reliefSourceName = reliefSourceName;
    }

    public String getMedicalPayName() {
        return this.medicalPayName;
    }

    public void setMedicalPayName(String medicalPayName) {
        this.medicalPayName = medicalPayName;
    }

    public String getContactRelationName() {
        return this.contactRelationName;
    }

    public void setContactRelationName(String contactRelationName) {
        this.contactRelationName = contactRelationName;
    }

    public String getGuardianRelationName() {
        return this.guardianRelationName;
    }

    public void setGuardianRelationName(String guardianRelationName) {
        this.guardianRelationName = guardianRelationName;
    }

    public String getGuardianNationalityName() {
        return this.guardianNationalityName;
    }

    public void setGuardianNationalityName(String guardianNationalityName) {
        this.guardianNationalityName = guardianNationalityName;
    }

    public String getGuardianEthnicityName() {
        return this.guardianEthnicityName;
    }

    public void setGuardianEthnicityName(String guardianEthnicityName) {
        this.guardianEthnicityName = guardianEthnicityName;
    }

    public String getWorkAddrProvince() {
        return this.workAddrProvince;
    }

    public void setWorkAddrProvince(String workAddrProvince) {
        this.workAddrProvince = workAddrProvince;
    }

    public String getWorkAddrCity() {
        return this.workAddrCity;
    }

    public void setWorkAddrCity(String workAddrCity) {
        this.workAddrCity = workAddrCity;
    }

    public String getWorkAddrDistrict() {
        return this.workAddrDistrict;
    }

    public void setWorkAddrDistrict(String workAddrDistrict) {
        this.workAddrDistrict = workAddrDistrict;
    }

    public String getWorkAddrTown() {
        return this.workAddrTown;
    }

    public void setWorkAddrTown(String workAddrTown) {
        this.workAddrTown = workAddrTown;
    }

    public String getWorkAddrVillage() {
        return this.workAddrVillage;
    }

    public void setWorkAddrVillage(String workAddrVillage) {
        this.workAddrVillage = workAddrVillage;
    }

    public String getWorkAddrDoor() {
        return this.workAddrDoor;
    }

    public void setWorkAddrDoor(String workAddrDoor) {
        this.workAddrDoor = workAddrDoor;
    }

    public String getContactAddrProvince() {
        return this.contactAddrProvince;
    }

    public void setContactAddrProvince(String contactAddrProvince) {
        this.contactAddrProvince = contactAddrProvince;
    }

    public String getContactAddrCity() {
        return this.contactAddrCity;
    }

    public void setContactAddrCity(String contactAddrCity) {
        this.contactAddrCity = contactAddrCity;
    }

    public String getContactAddrCounty() {
        return this.contactAddrCounty;
    }

    public void setContactAddrCounty(String contactAddrCounty) {
        this.contactAddrCounty = contactAddrCounty;
    }

    public String getContactAddrTown() {
        return this.contactAddrTown;
    }

    public void setContactAddrTown(String contactAddrTown) {
        this.contactAddrTown = contactAddrTown;
    }

    public String getContactAddrVillage() {
        return this.contactAddrVillage;
    }

    public void setContactAddrVillage(String contactAddrVillage) {
        this.contactAddrVillage = contactAddrVillage;
    }

    public String getContactAddrDoor() {
        return this.contactAddrDoor;
    }

    public void setContactAddrDoor(String contactAddrDoor) {
        this.contactAddrDoor = contactAddrDoor;
    }

    public String getLivingAddrProvince() {
        return this.livingAddrProvince;
    }

    public void setLivingAddrProvince(String livingAddrProvince) {
        this.livingAddrProvince = livingAddrProvince;
    }

    public String getLivingAddrCity() {
        return this.livingAddrCity;
    }

    public void setLivingAddrCity(String livingAddrCity) {
        this.livingAddrCity = livingAddrCity;
    }

    public String getLivingAddrCounty() {
        return this.livingAddrCounty;
    }

    public void setLivingAddrCounty(String livingAddrCounty) {
        this.livingAddrCounty = livingAddrCounty;
    }

    public String getLivingAddrTown() {
        return this.livingAddrTown;
    }

    public void setLivingAddrTown(String livingAddrTown) {
        this.livingAddrTown = livingAddrTown;
    }

    public String getLivingAddrVillage() {
        return this.livingAddrVillage;
    }

    public void setLivingAddrVillage(String livingAddrVillage) {
        this.livingAddrVillage = livingAddrVillage;
    }

    public String getLivingAddrDoor() {
        return this.livingAddrDoor;
    }

    public void setLivingAddrDoor(String livingAddrDoor) {
        this.livingAddrDoor = livingAddrDoor;
    }

    public Date getCollectionDatetime() {
        return this.collectionDatetime;
    }

    public void setCollectionDatetime(Date collectionDatetime) {
        this.collectionDatetime = collectionDatetime;
    }

    public String getRegisteredAddrProvince() {
        return this.registeredAddrProvince;
    }

    public void setRegisteredAddrProvince(String registeredAddrProvince) {
        this.registeredAddrProvince = registeredAddrProvince;
    }

    public String getRegisteredAddrCity() {
        return this.registeredAddrCity;
    }

    public void setRegisteredAddrCity(String registeredAddrCity) {
        this.registeredAddrCity = registeredAddrCity;
    }

    public String getRegisteredAddrDistrict() {
        return this.registeredAddrDistrict;
    }

    public void setRegisteredAddrDistrict(String registeredAddrDistrict) {
        this.registeredAddrDistrict = registeredAddrDistrict;
    }

    public String getRegisteredAddrTown() {
        return this.registeredAddrTown;
    }

    public void setRegisteredAddrTown(String registeredAddrTown) {
        this.registeredAddrTown = registeredAddrTown;
    }

    public String getRegisteredAddrDetail() {
        return this.registeredAddrDetail;
    }

    public void setRegisteredAddrDetail(String registeredAddrDetail) {
        this.registeredAddrDetail = registeredAddrDetail;
    }

    public String getRegisteredPostcode() {
        return this.registeredPostcode;
    }

    public void setRegisteredPostcode(String registeredPostcode) {
        this.registeredPostcode = registeredPostcode;
    }

    public String getCollectionRegionCode() {
        return this.collectionRegionCode;
    }

    public void setCollectionRegionCode(String collectionRegionCode) {
        this.collectionRegionCode = collectionRegionCode;
    }

    public String getCollectionOrgCode() {
        return this.collectionOrgCode;
    }

    public void setCollectionOrgCode(String collectionOrgCode) {
        this.collectionOrgCode = collectionOrgCode;
    }

    public String getCollectionOrgName() {
        return this.collectionOrgName;
    }

    public void setCollectionOrgName(String collectionOrgName) {
        this.collectionOrgName = collectionOrgName;
    }

    public String getRegisteredAddrVillage() {
        return this.registeredAddrVillage;
    }

    public void setRegisteredAddrVillage(String registeredAddrVillage) {
        this.registeredAddrVillage = registeredAddrVillage;
    }

    public String getDataUapId() {
        return this.dataUapId;
    }

    public void setDataUapId(String dataUapId) {
        this.dataUapId = dataUapId;
    }

    public String getStdLivingAddrAreaCode() {
        return this.stdLivingAddrAreaCode;
    }

    public void setStdLivingAddrAreaCode(String stdLivingAddrAreaCode) {
        this.stdLivingAddrAreaCode = stdLivingAddrAreaCode;
    }

    public String getStdLivingAddrArea() {
        return this.stdLivingAddrArea;
    }

    public void setStdLivingAddrArea(String stdLivingAddrArea) {
        this.stdLivingAddrArea = stdLivingAddrArea;
    }

    public String getStdWorkAddrAreaCode() {
        return this.stdWorkAddrAreaCode;
    }

    public void setStdWorkAddrAreaCode(String stdWorkAddrAreaCode) {
        this.stdWorkAddrAreaCode = stdWorkAddrAreaCode;
    }

    public String getStdWorkAddrArea() {
        return this.stdWorkAddrArea;
    }

    public void setStdWorkAddrArea(String stdWorkAddrArea) {
        this.stdWorkAddrArea = stdWorkAddrArea;
    }

    public String getStdRegisteredAddrAreaCode() {
        return this.stdRegisteredAddrAreaCode;
    }

    public void setStdRegisteredAddrAreaCode(String stdRegisteredAddrAreaCode) {
        this.stdRegisteredAddrAreaCode = stdRegisteredAddrAreaCode;
    }

    public String getStdRegisteredAddrArea() {
        return this.stdRegisteredAddrArea;
    }

    public void setStdRegisteredAddrArea(String stdRegisteredAddrArea) {
        this.stdRegisteredAddrArea = stdRegisteredAddrArea;
    }

    public String getStdContactAddrAreaCode() {
        return this.stdContactAddrAreaCode;
    }

    public void setStdContactAddrAreaCode(String stdContactAddrAreaCode) {
        this.stdContactAddrAreaCode = stdContactAddrAreaCode;
    }

    public String getStdContactAddrArea() {
        return this.stdContactAddrArea;
    }

    public void setStdContactAddrArea(String stdContactAddrArea) {
        this.stdContactAddrArea = stdContactAddrArea;
    }

    public String getCompanyCode() {
        return this.companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getLivingAddrProvinceCode() {
        return this.livingAddrProvinceCode;
    }

    public void setLivingAddrProvinceCode(String livingAddrProvinceCode) {
        this.livingAddrProvinceCode = livingAddrProvinceCode;
    }

    public String getLivingAddrCityCode() {
        return this.livingAddrCityCode;
    }

    public void setLivingAddrCityCode(String livingAddrCityCode) {
        this.livingAddrCityCode = livingAddrCityCode;
    }

    public String getLivingAddrCountyCode() {
        return this.livingAddrCountyCode;
    }

    public void setLivingAddrCountyCode(String livingAddrCountyCode) {
        this.livingAddrCountyCode = livingAddrCountyCode;
    }

    public String getLivingAddrTownCode() {
        return this.livingAddrTownCode;
    }

    public void setLivingAddrTownCode(String livingAddrTownCode) {
        this.livingAddrTownCode = livingAddrTownCode;
    }

    public String getLivingAddrVillageCode() {
        return this.livingAddrVillageCode;
    }

    public void setLivingAddrVillageCode(String livingAddrVillageCode) {
        this.livingAddrVillageCode = livingAddrVillageCode;
    }

    public String getPersonTypeCode() {
        return this.personTypeCode;
    }

    public void setPersonTypeCode(String personTypeCode) {
        this.personTypeCode = personTypeCode;
    }

    public String getPersonTypeName() {
        return this.personTypeName;
    }

    public void setPersonTypeName(String personTypeName) {
        this.personTypeName = personTypeName;
    }

    public PatientBaseInfoDto() {
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof PatientBaseInfoDto)) {
            return false;
        } else {
            PatientBaseInfoDto other = (PatientBaseInfoDto)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label1511: {
                    Object this$localPatientId = this.getLocalPatientId();
                    Object other$localPatientId = other.getLocalPatientId();
                    if (this$localPatientId == null) {
                        if (other$localPatientId == null) {
                            break label1511;
                        }
                    } else if (this$localPatientId.equals(other$localPatientId)) {
                        break label1511;
                    }

                    return false;
                }

                Object this$hisPatientId = this.getHisPatientId();
                Object other$hisPatientId = other.getHisPatientId();
                if (this$hisPatientId == null) {
                    if (other$hisPatientId != null) {
                        return false;
                    }
                } else if (!this$hisPatientId.equals(other$hisPatientId)) {
                    return false;
                }

                label1497: {
                    Object this$ehrId = this.getEhrId();
                    Object other$ehrId = other.getEhrId();
                    if (this$ehrId == null) {
                        if (other$ehrId == null) {
                            break label1497;
                        }
                    } else if (this$ehrId.equals(other$ehrId)) {
                        break label1497;
                    }

                    return false;
                }

                Object this$identityTypeCode = this.getIdentityTypeCode();
                Object other$identityTypeCode = other.getIdentityTypeCode();
                if (this$identityTypeCode == null) {
                    if (other$identityTypeCode != null) {
                        return false;
                    }
                } else if (!this$identityTypeCode.equals(other$identityTypeCode)) {
                    return false;
                }

                label1483: {
                    Object this$otherIdCard = this.getOtherIdCard();
                    Object other$otherIdCard = other.getOtherIdCard();
                    if (this$otherIdCard == null) {
                        if (other$otherIdCard == null) {
                            break label1483;
                        }
                    } else if (this$otherIdCard.equals(other$otherIdCard)) {
                        break label1483;
                    }

                    return false;
                }

                Object this$healthCard = this.getHealthCard();
                Object other$healthCard = other.getHealthCard();
                if (this$healthCard == null) {
                    if (other$healthCard != null) {
                        return false;
                    }
                } else if (!this$healthCard.equals(other$healthCard)) {
                    return false;
                }

                label1469: {
                    Object this$insuranceCard = this.getInsuranceCard();
                    Object other$insuranceCard = other.getInsuranceCard();
                    if (this$insuranceCard == null) {
                        if (other$insuranceCard == null) {
                            break label1469;
                        }
                    } else if (this$insuranceCard.equals(other$insuranceCard)) {
                        break label1469;
                    }

                    return false;
                }

                label1462: {
                    Object this$medicalCardType = this.getMedicalCardType();
                    Object other$medicalCardType = other.getMedicalCardType();
                    if (this$medicalCardType == null) {
                        if (other$medicalCardType == null) {
                            break label1462;
                        }
                    } else if (this$medicalCardType.equals(other$medicalCardType)) {
                        break label1462;
                    }

                    return false;
                }

                Object this$medicalCard = this.getMedicalCard();
                Object other$medicalCard = other.getMedicalCard();
                if (this$medicalCard == null) {
                    if (other$medicalCard != null) {
                        return false;
                    }
                } else if (!this$medicalCard.equals(other$medicalCard)) {
                    return false;
                }

                label1448: {
                    Object this$passport = this.getPassport();
                    Object other$passport = other.getPassport();
                    if (this$passport == null) {
                        if (other$passport == null) {
                            break label1448;
                        }
                    } else if (this$passport.equals(other$passport)) {
                        break label1448;
                    }

                    return false;
                }

                label1441: {
                    Object this$immuneCard = this.getImmuneCard();
                    Object other$immuneCard = other.getImmuneCard();
                    if (this$immuneCard == null) {
                        if (other$immuneCard == null) {
                            break label1441;
                        }
                    } else if (this$immuneCard.equals(other$immuneCard)) {
                        break label1441;
                    }

                    return false;
                }

                Object this$ruralCard = this.getRuralCard();
                Object other$ruralCard = other.getRuralCard();
                if (this$ruralCard == null) {
                    if (other$ruralCard != null) {
                        return false;
                    }
                } else if (!this$ruralCard.equals(other$ruralCard)) {
                    return false;
                }

                Object this$patientName = this.getPatientName();
                Object other$patientName = other.getPatientName();
                if (this$patientName == null) {
                    if (other$patientName != null) {
                        return false;
                    }
                } else if (!this$patientName.equals(other$patientName)) {
                    return false;
                }

                label1420: {
                    Object this$sexCode = this.getSexCode();
                    Object other$sexCode = other.getSexCode();
                    if (this$sexCode == null) {
                        if (other$sexCode == null) {
                            break label1420;
                        }
                    } else if (this$sexCode.equals(other$sexCode)) {
                        break label1420;
                    }

                    return false;
                }

                Object this$birthday = this.getBirthday();
                Object other$birthday = other.getBirthday();
                if (this$birthday == null) {
                    if (other$birthday != null) {
                        return false;
                    }
                } else if (!this$birthday.equals(other$birthday)) {
                    return false;
                }

                Object this$deathDate = this.getDeathDate();
                Object other$deathDate = other.getDeathDate();
                if (this$deathDate == null) {
                    if (other$deathDate != null) {
                        return false;
                    }
                } else if (!this$deathDate.equals(other$deathDate)) {
                    return false;
                }

                label1399: {
                    Object this$nationality = this.getNationality();
                    Object other$nationality = other.getNationality();
                    if (this$nationality == null) {
                        if (other$nationality == null) {
                            break label1399;
                        }
                    } else if (this$nationality.equals(other$nationality)) {
                        break label1399;
                    }

                    return false;
                }

                Object this$ethnicity = this.getEthnicity();
                Object other$ethnicity = other.getEthnicity();
                if (this$ethnicity == null) {
                    if (other$ethnicity != null) {
                        return false;
                    }
                } else if (!this$ethnicity.equals(other$ethnicity)) {
                    return false;
                }

                label1385: {
                    Object this$maritalStatus = this.getMaritalStatus();
                    Object other$maritalStatus = other.getMaritalStatus();
                    if (this$maritalStatus == null) {
                        if (other$maritalStatus == null) {
                            break label1385;
                        }
                    } else if (this$maritalStatus.equals(other$maritalStatus)) {
                        break label1385;
                    }

                    return false;
                }

                Object this$weddingDate = this.getWeddingDate();
                Object other$weddingDate = other.getWeddingDate();
                if (this$weddingDate == null) {
                    if (other$weddingDate != null) {
                        return false;
                    }
                } else if (!this$weddingDate.equals(other$weddingDate)) {
                    return false;
                }

                label1371: {
                    Object this$obstetricalMark = this.getObstetricalMark();
                    Object other$obstetricalMark = other.getObstetricalMark();
                    if (this$obstetricalMark == null) {
                        if (other$obstetricalMark == null) {
                            break label1371;
                        }
                    } else if (this$obstetricalMark.equals(other$obstetricalMark)) {
                        break label1371;
                    }

                    return false;
                }

                Object this$careerType = this.getCareerType();
                Object other$careerType = other.getCareerType();
                if (this$careerType == null) {
                    if (other$careerType != null) {
                        return false;
                    }
                } else if (!this$careerType.equals(other$careerType)) {
                    return false;
                }

                label1357: {
                    Object this$careerStartDate = this.getCareerStartDate();
                    Object other$careerStartDate = other.getCareerStartDate();
                    if (this$careerStartDate == null) {
                        if (other$careerStartDate == null) {
                            break label1357;
                        }
                    } else if (this$careerStartDate.equals(other$careerStartDate)) {
                        break label1357;
                    }

                    return false;
                }

                label1350: {
                    Object this$careerEndDate = this.getCareerEndDate();
                    Object other$careerEndDate = other.getCareerEndDate();
                    if (this$careerEndDate == null) {
                        if (other$careerEndDate == null) {
                            break label1350;
                        }
                    } else if (this$careerEndDate.equals(other$careerEndDate)) {
                        break label1350;
                    }

                    return false;
                }

                Object this$bloodAbo = this.getBloodAbo();
                Object other$bloodAbo = other.getBloodAbo();
                if (this$bloodAbo == null) {
                    if (other$bloodAbo != null) {
                        return false;
                    }
                } else if (!this$bloodAbo.equals(other$bloodAbo)) {
                    return false;
                }

                label1336: {
                    Object this$bloodRh = this.getBloodRh();
                    Object other$bloodRh = other.getBloodRh();
                    if (this$bloodRh == null) {
                        if (other$bloodRh == null) {
                            break label1336;
                        }
                    } else if (this$bloodRh.equals(other$bloodRh)) {
                        break label1336;
                    }

                    return false;
                }

                label1329: {
                    Object this$economicStatus = this.getEconomicStatus();
                    Object other$economicStatus = other.getEconomicStatus();
                    if (this$economicStatus == null) {
                        if (other$economicStatus == null) {
                            break label1329;
                        }
                    } else if (this$economicStatus.equals(other$economicStatus)) {
                        break label1329;
                    }

                    return false;
                }

                Object this$insuranceType = this.getInsuranceType();
                Object other$insuranceType = other.getInsuranceType();
                if (this$insuranceType == null) {
                    if (other$insuranceType != null) {
                        return false;
                    }
                } else if (!this$insuranceType.equals(other$insuranceType)) {
                    return false;
                }

                Object this$insuranceName = this.getInsuranceName();
                Object other$insuranceName = other.getInsuranceName();
                if (this$insuranceName == null) {
                    if (other$insuranceName != null) {
                        return false;
                    }
                } else if (!this$insuranceName.equals(other$insuranceName)) {
                    return false;
                }

                label1308: {
                    Object this$reliefSource = this.getReliefSource();
                    Object other$reliefSource = other.getReliefSource();
                    if (this$reliefSource == null) {
                        if (other$reliefSource == null) {
                            break label1308;
                        }
                    } else if (this$reliefSource.equals(other$reliefSource)) {
                        break label1308;
                    }

                    return false;
                }

                Object this$medicalPayWay = this.getMedicalPayWay();
                Object other$medicalPayWay = other.getMedicalPayWay();
                if (this$medicalPayWay == null) {
                    if (other$medicalPayWay != null) {
                        return false;
                    }
                } else if (!this$medicalPayWay.equals(other$medicalPayWay)) {
                    return false;
                }

                Object this$company = this.getCompany();
                Object other$company = other.getCompany();
                if (this$company == null) {
                    if (other$company != null) {
                        return false;
                    }
                } else if (!this$company.equals(other$company)) {
                    return false;
                }

                label1287: {
                    Object this$workAddrDetail = this.getWorkAddrDetail();
                    Object other$workAddrDetail = other.getWorkAddrDetail();
                    if (this$workAddrDetail == null) {
                        if (other$workAddrDetail == null) {
                            break label1287;
                        }
                    } else if (this$workAddrDetail.equals(other$workAddrDetail)) {
                        break label1287;
                    }

                    return false;
                }

                Object this$workRegionCode = this.getWorkRegionCode();
                Object other$workRegionCode = other.getWorkRegionCode();
                if (this$workRegionCode == null) {
                    if (other$workRegionCode != null) {
                        return false;
                    }
                } else if (!this$workRegionCode.equals(other$workRegionCode)) {
                    return false;
                }

                label1273: {
                    Object this$telephone = this.getTelephone();
                    Object other$telephone = other.getTelephone();
                    if (this$telephone == null) {
                        if (other$telephone == null) {
                            break label1273;
                        }
                    } else if (this$telephone.equals(other$telephone)) {
                        break label1273;
                    }

                    return false;
                }

                Object this$email = this.getEmail();
                Object other$email = other.getEmail();
                if (this$email == null) {
                    if (other$email != null) {
                        return false;
                    }
                } else if (!this$email.equals(other$email)) {
                    return false;
                }

                label1259: {
                    Object this$wechat = this.getWechat();
                    Object other$wechat = other.getWechat();
                    if (this$wechat == null) {
                        if (other$wechat == null) {
                            break label1259;
                        }
                    } else if (this$wechat.equals(other$wechat)) {
                        break label1259;
                    }

                    return false;
                }

                Object this$educationCode = this.getEducationCode();
                Object other$educationCode = other.getEducationCode();
                if (this$educationCode == null) {
                    if (other$educationCode != null) {
                        return false;
                    }
                } else if (!this$educationCode.equals(other$educationCode)) {
                    return false;
                }

                label1245: {
                    Object this$educationName = this.getEducationName();
                    Object other$educationName = other.getEducationName();
                    if (this$educationName == null) {
                        if (other$educationName == null) {
                            break label1245;
                        }
                    } else if (this$educationName.equals(other$educationName)) {
                        break label1245;
                    }

                    return false;
                }

                label1238: {
                    Object this$photoUrl = this.getPhotoUrl();
                    Object other$photoUrl = other.getPhotoUrl();
                    if (this$photoUrl == null) {
                        if (other$photoUrl == null) {
                            break label1238;
                        }
                    } else if (this$photoUrl.equals(other$photoUrl)) {
                        break label1238;
                    }

                    return false;
                }

                Object this$contactRelationCode = this.getContactRelationCode();
                Object other$contactRelationCode = other.getContactRelationCode();
                if (this$contactRelationCode == null) {
                    if (other$contactRelationCode != null) {
                        return false;
                    }
                } else if (!this$contactRelationCode.equals(other$contactRelationCode)) {
                    return false;
                }

                label1224: {
                    Object this$contactName = this.getContactName();
                    Object other$contactName = other.getContactName();
                    if (this$contactName == null) {
                        if (other$contactName == null) {
                            break label1224;
                        }
                    } else if (this$contactName.equals(other$contactName)) {
                        break label1224;
                    }

                    return false;
                }

                label1217: {
                    Object this$contactTel = this.getContactTel();
                    Object other$contactTel = other.getContactTel();
                    if (this$contactTel == null) {
                        if (other$contactTel == null) {
                            break label1217;
                        }
                    } else if (this$contactTel.equals(other$contactTel)) {
                        break label1217;
                    }

                    return false;
                }

                Object this$contactAddrDetail = this.getContactAddrDetail();
                Object other$contactAddrDetail = other.getContactAddrDetail();
                if (this$contactAddrDetail == null) {
                    if (other$contactAddrDetail != null) {
                        return false;
                    }
                } else if (!this$contactAddrDetail.equals(other$contactAddrDetail)) {
                    return false;
                }

                Object this$residentMark = this.getResidentMark();
                Object other$residentMark = other.getResidentMark();
                if (this$residentMark == null) {
                    if (other$residentMark != null) {
                        return false;
                    }
                } else if (!this$residentMark.equals(other$residentMark)) {
                    return false;
                }

                label1196: {
                    Object this$livingRegionCode = this.getLivingRegionCode();
                    Object other$livingRegionCode = other.getLivingRegionCode();
                    if (this$livingRegionCode == null) {
                        if (other$livingRegionCode == null) {
                            break label1196;
                        }
                    } else if (this$livingRegionCode.equals(other$livingRegionCode)) {
                        break label1196;
                    }

                    return false;
                }

                Object this$livingAddrDetail = this.getLivingAddrDetail();
                Object other$livingAddrDetail = other.getLivingAddrDetail();
                if (this$livingAddrDetail == null) {
                    if (other$livingAddrDetail != null) {
                        return false;
                    }
                } else if (!this$livingAddrDetail.equals(other$livingAddrDetail)) {
                    return false;
                }

                Object this$livingPostcode = this.getLivingPostcode();
                Object other$livingPostcode = other.getLivingPostcode();
                if (this$livingPostcode == null) {
                    if (other$livingPostcode != null) {
                        return false;
                    }
                } else if (!this$livingPostcode.equals(other$livingPostcode)) {
                    return false;
                }

                label1175: {
                    Object this$guardianRelationCode = this.getGuardianRelationCode();
                    Object other$guardianRelationCode = other.getGuardianRelationCode();
                    if (this$guardianRelationCode == null) {
                        if (other$guardianRelationCode == null) {
                            break label1175;
                        }
                    } else if (this$guardianRelationCode.equals(other$guardianRelationCode)) {
                        break label1175;
                    }

                    return false;
                }

                Object this$guardianId = this.getGuardianId();
                Object other$guardianId = other.getGuardianId();
                if (this$guardianId == null) {
                    if (other$guardianId != null) {
                        return false;
                    }
                } else if (!this$guardianId.equals(other$guardianId)) {
                    return false;
                }

                label1161: {
                    Object this$guardianName = this.getGuardianName();
                    Object other$guardianName = other.getGuardianName();
                    if (this$guardianName == null) {
                        if (other$guardianName == null) {
                            break label1161;
                        }
                    } else if (this$guardianName.equals(other$guardianName)) {
                        break label1161;
                    }

                    return false;
                }

                Object this$guardianAge = this.getGuardianAge();
                Object other$guardianAge = other.getGuardianAge();
                if (this$guardianAge == null) {
                    if (other$guardianAge != null) {
                        return false;
                    }
                } else if (!this$guardianAge.equals(other$guardianAge)) {
                    return false;
                }

                label1147: {
                    Object this$guardianNationality = this.getGuardianNationality();
                    Object other$guardianNationality = other.getGuardianNationality();
                    if (this$guardianNationality == null) {
                        if (other$guardianNationality == null) {
                            break label1147;
                        }
                    } else if (this$guardianNationality.equals(other$guardianNationality)) {
                        break label1147;
                    }

                    return false;
                }

                Object this$guardianEthnicity = this.getGuardianEthnicity();
                Object other$guardianEthnicity = other.getGuardianEthnicity();
                if (this$guardianEthnicity == null) {
                    if (other$guardianEthnicity != null) {
                        return false;
                    }
                } else if (!this$guardianEthnicity.equals(other$guardianEthnicity)) {
                    return false;
                }

                label1133: {
                    Object this$guardianTelephone = this.getGuardianTelephone();
                    Object other$guardianTelephone = other.getGuardianTelephone();
                    if (this$guardianTelephone == null) {
                        if (other$guardianTelephone == null) {
                            break label1133;
                        }
                    } else if (this$guardianTelephone.equals(other$guardianTelephone)) {
                        break label1133;
                    }

                    return false;
                }

                label1126: {
                    Object this$updateOrgCode = this.getUpdateOrgCode();
                    Object other$updateOrgCode = other.getUpdateOrgCode();
                    if (this$updateOrgCode == null) {
                        if (other$updateOrgCode == null) {
                            break label1126;
                        }
                    } else if (this$updateOrgCode.equals(other$updateOrgCode)) {
                        break label1126;
                    }

                    return false;
                }

                Object this$updateOrgName = this.getUpdateOrgName();
                Object other$updateOrgName = other.getUpdateOrgName();
                if (this$updateOrgName == null) {
                    if (other$updateOrgName != null) {
                        return false;
                    }
                } else if (!this$updateOrgName.equals(other$updateOrgName)) {
                    return false;
                }

                label1112: {
                    Object this$updateId = this.getUpdateId();
                    Object other$updateId = other.getUpdateId();
                    if (this$updateId == null) {
                        if (other$updateId == null) {
                            break label1112;
                        }
                    } else if (this$updateId.equals(other$updateId)) {
                        break label1112;
                    }

                    return false;
                }

                label1105: {
                    Object this$updateName = this.getUpdateName();
                    Object other$updateName = other.getUpdateName();
                    if (this$updateName == null) {
                        if (other$updateName == null) {
                            break label1105;
                        }
                    } else if (this$updateName.equals(other$updateName)) {
                        break label1105;
                    }

                    return false;
                }

                Object this$fiveGuaranteesFlag = this.getFiveGuaranteesFlag();
                Object other$fiveGuaranteesFlag = other.getFiveGuaranteesFlag();
                if (this$fiveGuaranteesFlag == null) {
                    if (other$fiveGuaranteesFlag != null) {
                        return false;
                    }
                } else if (!this$fiveGuaranteesFlag.equals(other$fiveGuaranteesFlag)) {
                    return false;
                }

                Object this$chronicDiseaseFlag = this.getChronicDiseaseFlag();
                Object other$chronicDiseaseFlag = other.getChronicDiseaseFlag();
                if (this$chronicDiseaseFlag == null) {
                    if (other$chronicDiseaseFlag != null) {
                        return false;
                    }
                } else if (!this$chronicDiseaseFlag.equals(other$chronicDiseaseFlag)) {
                    return false;
                }

                label1084: {
                    Object this$residentIdCard = this.getResidentIdCard();
                    Object other$residentIdCard = other.getResidentIdCard();
                    if (this$residentIdCard == null) {
                        if (other$residentIdCard == null) {
                            break label1084;
                        }
                    } else if (this$residentIdCard.equals(other$residentIdCard)) {
                        break label1084;
                    }

                    return false;
                }

                Object this$nativePlace = this.getNativePlace();
                Object other$nativePlace = other.getNativePlace();
                if (this$nativePlace == null) {
                    if (other$nativePlace != null) {
                        return false;
                    }
                } else if (!this$nativePlace.equals(other$nativePlace)) {
                    return false;
                }

                Object this$identityTypeName = this.getIdentityTypeName();
                Object other$identityTypeName = other.getIdentityTypeName();
                if (this$identityTypeName == null) {
                    if (other$identityTypeName != null) {
                        return false;
                    }
                } else if (!this$identityTypeName.equals(other$identityTypeName)) {
                    return false;
                }

                label1063: {
                    Object this$medicalCardTypeName = this.getMedicalCardTypeName();
                    Object other$medicalCardTypeName = other.getMedicalCardTypeName();
                    if (this$medicalCardTypeName == null) {
                        if (other$medicalCardTypeName == null) {
                            break label1063;
                        }
                    } else if (this$medicalCardTypeName.equals(other$medicalCardTypeName)) {
                        break label1063;
                    }

                    return false;
                }

                Object this$sexName = this.getSexName();
                Object other$sexName = other.getSexName();
                if (this$sexName == null) {
                    if (other$sexName != null) {
                        return false;
                    }
                } else if (!this$sexName.equals(other$sexName)) {
                    return false;
                }

                label1049: {
                    Object this$nationalityName = this.getNationalityName();
                    Object other$nationalityName = other.getNationalityName();
                    if (this$nationalityName == null) {
                        if (other$nationalityName == null) {
                            break label1049;
                        }
                    } else if (this$nationalityName.equals(other$nationalityName)) {
                        break label1049;
                    }

                    return false;
                }

                Object this$ethnicityName = this.getEthnicityName();
                Object other$ethnicityName = other.getEthnicityName();
                if (this$ethnicityName == null) {
                    if (other$ethnicityName != null) {
                        return false;
                    }
                } else if (!this$ethnicityName.equals(other$ethnicityName)) {
                    return false;
                }

                label1035: {
                    Object this$maritalName = this.getMaritalName();
                    Object other$maritalName = other.getMaritalName();
                    if (this$maritalName == null) {
                        if (other$maritalName == null) {
                            break label1035;
                        }
                    } else if (this$maritalName.equals(other$maritalName)) {
                        break label1035;
                    }

                    return false;
                }

                Object this$careerName = this.getCareerName();
                Object other$careerName = other.getCareerName();
                if (this$careerName == null) {
                    if (other$careerName != null) {
                        return false;
                    }
                } else if (!this$careerName.equals(other$careerName)) {
                    return false;
                }

                label1021: {
                    Object this$bloodAboName = this.getBloodAboName();
                    Object other$bloodAboName = other.getBloodAboName();
                    if (this$bloodAboName == null) {
                        if (other$bloodAboName == null) {
                            break label1021;
                        }
                    } else if (this$bloodAboName.equals(other$bloodAboName)) {
                        break label1021;
                    }

                    return false;
                }

                label1014: {
                    Object this$bloodRhName = this.getBloodRhName();
                    Object other$bloodRhName = other.getBloodRhName();
                    if (this$bloodRhName == null) {
                        if (other$bloodRhName == null) {
                            break label1014;
                        }
                    } else if (this$bloodRhName.equals(other$bloodRhName)) {
                        break label1014;
                    }

                    return false;
                }

                Object this$economicName = this.getEconomicName();
                Object other$economicName = other.getEconomicName();
                if (this$economicName == null) {
                    if (other$economicName != null) {
                        return false;
                    }
                } else if (!this$economicName.equals(other$economicName)) {
                    return false;
                }

                label1000: {
                    Object this$reliefSourceName = this.getReliefSourceName();
                    Object other$reliefSourceName = other.getReliefSourceName();
                    if (this$reliefSourceName == null) {
                        if (other$reliefSourceName == null) {
                            break label1000;
                        }
                    } else if (this$reliefSourceName.equals(other$reliefSourceName)) {
                        break label1000;
                    }

                    return false;
                }

                label993: {
                    Object this$medicalPayName = this.getMedicalPayName();
                    Object other$medicalPayName = other.getMedicalPayName();
                    if (this$medicalPayName == null) {
                        if (other$medicalPayName == null) {
                            break label993;
                        }
                    } else if (this$medicalPayName.equals(other$medicalPayName)) {
                        break label993;
                    }

                    return false;
                }

                Object this$contactRelationName = this.getContactRelationName();
                Object other$contactRelationName = other.getContactRelationName();
                if (this$contactRelationName == null) {
                    if (other$contactRelationName != null) {
                        return false;
                    }
                } else if (!this$contactRelationName.equals(other$contactRelationName)) {
                    return false;
                }

                Object this$guardianRelationName = this.getGuardianRelationName();
                Object other$guardianRelationName = other.getGuardianRelationName();
                if (this$guardianRelationName == null) {
                    if (other$guardianRelationName != null) {
                        return false;
                    }
                } else if (!this$guardianRelationName.equals(other$guardianRelationName)) {
                    return false;
                }

                label972: {
                    Object this$guardianNationalityName = this.getGuardianNationalityName();
                    Object other$guardianNationalityName = other.getGuardianNationalityName();
                    if (this$guardianNationalityName == null) {
                        if (other$guardianNationalityName == null) {
                            break label972;
                        }
                    } else if (this$guardianNationalityName.equals(other$guardianNationalityName)) {
                        break label972;
                    }

                    return false;
                }

                Object this$guardianEthnicityName = this.getGuardianEthnicityName();
                Object other$guardianEthnicityName = other.getGuardianEthnicityName();
                if (this$guardianEthnicityName == null) {
                    if (other$guardianEthnicityName != null) {
                        return false;
                    }
                } else if (!this$guardianEthnicityName.equals(other$guardianEthnicityName)) {
                    return false;
                }

                Object this$workAddrProvince = this.getWorkAddrProvince();
                Object other$workAddrProvince = other.getWorkAddrProvince();
                if (this$workAddrProvince == null) {
                    if (other$workAddrProvince != null) {
                        return false;
                    }
                } else if (!this$workAddrProvince.equals(other$workAddrProvince)) {
                    return false;
                }

                label951: {
                    Object this$workAddrCity = this.getWorkAddrCity();
                    Object other$workAddrCity = other.getWorkAddrCity();
                    if (this$workAddrCity == null) {
                        if (other$workAddrCity == null) {
                            break label951;
                        }
                    } else if (this$workAddrCity.equals(other$workAddrCity)) {
                        break label951;
                    }

                    return false;
                }

                Object this$workAddrDistrict = this.getWorkAddrDistrict();
                Object other$workAddrDistrict = other.getWorkAddrDistrict();
                if (this$workAddrDistrict == null) {
                    if (other$workAddrDistrict != null) {
                        return false;
                    }
                } else if (!this$workAddrDistrict.equals(other$workAddrDistrict)) {
                    return false;
                }

                label937: {
                    Object this$workAddrTown = this.getWorkAddrTown();
                    Object other$workAddrTown = other.getWorkAddrTown();
                    if (this$workAddrTown == null) {
                        if (other$workAddrTown == null) {
                            break label937;
                        }
                    } else if (this$workAddrTown.equals(other$workAddrTown)) {
                        break label937;
                    }

                    return false;
                }

                Object this$workAddrVillage = this.getWorkAddrVillage();
                Object other$workAddrVillage = other.getWorkAddrVillage();
                if (this$workAddrVillage == null) {
                    if (other$workAddrVillage != null) {
                        return false;
                    }
                } else if (!this$workAddrVillage.equals(other$workAddrVillage)) {
                    return false;
                }

                label923: {
                    Object this$workAddrDoor = this.getWorkAddrDoor();
                    Object other$workAddrDoor = other.getWorkAddrDoor();
                    if (this$workAddrDoor == null) {
                        if (other$workAddrDoor == null) {
                            break label923;
                        }
                    } else if (this$workAddrDoor.equals(other$workAddrDoor)) {
                        break label923;
                    }

                    return false;
                }

                Object this$contactAddrProvince = this.getContactAddrProvince();
                Object other$contactAddrProvince = other.getContactAddrProvince();
                if (this$contactAddrProvince == null) {
                    if (other$contactAddrProvince != null) {
                        return false;
                    }
                } else if (!this$contactAddrProvince.equals(other$contactAddrProvince)) {
                    return false;
                }

                label909: {
                    Object this$contactAddrCity = this.getContactAddrCity();
                    Object other$contactAddrCity = other.getContactAddrCity();
                    if (this$contactAddrCity == null) {
                        if (other$contactAddrCity == null) {
                            break label909;
                        }
                    } else if (this$contactAddrCity.equals(other$contactAddrCity)) {
                        break label909;
                    }

                    return false;
                }

                label902: {
                    Object this$contactAddrCounty = this.getContactAddrCounty();
                    Object other$contactAddrCounty = other.getContactAddrCounty();
                    if (this$contactAddrCounty == null) {
                        if (other$contactAddrCounty == null) {
                            break label902;
                        }
                    } else if (this$contactAddrCounty.equals(other$contactAddrCounty)) {
                        break label902;
                    }

                    return false;
                }

                Object this$contactAddrTown = this.getContactAddrTown();
                Object other$contactAddrTown = other.getContactAddrTown();
                if (this$contactAddrTown == null) {
                    if (other$contactAddrTown != null) {
                        return false;
                    }
                } else if (!this$contactAddrTown.equals(other$contactAddrTown)) {
                    return false;
                }

                label888: {
                    Object this$contactAddrVillage = this.getContactAddrVillage();
                    Object other$contactAddrVillage = other.getContactAddrVillage();
                    if (this$contactAddrVillage == null) {
                        if (other$contactAddrVillage == null) {
                            break label888;
                        }
                    } else if (this$contactAddrVillage.equals(other$contactAddrVillage)) {
                        break label888;
                    }

                    return false;
                }

                label881: {
                    Object this$contactAddrDoor = this.getContactAddrDoor();
                    Object other$contactAddrDoor = other.getContactAddrDoor();
                    if (this$contactAddrDoor == null) {
                        if (other$contactAddrDoor == null) {
                            break label881;
                        }
                    } else if (this$contactAddrDoor.equals(other$contactAddrDoor)) {
                        break label881;
                    }

                    return false;
                }

                Object this$livingAddrProvince = this.getLivingAddrProvince();
                Object other$livingAddrProvince = other.getLivingAddrProvince();
                if (this$livingAddrProvince == null) {
                    if (other$livingAddrProvince != null) {
                        return false;
                    }
                } else if (!this$livingAddrProvince.equals(other$livingAddrProvince)) {
                    return false;
                }

                Object this$livingAddrCity = this.getLivingAddrCity();
                Object other$livingAddrCity = other.getLivingAddrCity();
                if (this$livingAddrCity == null) {
                    if (other$livingAddrCity != null) {
                        return false;
                    }
                } else if (!this$livingAddrCity.equals(other$livingAddrCity)) {
                    return false;
                }

                label860: {
                    Object this$livingAddrCounty = this.getLivingAddrCounty();
                    Object other$livingAddrCounty = other.getLivingAddrCounty();
                    if (this$livingAddrCounty == null) {
                        if (other$livingAddrCounty == null) {
                            break label860;
                        }
                    } else if (this$livingAddrCounty.equals(other$livingAddrCounty)) {
                        break label860;
                    }

                    return false;
                }

                Object this$livingAddrTown = this.getLivingAddrTown();
                Object other$livingAddrTown = other.getLivingAddrTown();
                if (this$livingAddrTown == null) {
                    if (other$livingAddrTown != null) {
                        return false;
                    }
                } else if (!this$livingAddrTown.equals(other$livingAddrTown)) {
                    return false;
                }

                Object this$livingAddrVillage = this.getLivingAddrVillage();
                Object other$livingAddrVillage = other.getLivingAddrVillage();
                if (this$livingAddrVillage == null) {
                    if (other$livingAddrVillage != null) {
                        return false;
                    }
                } else if (!this$livingAddrVillage.equals(other$livingAddrVillage)) {
                    return false;
                }

                label839: {
                    Object this$livingAddrDoor = this.getLivingAddrDoor();
                    Object other$livingAddrDoor = other.getLivingAddrDoor();
                    if (this$livingAddrDoor == null) {
                        if (other$livingAddrDoor == null) {
                            break label839;
                        }
                    } else if (this$livingAddrDoor.equals(other$livingAddrDoor)) {
                        break label839;
                    }

                    return false;
                }

                Object this$collectionDatetime = this.getCollectionDatetime();
                Object other$collectionDatetime = other.getCollectionDatetime();
                if (this$collectionDatetime == null) {
                    if (other$collectionDatetime != null) {
                        return false;
                    }
                } else if (!this$collectionDatetime.equals(other$collectionDatetime)) {
                    return false;
                }

                label825: {
                    Object this$registeredAddrProvince = this.getRegisteredAddrProvince();
                    Object other$registeredAddrProvince = other.getRegisteredAddrProvince();
                    if (this$registeredAddrProvince == null) {
                        if (other$registeredAddrProvince == null) {
                            break label825;
                        }
                    } else if (this$registeredAddrProvince.equals(other$registeredAddrProvince)) {
                        break label825;
                    }

                    return false;
                }

                Object this$registeredAddrCity = this.getRegisteredAddrCity();
                Object other$registeredAddrCity = other.getRegisteredAddrCity();
                if (this$registeredAddrCity == null) {
                    if (other$registeredAddrCity != null) {
                        return false;
                    }
                } else if (!this$registeredAddrCity.equals(other$registeredAddrCity)) {
                    return false;
                }

                label811: {
                    Object this$registeredAddrDistrict = this.getRegisteredAddrDistrict();
                    Object other$registeredAddrDistrict = other.getRegisteredAddrDistrict();
                    if (this$registeredAddrDistrict == null) {
                        if (other$registeredAddrDistrict == null) {
                            break label811;
                        }
                    } else if (this$registeredAddrDistrict.equals(other$registeredAddrDistrict)) {
                        break label811;
                    }

                    return false;
                }

                Object this$registeredAddrTown = this.getRegisteredAddrTown();
                Object other$registeredAddrTown = other.getRegisteredAddrTown();
                if (this$registeredAddrTown == null) {
                    if (other$registeredAddrTown != null) {
                        return false;
                    }
                } else if (!this$registeredAddrTown.equals(other$registeredAddrTown)) {
                    return false;
                }

                label797: {
                    Object this$registeredAddrDetail = this.getRegisteredAddrDetail();
                    Object other$registeredAddrDetail = other.getRegisteredAddrDetail();
                    if (this$registeredAddrDetail == null) {
                        if (other$registeredAddrDetail == null) {
                            break label797;
                        }
                    } else if (this$registeredAddrDetail.equals(other$registeredAddrDetail)) {
                        break label797;
                    }

                    return false;
                }

                label790: {
                    Object this$registeredPostcode = this.getRegisteredPostcode();
                    Object other$registeredPostcode = other.getRegisteredPostcode();
                    if (this$registeredPostcode == null) {
                        if (other$registeredPostcode == null) {
                            break label790;
                        }
                    } else if (this$registeredPostcode.equals(other$registeredPostcode)) {
                        break label790;
                    }

                    return false;
                }

                Object this$collectionRegionCode = this.getCollectionRegionCode();
                Object other$collectionRegionCode = other.getCollectionRegionCode();
                if (this$collectionRegionCode == null) {
                    if (other$collectionRegionCode != null) {
                        return false;
                    }
                } else if (!this$collectionRegionCode.equals(other$collectionRegionCode)) {
                    return false;
                }

                label776: {
                    Object this$collectionOrgCode = this.getCollectionOrgCode();
                    Object other$collectionOrgCode = other.getCollectionOrgCode();
                    if (this$collectionOrgCode == null) {
                        if (other$collectionOrgCode == null) {
                            break label776;
                        }
                    } else if (this$collectionOrgCode.equals(other$collectionOrgCode)) {
                        break label776;
                    }

                    return false;
                }

                label769: {
                    Object this$collectionOrgName = this.getCollectionOrgName();
                    Object other$collectionOrgName = other.getCollectionOrgName();
                    if (this$collectionOrgName == null) {
                        if (other$collectionOrgName == null) {
                            break label769;
                        }
                    } else if (this$collectionOrgName.equals(other$collectionOrgName)) {
                        break label769;
                    }

                    return false;
                }

                Object this$registeredAddrVillage = this.getRegisteredAddrVillage();
                Object other$registeredAddrVillage = other.getRegisteredAddrVillage();
                if (this$registeredAddrVillage == null) {
                    if (other$registeredAddrVillage != null) {
                        return false;
                    }
                } else if (!this$registeredAddrVillage.equals(other$registeredAddrVillage)) {
                    return false;
                }

                Object this$dataUapId = this.getDataUapId();
                Object other$dataUapId = other.getDataUapId();
                if (this$dataUapId == null) {
                    if (other$dataUapId != null) {
                        return false;
                    }
                } else if (!this$dataUapId.equals(other$dataUapId)) {
                    return false;
                }

                label748: {
                    Object this$stdLivingAddrAreaCode = this.getStdLivingAddrAreaCode();
                    Object other$stdLivingAddrAreaCode = other.getStdLivingAddrAreaCode();
                    if (this$stdLivingAddrAreaCode == null) {
                        if (other$stdLivingAddrAreaCode == null) {
                            break label748;
                        }
                    } else if (this$stdLivingAddrAreaCode.equals(other$stdLivingAddrAreaCode)) {
                        break label748;
                    }

                    return false;
                }

                Object this$stdLivingAddrArea = this.getStdLivingAddrArea();
                Object other$stdLivingAddrArea = other.getStdLivingAddrArea();
                if (this$stdLivingAddrArea == null) {
                    if (other$stdLivingAddrArea != null) {
                        return false;
                    }
                } else if (!this$stdLivingAddrArea.equals(other$stdLivingAddrArea)) {
                    return false;
                }

                Object this$stdWorkAddrAreaCode = this.getStdWorkAddrAreaCode();
                Object other$stdWorkAddrAreaCode = other.getStdWorkAddrAreaCode();
                if (this$stdWorkAddrAreaCode == null) {
                    if (other$stdWorkAddrAreaCode != null) {
                        return false;
                    }
                } else if (!this$stdWorkAddrAreaCode.equals(other$stdWorkAddrAreaCode)) {
                    return false;
                }

                label727: {
                    Object this$stdWorkAddrArea = this.getStdWorkAddrArea();
                    Object other$stdWorkAddrArea = other.getStdWorkAddrArea();
                    if (this$stdWorkAddrArea == null) {
                        if (other$stdWorkAddrArea == null) {
                            break label727;
                        }
                    } else if (this$stdWorkAddrArea.equals(other$stdWorkAddrArea)) {
                        break label727;
                    }

                    return false;
                }

                Object this$stdRegisteredAddrAreaCode = this.getStdRegisteredAddrAreaCode();
                Object other$stdRegisteredAddrAreaCode = other.getStdRegisteredAddrAreaCode();
                if (this$stdRegisteredAddrAreaCode == null) {
                    if (other$stdRegisteredAddrAreaCode != null) {
                        return false;
                    }
                } else if (!this$stdRegisteredAddrAreaCode.equals(other$stdRegisteredAddrAreaCode)) {
                    return false;
                }

                label713: {
                    Object this$stdRegisteredAddrArea = this.getStdRegisteredAddrArea();
                    Object other$stdRegisteredAddrArea = other.getStdRegisteredAddrArea();
                    if (this$stdRegisteredAddrArea == null) {
                        if (other$stdRegisteredAddrArea == null) {
                            break label713;
                        }
                    } else if (this$stdRegisteredAddrArea.equals(other$stdRegisteredAddrArea)) {
                        break label713;
                    }

                    return false;
                }

                Object this$stdContactAddrAreaCode = this.getStdContactAddrAreaCode();
                Object other$stdContactAddrAreaCode = other.getStdContactAddrAreaCode();
                if (this$stdContactAddrAreaCode == null) {
                    if (other$stdContactAddrAreaCode != null) {
                        return false;
                    }
                } else if (!this$stdContactAddrAreaCode.equals(other$stdContactAddrAreaCode)) {
                    return false;
                }

                label699: {
                    Object this$stdContactAddrArea = this.getStdContactAddrArea();
                    Object other$stdContactAddrArea = other.getStdContactAddrArea();
                    if (this$stdContactAddrArea == null) {
                        if (other$stdContactAddrArea == null) {
                            break label699;
                        }
                    } else if (this$stdContactAddrArea.equals(other$stdContactAddrArea)) {
                        break label699;
                    }

                    return false;
                }

                Object this$companyCode = this.getCompanyCode();
                Object other$companyCode = other.getCompanyCode();
                if (this$companyCode == null) {
                    if (other$companyCode != null) {
                        return false;
                    }
                } else if (!this$companyCode.equals(other$companyCode)) {
                    return false;
                }

                label685: {
                    Object this$livingAddrProvinceCode = this.getLivingAddrProvinceCode();
                    Object other$livingAddrProvinceCode = other.getLivingAddrProvinceCode();
                    if (this$livingAddrProvinceCode == null) {
                        if (other$livingAddrProvinceCode == null) {
                            break label685;
                        }
                    } else if (this$livingAddrProvinceCode.equals(other$livingAddrProvinceCode)) {
                        break label685;
                    }

                    return false;
                }

                label678: {
                    Object this$livingAddrCityCode = this.getLivingAddrCityCode();
                    Object other$livingAddrCityCode = other.getLivingAddrCityCode();
                    if (this$livingAddrCityCode == null) {
                        if (other$livingAddrCityCode == null) {
                            break label678;
                        }
                    } else if (this$livingAddrCityCode.equals(other$livingAddrCityCode)) {
                        break label678;
                    }

                    return false;
                }

                Object this$livingAddrCountyCode = this.getLivingAddrCountyCode();
                Object other$livingAddrCountyCode = other.getLivingAddrCountyCode();
                if (this$livingAddrCountyCode == null) {
                    if (other$livingAddrCountyCode != null) {
                        return false;
                    }
                } else if (!this$livingAddrCountyCode.equals(other$livingAddrCountyCode)) {
                    return false;
                }

                label664: {
                    Object this$livingAddrTownCode = this.getLivingAddrTownCode();
                    Object other$livingAddrTownCode = other.getLivingAddrTownCode();
                    if (this$livingAddrTownCode == null) {
                        if (other$livingAddrTownCode == null) {
                            break label664;
                        }
                    } else if (this$livingAddrTownCode.equals(other$livingAddrTownCode)) {
                        break label664;
                    }

                    return false;
                }

                label657: {
                    Object this$livingAddrVillageCode = this.getLivingAddrVillageCode();
                    Object other$livingAddrVillageCode = other.getLivingAddrVillageCode();
                    if (this$livingAddrVillageCode == null) {
                        if (other$livingAddrVillageCode == null) {
                            break label657;
                        }
                    } else if (this$livingAddrVillageCode.equals(other$livingAddrVillageCode)) {
                        break label657;
                    }

                    return false;
                }

                Object this$personTypeCode = this.getPersonTypeCode();
                Object other$personTypeCode = other.getPersonTypeCode();
                if (this$personTypeCode == null) {
                    if (other$personTypeCode != null) {
                        return false;
                    }
                } else if (!this$personTypeCode.equals(other$personTypeCode)) {
                    return false;
                }

                Object this$personTypeName = this.getPersonTypeName();
                Object other$personTypeName = other.getPersonTypeName();
                if (this$personTypeName == null) {
                    if (other$personTypeName != null) {
                        return false;
                    }
                } else if (!this$personTypeName.equals(other$personTypeName)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof PatientBaseInfoDto;
    }

    public int hashCode() {
        int result = 1;
        Object $localPatientId = this.getLocalPatientId();
        result = result * 59 + ($localPatientId == null ? 43 : $localPatientId.hashCode());
        Object $hisPatientId = this.getHisPatientId();
        result = result * 59 + ($hisPatientId == null ? 43 : $hisPatientId.hashCode());
        Object $ehrId = this.getEhrId();
        result = result * 59 + ($ehrId == null ? 43 : $ehrId.hashCode());
        Object $identityTypeCode = this.getIdentityTypeCode();
        result = result * 59 + ($identityTypeCode == null ? 43 : $identityTypeCode.hashCode());
        Object $otherIdCard = this.getOtherIdCard();
        result = result * 59 + ($otherIdCard == null ? 43 : $otherIdCard.hashCode());
        Object $healthCard = this.getHealthCard();
        result = result * 59 + ($healthCard == null ? 43 : $healthCard.hashCode());
        Object $insuranceCard = this.getInsuranceCard();
        result = result * 59 + ($insuranceCard == null ? 43 : $insuranceCard.hashCode());
        Object $medicalCardType = this.getMedicalCardType();
        result = result * 59 + ($medicalCardType == null ? 43 : $medicalCardType.hashCode());
        Object $medicalCard = this.getMedicalCard();
        result = result * 59 + ($medicalCard == null ? 43 : $medicalCard.hashCode());
        Object $passport = this.getPassport();
        result = result * 59 + ($passport == null ? 43 : $passport.hashCode());
        Object $immuneCard = this.getImmuneCard();
        result = result * 59 + ($immuneCard == null ? 43 : $immuneCard.hashCode());
        Object $ruralCard = this.getRuralCard();
        result = result * 59 + ($ruralCard == null ? 43 : $ruralCard.hashCode());
        Object $patientName = this.getPatientName();
        result = result * 59 + ($patientName == null ? 43 : $patientName.hashCode());
        Object $sexCode = this.getSexCode();
        result = result * 59 + ($sexCode == null ? 43 : $sexCode.hashCode());
        Object $birthday = this.getBirthday();
        result = result * 59 + ($birthday == null ? 43 : $birthday.hashCode());
        Object $deathDate = this.getDeathDate();
        result = result * 59 + ($deathDate == null ? 43 : $deathDate.hashCode());
        Object $nationality = this.getNationality();
        result = result * 59 + ($nationality == null ? 43 : $nationality.hashCode());
        Object $ethnicity = this.getEthnicity();
        result = result * 59 + ($ethnicity == null ? 43 : $ethnicity.hashCode());
        Object $maritalStatus = this.getMaritalStatus();
        result = result * 59 + ($maritalStatus == null ? 43 : $maritalStatus.hashCode());
        Object $weddingDate = this.getWeddingDate();
        result = result * 59 + ($weddingDate == null ? 43 : $weddingDate.hashCode());
        Object $obstetricalMark = this.getObstetricalMark();
        result = result * 59 + ($obstetricalMark == null ? 43 : $obstetricalMark.hashCode());
        Object $careerType = this.getCareerType();
        result = result * 59 + ($careerType == null ? 43 : $careerType.hashCode());
        Object $careerStartDate = this.getCareerStartDate();
        result = result * 59 + ($careerStartDate == null ? 43 : $careerStartDate.hashCode());
        Object $careerEndDate = this.getCareerEndDate();
        result = result * 59 + ($careerEndDate == null ? 43 : $careerEndDate.hashCode());
        Object $bloodAbo = this.getBloodAbo();
        result = result * 59 + ($bloodAbo == null ? 43 : $bloodAbo.hashCode());
        Object $bloodRh = this.getBloodRh();
        result = result * 59 + ($bloodRh == null ? 43 : $bloodRh.hashCode());
        Object $economicStatus = this.getEconomicStatus();
        result = result * 59 + ($economicStatus == null ? 43 : $economicStatus.hashCode());
        Object $insuranceType = this.getInsuranceType();
        result = result * 59 + ($insuranceType == null ? 43 : $insuranceType.hashCode());
        Object $insuranceName = this.getInsuranceName();
        result = result * 59 + ($insuranceName == null ? 43 : $insuranceName.hashCode());
        Object $reliefSource = this.getReliefSource();
        result = result * 59 + ($reliefSource == null ? 43 : $reliefSource.hashCode());
        Object $medicalPayWay = this.getMedicalPayWay();
        result = result * 59 + ($medicalPayWay == null ? 43 : $medicalPayWay.hashCode());
        Object $company = this.getCompany();
        result = result * 59 + ($company == null ? 43 : $company.hashCode());
        Object $workAddrDetail = this.getWorkAddrDetail();
        result = result * 59 + ($workAddrDetail == null ? 43 : $workAddrDetail.hashCode());
        Object $workRegionCode = this.getWorkRegionCode();
        result = result * 59 + ($workRegionCode == null ? 43 : $workRegionCode.hashCode());
        Object $telephone = this.getTelephone();
        result = result * 59 + ($telephone == null ? 43 : $telephone.hashCode());
        Object $email = this.getEmail();
        result = result * 59 + ($email == null ? 43 : $email.hashCode());
        Object $wechat = this.getWechat();
        result = result * 59 + ($wechat == null ? 43 : $wechat.hashCode());
        Object $educationCode = this.getEducationCode();
        result = result * 59 + ($educationCode == null ? 43 : $educationCode.hashCode());
        Object $educationName = this.getEducationName();
        result = result * 59 + ($educationName == null ? 43 : $educationName.hashCode());
        Object $photoUrl = this.getPhotoUrl();
        result = result * 59 + ($photoUrl == null ? 43 : $photoUrl.hashCode());
        Object $contactRelationCode = this.getContactRelationCode();
        result = result * 59 + ($contactRelationCode == null ? 43 : $contactRelationCode.hashCode());
        Object $contactName = this.getContactName();
        result = result * 59 + ($contactName == null ? 43 : $contactName.hashCode());
        Object $contactTel = this.getContactTel();
        result = result * 59 + ($contactTel == null ? 43 : $contactTel.hashCode());
        Object $contactAddrDetail = this.getContactAddrDetail();
        result = result * 59 + ($contactAddrDetail == null ? 43 : $contactAddrDetail.hashCode());
        Object $residentMark = this.getResidentMark();
        result = result * 59 + ($residentMark == null ? 43 : $residentMark.hashCode());
        Object $livingRegionCode = this.getLivingRegionCode();
        result = result * 59 + ($livingRegionCode == null ? 43 : $livingRegionCode.hashCode());
        Object $livingAddrDetail = this.getLivingAddrDetail();
        result = result * 59 + ($livingAddrDetail == null ? 43 : $livingAddrDetail.hashCode());
        Object $livingPostcode = this.getLivingPostcode();
        result = result * 59 + ($livingPostcode == null ? 43 : $livingPostcode.hashCode());
        Object $guardianRelationCode = this.getGuardianRelationCode();
        result = result * 59 + ($guardianRelationCode == null ? 43 : $guardianRelationCode.hashCode());
        Object $guardianId = this.getGuardianId();
        result = result * 59 + ($guardianId == null ? 43 : $guardianId.hashCode());
        Object $guardianName = this.getGuardianName();
        result = result * 59 + ($guardianName == null ? 43 : $guardianName.hashCode());
        Object $guardianAge = this.getGuardianAge();
        result = result * 59 + ($guardianAge == null ? 43 : $guardianAge.hashCode());
        Object $guardianNationality = this.getGuardianNationality();
        result = result * 59 + ($guardianNationality == null ? 43 : $guardianNationality.hashCode());
        Object $guardianEthnicity = this.getGuardianEthnicity();
        result = result * 59 + ($guardianEthnicity == null ? 43 : $guardianEthnicity.hashCode());
        Object $guardianTelephone = this.getGuardianTelephone();
        result = result * 59 + ($guardianTelephone == null ? 43 : $guardianTelephone.hashCode());
        Object $updateOrgCode = this.getUpdateOrgCode();
        result = result * 59 + ($updateOrgCode == null ? 43 : $updateOrgCode.hashCode());
        Object $updateOrgName = this.getUpdateOrgName();
        result = result * 59 + ($updateOrgName == null ? 43 : $updateOrgName.hashCode());
        Object $updateId = this.getUpdateId();
        result = result * 59 + ($updateId == null ? 43 : $updateId.hashCode());
        Object $updateName = this.getUpdateName();
        result = result * 59 + ($updateName == null ? 43 : $updateName.hashCode());
        Object $fiveGuaranteesFlag = this.getFiveGuaranteesFlag();
        result = result * 59 + ($fiveGuaranteesFlag == null ? 43 : $fiveGuaranteesFlag.hashCode());
        Object $chronicDiseaseFlag = this.getChronicDiseaseFlag();
        result = result * 59 + ($chronicDiseaseFlag == null ? 43 : $chronicDiseaseFlag.hashCode());
        Object $residentIdCard = this.getResidentIdCard();
        result = result * 59 + ($residentIdCard == null ? 43 : $residentIdCard.hashCode());
        Object $nativePlace = this.getNativePlace();
        result = result * 59 + ($nativePlace == null ? 43 : $nativePlace.hashCode());
        Object $identityTypeName = this.getIdentityTypeName();
        result = result * 59 + ($identityTypeName == null ? 43 : $identityTypeName.hashCode());
        Object $medicalCardTypeName = this.getMedicalCardTypeName();
        result = result * 59 + ($medicalCardTypeName == null ? 43 : $medicalCardTypeName.hashCode());
        Object $sexName = this.getSexName();
        result = result * 59 + ($sexName == null ? 43 : $sexName.hashCode());
        Object $nationalityName = this.getNationalityName();
        result = result * 59 + ($nationalityName == null ? 43 : $nationalityName.hashCode());
        Object $ethnicityName = this.getEthnicityName();
        result = result * 59 + ($ethnicityName == null ? 43 : $ethnicityName.hashCode());
        Object $maritalName = this.getMaritalName();
        result = result * 59 + ($maritalName == null ? 43 : $maritalName.hashCode());
        Object $careerName = this.getCareerName();
        result = result * 59 + ($careerName == null ? 43 : $careerName.hashCode());
        Object $bloodAboName = this.getBloodAboName();
        result = result * 59 + ($bloodAboName == null ? 43 : $bloodAboName.hashCode());
        Object $bloodRhName = this.getBloodRhName();
        result = result * 59 + ($bloodRhName == null ? 43 : $bloodRhName.hashCode());
        Object $economicName = this.getEconomicName();
        result = result * 59 + ($economicName == null ? 43 : $economicName.hashCode());
        Object $reliefSourceName = this.getReliefSourceName();
        result = result * 59 + ($reliefSourceName == null ? 43 : $reliefSourceName.hashCode());
        Object $medicalPayName = this.getMedicalPayName();
        result = result * 59 + ($medicalPayName == null ? 43 : $medicalPayName.hashCode());
        Object $contactRelationName = this.getContactRelationName();
        result = result * 59 + ($contactRelationName == null ? 43 : $contactRelationName.hashCode());
        Object $guardianRelationName = this.getGuardianRelationName();
        result = result * 59 + ($guardianRelationName == null ? 43 : $guardianRelationName.hashCode());
        Object $guardianNationalityName = this.getGuardianNationalityName();
        result = result * 59 + ($guardianNationalityName == null ? 43 : $guardianNationalityName.hashCode());
        Object $guardianEthnicityName = this.getGuardianEthnicityName();
        result = result * 59 + ($guardianEthnicityName == null ? 43 : $guardianEthnicityName.hashCode());
        Object $workAddrProvince = this.getWorkAddrProvince();
        result = result * 59 + ($workAddrProvince == null ? 43 : $workAddrProvince.hashCode());
        Object $workAddrCity = this.getWorkAddrCity();
        result = result * 59 + ($workAddrCity == null ? 43 : $workAddrCity.hashCode());
        Object $workAddrDistrict = this.getWorkAddrDistrict();
        result = result * 59 + ($workAddrDistrict == null ? 43 : $workAddrDistrict.hashCode());
        Object $workAddrTown = this.getWorkAddrTown();
        result = result * 59 + ($workAddrTown == null ? 43 : $workAddrTown.hashCode());
        Object $workAddrVillage = this.getWorkAddrVillage();
        result = result * 59 + ($workAddrVillage == null ? 43 : $workAddrVillage.hashCode());
        Object $workAddrDoor = this.getWorkAddrDoor();
        result = result * 59 + ($workAddrDoor == null ? 43 : $workAddrDoor.hashCode());
        Object $contactAddrProvince = this.getContactAddrProvince();
        result = result * 59 + ($contactAddrProvince == null ? 43 : $contactAddrProvince.hashCode());
        Object $contactAddrCity = this.getContactAddrCity();
        result = result * 59 + ($contactAddrCity == null ? 43 : $contactAddrCity.hashCode());
        Object $contactAddrCounty = this.getContactAddrCounty();
        result = result * 59 + ($contactAddrCounty == null ? 43 : $contactAddrCounty.hashCode());
        Object $contactAddrTown = this.getContactAddrTown();
        result = result * 59 + ($contactAddrTown == null ? 43 : $contactAddrTown.hashCode());
        Object $contactAddrVillage = this.getContactAddrVillage();
        result = result * 59 + ($contactAddrVillage == null ? 43 : $contactAddrVillage.hashCode());
        Object $contactAddrDoor = this.getContactAddrDoor();
        result = result * 59 + ($contactAddrDoor == null ? 43 : $contactAddrDoor.hashCode());
        Object $livingAddrProvince = this.getLivingAddrProvince();
        result = result * 59 + ($livingAddrProvince == null ? 43 : $livingAddrProvince.hashCode());
        Object $livingAddrCity = this.getLivingAddrCity();
        result = result * 59 + ($livingAddrCity == null ? 43 : $livingAddrCity.hashCode());
        Object $livingAddrCounty = this.getLivingAddrCounty();
        result = result * 59 + ($livingAddrCounty == null ? 43 : $livingAddrCounty.hashCode());
        Object $livingAddrTown = this.getLivingAddrTown();
        result = result * 59 + ($livingAddrTown == null ? 43 : $livingAddrTown.hashCode());
        Object $livingAddrVillage = this.getLivingAddrVillage();
        result = result * 59 + ($livingAddrVillage == null ? 43 : $livingAddrVillage.hashCode());
        Object $livingAddrDoor = this.getLivingAddrDoor();
        result = result * 59 + ($livingAddrDoor == null ? 43 : $livingAddrDoor.hashCode());
        Object $collectionDatetime = this.getCollectionDatetime();
        result = result * 59 + ($collectionDatetime == null ? 43 : $collectionDatetime.hashCode());
        Object $registeredAddrProvince = this.getRegisteredAddrProvince();
        result = result * 59 + ($registeredAddrProvince == null ? 43 : $registeredAddrProvince.hashCode());
        Object $registeredAddrCity = this.getRegisteredAddrCity();
        result = result * 59 + ($registeredAddrCity == null ? 43 : $registeredAddrCity.hashCode());
        Object $registeredAddrDistrict = this.getRegisteredAddrDistrict();
        result = result * 59 + ($registeredAddrDistrict == null ? 43 : $registeredAddrDistrict.hashCode());
        Object $registeredAddrTown = this.getRegisteredAddrTown();
        result = result * 59 + ($registeredAddrTown == null ? 43 : $registeredAddrTown.hashCode());
        Object $registeredAddrDetail = this.getRegisteredAddrDetail();
        result = result * 59 + ($registeredAddrDetail == null ? 43 : $registeredAddrDetail.hashCode());
        Object $registeredPostcode = this.getRegisteredPostcode();
        result = result * 59 + ($registeredPostcode == null ? 43 : $registeredPostcode.hashCode());
        Object $collectionRegionCode = this.getCollectionRegionCode();
        result = result * 59 + ($collectionRegionCode == null ? 43 : $collectionRegionCode.hashCode());
        Object $collectionOrgCode = this.getCollectionOrgCode();
        result = result * 59 + ($collectionOrgCode == null ? 43 : $collectionOrgCode.hashCode());
        Object $collectionOrgName = this.getCollectionOrgName();
        result = result * 59 + ($collectionOrgName == null ? 43 : $collectionOrgName.hashCode());
        Object $registeredAddrVillage = this.getRegisteredAddrVillage();
        result = result * 59 + ($registeredAddrVillage == null ? 43 : $registeredAddrVillage.hashCode());
        Object $dataUapId = this.getDataUapId();
        result = result * 59 + ($dataUapId == null ? 43 : $dataUapId.hashCode());
        Object $stdLivingAddrAreaCode = this.getStdLivingAddrAreaCode();
        result = result * 59 + ($stdLivingAddrAreaCode == null ? 43 : $stdLivingAddrAreaCode.hashCode());
        Object $stdLivingAddrArea = this.getStdLivingAddrArea();
        result = result * 59 + ($stdLivingAddrArea == null ? 43 : $stdLivingAddrArea.hashCode());
        Object $stdWorkAddrAreaCode = this.getStdWorkAddrAreaCode();
        result = result * 59 + ($stdWorkAddrAreaCode == null ? 43 : $stdWorkAddrAreaCode.hashCode());
        Object $stdWorkAddrArea = this.getStdWorkAddrArea();
        result = result * 59 + ($stdWorkAddrArea == null ? 43 : $stdWorkAddrArea.hashCode());
        Object $stdRegisteredAddrAreaCode = this.getStdRegisteredAddrAreaCode();
        result = result * 59 + ($stdRegisteredAddrAreaCode == null ? 43 : $stdRegisteredAddrAreaCode.hashCode());
        Object $stdRegisteredAddrArea = this.getStdRegisteredAddrArea();
        result = result * 59 + ($stdRegisteredAddrArea == null ? 43 : $stdRegisteredAddrArea.hashCode());
        Object $stdContactAddrAreaCode = this.getStdContactAddrAreaCode();
        result = result * 59 + ($stdContactAddrAreaCode == null ? 43 : $stdContactAddrAreaCode.hashCode());
        Object $stdContactAddrArea = this.getStdContactAddrArea();
        result = result * 59 + ($stdContactAddrArea == null ? 43 : $stdContactAddrArea.hashCode());
        Object $companyCode = this.getCompanyCode();
        result = result * 59 + ($companyCode == null ? 43 : $companyCode.hashCode());
        Object $livingAddrProvinceCode = this.getLivingAddrProvinceCode();
        result = result * 59 + ($livingAddrProvinceCode == null ? 43 : $livingAddrProvinceCode.hashCode());
        Object $livingAddrCityCode = this.getLivingAddrCityCode();
        result = result * 59 + ($livingAddrCityCode == null ? 43 : $livingAddrCityCode.hashCode());
        Object $livingAddrCountyCode = this.getLivingAddrCountyCode();
        result = result * 59 + ($livingAddrCountyCode == null ? 43 : $livingAddrCountyCode.hashCode());
        Object $livingAddrTownCode = this.getLivingAddrTownCode();
        result = result * 59 + ($livingAddrTownCode == null ? 43 : $livingAddrTownCode.hashCode());
        Object $livingAddrVillageCode = this.getLivingAddrVillageCode();
        result = result * 59 + ($livingAddrVillageCode == null ? 43 : $livingAddrVillageCode.hashCode());
        Object $personTypeCode = this.getPersonTypeCode();
        result = result * 59 + ($personTypeCode == null ? 43 : $personTypeCode.hashCode());
        Object $personTypeName = this.getPersonTypeName();
        result = result * 59 + ($personTypeName == null ? 43 : $personTypeName.hashCode());
        return result;
    }

    public String toString() {
        return "PatientBaseInfoDto(localPatientId=" + this.getLocalPatientId() + ", hisPatientId=" + this.getHisPatientId() + ", ehrId=" + this.getEhrId() + ", identityTypeCode=" + this.getIdentityTypeCode() + ", otherIdCard=" + this.getOtherIdCard() + ", healthCard=" + this.getHealthCard() + ", insuranceCard=" + this.getInsuranceCard() + ", medicalCardType=" + this.getMedicalCardType() + ", medicalCard=" + this.getMedicalCard() + ", passport=" + this.getPassport() + ", immuneCard=" + this.getImmuneCard() + ", ruralCard=" + this.getRuralCard() + ", patientName=" + this.getPatientName() + ", sexCode=" + this.getSexCode() + ", birthday=" + this.getBirthday() + ", deathDate=" + this.getDeathDate() + ", nationality=" + this.getNationality() + ", ethnicity=" + this.getEthnicity() + ", maritalStatus=" + this.getMaritalStatus() + ", weddingDate=" + this.getWeddingDate() + ", obstetricalMark=" + this.getObstetricalMark() + ", careerType=" + this.getCareerType() + ", careerStartDate=" + this.getCareerStartDate() + ", careerEndDate=" + this.getCareerEndDate() + ", bloodAbo=" + this.getBloodAbo() + ", bloodRh=" + this.getBloodRh() + ", economicStatus=" + this.getEconomicStatus() + ", insuranceType=" + this.getInsuranceType() + ", insuranceName=" + this.getInsuranceName() + ", reliefSource=" + this.getReliefSource() + ", medicalPayWay=" + this.getMedicalPayWay() + ", company=" + this.getCompany() + ", workAddrDetail=" + this.getWorkAddrDetail() + ", workRegionCode=" + this.getWorkRegionCode() + ", telephone=" + this.getTelephone() + ", email=" + this.getEmail() + ", wechat=" + this.getWechat() + ", educationCode=" + this.getEducationCode() + ", educationName=" + this.getEducationName() + ", photoUrl=" + this.getPhotoUrl() + ", contactRelationCode=" + this.getContactRelationCode() + ", contactName=" + this.getContactName() + ", contactTel=" + this.getContactTel() + ", contactAddrDetail=" + this.getContactAddrDetail() + ", residentMark=" + this.getResidentMark() + ", livingRegionCode=" + this.getLivingRegionCode() + ", livingAddrDetail=" + this.getLivingAddrDetail() + ", livingPostcode=" + this.getLivingPostcode() + ", guardianRelationCode=" + this.getGuardianRelationCode() + ", guardianId=" + this.getGuardianId() + ", guardianName=" + this.getGuardianName() + ", guardianAge=" + this.getGuardianAge() + ", guardianNationality=" + this.getGuardianNationality() + ", guardianEthnicity=" + this.getGuardianEthnicity() + ", guardianTelephone=" + this.getGuardianTelephone() + ", updateOrgCode=" + this.getUpdateOrgCode() + ", updateOrgName=" + this.getUpdateOrgName() + ", updateId=" + this.getUpdateId() + ", updateName=" + this.getUpdateName() + ", fiveGuaranteesFlag=" + this.getFiveGuaranteesFlag() + ", chronicDiseaseFlag=" + this.getChronicDiseaseFlag() + ", residentIdCard=" + this.getResidentIdCard() + ", nativePlace=" + this.getNativePlace() + ", identityTypeName=" + this.getIdentityTypeName() + ", medicalCardTypeName=" + this.getMedicalCardTypeName() + ", sexName=" + this.getSexName() + ", nationalityName=" + this.getNationalityName() + ", ethnicityName=" + this.getEthnicityName() + ", maritalName=" + this.getMaritalName() + ", careerName=" + this.getCareerName() + ", bloodAboName=" + this.getBloodAboName() + ", bloodRhName=" + this.getBloodRhName() + ", economicName=" + this.getEconomicName() + ", reliefSourceName=" + this.getReliefSourceName() + ", medicalPayName=" + this.getMedicalPayName() + ", contactRelationName=" + this.getContactRelationName() + ", guardianRelationName=" + this.getGuardianRelationName() + ", guardianNationalityName=" + this.getGuardianNationalityName() + ", guardianEthnicityName=" + this.getGuardianEthnicityName() + ", workAddrProvince=" + this.getWorkAddrProvince() + ", workAddrCity=" + this.getWorkAddrCity() + ", workAddrDistrict=" + this.getWorkAddrDistrict() + ", workAddrTown=" + this.getWorkAddrTown() + ", workAddrVillage=" + this.getWorkAddrVillage() + ", workAddrDoor=" + this.getWorkAddrDoor() + ", contactAddrProvince=" + this.getContactAddrProvince() + ", contactAddrCity=" + this.getContactAddrCity() + ", contactAddrCounty=" + this.getContactAddrCounty() + ", contactAddrTown=" + this.getContactAddrTown() + ", contactAddrVillage=" + this.getContactAddrVillage() + ", contactAddrDoor=" + this.getContactAddrDoor() + ", livingAddrProvince=" + this.getLivingAddrProvince() + ", livingAddrCity=" + this.getLivingAddrCity() + ", livingAddrCounty=" + this.getLivingAddrCounty() + ", livingAddrTown=" + this.getLivingAddrTown() + ", livingAddrVillage=" + this.getLivingAddrVillage() + ", livingAddrDoor=" + this.getLivingAddrDoor() + ", collectionDatetime=" + this.getCollectionDatetime() + ", registeredAddrProvince=" + this.getRegisteredAddrProvince() + ", registeredAddrCity=" + this.getRegisteredAddrCity() + ", registeredAddrDistrict=" + this.getRegisteredAddrDistrict() + ", registeredAddrTown=" + this.getRegisteredAddrTown() + ", registeredAddrDetail=" + this.getRegisteredAddrDetail() + ", registeredPostcode=" + this.getRegisteredPostcode() + ", collectionRegionCode=" + this.getCollectionRegionCode() + ", collectionOrgCode=" + this.getCollectionOrgCode() + ", collectionOrgName=" + this.getCollectionOrgName() + ", registeredAddrVillage=" + this.getRegisteredAddrVillage() + ", dataUapId=" + this.getDataUapId() + ", stdLivingAddrAreaCode=" + this.getStdLivingAddrAreaCode() + ", stdLivingAddrArea=" + this.getStdLivingAddrArea() + ", stdWorkAddrAreaCode=" + this.getStdWorkAddrAreaCode() + ", stdWorkAddrArea=" + this.getStdWorkAddrArea() + ", stdRegisteredAddrAreaCode=" + this.getStdRegisteredAddrAreaCode() + ", stdRegisteredAddrArea=" + this.getStdRegisteredAddrArea() + ", stdContactAddrAreaCode=" + this.getStdContactAddrAreaCode() + ", stdContactAddrArea=" + this.getStdContactAddrArea() + ", companyCode=" + this.getCompanyCode() + ", livingAddrProvinceCode=" + this.getLivingAddrProvinceCode() + ", livingAddrCityCode=" + this.getLivingAddrCityCode() + ", livingAddrCountyCode=" + this.getLivingAddrCountyCode() + ", livingAddrTownCode=" + this.getLivingAddrTownCode() + ", livingAddrVillageCode=" + this.getLivingAddrVillageCode() + ", personTypeCode=" + this.getPersonTypeCode() + ", personTypeName=" + this.getPersonTypeName() + ")";
    }
}

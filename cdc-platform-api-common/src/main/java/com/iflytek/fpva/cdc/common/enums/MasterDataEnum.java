package com.iflytek.fpva.cdc.common.enums;

import lombok.Getter;

@Getter
public enum MasterDataEnum {

    INFECTED("传染病主数据", "infected"),

    SYNDROME("症候群主数据", "syndrome"),

    SYMPTOM("症状主数据", "symptom"), 

    PATHOGEN("病原主数据", "pathogen"),

    ;

    private final String name;

    private final String code;

    private MasterDataEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }
}

package com.iflytek.fpva.cdc.common.utils;

import com.iflytek.fpva.cdc.common.enums.AreaLevelEnum;

import java.util.function.BiConsumer;

public class AreaUtils {

    /**
     * 将areaLevel areaCode 转换成对应的省 市 或区的
     */
    public static <T> void convert(Integer areaLevel,
                               String areaCode,
                               T t,
                               BiConsumer<T, String> provinceCodeBi,
                               BiConsumer<T, String> cityCodeBi,
                               BiConsumer<T, String> districtCodeBi
                               ){
        if (areaLevel == null){
            return;
        }
        AreaLevelEnum level = AreaLevelEnum.level(areaLevel);
        switch (level){
            case PROVINCE:
                provinceCodeBi.accept(t,areaCode);
                break;
            case CITY:
                cityCodeBi.accept(t,areaCode);
                break;
            case DISTRICT:
                districtCodeBi.accept(t,areaCode);
                break;
            default:
                break;
        }
    }
}

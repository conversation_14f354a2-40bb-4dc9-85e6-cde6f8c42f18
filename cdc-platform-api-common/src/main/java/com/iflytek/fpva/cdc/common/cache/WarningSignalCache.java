package com.iflytek.fpva.cdc.common.cache;

import cn.hutool.core.collection.CollectionUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.constant.Common;
import com.iflytek.fpva.cdc.common.entity.TbCdcmrWarningRiskLevelDetail;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class WarningSignalCache {

    private static final String INFECTED_WARNING = "infected";

    private static final String SYNDROME_WARNING = "syndrome";

    @Resource
    private AdminServiceProApi adminServiceProApi;

    private final Cache<String, List<TbCdcmrWarningRiskLevelDetail>> cache = Caffeine.newBuilder()
                                                                                     .expireAfterWrite(1, TimeUnit.DAYS)
                                                                                     .build();

    public void refreshCache() {

        Map<String, List<TbCdcmrWarningRiskLevelDetail>> riskLevelMap = new HashMap<>();
        //查询症候群的风险等级配置
        List<TbCdcmrWarningRiskLevelDetail> infectedLevelMap = adminServiceProApi.listDiseaseRiskLevel(INFECTED_WARNING);
        //查询传染病的风险等级配置
        List<TbCdcmrWarningRiskLevelDetail> syndromeLevelMap = adminServiceProApi.listDiseaseRiskLevel(SYNDROME_WARNING);

        riskLevelMap.put(Common.INFECTED, infectedLevelMap);
        riskLevelMap.put(Common.SYNDROME, syndromeLevelMap);

        cache.putAll(riskLevelMap);
    }

    public TbCdcmrWarningRiskLevelDetail loadWarningRiskLevel(String riskLevelDetailId, String warningType, String diseaseCode) {

        //获取缓存中的风险等级，如果不存在则调用getRiskLevelDetail获取
        List<TbCdcmrWarningRiskLevelDetail> detailList = cache.get(warningType, res -> listRiskLevelDetail(warningType));
        if(CollectionUtil.isEmpty(detailList)){
            return null;
        }

        //通过cache得到的结果需要根据情况过滤
        if(warningType.equals(Common.INFECTED) && CollectionUtil.isNotEmpty(detailList)) {
            //传染病在风险等级层面
            detailList = detailList.stream().filter(e -> Objects.equals(riskLevelDetailId, e.getId())).collect(Collectors.toList());
        }
        if(warningType.equals(Common.SYNDROME) && CollectionUtil.isNotEmpty(detailList)){
            //症候群在病种层面
            detailList = detailList.stream().filter(e -> Objects.equals(riskLevelDetailId, e.getId()) && diseaseCode.equals(e.getDiseaseCode())).collect(Collectors.toList());
        }
        return detailList.get(0);
    }

    /**
     * 根据风险等级id以及疾病code查询对应的风险等级详情
     * */
    private List<TbCdcmrWarningRiskLevelDetail> listRiskLevelDetail(String warningType) {

        //查询detail表中所有疾病的风险等级
        List<TbCdcmrWarningRiskLevelDetail> riskLevelDetailList = new ArrayList<>();
        if(warningType.equals(Common.INFECTED)) {
            //传染病在风险等级层面
            riskLevelDetailList =  adminServiceProApi.listDiseaseRiskLevel(INFECTED_WARNING);

        }
        if(warningType.equals(Common.SYNDROME)){
            //症候群在病种层面
            riskLevelDetailList = adminServiceProApi.listDiseaseRiskLevel(SYNDROME_WARNING);
        }
        return riskLevelDetailList;
    }
}

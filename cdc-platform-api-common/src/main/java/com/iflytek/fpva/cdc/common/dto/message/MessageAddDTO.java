package com.iflytek.fpva.cdc.common.dto.message;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class MessageAddDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "消息类型  1-待办提醒 ；2-系统通知")
    private Integer messageType;

    @ApiModelProperty(value = "消息名称")
    private String messageName;

    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    @ApiModelProperty(value = "消息发送的时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String sendTime;


    @ApiModelProperty(value = "发送人ID")
    private String senderId;

    @ApiModelProperty(value = "发送人")
    private String sender;

    @ApiModelProperty(value = "接收人ID")
    private String receiverId;

    @ApiModelProperty(value = "接收人")
    private String receiver;

    @ApiModelProperty(value = "查询参数")
    private String requestParam;

    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;

    @ApiModelProperty(value = "系统中相对路径")
    private String systemRelativePath;
    private String signalPushConfigurationId;

    private String messageConfigId;
    public static String buildParam(String id) {
        JSONObject param = new JSONObject();
        param.put("id", id);
        param.put("formWorkPlat",true);
        return param.toJSONString();
    }


    public static MessageAddDTO generateVO(MessageConfig messageConfig){
        MessageAddDTO addDTO = new MessageAddDTO();
        addDTO.setAppCode(messageConfig.getAppCode());
        addDTO.setMessageType(messageConfig.getMessageType());
        addDTO.setMessageName(messageConfig.getMessageName());
        addDTO.setSourceSystemCode(messageConfig.getSourceSystemCode());
        addDTO.setSourceSystemName(messageConfig.getSourceSystemName());
        addDTO.setSystemRelativePath(messageConfig.getSystemRelativePath());
        addDTO.setSendTime(DateUtil.formatDateTime(new Date()) );
        return addDTO;
    }
}

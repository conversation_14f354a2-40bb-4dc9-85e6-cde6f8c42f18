package com.iflytek.fpva.cdc.common.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.constant.Common;
import com.iflytek.fpva.cdc.common.entity.TbCdcAttachment;
import com.iflytek.fpva.cdc.common.entity.TbCdcmrExportTask;
import com.iflytek.fpva.cdc.common.model.dto.ExportTaskDTO;
import com.iflytek.fpva.cdc.common.model.vo.ExportTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.IntSupplier;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.UserInfoInterceptor.userInfo;

@Service
@Slf4j
public class ExportTaskService {

    @Value("${storage.switch:minio}")
    private String storageSwitch;

    @Value("${swift.prefix:/file-obj}")
    private String swiftPrefix;

    @Value("${minio.prefix:/minIoFile}")
    private String minioPrefix;

    @Resource
    private AdminServiceProApi adminServiceProApi;

    @Resource
    private FileService fileService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public <K, T> TbCdcmrExportTask addAndUploadFile(Object param,
                                                     Supplier<List<K>> dataSupplier,
                                                     IntSupplier countSupplier,
                                                     ExportTaskDTO taskDTO,
                                                     Class<T> excelClass,
                                                     Function<K, T> convertFunction) {
        String taskParams = JSONObject.toJSONString(param);
        taskDTO.setTaskParam(taskParams);
        String loginUserId = userInfo.get().getId();
        TbCdcmrExportTask existTask = adminServiceProApi.checkExistTask(taskDTO, loginUserId);
        if (null != existTask) {
            return existTask;
        }
        adminServiceProApi.checkExportTaskCount(loginUserId);
        int count = countSupplier.getAsInt();
        adminServiceProApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceProApi.addExportTask(taskDTO, loginUserId);
        runTaskAndUploadFile(dataSupplier.get(), exportTask, excelClass, convertFunction);
        return exportTask;
    }

    public ExportTaskVO addAndUploadFile(Object param,
                                         Object objList,
                                         ExportTaskDTO taskDTO) {
        String taskParams = JSONObject.toJSONString(param);
        taskDTO.setTaskParam(taskParams);
        ObjectMapper objectMapper = new ObjectMapper();
        // 将JSON字符串解析为Map
        Map<String, Object> data = null;
        try {
            data = objectMapper.readValue(taskParams, Map.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List list = new ArrayList();
        if (objList instanceof List) {
            list = (List) objList;
        }
        //提取headers数据
        List<LinkedHashMap<String, String>> headers = (List<LinkedHashMap<String, String>>) data.get("headers");
        String loginUserId = userInfo.get().getId();
        TbCdcmrExportTask existTask = adminServiceProApi.checkExistTask(taskDTO, loginUserId);
        if (null != existTask) {
            ExportTaskVO exportTaskVO = new ExportTaskVO();
            BeanUtils.copyProperties(existTask, exportTaskVO);
            return exportTaskVO;
        }
        adminServiceProApi.checkExportTaskCount(loginUserId);
        int count = list.size();
        adminServiceProApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceProApi.addExportTask(taskDTO, loginUserId);
        runTaskAndUploadFileDynamicHeader(list, exportTask, headers);
        ExportTaskVO exportTaskVO = new ExportTaskVO();
        BeanUtils.copyProperties(exportTask, exportTaskVO);
        return exportTaskVO;
    }

    // 动态表头
    private void runTaskAndUploadFileDynamicHeader(List<Object> dataList, TbCdcmrExportTask exportTask, List<LinkedHashMap<String, String>> heads) {
        List<Map<String, Object>> mapList = convert(dataList);
        Runnable task = () -> {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 动态构建表头和数据
            List<List<Object>> data = buildData(heads, mapList);
            EasyExcel.write(outputStream).head(buildHead(heads)).sheet().doWrite(data);
            byte[] bytes = outputStream.toByteArray();
            uploadAndRecord(exportTask, bytes);
        };
        runTask(exportTask, adminServiceProApi.getMaxCsvTimeoutTask(), task);
    }

    public static List<Map<String, Object>> convert(List<Object> objects) {
        return objects.stream()
                .map(obj -> (Map<String, Object>) objectMapper.convertValue(obj, Map.class))
                .collect(Collectors.<Map<String, Object>>toList());
    }

    private <M, N> void runTaskAndUploadFile(List<N> dataList, TbCdcmrExportTask exportTask, Class<M> excelClass, Function<N, M> convertFunction) {
        Runnable task = () -> {
            List<M> excelList = dataList.stream().map(convertFunction).collect(Collectors.toList());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, excelClass)
                    .sheet().doWrite(excelList);
            byte[] bytes = outputStream.toByteArray();
            uploadAndRecord(exportTask, bytes);
        };

        runTask(exportTask, adminServiceProApi.getMaxCsvTimeoutTask(), task);
    }

    public void uploadAndRecord(TbCdcmrExportTask exportTask, byte[] bytes) {
        log.info("开始上传原始文件");
        TbCdcAttachment tbCdcAttachment = fileService.uploadFile(bytes, exportTask.getId() + ".xlsx");
        log.info("结束上传原始文件");

        String attachmentPath = tbCdcAttachment.getAttachmentPath();
        if ("swift".equals(storageSwitch)) {
            attachmentPath = swiftPrefix.concat(attachmentPath);
        } else {
            attachmentPath = minioPrefix.concat(attachmentPath);
        }
        exportTask.setAttachmentId(tbCdcAttachment.getId());
        exportTask.setAttachmentUrl(attachmentPath);
        exportTask.setAttachmentSize(String.valueOf(bytes.length));
        exportTask.setUpdateTime(new Date());
        //启用状态 1进行中;2已完成;3任务中断
        exportTask.setStatus(Common.TASK_STATUS_DONE);
        adminServiceProApi.updateExportTaskById(exportTask);
    }

    private void runTask(TbCdcmrExportTask exportTask, Long timeOut, Runnable task) {
        log.info("流程开始");
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<?> future = executorService.submit(task);
        try {
            future.get(timeOut, TimeUnit.SECONDS);
            log.info("指定时间内完成了");
        } catch (Exception e) {
            log.error("写文件线程异常", e);
            String errorDesc = e.toString();
            // 修改文件上传任务状态
            exportTask.setUpdateTime(new Date());
            //启用状态 1进行中;2已完成;3任务中断
            exportTask.setStatus(Common.TASK_STATUS_ERROR);
            exportTask.setErrorDesc(errorDesc);
            adminServiceProApi.updateExportTaskById(exportTask);
            executorService.shutdownNow();
            future.cancel(true);
        } finally {
            executorService.shutdown();
            log.info("流程finally exportTaskId#{}", exportTask.getId());
        }
        log.info("流程结束 exportTaskId#{}", exportTask.getId());
    }

    // 构建表头
    private List<List<String>> buildHead(List<LinkedHashMap<String, String>> headers) {
        List<List<String>> head = new ArrayList<>();
        for (LinkedHashMap<String, String> header : headers) {
            List<String> columnHead = Collections.singletonList(header.get("title"));
            head.add(columnHead);
        }
        return head;
    }

    // 构建数据行
    private List<List<Object>> buildData(List<LinkedHashMap<String, String>> headers, List<Map<String, Object>> dataList) {
        List<List<Object>> data = new ArrayList<>();
        for (Map<String, Object> rowData : dataList) {
            List<Object> row = new ArrayList<>();
            for (LinkedHashMap<String, String> header : headers) {
                row.add(rowData.get(header.get("field")));

            }
            data.add(row);
        }
        return data;
    }
}

package com.iflytek.fpva.cdc.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;

@Data
public class UploadResultVO {

    @ApiModelProperty("总数")
    private int totalCount;

    @ApiModelProperty("成功数")
    private int successCount;

    @ApiModelProperty("失败数")
    private int failedCount;

    @ApiModelProperty("上传文件ID")
    private String attachmentId;

    @ApiModelProperty("结果文件ID")
    private String resultAttachmentId;

}

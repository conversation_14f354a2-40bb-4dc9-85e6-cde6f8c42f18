package com.iflytek.fpva.cdc.common.model.po;

import lombok.Data;

import java.util.Date;

@Data
public class UapOrganization {

    private String id;

    private String name;

    private String code;

    private String districtCode;

    private String cityCode;

    private String provinceCode;

    private String district;

    private String city;

    private String province;

    private String shortName;

    private String orgType;

    private String orgTypeCode;

    private String highOrg;

    private String higherName;

    private String level;

    private String status;

    private String sort;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private String highOrgCode;

    private String street;

    private String streetCode;

    private String extJson;
}

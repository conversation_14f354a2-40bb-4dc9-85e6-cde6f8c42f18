package com.iflytek.fpva.cdc.common.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CustomizedTypeEnum {
    INFECTIOUS( "传染病", "infected"),
    SYNDROME("症候群", "syndrome"),
    FOOD("食源性疾病", "food"),
    PESTICIDE("农药中毒", "pesticide"),
    HEAT_STROKE( "高温中暑", "heatStroke"),
    CARBON( "非职业性一氧化碳中毒", "carbon"),
    POISON_OTHER( "职业中毒或其他", "poisonOther"),
    UNKNOWN_REASON( "不明原因疾病", "unknownReason"),
    OVEREXPOSURE( "过量受照", "overexposure"),

    CUSTOMIZED("自定义预警", "customized"),
    ;
    private String name;
    private String code;

    CustomizedTypeEnum(String name, String code) {
        this.code = code;
        this.name = name;
    }

    public static CustomizedTypeEnum getByCode(String code){
        return Arrays.stream(CustomizedTypeEnum.values()).filter(c -> c.getCode().equals(code)).findFirst().orElse(null);
    }
}

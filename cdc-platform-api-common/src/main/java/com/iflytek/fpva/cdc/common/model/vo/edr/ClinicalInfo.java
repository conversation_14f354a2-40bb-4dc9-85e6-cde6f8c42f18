package com.iflytek.fpva.cdc.common.model.vo.edr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("临床诊疗信息")
public class ClinicalInfo {

    @ApiModelProperty("病历类型编码")
    private String medicalTypeCode;

    @ApiModelProperty("病历类型名称")
    private String medicalTypeName;

    @ApiModelProperty("就诊日期时间")
    private String visitDate;

    @ApiModelProperty("初步诊断编码")
    private String initialDiagnosisCode;

    @ApiModelProperty("初步诊断名称")
    private String initialDiagnosisName;

    @ApiModelProperty("初步诊断机构编码")
    private String orgCode;

    @ApiModelProperty("初步诊断机构名称")
    private String orgName;

    @ApiModelProperty("就诊流水号")
    private String eventId;

    @ApiModelProperty("入院日期")
    private Date admissionDate;

    @ApiModelProperty("出院日期")
    private Date dischargeDate;

    @ApiModelProperty("入院诊断编码")
    private String admissionDiagnosisCode;

    @ApiModelProperty("入院诊断名称")
    private String admissionDiagnosisName;

    @ApiModelProperty("出院诊断编码")
    private String dischargeDiagnosisCode;

    @ApiModelProperty("出院诊断名称")
    private String dischargeDiagnosisName;

    @ApiModelProperty("病情转归编码")
    private String diseaseProgressionCode;

    @ApiModelProperty("病情转归名称")
    private String diseaseProgressionName;

    @ApiModelProperty("症状信息")
    private List<SymptomInfo> symptomInfos;

    @ApiModelProperty("体格检查信息")
    private List<ExaminationInfo> examinationInfos;

    @ApiModelProperty("影像学检查信息")
    private List<ImagingInfo> imagingInfos;

    @ApiModelProperty("实验室检测信息")
    private List<LabTestInfo> labTestInfos;

    @ApiModelProperty("确诊结果信息")
    private List<DiagnosisResultInfo> diagnosisResultInfos;

    @ApiModelProperty("治疗用药")
    private List<TherapeuticDrugInfo> therapeuticDrugInfos;

    @ApiModelProperty("治疗用药明细")
    private List<TherapeuticDrugInfoDetail> therapeuticDrugInfoDetails;

//    @ApiModelProperty("出入院信息")
//    private List<AdmissionDischargeInfo> admissionDischargeInfos;
}
package com.iflytek.fpva.cdc.common.apiService;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.iflytek.fpva.cdc.common.dto.LogExportReqDto;
import com.iflytek.fpva.cdc.common.dto.ReadRecordQueryDTO;
import com.iflytek.fpva.cdc.common.dto.SignalPushRuleDTO;
import com.iflytek.fpva.cdc.common.dto.message.MessageAddDTO;
import com.iflytek.fpva.cdc.common.dto.message.MessageConfig;
import com.iflytek.fpva.cdc.common.entity.*;
import com.iflytek.fpva.cdc.common.model.dto.ExportTaskDTO;
import com.iflytek.fpva.cdc.common.model.dto.admin.*;
import com.iflytek.fpva.cdc.common.model.outbound.*;
import com.iflytek.fpva.cdc.common.model.vo.TreeNode;
import com.iflytek.fpva.cdc.common.model.vo.admin.*;
import com.iflytek.fpva.cdc.common.model.epi.vo.EcdOrgUserVO;
import com.iflytek.fpva.cdc.common.utils.ApiTool;
import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminServiceProApi {

    @Resource
    private ApiTool apiTool;

    @Value("${cdc-admin-service:http://cdc-admin-service}")
    private String cdcAdminService;

    @Resource
    private ApiService apiService;

    private static final String DIAGNOSE_DESENSITIZED_NAMES = "cdc-platform-service:diagnose_desensitized_names";

    private static final String  DIAGNOSE_DESENSITIZED_ROLE = "cdc-platform-service:diagnose_desensitized_role";

    private static final String EXPORT_MAX_CODE = "cdc-platform-service:export-max";

    private static final int EXPORT_MAX_VALUE = 10000;

    private static final String CSV_TASK_MAX_TIMEOUT_CODE = "cdc-platform-service:csv_task_max_timeout";
    /**
     * 默认超时 5分钟
     */
    private static final Long CSV_TASK_MAX_TIMEOUT_VALUE = 300L;

    /**
     * 或者诊断脱敏角色名称
     */
    public String getDiagnoseDesensitizedRole() {
        return getCdcAdminServiceConfigValue(null, DIAGNOSE_DESENSITIZED_ROLE);
    }

    public String getCdcAdminServiceConfigValue(String orgId, String configCode) {
        String url = cdcAdminService + "/v1/pt/param/config/paramInfoByCode";
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("orgId", orgId);
        requestMap.put("configCode", configCode);
        String configValue = null;

        try {
            configValue = Objects.requireNonNull(apiTool.doPost(url, requestMap, JSONObject.class)).getString("configValue");
        } catch (Exception e) {
            log.error("调用基础管理子系统获取配置出错,orgId:" + orgId + ",configCode:" + configCode, e);
        }
        return configValue;
    }

    public List<String> getDesensitizedDiagnoseNames() {
        final String configValue = getCdcAdminServiceConfigValue(null, DIAGNOSE_DESENSITIZED_NAMES);
        try {
            if (StringUtil.isEmpty(configValue)) {
                return new ArrayList<>();
            }
            return Arrays.asList(configValue.split(","));
        } catch (Exception e) {
            log.error("{}返回值有误#{}", DIAGNOSE_DESENSITIZED_NAMES, configValue, e);
        }
        return new ArrayList<>();
    }

    /**
     * 查询风险等级
     */
    public List<RiskLevelVO> listRiskLevel(String warningType){
        String url = cdcAdminService + "/pt/v1/warningRiskLevelDetail/listRiskLevel?warningType=" + warningType;
        return apiService.doGetList(url, RiskLevelVO.class);
    }

    /**
     * 查询风险等级详情
     */
    public List<RiskLevelDetailVO> listRiskLevelDetail(String warningType){

        String url = cdcAdminService + "/pt/v1/warningRiskLevelDetail/listDiseaseRiskLevel?warningType=" + warningType;
        return Arrays.asList(apiService.doGet(url, RiskLevelDetailVO[].class));
    }

    /**
     * 态势感知
     */
    public String awareness(AwarenessRequestDTO awarenessRequest, String loginUserId){
        Gson gson = new Gson();
        log.info(gson.toJson(awarenessRequest));
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/awareness?loginUserId=" + loginUserId;
        return apiService.doPost(url, awarenessRequest, String.class);
    }

    /**
     * 结果获取
     */
    public AwarenessResultVO loadAwarenessResult(String taskId, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/loadAwarenessResult?loginUserId=" + loginUserId + "&taskId=" + taskId;
        return apiService.doPost(url, new Object(), AwarenessResultVO.class);
    }

    /**
     * 风险等级研判
     */
    public EventClassifyResponseVO eventClassify(EventClassifyRequestDTO requestDTO, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/eventClassify?loginUserId=" + loginUserId;
        return apiService.doPost(url, requestDTO, EventClassifyResponseVO.class);
    }

    /**
     * 风险处置建议
     */
    public DispositionAdviceResponseVO dispositionAdvice( DispositionAdviceRequestDTO requestDTO, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/dispositionAdvice?loginUserId=" + loginUserId;
        return apiService.doPost(url, requestDTO, DispositionAdviceResponseVO.class);
    }

    /**
     * 根据区域编码统计
     */
    public PopulationDataInfoVO statByAreaCodes(PopulationDataInfoQueryDTO queryDTO)  {
        String url = cdcAdminService + "/v1/pt/populationDataInfo/statByAreaCodes";
        return apiTool.doPost(url, queryDTO, PopulationDataInfoVO.class);
    }


    /**
     * 态势推演-时间序列模型 预警风险研究
     */
    public List<Integer> arima(NonDynamicsRequestDTO requestDTO, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/arima?loginUserId=" + loginUserId;
        return apiService.doPostList(url, requestDTO, Integer.class);
    }

    /**
     * 态势推演-时空统计模型
     */
    public List<Integer> stgnn(NonDynamicsRequestDTO request, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/stgnn?loginUserId=" + loginUserId;
        return apiService.doPostList(url, request, Integer.class);
    }

    /**
     * 态势推演-神经网络模型
     */
    public List<Integer> lstm(NonDynamicsRequestDTO request, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/lstm?loginUserId=" + loginUserId;
        return apiService.doPostList(url, request, Integer.class);
    }

    /**
     * 态势推演-传染病特定组合模型
     */
    public List<Integer> seir(DynamicsRequestDTO request, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/seir?loginUserId=" + loginUserId;
        return apiService.doPostList(url, request, Integer.class);
    }

    /**
     * 态势推演-混合模型
     */
    public List<Integer> ensemble(DynamicsRequestDTO request, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/ensemble?loginUserId=" + loginUserId;
        return apiService.doPostList(url, request, Integer.class);
    }

    /**
     * 疾病趋势预测
     */
    public List<Integer> cdcDiseaseTrendPrediction(DiseaseTrendRequestDTO requestDTO, String loginUserId){
        String url = cdcAdminService + "/v1/pt/capacity/algorithm/cdcDiseaseTrendPrediction?loginUserId=" + loginUserId;
        return apiService.doPostList(url, requestDTO, Integer.class);
    }

    public <T> T getConfigValue(String configCode, String configGroup, Function<String, T> mapping, T defaultValue) {
        Optional<String> optional = Optional.ofNullable(this.getStatusByCodeAndGroup(configCode, configGroup));
        if (optional.isPresent()) {
            try {
                return mapping.apply(optional.get());
            } catch (Exception e) {
                log.error("参数转换错误", e);
            }
        }

        return defaultValue;
    }

    public String getStatusByCodeAndGroup(String configCode, String configGroup){
        String url = cdcAdminService + "/v1/pt/param/config/getStatusByCodeAndGroup?configCode=" + configCode + "&configGroup=" + configGroup;
        try {
            return apiTool.doGet(url,String.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取开关状态出错", e);
        }
        return null;
    }

    /**
     * 获取疾病风险等级配置
     * */
    public List<TbCdcmrWarningRiskLevelDetail> listDiseaseRiskLevel(String warningType){
        try {
            String url = cdcAdminService + "/pt/v1/warningRiskLevelDetail/listDiseaseRiskLevel?warningType=" + warningType;
            return Arrays.asList(Objects.requireNonNull(apiTool.doGet(url, TbCdcmrWarningRiskLevelDetail[].class)));
        }catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    /**
     * 获取专题的病原配置信息
     * */
    public List<TbCdcmrMultichannelTopicConfig> listPathogenByTopicId(String topicId){
        try {
            String url = cdcAdminService + "/pt/v1/multichannelTopic/listPathogenByTopicId?topicId=" + topicId;
            return Arrays.asList(Objects.requireNonNull(apiTool.doPost(url, null, TbCdcmrMultichannelTopicConfig[].class)));
        }catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    /**
     * 获取病原信息
     * */
    public List<TbCdcmrPathogenInfo> getPathogenInfo(){
        try {
            String url = cdcAdminService + "/pt/v1/multichannelTopic/getPathogenInfo";
            return Arrays.asList(apiTool.doGet(url, TbCdcmrPathogenInfo[].class));
        }catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    /**
     * 执行外呼任务
     * @param callTask 外呼任务
     * @return 创建结果
     */
    public OutboundRecord execCallTaskAndSave(CallTask callTask) {
        String url = getUrl("/pt/v1/outbound/exec/execCallTaskAndSave");
        return apiService.doPost(url, callTask, OutboundRecord.class);
    }

    /**
     * 执行短信任务
     * @param smsTask 短信任务
     * @return 创建结果
     */
    public OutboundRecord execSmsTaskAndSave(SmsTask smsTask) {
        String url = getUrl("/pt/v1/outbound/exec/execSmsTaskAndSave");
        return apiService.doPost(url, smsTask, OutboundRecord.class);
    }

    /**
     * 加载外呼模板
     */
    public OutboundTemplate loadOutboundTemplate(String id){
        String url = getUrl("/pt/v1/outbound-template/loadById?id=" + id);
        return apiService.doGet(url, OutboundTemplate.class);
    }

    public List<OutboundPersonVO> listPersonByRecordIds(List<String> recordIds){
        String url = getUrl("/pt/v1/outbound/query/listPersonByRecordIds");
        return apiService.doPostList(url, recordIds, OutboundPersonVO.class);

    }

    private String getUrl(String url){
        return cdcAdminService + url;
    }

    public String saveOperationLog(LogExportReqDto record,String loginUserId) {
        String url = getUrl("/v1/pt/operationLog/save?loginUserId="+loginUserId);
        return apiService.doPost(url, record, String.class);
    }
    public String saveMessage(MessageAddDTO dto) {
        String url = getUrl("/v2/pb/message/save");
        return apiService.doPost(url, dto, String.class);
    }
    
    public MessageConfig getMessageConfigById(String id) {
        String url = getUrl("/v2/pb/message/getConfigById?id=" + id);
        return apiService.doGet(url, MessageConfig.class);
    }

    /**
     * 通过区域编码查询
     */
    public Map<String, PopulationDataInfoVO> groupByAreaCodes(Date date,
                                                              List<String> provinceCodes,
                                                              List<String> cityCodes,
                                                              List<String> districtCodes){
        return listByAreaCodes(date, provinceCodes, cityCodes, districtCodes).stream().collect(Collectors.toMap(
                PopulationDataInfoVO::getAreaCode,
                p -> p
        ));
    }

    /**
     * 通过区域编码查询
     */
    public List<PopulationDataInfoVO> listByAreaCodes(Date statDate,
                                                      List<String> provinceCodes,
                                                      List<String> cityCodes,
                                                      List<String> districtCodes){
        Map<String, Object> params = new HashMap<>();
        params.put("provinceCodes", provinceCodes);
        params.put("cityCodes", cityCodes);
        params.put("districtCodes", districtCodes);
        params.put("statDate", statDate);

        String url = getUrl("/v2/pt/populationDataInfo/listByAreaCodes");
        return apiService.doPostList(url, params, PopulationDataInfoVO.class);
    }
    /**
     * 信号手动推送规则
     */
    public Void saveSignPushRule(List<SignalPushRuleDTO> dtos) {
        String url = getUrl("/v2/pt/warningChargePerson/savePushRule");
        return apiService.doPost(url, dtos, Void.class);
    }
    /**
     * 任务完成停止消息提醒
     */
    public Integer stopSignalPushMessage(SignalPushRuleDTO dto) {
        String url = getUrl("/v2/pt/warningChargePerson/stopSignalPushMessage");
        return apiService.doPost(url, dto, Integer.class);
    }
    /**
     * 任务完成停止消息提醒
     */
    public Integer stopSignalPushMessage(List<SignalPushRuleDTO> dtos) {
        String url = getUrl("/v2/pt/warningChargePerson/stopSignalPushMessage");
        return apiService.doPost(url, dtos, Integer.class);
    }
    /**
     * 查询信号推送规则
     */
    public List<SignalPushRuleDTO> getSignalPushRule(List<SignalPushRuleDTO> signalList) {
        String url = getUrl("/v1/pt/warningChargePerson/getSignalPushRule");
        return apiService.doPostList(url,signalList,SignalPushRuleDTO.class);
    }

    /**
     * 检查导出任务是否存在
     * */
    public TbCdcmrExportTask checkExistTask(ExportTaskDTO taskDTO, String loginUserId) {
        String url = cdcAdminService + "/pt/v2/exportTask/checkExistTask?loginUserId="+loginUserId;
        return apiTool.doPost(url, taskDTO, TbCdcmrExportTask.class);
    }

    /**
     * 检查导出数量
     * */
    public Boolean checkExportTaskCount( String loginUserId) {
        String url = cdcAdminService + "/pt/v2/exportTask/checkExportTaskCount?loginUserId="+loginUserId;
        Boolean countFlag = apiTool.doPost(url,null, Boolean.class);
        if (!countFlag){
            throw new MedicalBusinessException("任务排队中，请稍后重试");
        }
        return true;
    }

    public void checkExportMax(int count) {
        int exportMax = getExportMax();
        if (count > exportMax) {
            throw new MedicalBusinessException("最多导出" + exportMax + "条数据");
        }

    }

    /**
     * 新增导出任务
     * */
    public TbCdcmrExportTask addExportTask(ExportTaskDTO taskDTO, String loginUserId) {
        String url = cdcAdminService + "/pt/v2/exportTask/add?loginUserId="+loginUserId;
        return apiTool.doPost(url, taskDTO, TbCdcmrExportTask.class);
    }

    public int getExportMax(){
        final String configValue = getCdcAdminServiceConfigValue(null, EXPORT_MAX_CODE);
        try {
            return Integer.parseInt(configValue);
        } catch (Exception e){
            log.error("返回值有误#{}",configValue,e);
        }
        return EXPORT_MAX_VALUE;
    }

    public Long getMaxCsvTimeoutTask() {
        final String configValue = getCdcAdminServiceConfigValue(null, CSV_TASK_MAX_TIMEOUT_CODE);
        try {
            return Long.valueOf(configValue);
        } catch (Exception e) {
            log.error("返回值有误#{}", configValue, e);
        }
        return CSV_TASK_MAX_TIMEOUT_VALUE;
    }

    public Boolean updateExportTaskById(TbCdcmrExportTask exportTask) {
        String url = cdcAdminService + "/pt/v2/exportTask/updateById";
        return apiTool.doPost(url, exportTask, Boolean.class);
    }

    public TbCdcAttachment saveAttachmentRecord(TbCdcAttachment tbCdcAttachment) {
        String url = cdcAdminService + "/pt/v2/fileManage/saveAttachmentRecord";
        return apiTool.doPost(url, tbCdcAttachment, TbCdcAttachment.class);
    }

    /**
     * 事件处理配置-疾病查询
     */
    public List<EventChargePersonVO> listEventChargePerson(@RequestBody List<EventChargePersonQuery> input) {
        String url = cdcAdminService + "/v1/pt/eventChargePerson/getDealPersonInfoBy";
        return apiService.doPostList(url, input, EventChargePersonVO.class);
    }

    /**
     * 获取主数据级联信息
     * */
    public List<TreeNode> getMasterDataInfo(String masterDataType) {
        Map<String, String> param = new HashMap<>();
        param.put("masterDataType", masterDataType);

        String url = cdcAdminService + "/pt/v1/masterData/getMasterData";
        try {
            return Arrays.asList(apiTool.doPost(url, param, TreeNode[].class));
        } catch (Exception e) {
            log.error("调用基础管理子系统获取主数据信息出错", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取专家列表
     * */
    public List<String> getExpertUserId() {

        String url = cdcAdminService + "/v1/pt/warningChargePerson/getExpertList";
        try {
            List<EcdOrgUserVO> result = Arrays.asList(apiTool.doGet(url, EcdOrgUserVO[].class));
            return result.stream().map(EcdOrgUserVO::getUapUserId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("调用基础管理子系统获取专家列表出错", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取专家列表
     * */
    public List<EcdOrgUserVO> getExpertList() {

        String url = cdcAdminService + "/v1/pt/warningChargePerson/getExpertList";
        try {
            return Arrays.asList(apiTool.doGet(url, EcdOrgUserVO[].class));
        } catch (Exception e) {
            log.error("调用基础管理子系统获取专家列表出错", e);
        }
        return new ArrayList<>();
    }

    /**
     * 根据疾病的code以及风险等级，获取对应的责任人信息
     * */
    public List<WarningChargePersonVO> getDealPersonInfoBy(List<DealPersonQueryDTO> dtoList){
        try {
            String url = cdcAdminService + "/v1/pt/warningChargePerson/getDealPersonInfoBy";
            return Arrays.asList(apiTool.doPost(url, dtoList, WarningChargePersonVO[].class));
        }catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    /**
     * 根据省市区code获取应急值班人员
     */
    public List<DutyUserVO> getDutyUserList(List<DutyUserVO> dtoList){
        try {
            String url = cdcAdminService + "/v1/pt/warningChargePerson/getDutyUserList";
            return Arrays.asList(apiTool.doPost(url, dtoList, DutyUserVO[].class));
        }catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    /**
     * 根据省市区code获取应急值班人员
     */
    public List<TbCdcdmDataModelForm> getModelFormByConfigInfo(String configInfo){
        try {
            String url = cdcAdminService + "/pt/v1/dataModel/getModelFormByConfigInfo?configInfo=" + configInfo;
            return Arrays.asList(apiTool.doGet(url, TbCdcdmDataModelForm[].class));
        }catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    /**
     * 查询信号已读状态
     */
    public List<ReadRecordVO> getReadStatusRecord(ReadRecordQueryDTO queryDTO, String loginUserId) {

        try {
            String url = cdcAdminService + "/v1/pt/readRecord/findRecordList?loginUserId=" + loginUserId;
            return Arrays.asList(apiTool.doPost(url, queryDTO, ReadRecordVO[].class));
        } catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

    public ReportRemindTimeGapConfigVO getReportRemindTimeGapConfigVO(String loginUserId){
        try {
            String url = cdcAdminService + "/pt/v1/reportRemind/config/queryByLoginUserId?loginUserId=" + loginUserId;
            return apiTool.doGet(url, ReportRemindTimeGapConfigVO.class);
        } catch (Exception e) {
            log.error("调用后管接口异常", e);
            throw new MedicalBusinessException("11464001", "调用后管接口异常");
        }
    }

}

package com.iflytek.fpva.cdc.common.dto.message;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 消息提醒表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MessageConfig对象", description="消息提醒配置表")
public class MessageConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String TB_NAME ="app.tb_cdcmr_message_config";

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "app编码")
    private String appCode;

    @ApiModelProperty(value = "消息类型  1-待办提醒 ；2-系统通知")
    private Integer messageType;

    @ApiModelProperty(value = "消息名称")
    private String messageName;

    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    @ApiModelProperty(value = "来源系统编码")
    private String sourceSystemCode;

    @ApiModelProperty(value = "来源系统名称")
    private String sourceSystemName;

    @ApiModelProperty(value = "系统中相对路径")
    private String systemRelativePath;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String creatorId;

    private String creator;

    private String updaterId;

    private String updater;

    @ApiModelProperty(value = "是否删除 0未删除 1删除")
    private String deleteFlag;


}

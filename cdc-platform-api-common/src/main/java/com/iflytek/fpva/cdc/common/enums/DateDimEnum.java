package com.iflytek.fpva.cdc.common.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DateDimEnum {
    DAY("day", "日"),
    WEEK("week", "周"),
    MEADOW("meadow", "旬"),
    MONTH("month", "月"),
    QUARTER("quarter", "季度"),
    HALF_YEAR("halfYear", "半年"),
    YEAR("year", "年")

    ;
    private String code;
    private String desc;

     DateDimEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DateDimEnum getByCode(String code){
         return Arrays.stream(DateDimEnum.values()).filter(d -> d.getCode().equals(code)).findFirst().orElse(null);
    }
}

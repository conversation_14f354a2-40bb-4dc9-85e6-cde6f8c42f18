package com.iflytek.fpva.cdc.common.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;


@Getter
public enum SourceKeyTypeEnum implements BaseEnum{
    MEDICAL_PROCESS("PROCESS", "病例进程"),
    REPORT_CARD("2", "报卡");

    private String code;
    private String desc;

    SourceKeyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<String, Object> mapValues() {
        return Arrays.stream(SourceKeyTypeEnum.values()).collect(Collectors.toMap(SourceKeyTypeEnum::name, s -> s));
    }




}

package com.iflytek.fpva.cdc.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class MsMedicalQuery{
    @ApiModelProperty("区域查询类型")
    private String addressType;
    @ApiModelProperty("时间查询类型")
    private String timeType;
    @ApiModelProperty("传染病编码")
    private String infectCode;
    @ApiModelProperty("传染病大类")
    private String infectClass;
    @ApiModelProperty("传染病类型")
    private String infectType;
    @ApiModelProperty("传染病类型")
    private List<String> infectTypes;
    @ApiModelProperty("过滤的传染病")
    private List<String> filterInfectCodes;
    @ApiModelProperty( "机构类型")
    private String orgType;
    @ApiModelProperty("疾病类型，传染病：infected，症候群：syndrome")
    private String warningType;
    @ApiModelProperty("疾病编码")
    private String diseaseCode;
    @ApiModelProperty("疾病名称")
    private String diseaseName;
    @ApiModelProperty("病程id集合")
    private List<String> processIds;

    private List<String> eventIds;

}

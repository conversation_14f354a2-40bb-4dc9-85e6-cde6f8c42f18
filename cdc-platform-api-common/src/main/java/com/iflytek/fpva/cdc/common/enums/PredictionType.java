package com.iflytek.fpva.cdc.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum PredictionType {
    MED("med", "病例数"),
    DEATH("death", "死亡数"),
    CASE_FATALITY("caseFatality", "病死数")
    ;


    private String code;
    private String desc;

    PredictionType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

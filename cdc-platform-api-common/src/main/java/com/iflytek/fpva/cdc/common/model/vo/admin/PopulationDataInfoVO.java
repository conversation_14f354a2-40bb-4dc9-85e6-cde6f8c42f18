package com.iflytek.fpva.cdc.common.model.vo.admin;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PopulationDataInfoVO {
    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区域级别
     */
    private Integer areaLevel;

    /**
     * 常住人口
     */
    private Integer residentPopulation;

    /**
     * 户籍人口
     */
    private Integer registeredPopulation;

    /**
     * GDP数据-单位（亿元）
     */
    private BigDecimal gdp;

    /**
     * 城镇居民可支配收入
     */
    private BigDecimal urbanDpi;

    /**
     * 农村居民可支配收入
     */
    private BigDecimal ruralDpi;



    /**
     * 统计时间
     */
    private Date statDate;

    /**
     * 查询时间
     */
    private Date queryDate;


    private String provinceCode;

    private String cityCode;

    private String districtCode;
}

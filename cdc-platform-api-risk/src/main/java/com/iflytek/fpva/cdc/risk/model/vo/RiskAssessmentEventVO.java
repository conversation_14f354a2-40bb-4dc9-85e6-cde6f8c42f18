package com.iflytek.fpva.cdc.risk.model.vo;

import com.github.pagehelper.PageInfo;
import com.iflytek.fpva.cdc.risk.entity.RiskAssessmentEventMonitorRecord;
import com.iflytek.fpva.cdc.risk.model.dto.MonitoringDataRecordingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("风险评估详情VO")
@Data
public class RiskAssessmentEventVO {
    @ApiModelProperty("监测数据记录")
    private PageInfo<RiskAssessmentEventMonitorRecord> monitoringDataRecordingDTOList;

    /**
     * 风险评估事件评估准备id
     */
    @ApiModelProperty("风险评估事件评估准备id")
    private String prepareId;

    /**
     * 评估准备
     */
    @ApiModelProperty(value = "评估准备")
    private String prepareInfo;

    /**
     * 评估类型
     */
    @ApiModelProperty(value = "评估类型-routineRiskAssessment/expertRiskAssessment")
    private String assessmentType;

    /**
     * 评估方法
     */
    @ApiModelProperty(value = "评估方法")
    private String assessmentMethod;

    /**
     * 评估议题
     */
    @ApiModelProperty(value = "评估议题")
    private String assessmentTopic;

    /**
     * 业务主持人
     */
    @ApiModelProperty(value = "业务主持人")
    private String moderator;

    /**
     * 会商组织者
     */
    @ApiModelProperty(value = "会商组织者")
    private String organizer;

    /**
     * 事件类型
     */
    @ApiModelProperty(value = "事件类型")
    private String eventType;

    /**
     * 事件状态
     */
    @ApiModelProperty(value = "事件状态")
    private String eventStatus;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String cityName;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private String districtCode;

    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称")
    private String districtName;

    /**
     * 街道编码
     */
    @ApiModelProperty(value = "街道编码")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(value = "街道名称")
    private String streetName;

    /**
     * 评估月份
     */
    @ApiModelProperty(value = "评估月份")
    private String assessmentMonth;

    /**
     * 截止时间
     */
    @ApiModelProperty(value = "截止时间")
    private Date endDate;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String notes;

    @ApiModelProperty(value = "专家评议")
    private String expertReview;
}

package com.iflytek.fpva.cdc.risk.service.impl;

import java.util.*;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.fpva.cdc.common.apiService.UapServiceProApi;
import com.iflytek.fpva.cdc.common.apiService.AdminServiceProApi;
import com.iflytek.fpva.cdc.common.constant.CommonConstants;
import com.iflytek.fpva.cdc.common.constant.Constants;
import com.iflytek.fpva.cdc.common.enums.BooleanEnum;
import com.iflytek.fpva.cdc.common.model.epi.vo.EcdOrgUserVO;
import com.iflytek.fpva.cdc.common.model.po.UapUserPo;
import com.iflytek.fpva.cdc.constant.enums.DeleteFlagEnum;
import com.iflytek.fpva.cdc.emergency.enums.EventStatusEnum;
import com.iflytek.fpva.cdc.emergency.mapper.TbCdcewEmergencyEventMapper;
import com.iflytek.fpva.cdc.emergency.service.EmergencyCommonService;
import com.iflytek.fpva.cdc.emergency.service.EmergencyEventProcessTaskService;
import com.iflytek.fpva.cdc.model.vo.DistrictMappingVO;
import com.iflytek.fpva.cdc.risk.entity.*;
import com.iflytek.fpva.cdc.risk.enums.AssessmentStatusEnum;
import com.iflytek.fpva.cdc.risk.enums.ReportReviewStatusEnum;
import com.iflytek.fpva.cdc.risk.enums.RiskGradeEnum;
import com.iflytek.fpva.cdc.risk.enums.RiskLevelEnum;
import com.iflytek.fpva.cdc.risk.mapper.*;
import com.iflytek.fpva.cdc.risk.model.dto.*;
import com.iflytek.fpva.cdc.risk.model.vo.*;
import com.iflytek.fpva.cdc.service.common.AreaService;
import com.iflytek.fpva.cdc.util.BeanUtils;
import com.iflytek.fpva.cdc.warning.enums.TimeLineMomentTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iflytek.fpva.cdc.risk.service.RiskAssessmentCommonService;
import com.iflytek.fpva.cdc.risk.service.RiskAssessmentService;
import com.iflytek.medicalboot.core.id.BatchUidService;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.fpva.cdc.common.interceptor.UserInfoInterceptor.userInfo;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;

@Service
@Slf4j
public class RiskAssessmentServiceImpl implements RiskAssessmentService {

    private static final String TB_CDCEW_EMERGENCY_EVENT = "tb_cdcew_emergency_event";

    public static final String TB_CDCEW_RISK_ASSESSMENT_EVENT_REPORT = "tb_cdcew_risk_assessment_event_report";

    public static final String TB_CDCEW_RISK_ASSESSMENT_EVENT_TIMELINE = "tb_cdcew_risk_assessment_event_timeline";

    public static final String TB_CDCEW_RISK_ASSESSMENT_EVENT_TIMELINE_DETAIL = "tb_cdcew_risk_assessment_event_timeline_detail";

    @Resource
    private RiskAssessmentEventReportMapper riskAssessmentEventReportMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private RiskAssessmentCommonService riskAssessmentCommonService;

    @Resource
    private RiskAssessmentEventTimelineMapper riskAssessmentEventTimelineMapper;
    @Resource
    private RiskAssessmentEventMonitorRecordMapper riskAssessmentEventMonitorRecordMapper;

    @Resource
    private RiskAssessmentEventTimelineDetailMapper riskAssessmentEventTimelineDetailMapper;
    @Resource
    private RiskAssessmentEventMapper riskAssessmentEventMapper;

    @Resource
    private RiskAssessmentEventPrepareMapper riskAssessmentEventPrepareMapper;

    @Resource
    private RiskAssessmentEventImplementationMapper riskAssessmentEventImplementationMapper;
    
    @Resource
    private TbCdcewRiskAssessmentQuestionTemplateMapper tbCdcewRiskAssessmentQuestionTemplateMapper;

    @Resource
    private UapServiceProApi uapServiceProApi;

    @Resource
    private AdminServiceProApi adminServiceProApi;

    @Resource
    private TbCdcewRiskAssessmentQuestionResultMapper tbCdcewRiskAssessmentQuestionResultMapper;

    @Resource
    private TbCdcewRiskAssessmentQuestionnaireRoundMapper tbCdcewRiskAssessmentQuestionnaireRoundMapper;

    @Resource
    private EmergencyCommonService emergencyCommonService;
    
    @Resource
    private TbCdcewEmergencyEventMapper tbCdcewEmergencyEventMapper;

    @Resource
    private EmergencyEventProcessTaskService emergencyEventProcessTaskService;

    @Value("${role.name.moderator:业务主持人}")
    private String moderator;

    @Resource
    private TbCdcewRiskAssessmentFactorResultMapper tbCdcewRiskAssessmentFactorResultMapper;

    @Resource
    private TbCdcewEmergencyEventRiskAssessmentRelationMapper tbCdcewEmergencyEventRiskAssessmentRelationMapper;

    @Resource
    private TbCdcewRiskAssessmentExpertReviewIndexMapper tbCdcewRiskAssessmentExpertReviewIndexMapper;

    @Autowired
    private AreaService areaService;

    @Override
    public void editAssessmentEventReport(RiskAssessmentEventReport report) {
        if (StringUtils.isEmpty(report.getAssessmentEventId())) {
            return;
        }
        // 根据评估事件ID查询是否已存在报告记录
        RiskAssessmentEventReport oldReport = riskAssessmentEventReportMapper.selectByAssessmentEventId(report.getAssessmentEventId());
        if (oldReport == null) {
//            新增
            report.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_RISK_ASSESSMENT_EVENT_REPORT)));
        }
        else {
            //        如果报告状态为已归档，不可修改
            if (StringUtils.equals(oldReport.getReviewStatus(), ReportReviewStatusEnum.ARCHIVED.getCode())) {
                throw new MedicalBusinessException("报告已归档不可修改");
            }
//            编辑
            report.setId(oldReport.getId());
        }
        riskAssessmentCommonService.setUserInfo(report);
        RiskAssessmentEvent event = riskAssessmentEventMapper.selectById(report.getAssessmentEventId());
        //            如果事件状态不为空且为报告撰写进行中，修改风险评估状态为已完成
        if (event != null && StringUtils.isNotEmpty(report.getEventStatus()) && AssessmentStatusEnum.IN_REPORT_WRITING.getCode().equals(report.getEventStatus())) {
            event.setEventStatus(AssessmentStatusEnum.COMPLETED.getCode());
            riskAssessmentEventMapper.updateById(event);
        }
        riskAssessmentEventReportMapper.upsert(report);
    }

    @Override
    public RiskAssessmentEventReportVO getRiskAssessmentEventReport(String eventId) {

        QueryWrapper<RiskAssessmentEventReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("assessment_event_id", eventId);
        RiskAssessmentEventReport report = riskAssessmentEventReportMapper.selectOne(queryWrapper);
        RiskAssessmentEvent event = riskAssessmentEventMapper.selectById(eventId);
        RiskAssessmentEventReportVO vo = new RiskAssessmentEventReportVO();
        BeanUtils.copyProperties(report, vo);
        BeanUtils.copyProperties(event, vo);
//        设置评估准备
        RiskAssessmentEventPrepare prepare = riskAssessmentEventPrepareMapper.selectByAssessmentEventId(eventId);
        if (prepare != null) {
            vo.setPrepareInfo(prepare.getPrepareInfo());
        }
        vo.setUpdateTime(report.getUpdateTime());
        return vo;
    }

    @Override
    @Transactional
    public void addAssessmentTimeline(RiskAssessmentEventTimeline timeline) {

        timeline.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_RISK_ASSESSMENT_EVENT_TIMELINE)));
        riskAssessmentCommonService.setUserInfo(timeline);
        riskAssessmentEventTimelineMapper.insert(timeline);

        List<RiskAssessmentEventTimelineDetail> timelineDetails = timeline.getTimelineDetails();
        if (CollUtil.isNotEmpty(timelineDetails)) {
            timelineDetails.forEach(timelineDetail -> {
                timelineDetail.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_RISK_ASSESSMENT_EVENT_TIMELINE_DETAIL)));
                timelineDetail.setTimelineId(timeline.getId());
                timelineDetail.setCreateTime(new Date());
            });
            riskAssessmentEventTimelineDetailMapper.insertBatch(timelineDetails);
        }
    }

    @Override
    public List<RiskAssessmentEventTimeline> listAssessmentTimeline(String eventId) {

        QueryWrapper<RiskAssessmentEventTimeline> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("assessment_event_id", eventId);
        queryWrapper.orderByDesc("create_time");
        return riskAssessmentEventTimelineMapper.selectList(queryWrapper);
    }

    @Override
    public List<RiskAssessmentEventTimelineDetail> listAssessmentTimelineDetail(String timelineId) {

        QueryWrapper<RiskAssessmentEventTimelineDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("timeline_id", timelineId);
        queryWrapper.orderByDesc("create_time");
        return riskAssessmentEventTimelineDetailMapper.selectList(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveOrSubmitAssessment(RiskAssessmentEventDTO assessmentEventDTO) {
        boolean isEdit = StringUtils.isNotEmpty(assessmentEventDTO.getId());
        String eventId = assessmentEventDTO.getId();

        // 风险评估基本信息
        RiskAssessmentEvent riskAssessmentEvent = new RiskAssessmentEvent();
        BeanUtils.copyProperties(assessmentEventDTO, riskAssessmentEvent);

        RiskAssessmentEvent existingEvent = null;

        // 设置主键ID（新增时）
        if (!isEdit) {
            eventId = String.valueOf(batchUidService.getUid(RiskAssessmentEvent.TABLE_NAME));
        } else {
            // 查询风险评估事件
            existingEvent = riskAssessmentEventMapper.selectById(eventId);
        }
        riskAssessmentEvent.setId(eventId);

        // 设置用户信息
        riskAssessmentCommonService.setUserInfo(riskAssessmentEvent);

        // 设置事件状态
        if ("save".equalsIgnoreCase(assessmentEventDTO.getSaveType())) {
            riskAssessmentEvent.setEventStatus(AssessmentStatusEnum.NO_START.getCode());
        } else if ("submit".equalsIgnoreCase(assessmentEventDTO.getSaveType())) {
            // 如果事件状态为空或为未开始，状态修改为进行中
            if (StringUtils.isBlank(riskAssessmentEvent.getEventStatus())
                    || AssessmentStatusEnum.NO_START.getCode().equals(riskAssessmentEvent.getEventStatus())) {
                riskAssessmentEvent.setEventStatus(AssessmentStatusEnum.IN_IMPLEMENTATION.getCode());
            }
        }

        // 仅当 Moderator 原来为空时设置
        if (!isEdit || (existingEvent != null && StringUtils.isBlank(existingEvent.getModerator()))) {
            riskAssessmentEvent.setModerator(Optional.ofNullable(userInfo.get()).orElse(new UapUserPo()).getName());
        }

        // 填充省市区
        dealAreaInfo(riskAssessmentEvent);
        // 保存或更新
        riskAssessmentEventMapper.upsert(riskAssessmentEvent);

        // 处理监测数据记录
        if (CollectionUtils.isNotEmpty(assessmentEventDTO.getMonitoringDataRecordingDTOList())) {
            List<RiskAssessmentEventMonitorRecord> recordList = new ArrayList<>();
            for (MonitoringDataRecordingDTO dto : assessmentEventDTO.getMonitoringDataRecordingDTOList()) {
                RiskAssessmentEventMonitorRecord record = new RiskAssessmentEventMonitorRecord();
                BeanUtils.copyProperties(dto, record);
                if (StringUtils.isEmpty(dto.getMonitorDataId())) {
                    // 新增
                    record.setId(String.valueOf(batchUidService.getUid(RiskAssessmentEventMonitorRecord.TABLE_NAME)));
                }
                else {
                    record.setId(dto.getMonitorDataId());
                }
                riskAssessmentCommonService.setUserInfo(record);
                record.setAssessmentEventId(eventId);
                record.setUpdateTime(dto.getUpdateTime());
                recordList.add(record);
            }
            if(recordList.size() > 0) {
                riskAssessmentEventMonitorRecordMapper.upsert(recordList);
            }
        }

        // 处理评估准备数据
        RiskAssessmentEventPrepare prepare = new RiskAssessmentEventPrepare();
        if (StringUtils.isEmpty(assessmentEventDTO.getPrepareId())) {
            prepare.setId(String.valueOf(batchUidService.getUid(RiskAssessmentEventPrepare.TABLE_NAME)));
        } else {
            prepare.setId(assessmentEventDTO.getPrepareId());
        }
        riskAssessmentCommonService.setUserInfo(prepare);
        prepare.setAssessmentEventId(eventId);
        prepare.setPrepareInfo(assessmentEventDTO.getPrepareInfo());
        riskAssessmentEventPrepareMapper.upsert(prepare);
        return eventId;
    }

    private void dealAreaInfo(RiskAssessmentEvent riskAssessmentEvent) {
        //当省code缺失有市code 填充省code 当省市code缺失有区县code 填充省市code 当省市区code缺失有街道code 填充省市区code
        if (StringUtils.isBlank(riskAssessmentEvent.getProvinceCode()) && StringUtils.isNotBlank(riskAssessmentEvent.getCityCode())) {
            List<DistrictMappingVO> list = areaService.queryRegionInfoByRegionCodes(Collections.singletonList(riskAssessmentEvent.getCityCode()));
            if (CollectionUtils.isNotEmpty(list)) {
                riskAssessmentEvent.setProvinceCode(list.get(0).getProvinceCode());
                riskAssessmentEvent.setProvinceName(list.get(0).getProvinceName());
            }
        } else if (StringUtils.isAllBlank(riskAssessmentEvent.getProvinceCode(), riskAssessmentEvent.getCityCode()) && StringUtils.isNotBlank(riskAssessmentEvent.getDistrictCode())) {
            List<DistrictMappingVO> list = areaService.queryRegionInfoByRegionCodes(Collections.singletonList(riskAssessmentEvent.getDistrictCode()));
            if (CollectionUtils.isNotEmpty(list)) {
                riskAssessmentEvent.setProvinceCode(list.get(0).getProvinceCode());
                riskAssessmentEvent.setProvinceName(list.get(0).getProvinceName());
                riskAssessmentEvent.setCityCode(list.get(0).getCityCode());
                riskAssessmentEvent.setCityName(list.get(0).getCityName());
            }
        } else if (StringUtils.isAllBlank(riskAssessmentEvent.getProvinceCode(), riskAssessmentEvent.getCityCode(), riskAssessmentEvent.getDistrictCode())
                && StringUtils.isNotBlank(riskAssessmentEvent.getStreetCode())) {
            List<DistrictMappingVO> list = areaService.queryRegionInfoByRegionCodes(Collections.singletonList(riskAssessmentEvent.getStreetCode()));
            if (CollectionUtils.isNotEmpty(list)) {
                riskAssessmentEvent.setProvinceCode(list.get(0).getProvinceCode());
                riskAssessmentEvent.setProvinceName(list.get(0).getProvinceName());
                riskAssessmentEvent.setCityCode(list.get(0).getCityCode());
                riskAssessmentEvent.setCityName(list.get(0).getCityName());
                riskAssessmentEvent.setDistrictCode(list.get(0).getDistrictCode());
                riskAssessmentEvent.setDistrictName(list.get(0).getDistrictName());
            }
        }
    }

    @Override
    public RiskAssessmentEventVO getAssessmentEventDetail(AssessmentEventDetailDTO dto) {
        RiskAssessmentEventVO vo = new RiskAssessmentEventVO();

        // 基本信息
        RiskAssessmentEvent event = riskAssessmentEventMapper.selectById(dto.getId());
        if (event == null) {
            return null;
        }
        BeanUtils.copyProperties(event, vo);

        // 监测数据列表
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<RiskAssessmentEventMonitorRecord> monitorRecords = riskAssessmentEventMonitorRecordMapper.selectByAssessmentEventId(dto.getId());
        vo.setMonitoringDataRecordingDTOList(new PageInfo<>(monitorRecords));

        // 评估准备信息
        RiskAssessmentEventPrepare prepare = riskAssessmentEventPrepareMapper.selectByAssessmentEventId(dto.getId());
        if (prepare != null) {
            vo.setPrepareId(prepare.getId());
            vo.setPrepareInfo(prepare.getPrepareInfo());
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitImplementation(RiskAssessmentEventImplementDTO assessmentEventImplementDTO) {
        if (StringUtils.isEmpty(assessmentEventImplementDTO.getAssessmentEventId())) {
            return;
        }

        // 根据评估事件ID查找是否已存在实施记录
        RiskAssessmentEventImplementation existing = riskAssessmentEventImplementationMapper
                .selectByAssessmentEventId(assessmentEventImplementDTO.getAssessmentEventId());

        if (existing == null) {
            // 新增
            RiskAssessmentEventImplementation implementation = new RiskAssessmentEventImplementation();
            BeanUtils.copyProperties(assessmentEventImplementDTO, implementation);
            implementation.setId(String.valueOf(batchUidService.getUid(RiskAssessmentEventImplementation.TABLE_NAME)));
            riskAssessmentCommonService.setUserInfo(implementation);
            riskAssessmentEventImplementationMapper.insert(implementation);
        } else {
            // 编辑
            existing.setImplementation(assessmentEventImplementDTO.getImplementation());
            riskAssessmentCommonService.updateUserInfo(existing);
            riskAssessmentEventImplementationMapper.updateById(existing);
        }
        RiskAssessmentEvent event = riskAssessmentEventMapper.selectById(assessmentEventImplementDTO.getAssessmentEventId());
        //            如果事件状态不为空且为实施进行中，修改风险评估状态为报告撰写进行中
        if (StringUtils.isNotEmpty(event.getEventStatus()) && AssessmentStatusEnum.IN_IMPLEMENTATION.getCode().equals(event.getEventStatus())) {
            event.setEventStatus(AssessmentStatusEnum. IN_REPORT_WRITING.getCode());
        }

        // 解析 implementation 字段中的 riskLevel
        String implementationJson = assessmentEventImplementDTO.getImplementation();
        if (StringUtils.isNotBlank(implementationJson)) {
            try {
                JSONObject json = JSONObject.parseObject(implementationJson);
                String riskLevel = json.getString("accuracyLevel");
                event.setRiskLevel(RiskLevelEnum.getNameByLabel(riskLevel));
            } catch (Exception e) {
                log.warn("实施内容中解析 riskLevel 失败: {}", e.getMessage());
            }
        }
        riskAssessmentCommonService.updateUserInfo(event);
        riskAssessmentEventMapper.updateById(event);
    }

    @Override
    public PageInfo<RiskAssessmentEventPageVO> pageListAssessment(RiskAssessmentEventQueryDTO dto) {
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        List<RiskAssessmentEventPageVO> list = riskAssessmentEventMapper.list(dto, uapUserPo.getId(), uapUserPo.getName());
        return new PageInfo<>(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAssessment(String eventId) {
        if (StringUtils.isEmpty(eventId)) {
            throw new MedicalBusinessException("评估ID不能为空");
        } 
        UpdateWrapper<RiskAssessmentEvent> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("delete_flag", DeleteFlagEnum.YES.getCode());
        updateWrapper.eq("id", eventId);
        riskAssessmentEventMapper.update(null, updateWrapper);
    }

    @Override
    public RiskAssessmentEventImplementation getImplementationDetail(String eventId) {
        RiskAssessmentEventImplementation implementation = riskAssessmentEventImplementationMapper.selectByAssessmentEventId(eventId);
        return implementation;
    }

//    将已到截止时间但未完成的评估事件状态更新为“已结束”
    @Scheduled(cron = "0 */5 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToEnded() {
        riskAssessmentEventMapper.updateStatusToEndedIfExpired();
    }

    @Override
    public List<String> getModerators() {
//        获取业务主持人列表
        return riskAssessmentEventMapper.listModerators();
    }

    @Override
    public void saveQuestionnaireTemplate(TbCdcewRiskAssessmentQuestionTemplate template) {
//        新增
        if (StringUtils.isEmpty(template.getId())) {
            template.setId(String.valueOf(batchUidService.getUid(TbCdcewRiskAssessmentQuestionTemplate.TABLE_NAME)));
            riskAssessmentCommonService.setUserInfo(template);
            template.setStatus(CommonConstants.STR_ONE);
            tbCdcewRiskAssessmentQuestionTemplateMapper.insert(template);
        }
        else {
            riskAssessmentCommonService.updateUserInfo(template);
            tbCdcewRiskAssessmentQuestionTemplateMapper.updateById(template);
        }
    }

    @Override
    public TbCdcewRiskAssessmentQuestionTemplate getQuestionnaireTemplate(String id) {
        return tbCdcewRiskAssessmentQuestionTemplateMapper.selectById(id);
    }

    @Override
    public void deleteQuestionnaireTemplate(String id) {
        tbCdcewRiskAssessmentQuestionTemplateMapper.deleteById(id);
    }

    @Override
    public PageInfo<TbCdcewRiskAssessmentQuestionTemplate> pageListQuestionnaireTemplate(QuestionnaireTemplateQueryDTO dto) {
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        List<TbCdcewRiskAssessmentQuestionTemplate> list = tbCdcewRiskAssessmentQuestionTemplateMapper.list(dto);
        return new PageInfo<>(list);
    }

    @Override
    public List<QuestionTemplateSimpleDTO> listQuestionnaireTemplate() {
        return tbCdcewRiskAssessmentQuestionTemplateMapper.listQuestionnaireTemplate();
    }

    @Override
    public void saveDelphiSummary(DelphiSummaryDTO dto) {
//        如果当前轮次为最后一轮
        if (dto.getIsLastRound().equals(CommonConstants.STR_ONE)){
            TbCdcewRiskAssessmentQuestionnaireRound existRound = tbCdcewRiskAssessmentQuestionnaireRoundMapper.selectByAssessmentEventIdAndRound(dto.getEventId(), dto.getRound());
            if (existRound != null) {
                existRound.setIsLastRound(CommonConstants.STR_ONE);
                riskAssessmentCommonService.updateUserInfo(existRound);
                tbCdcewRiskAssessmentQuestionnaireRoundMapper.updateById(existRound);
            }
//            如果当前轮次为第一轮且为最后一轮，保存问卷轮次表
            TbCdcewRiskAssessmentQuestionnaireRound round = new TbCdcewRiskAssessmentQuestionnaireRound();
            round.setId(String.valueOf(batchUidService.getUid(TbCdcewRiskAssessmentQuestionnaireRound.TABLE_NAME)));
            round.setRound(dto.getRound());
            round.setAssessmentEventId(dto.getEventId());
            round.setIsLastRound(CommonConstants.STR_ONE);
            riskAssessmentCommonService.setUserInfo(round);
            tbCdcewRiskAssessmentQuestionnaireRoundMapper.insert(round);
//            修改状态
            RiskAssessmentEvent event = riskAssessmentEventMapper.selectById(dto.getEventId());
            if (event != null) {
                event.setEventStatus(AssessmentStatusEnum.IN_REPORT_WRITING.getCode());
                riskAssessmentCommonService.updateUserInfo(event);
                riskAssessmentEventMapper.updateById(event);
            }
        }
        else {
//        查询该风险评估事件该轮次是否已有轮次记录
            //                当前轮次+1
            String Round = String.valueOf(Integer.parseInt(dto.getRound()) + 1);
            TbCdcewRiskAssessmentQuestionnaireRound existRound = tbCdcewRiskAssessmentQuestionnaireRoundMapper.selectByAssessmentEventIdAndRound(dto.getEventId(), Round);
            if (existRound == null) {
                // 新增
                //        保存问卷模板到问卷轮次表
                TbCdcewRiskAssessmentQuestionnaireRound round = new TbCdcewRiskAssessmentQuestionnaireRound();
                round.setId(String.valueOf(batchUidService.getUid(TbCdcewRiskAssessmentQuestionnaireRound.TABLE_NAME)));
                round.setQuestionTemplateId(dto.getTemplateId());
                round.setRound(Round);
                round.setAssessmentEventId(dto.getEventId());
                round.setIsLastRound(CommonConstants.STR_ZERO);
                riskAssessmentCommonService.setUserInfo(round);
                tbCdcewRiskAssessmentQuestionnaireRoundMapper.insert(round);
            }
        }
    }

    public List<RoundSummaryVO> getDelphiSummaryByAssessmentEvent(String eventId) {
        List<RoundSummaryVO> result = new ArrayList<>();
        // 当前登录用户
        String currentUserId = Optional.ofNullable(userInfo.get()).map(UapUserPo::getId).orElse(null);

        // 查询 prepare_info 字段并解析专家人数
        int expertCount = getExpertCount(eventId);
        List<TbCdcewRiskAssessmentQuestionResult> records = tbCdcewRiskAssessmentQuestionResultMapper.selectByAssessmentEventId(eventId);

        if (CollectionUtils.isEmpty(records)) {
            result.add(createEmptyRound("1", expertCount));
            return result;
        }

        // 按轮次分组记录
        Map<String, List<TbCdcewRiskAssessmentQuestionResult>> groupedByRound = records.stream()
                .collect(Collectors.groupingBy(TbCdcewRiskAssessmentQuestionResult::getRound, LinkedHashMap::new, Collectors.toList()));

        // 遍历每个轮次的记录
        for (Map.Entry<String, List<TbCdcewRiskAssessmentQuestionResult>> entry : groupedByRound.entrySet()) {
            result.add(buildRoundSummary(entry.getKey(), entry.getValue(), currentUserId, expertCount));
        }

        // 判断是否需要添加下一轮空的 roundDTO (当某轮次所有专家填写了，但下一轮没有专家填写,且主持人选择了进行下一轮时增加空轮)
        addNextEmptyRoundIfNeeded(groupedByRound, expertCount, eventId, result);
        return result;
    }


    private List<ExpertAnswerDTO.QuestionAnswer> parseQuestionAnswers(String json) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(json, new TypeReference<List<ExpertAnswerDTO.QuestionAnswer>>() {});
        } catch (Exception e) {
            log.info("问卷内容解析失败", e);
        }
        return Collections.emptyList();
    }

    private int getExpertCount(String eventId) {
        RiskAssessmentEventPrepare prepare = riskAssessmentEventPrepareMapper.selectByAssessmentEventId(eventId);
        if (prepare != null && StringUtils.isNotBlank(prepare.getPrepareInfo())) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(prepare.getPrepareInfo());
                JSONArray experts = jsonObject.getJSONArray("experts");
                return experts != null ? experts.size() : 0;
            } catch (Exception e) {
                log.warn("专家列表解析失败: {}", e.getMessage());
            }
        }
        return 0;
    }

    private RoundSummaryVO createEmptyRound(String round, int expertCount) {
        RoundSummaryVO roundDTO = new RoundSummaryVO();
        roundDTO.setRound(round);
        roundDTO.setExpertCount(expertCount);
        return roundDTO;
    }

    private RoundSummaryVO buildRoundSummary(String round, List<TbCdcewRiskAssessmentQuestionResult> expertResponses,
                                             String currentUserId, int expertCount) {
        // 判断当前用户是否填写了该轮问卷
        boolean currentUserSubmitted = expertResponses.stream()
                .anyMatch(r -> currentUserId != null && currentUserId.equals(r.getUpdaterId()));

        // 当前用户排在前面
        expertResponses.sort(Comparator.comparing(r -> !currentUserId.equals(r.getUpdaterId())));

        Map<String, List<String>> questionToAnswers = new LinkedHashMap<>();
        List<String> personalOpinions = new ArrayList<>();

        // 解析每个专家的回答
        for (TbCdcewRiskAssessmentQuestionResult response : expertResponses) {
            List<ExpertAnswerDTO.QuestionAnswer> answers = parseQuestionAnswers(response.getContent());
            for (ExpertAnswerDTO.QuestionAnswer qa : answers) {
                questionToAnswers.computeIfAbsent(qa.getQuestion(), k -> new ArrayList<>()).add(qa.getAnswer());
            }

            // 收集专家的个人意见
            if (response.getPersonalOpinion() != null) {
                personalOpinions.add(response.getPersonalOpinion());
            }
        }

        // 构建问题结果 DTO 列表
        List<RoundSummaryVO.QuestionResultDTO> questionList = new ArrayList<>();
        for (Map.Entry<String, List<String>> qEntry : questionToAnswers.entrySet()) {
            RoundSummaryVO.QuestionResultDTO dto = new RoundSummaryVO.QuestionResultDTO();
            dto.setDesc(qEntry.getKey());
            dto.setAnswer(qEntry.getValue());
            questionList.add(dto);
        }

        // 如果存在专家个人意见，添加到问题结果 DTO 列表
        if (!personalOpinions.isEmpty()) {
            RoundSummaryVO.QuestionResultDTO opinionDto = new RoundSummaryVO.QuestionResultDTO();
            opinionDto.setDesc("个人意见");
            opinionDto.setAnswer(personalOpinions);
            questionList.add(opinionDto);
        }

        // 创建问卷意见汇总DTO 并设置属性
        RoundSummaryVO roundDTO = new RoundSummaryVO();
        roundDTO.setRound(round);
        roundDTO.setQuestion(questionList);
        roundDTO.setExpertCount(expertCount);
        roundDTO.setCurrentUserSubmitted(currentUserSubmitted);
        return roundDTO;
    }

    private void addNextEmptyRoundIfNeeded(Map<String, List<TbCdcewRiskAssessmentQuestionResult>> groupedByRound,
                                           int expertCount, String eventId, List<RoundSummaryVO> result) {

        if (groupedByRound.isEmpty()) return;

        List<Map.Entry<String, List<TbCdcewRiskAssessmentQuestionResult>>> roundEntries = new ArrayList<>(groupedByRound.entrySet());
        Map.Entry<String, List<TbCdcewRiskAssessmentQuestionResult>> lastEntry = roundEntries.get(roundEntries.size() - 1);

        String lastRound = lastEntry.getKey();
        List<TbCdcewRiskAssessmentQuestionResult> lastRoundResponses = lastEntry.getValue();

//        检查是否所有专家提交
        boolean allExpertsSubmitted = lastRoundResponses.size() >= expertCount;

        // 检查下一轮是否存在
        String nextRound = String.valueOf(Integer.parseInt(lastRound) + 1);
        boolean nextRoundHasData = groupedByRound.containsKey(nextRound);

        // 查询 TbCdcewRiskAssessmentQuestionnaireRound 表中该 eventId 的最大轮次
        TbCdcewRiskAssessmentQuestionnaireRound maxRoundRecord =
                tbCdcewRiskAssessmentQuestionnaireRoundMapper.selectMaxRoundByAssessmentEventId(eventId);

        boolean hasCreatedNextRoundInDb = maxRoundRecord != null
                && Integer.parseInt(maxRoundRecord.getRound()) >= Integer.parseInt(nextRound);

        // 满足三个条件才添加空轮
        if (allExpertsSubmitted && !nextRoundHasData && hasCreatedNextRoundInDb) {
            result.add(createEmptyRound(nextRound, expertCount));
        }
    }

    @Override
    public TbCdcewRiskAssessmentQuestionTemplate getQuestionnaireTemplateContent(String eventId) {
//        获取最新轮次问卷模板id
        String questionTemplateId = tbCdcewRiskAssessmentQuestionnaireRoundMapper.getLatestQuestionTemplateId(eventId);
        if (questionTemplateId == null) {
//            当前为首轮问卷调查
            //        获取评估准备信息
            RiskAssessmentEventPrepare prepare = riskAssessmentEventPrepareMapper.selectByAssessmentEventId(eventId);
            if (prepare == null) {
                return null;
            }
            return this.getTemplateContent(prepare);

        }
        else {
//            根据模板id获取最新轮次模板内容
            return tbCdcewRiskAssessmentQuestionTemplateMapper.selectById(questionTemplateId);
        }
    }

    @Override
    public void saveExpertQuestionnaire(ExpertAnswerDTO dto) {
//        判断当前事件当前轮次当前专家是否有问卷记录
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        TbCdcewRiskAssessmentQuestionResult questionResult = tbCdcewRiskAssessmentQuestionResultMapper.selectByEventIdAndRound(dto.getEventId(), dto.getRound(), uapUserPo.getId());
        if (questionResult == null) {
            questionResult = new TbCdcewRiskAssessmentQuestionResult();
            BeanUtils.copyProperties(dto, questionResult);
            questionResult.setId(String.valueOf(batchUidService.getUid(TbCdcewRiskAssessmentQuestionResult.TABLE_NAME)));
            questionResult.setAssessmentEventId(dto.getEventId());
            riskAssessmentCommonService.setUserInfo(questionResult);
            tbCdcewRiskAssessmentQuestionResultMapper.insert(questionResult);
        }
        else {
//            编辑当前问卷记录并保存
            BeanUtils.copyProperties(dto, questionResult);
            riskAssessmentCommonService.updateUserInfo(questionResult);
            tbCdcewRiskAssessmentQuestionResultMapper.updateById(questionResult);
        }
    }

    @Override
    public RoundDetailVO getQuestionnaireDetail(String eventId, String round) {
//        获取专家填写问卷详情
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        TbCdcewRiskAssessmentQuestionResult questionResult = tbCdcewRiskAssessmentQuestionResultMapper.selectByEventIdAndRound(eventId, round, uapUserPo.getId());
        RoundDetailVO roundDetailVO = new RoundDetailVO();
        if (questionResult != null) {
            roundDetailVO.setContent(questionResult.getContent());
            roundDetailVO.setName(questionResult.getName());
            roundDetailVO.setPersonalOpinion(questionResult.getPersonalOpinion());
        }
//        获取主持人设置的问卷模板
//        如果轮次为第一轮
        if (StringUtils.equals(round, "1")) {
            //        获取评估准备信息
            RiskAssessmentEventPrepare prepare = riskAssessmentEventPrepareMapper.selectByAssessmentEventId(eventId);
            if (StringUtils.isNotEmpty(prepare.getPrepareInfo())) {
                TbCdcewRiskAssessmentQuestionTemplate template = this.getTemplateContent(prepare);
                if (template != null) {
                    roundDetailVO.setTemplateContent(template.getContent());
                    roundDetailVO.setName(template.getName());
                    roundDetailVO.setTemplateId(template.getId());
                }
            }
        }
        else {
//            根据轮次、风险评估事件id获取模板
            TbCdcewRiskAssessmentQuestionnaireRound questionnaireRoundround = tbCdcewRiskAssessmentQuestionnaireRoundMapper.selectByAssessmentEventIdAndRound(eventId, round);
//            通过模板id查问卷模板
            if (questionnaireRoundround != null && StringUtils.isNotEmpty(questionnaireRoundround.getQuestionTemplateId())) {
                TbCdcewRiskAssessmentQuestionTemplate template = tbCdcewRiskAssessmentQuestionTemplateMapper.selectById(questionnaireRoundround.getQuestionTemplateId());
                roundDetailVO.setTemplateContent(template.getContent());
                roundDetailVO.setTemplateName(template.getName());
                roundDetailVO.setTemplateId(template.getId());
            }
        }
        TbCdcewRiskAssessmentQuestionnaireRound existRound = tbCdcewRiskAssessmentQuestionnaireRoundMapper.selectByAssessmentEventIdAndRound(eventId, round);
        if (existRound != null && StringUtils.isNotEmpty(existRound.getIsLastRound())) {
            roundDetailVO.setIsLastRound(existRound.getIsLastRound());
        }
        else {
            roundDetailVO.setIsLastRound("0");
        }
        return roundDetailVO;
    }

    private TbCdcewRiskAssessmentQuestionTemplate getTemplateContent(RiskAssessmentEventPrepare prepare) {
        JSONObject prepareInfo = JSONObject.parseObject(prepare.getPrepareInfo());
        if (prepareInfo != null && StringUtils.isNotEmpty(prepareInfo.getString("templateId"))) {
            String templateId = prepareInfo.getString("templateId");
            TbCdcewRiskAssessmentQuestionTemplate template = tbCdcewRiskAssessmentQuestionTemplateMapper.selectById(templateId);
            if (template != null) {
                return template;
            }
        }
        return null;
    }

    @Override
    public RoleInfoVO judgeExpertOrModerator(String loginUserId) {
        
        RoleInfoVO roleInfoVO = new RoleInfoVO();
        //判断是否是主持人
        List<String> roleList = uapServiceProApi.getRoleListByLoginUserId(loginUserId);
        roleInfoVO.setModeratorFlag(roleList.contains(moderator) ? BooleanEnum.TRUE.getStrVal() : BooleanEnum.FALSE.getStrVal());
        //判断是否是专家
        List<String> expertList = adminServiceProApi.getExpertUserId();
        roleInfoVO.setExpertFlag(expertList.contains(loginUserId) ? BooleanEnum.TRUE.getStrVal() : BooleanEnum.FALSE.getStrVal());

        return roleInfoVO;
    }

    @Override
    public TbCdcewRiskAssessmentFactorResult saveOrUpdateRiskFactor(RiskFactorRatingDTO dto) {
        // 查询是否已有记录
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        TbCdcewRiskAssessmentFactorResult existingResult = tbCdcewRiskAssessmentFactorResultMapper
                .findByAssessmentEventIdAndUser(dto.getEventId(), uapUserPo.getId());
        if (existingResult != null) {
            existingResult.setContent(dto.getContent());
            existingResult.setManagementAdvice(dto.getManagementAdvice());
            riskAssessmentCommonService.updateUserInfo(existingResult);
            tbCdcewRiskAssessmentFactorResultMapper.updateById(existingResult);
            return existingResult;
        }

        TbCdcewRiskAssessmentFactorResult newResult = new TbCdcewRiskAssessmentFactorResult();
        newResult.setId(String.valueOf(batchUidService.getUid(TbCdcewRiskAssessmentFactorResult.TABLE_NAME)));
        newResult.setAssessmentEventId(dto.getEventId());
        newResult.setContent(dto.getContent());
        newResult.setManagementAdvice(dto.getManagementAdvice());
        riskAssessmentCommonService.setUserInfo(newResult);
        tbCdcewRiskAssessmentFactorResultMapper.insert(newResult);
        return newResult;
    }

    @Override
    public List<String> getRiskFactor(String eventId) {
        String prepareInfo = riskAssessmentEventPrepareMapper.findPrepareInfoByEventId(eventId);

        if (StringUtils.isBlank(prepareInfo)) {
            return Collections.emptyList();
        }

        try {
            JSONObject json = JSONObject.parseObject(prepareInfo);
            JSONArray riskFactors = json.getJSONArray("riskFactor");

            if (riskFactors == null || riskFactors.isEmpty()) {
                return Collections.emptyList();
            }

            List<String> result = new ArrayList<>();
            for (int i = 0; i < riskFactors.size(); i++) {
                result.add(riskFactors.getString(i));
            }
            return result;
        } catch (Exception e) {
            // 处理异常（如 prepareInfo 格式错误）
            return Collections.emptyList();
        }
    }



    @Override
    public TbCdcewRiskAssessmentFactorResult getRiskFactorDetail(String eventId) {
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        return tbCdcewRiskAssessmentFactorResultMapper.findByAssessmentEventIdAndUser(eventId, uapUserPo.getId());
    }

    public RiskMatrixSummaryVO getRiskMatrix(String assessmentEventId) {
        RiskMatrixSummaryVO summary = new RiskMatrixSummaryVO();
        // 根据评估事件ID查找所有的风险评估因素结果
        List<TbCdcewRiskAssessmentFactorResult> results = tbCdcewRiskAssessmentFactorResultMapper.findByAssessmentEventId(assessmentEventId);

        // 获取专家数量
        int expertCount = getExpertCount(assessmentEventId);
        // 判断是否所有专家都提交了评估结果
        summary.setAllExpertsSubmitted(results.size() == expertCount);

        Map<String, List<QuestionRating>> factorRatings = new LinkedHashMap<>();
        List<String> managementAdvices = new ArrayList<>();

        for (TbCdcewRiskAssessmentFactorResult result : results) {
            JSONArray arr = JSONArray.parseArray(result.getContent());
            for (Object obj : arr) {
                JSONObject json = (JSONObject) obj;
                String factorName = json.getString("factor");
                QuestionRating rating = new QuestionRating();
                // 将可能性和严重性转换为分数
                rating.possibilityScore = RiskLevelEnum.convertToScore(json.getString("possibility"));
                rating.severityScore = RiskLevelEnum.convertToScore(json.getString("severity"));
                rating.comment = json.getString("comment");
                factorRatings.computeIfAbsent(factorName, k -> new ArrayList<>()).add(rating);
            }

            // 如果存在管理建议，则添加到管理建议列表中
            if (StringUtils.isNotBlank(result.getManagementAdvice())) {
                managementAdvices.add(result.getManagementAdvice());
            }
        }

        List<RiskFactorEvaluationDTO> factorDTOList = new ArrayList<>();
        for (Map.Entry<String, List<QuestionRating>> entry : factorRatings.entrySet()) {
            String factorName = entry.getKey();
            List<QuestionRating> ratings = entry.getValue();

            // 计算平均可能性和严重性分数，默认值为3
            double avgPossibility = ratings.stream().mapToInt(r -> r.possibilityScore).average().orElse(3);
            double avgSeverity = ratings.stream().mapToInt(r -> r.severityScore).average().orElse(3);
            // 计算总分
            int total = (int) Math.round(avgPossibility + avgSeverity);

            RiskFactorEvaluationDTO dto = new RiskFactorEvaluationDTO();
            dto.setRiskFactorName(factorName);
            dto.setPossibilityLevel(RiskLevelEnum.fromScore(avgPossibility));
            dto.setSeverityLevel(RiskLevelEnum.fromScore(avgSeverity));
            dto.setRiskGrade(RiskGradeEnum.fromTotal(total));
            dto.setComments(ratings.stream().map(r -> r.comment).collect(Collectors.toList()));
            factorDTOList.add(dto);
        }

        summary.setAssessmentEventId(assessmentEventId);
        summary.setFactors(factorDTOList);
        summary.setGeneralManagementAdvices(managementAdvices);
        return summary;
    }

    static class QuestionRating {
        int possibilityScore;
        int severityScore;
        String comment;
    }

    @Override
    public PageInfo<RiskAssessmentEventReportQueryVO> pageListRiskAssessmentReport(RiskAssessmentEventReportQueryDTO dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(riskAssessmentEventReportMapper.listRiskAssessmentReport(dto));
    }

    @Override
    @Transactional
    public void pushEmergencyEvent(PushEmergencyEventDTO event) {
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        String orgId = uapUserPo.getOrgId();
        String id = null;
        if (StringUtils.isBlank(event.getEventId())) {
            //新增事件的状态为待核实
            id = String.valueOf(batchUidService.getUid(TB_CDCEW_EMERGENCY_EVENT));
            event.setEventId(id);
            event.setEventStatus(EventStatusEnum.WAIT_VERIFIED.getCode());
            event.setReporter(uapUserPo.getName());
            event.setReporterId(uapUserPo.getId());
            event.setReporterPhone(uapUserPo.getPhone());
            //新增事件需要根据责任人添加推送记录
            emergencyCommonService.addEmergencyEventPushRecord(event);
        }
        event.setCreateTime(new Date());
        event.setCreator(uapUserPo.getName());
        event.setCreatorId(uapUserPo.getId());
        event.setUpdateTime(new Date());
        event.setUpdater(uapUserPo.getName());
        event.setUpdaterId(uapUserPo.getId());
        event.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        tbCdcewEmergencyEventMapper.insert(event);

        //新增的事件
        if (StringUtils.isNotBlank(id)) {
            //责任人非空 新增一条核实任务
            if (StringUtils.isNotBlank(event.getChargePersonId()) || StringUtils.isNotBlank(event.getChargePersonName())) {
                emergencyEventProcessTaskService.createCheckTask(event);
            }

            //新增事件触发时间轴
            emergencyCommonService.generateEventTimeline(null, event, TimeLineMomentTypeEnum.EVENT_TRIGGER);
        }

//        修改风险评估已推送字段
        RiskAssessmentEvent assessmentEvent = riskAssessmentEventMapper.selectById(event.getAssessmentEventId());
        if (assessmentEvent != null) {
            assessmentEvent.setIsPushed(CommonConstants.STR_ONE);
            riskAssessmentEventMapper.updateById(assessmentEvent);
        }
//        添加突发公共卫生事件与风险评估关联数据
        TbCdcewEmergencyEventRiskAssessmentRelation relation = new TbCdcewEmergencyEventRiskAssessmentRelation();
        relation.setId(String.valueOf(batchUidService.getUid(TbCdcewEmergencyEventRiskAssessmentRelation.TABLE_NAME)));
        BeanUtils.copyProperties(event, relation);
        riskAssessmentCommonService.setUserInfo(relation);
        tbCdcewEmergencyEventRiskAssessmentRelationMapper.insert(relation);
    }

    @Override
    public List<EcdOrgUserVO> getExpertList() {
//        获取专家列表
        List<EcdOrgUserVO> expertList = adminServiceProApi.getExpertList();

        // 获取当前登录用户
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        String currentUserName = uapUserPo.getName();

        List<String> roleList = uapServiceProApi.getRoleListByLoginUserId(uapUserPo.getId());

        // 如果当前用户是业务主持人，则从专家列表中移除
        if (roleList.contains(moderator)) {
            expertList = expertList.stream()
                    .filter(expert -> !currentUserName.equals(expert.getName()))
                    .collect(Collectors.toList());
        }
        return expertList;
    }

    @Override
    public List<RiskAssessmentEvent> queryRiskAssessmentGradeMap(RiskAssessmentEvent riskAssessmentEvent) {
        if (StringUtils.isAnyBlank(riskAssessmentEvent.getZoningType(),riskAssessmentEvent.getAssessmentMonth())) {
            throw new MedicalBusinessException("参数不能为空");
        }
        //区划地图只能到区 选择区时 返回当前区
        List<RiskAssessmentEvent> res = riskAssessmentEventMapper.queryRiskAssessmentGradeMap(riskAssessmentEvent);
        //相同地区保留最新一条 查询结果已按照 create_time DESC
        Map<String, RiskAssessmentEvent> uniqueMap = new HashMap<>();
        for (RiskAssessmentEvent event : res) {
            uniqueMap.putIfAbsent(event.getName(), event);
        }
        return new ArrayList<>(uniqueMap.values());
    }

    @Override
    public PageInfo<RiskAssessmentEventExpertReviewVO> listExpertReview(RiskAssessmentExpertReviewQueryDTO queryDTO) {
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        queryDTO.setUserId(uapUserPo.getId());
        queryDTO.setEventStatusList(Arrays.asList(AssessmentStatusEnum.COMPLETED.getCode(), AssessmentStatusEnum.ENDED.getCode()));
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(riskAssessmentEventMapper.listExpertReview(queryDTO));
    }

    @Override
    public void expertReview(RiskAssessmentEvent expertReview) {
//        获取风险评估
        RiskAssessmentEvent riskAssessmentEvent = riskAssessmentEventMapper.selectById(expertReview.getId());
        if (riskAssessmentEvent == null) {
            return;
        }
//        保存专家评议
        riskAssessmentEvent.setExpertReview(expertReview.getExpertReview());
        riskAssessmentEvent.setReviewTime(new Date());
        riskAssessmentEvent.setReviewStatus(Constants.STR_ONE);
        riskAssessmentCommonService.updateUserInfo(riskAssessmentEvent);
        riskAssessmentEventMapper.updateById(riskAssessmentEvent);
    }

    @Override
    public void saveExpertReviewIndex(TbCdcewRiskAssessmentExpertReviewIndex dto) {
        // 编辑
        if (StringUtils.isNotEmpty(dto.getId())) {
            TbCdcewRiskAssessmentExpertReviewIndex original = tbCdcewRiskAssessmentExpertReviewIndexMapper.selectById(dto.getId());

            // 删除逻辑
            if (StringUtils.isNotEmpty(dto.getDeleteFlag()) && dto.getDeleteFlag().equals(DeleteFlagEnum.YES.getCode())) {
                tbCdcewRiskAssessmentExpertReviewIndexMapper.deleteById(dto.getId());
                return;
            }

            // 判断数量限制
            if ((original.getStatus() == null || original.getStatus() != 1) && dto.getStatus() != null && dto.getStatus() == 1) {
                int count = tbCdcewRiskAssessmentExpertReviewIndexMapper.countIndex();
                if (count >= 3) {
                    throw new MedicalBusinessException("最多开启三个评议指标");
                }
            }

            riskAssessmentCommonService.updateUserInfo(dto);
            tbCdcewRiskAssessmentExpertReviewIndexMapper.updateById(dto);
        } else {
            // 新增
            if (dto.getStatus() != null && dto.getStatus() == 1) {
                int count = tbCdcewRiskAssessmentExpertReviewIndexMapper.countIndex();
                if (count >= 3) {
                    throw new MedicalBusinessException("最多开启三个评议指标");
                }
            }

            // 判断名称重复
            int count = tbCdcewRiskAssessmentExpertReviewIndexMapper.countByName(dto.getIndexName());
            if (count > 0) {
                throw new MedicalBusinessException("评议指标名称重复");
            }

            dto.setId(String.valueOf(batchUidService.getUid(TbCdcewRiskAssessmentExpertReviewIndex.TABLE_NAME)));
            riskAssessmentCommonService.setUserInfo(dto);
            tbCdcewRiskAssessmentExpertReviewIndexMapper.insert(dto);
        }
    }


    @Override
    public PageInfo<TbCdcewRiskAssessmentExpertReviewIndex> listExpertReviewIndex(RiskAssessmentExpertReviewIndexQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(tbCdcewRiskAssessmentExpertReviewIndexMapper.selectExpertReviewIndexByQueryDTO(queryDTO));
    }

    @Override
    public void reviewReport(RiskAssessmentEventReport report) {
        report.setReviewTime(new Date());
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        report.setReviewer(uapUserPo.getName());
        report.setReviewerId(uapUserPo.getId());
        riskAssessmentEventReportMapper.updateById(report);
    }

    @Override
    public void updateShareStatus(RiskAssessmentEventReport report) {
        if (StringUtils.isEmpty(report.getId()) || StringUtils.isEmpty(report.getIsShare())) {
            return;
        }
        UpdateWrapper<RiskAssessmentEventReport> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", report.getId()).set("is_share", report.getIsShare());
        riskAssessmentEventReportMapper.update(null, updateWrapper);
    }

    @Override
    public RiskAssessmentEventReportQueryVO getReportReviewDetail(String id) {
        return riskAssessmentEventReportMapper.selectReportById(id);
    }

    @Override
    public List<TbCdcewRiskAssessmentExpertReviewIndex> getExpertReviewIndexes() {
        return tbCdcewRiskAssessmentExpertReviewIndexMapper.getExpertrReviewIndex();
    }

    @Override
    public PageInfo<RiskAssessmentEventReportQueryVO> listReportReview(RiskAssessmentEventReportQueryDTO dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(riskAssessmentEventReportMapper.listReportReview(dto));
    }

}

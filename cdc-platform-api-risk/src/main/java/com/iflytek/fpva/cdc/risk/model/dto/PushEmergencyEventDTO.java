package com.iflytek.fpva.cdc.risk.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iflytek.fpva.cdc.emergency.entity.TbCdcewEmergencyEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PushEmergencyEventDTO extends TbCdcewEmergencyEvent {
    /**
     * 风险评估事件id
     */
    @ApiModelProperty(value = "风险评估事件id")
    private String assessmentEventId;

    /**
     * 风险评估事件类型
     */
    @ApiModelProperty(value = "风险评估类型")
    private String assessmentType;

}

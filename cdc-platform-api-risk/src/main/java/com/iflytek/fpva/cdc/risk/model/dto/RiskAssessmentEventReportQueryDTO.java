package com.iflytek.fpva.cdc.risk.model.dto;

import com.iflytek.fpva.cdc.common.model.dto.CommonQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel("风险评估报告列表查询dto")
@Data
public class RiskAssessmentEventReportQueryDTO extends CommonQueryDTO {
    @ApiModelProperty(value = "风险评估报告ID")
    private String id;

    @ApiModelProperty(value = "风险评估报告名称")
    private String name;

    @ApiModelProperty(value = "风险评估id")
    private String assessmentEventId;

    @ApiModelProperty(value = "风险评估报告类型")
    private String assessmentType;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    private String riskLevel;

    /**
     * 报告撰写人
     */
    @ApiModelProperty(value = "报告撰写人")
    private String reportWriter;

    /**
     * 报告生成时间-开始
     */
    @ApiModelProperty(value = "报告生成时间-开始")
    private Date updateTimeStart;

    @ApiModelProperty(value = "报告生成时间-结束")
    private Date updateTimeEnd;

    @ApiModelProperty(value = "报告生成月份")
    private Date assessmentMonth;
    
    @ApiModelProperty(value = "风险评估报告名称")
    private String reportName;

    @ApiModelProperty(value = "是否共享")
    private String isShare;

}

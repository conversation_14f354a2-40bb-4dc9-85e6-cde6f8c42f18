package com.iflytek.fpva.cdc.risk.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum RiskGradeEnum {
    VERY_LOW(2, 4, "低风险"),
    MEDIUM(5, 6, "中等风险"),
    HIGH(7, 8, "高风险"),
    VERY_HIGH(9, 10, "极高风险");

    private final int minScore;
    private final int maxScore;
    private final String label;

    public static RiskGradeEnum fromTotal(int totalScore) {
        for (RiskGradeEnum grade : values()) {
            if (totalScore >= grade.minScore && totalScore <= grade.maxScore) {
                return grade;
            }
        }
        return MEDIUM;
    }
}

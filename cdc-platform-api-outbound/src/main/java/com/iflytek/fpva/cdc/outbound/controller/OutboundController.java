package com.iflytek.fpva.cdc.outbound.controller;

import com.iflytek.fpva.cdc.outbound.constant.OutboundConfig;
import com.iflytek.fpva.cdc.outbound.model.dto.SignDTO;
import com.iflytek.fpva.cdc.outbound.model.dto.input.BatchList;
import com.iflytek.fpva.cdc.outbound.model.dto.input.CallTask;
import com.iflytek.fpva.cdc.outbound.model.dto.input.PlanTask;
import com.iflytek.fpva.cdc.outbound.model.dto.input.SmsTask;
import com.iflytek.fpva.cdc.outbound.model.dto.output.*;
import com.iflytek.fpva.cdc.outbound.service.CreateOutboundService;
import com.iflytek.fpva.cdc.outbound.service.QueryOutBoundService;
import com.iflytek.fpva.cdc.outbound.util.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@Api(tags = "外呼开放接口")
public class OutboundController {

    @Resource
    private OutboundConfig config;

    @Resource
    private CreateOutboundService createOutboundService;

    @Resource
    private QueryOutBoundService queryOutBoundService;

    @GetMapping("/pt/v1/outbound/getSign")
    @ApiOperation("获取鉴权信息")
    public SignDTO getSign() {
        return createOutboundService.getSign();
    }

    @PostMapping("/pt/v1/outbound/exec/execPlanTask")
    @ApiOperation("执行外呼（方案）接口")
    public Response<CreateCallRs> execPlanTask(@RequestBody(required = false) PlanTask planTask) {
        return createOutboundService.execPlanTask(planTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execCallTask")
    @ApiOperation("执行外呼（话术）接口")
    public Response<CreateCallRs> execCallTask(@RequestBody(required = false) CallTask callTask) {
        return createOutboundService.execCallTask(callTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execSmsTask")
    @ApiOperation("执行外呼（短信）接口")
    public Response<CreateCallRs> execSmsTask(@RequestBody(required = false) SmsTask smsTask) {
        return createOutboundService.execSmsTask(smsTask);
    }

    @PostMapping("/pt/v1/outbound/query/querySpeechVariable")
    @ApiOperation("查询话术自定义变量")
    public Response<SpeechVariable> querySpeechVariable(String speechId) {
        return queryOutBoundService.querySpeechVariable(speechId);
    }

    @PostMapping("/pt/v1/outbound/query/queryBatchDetail")
    @ApiOperation("查询电话批次状态")
    public Response<BatchDetailRs> queryBatchDetail(String batchId) {
        return queryOutBoundService.queryBatchDetail(batchId);
    }

    @PostMapping("/pt/v1/outbound/query/queryBatchList")
    @ApiOperation("查询批次汇总数据")
    public Response<BatchListRs> queryBatchList(@RequestBody(required = false) BatchList batchList) {
        return queryOutBoundService.queryBatchList(batchList);
    }

    @PostMapping("/pt/v1/outbound/query/queryRecordDetail")
    @ApiOperation("查询电话记录详情")
    public Response<RecordDetailRs> queryRecordDetail(String recordId) {
        return queryOutBoundService.queryRecordDetail(recordId);
    }

}

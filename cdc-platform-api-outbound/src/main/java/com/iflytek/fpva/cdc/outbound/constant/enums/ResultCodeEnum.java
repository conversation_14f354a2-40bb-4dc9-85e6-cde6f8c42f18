package com.iflytek.fpva.cdc.outbound.constant.enums;

/**
 * 任务状态枚举值说明
 * <AUTHOR>
 */

public enum ResultCodeEnum {

    SUCCESS("0", "成功"),
    CALLING("2", "通话中"),
    UNABLE_TO_CONNECT("3", "无法接通"),
    SHUTDOWN("4", "关机"),
    USER_BUSY("5", "用户正忙"),
    BLANK_NUMBER("6", "空号"),
    WRONG_NUMBER("7", "号码错误"),
    STOP_USING("9", "停机"),
    NORMAL("15", "客户接听后并主动挂机"),
    USER_MISSED("20", "用户未接"),
    CALL_REMINDER("22", "转来电提醒"),
    CALL_RESTRICTION("23", "呼入限制"),
    NETWORK_FAILURE("26", "网络故障"),
    LINE_FAULT("28", "线路故障"),
    CALL_FAILED("30", "呼叫失败"),
    EXEC_FAILED("300", "任务执行失败");

    private String code;
    private String desc;

    ResultCodeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

}

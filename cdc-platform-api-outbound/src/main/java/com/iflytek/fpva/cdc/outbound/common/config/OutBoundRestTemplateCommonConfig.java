package com.iflytek.fpva.cdc.outbound.common.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: mjjiang3
 * @Date: 2020-04-13 10:04
 * @Description: rest-template 公共配置
 */
@Configuration
@ConfigurationProperties(prefix = "rest-template-config")
@EnableConfigurationProperties(OutBoundRestTemplateCommonConfig.class)
@RefreshScope
@Data
public class OutBoundRestTemplateCommonConfig {

    @ApiModelProperty("medicalBoot - rest调用配置")
    private MedicalBoot medicalBoot;

    @ApiModelProperty("outInvoke(外部调用) - 外部rest请求调用配置")
    private OutInvoke outInvoke;

    @Data
    public static class MedicalBoot {
        @ApiModelProperty("读取数据超时时间")
        private Integer readTimeout;

        @ApiModelProperty("http链接超时时间")
        private Integer connectTimeout;

        @ApiModelProperty("连接池获取连接的超时时间")
        private Integer connectRequestTimeout;

        @ApiModelProperty("单路由的并发数")
        private Integer defaultMaxPerRoute;

        @ApiModelProperty("最大连接数")
        private Integer maxTotle;

        @ApiModelProperty("重试次数")
        private Integer retryCount;

        @ApiModelProperty("长连接保持时长30秒 (单位:s)")
        private Long timeToLive;

    }

    @Data
    public static class OutInvoke {

        @ApiModelProperty("读取数据超时时间")
        private Integer readTimeout;

        @ApiModelProperty("http链接超时时间")
        private Integer connectTimeout;

        @ApiModelProperty("连接池获取连接的超时时间")
        private Integer connectRequestTimeout;

        @ApiModelProperty("最大连接数")
        private Integer maxTotle;

        @ApiModelProperty("单路由的并发数")
        private Integer defaultMaxPerRoute;
    }


}
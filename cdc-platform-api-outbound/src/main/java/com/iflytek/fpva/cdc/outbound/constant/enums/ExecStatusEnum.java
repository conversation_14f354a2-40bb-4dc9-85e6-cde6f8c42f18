package com.iflytek.fpva.cdc.outbound.constant.enums;

/**
 * 执行状态枚举值
 * <AUTHOR>
 */

public enum ExecStatusEnum {

    NOT_STARTED(1,"未开始"),
    EXECUTING(2,"执行中"),
    COMPLETED(3,"已完成"),
    CANCELLED(5,"已取消"),
    AUDIT_FAILED(6,"审核未通过");

    private Integer code;
    private String desc;

    ExecStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }
}

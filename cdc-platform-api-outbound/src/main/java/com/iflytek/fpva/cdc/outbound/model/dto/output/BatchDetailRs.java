package com.iflytek.fpva.cdc.outbound.model.dto.output;

import com.iflytek.fpva.cdc.outbound.constant.enums.ExecStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 电话批次状态对象
 * <AUTHOR>
 */
@Data
@Builder
public class BatchDetailRs {

    @ApiModelProperty("批次id")
    private String batch;

    @ApiModelProperty("任务开始时间")
    private Long taskTime;

    @ApiModelProperty("话术id")
    private String speechId;

    @ApiModelProperty("话术名称")
    private String speechName;

    @ApiModelProperty("任务名称")
    private String planName;

    @ApiModelProperty("短信id")
    private String smsId;

    @ApiModelProperty("短信名称")
    private String smsName;

    @ApiModelProperty("执行状态: 1：未开始, 2：执行中, 3：已完成, 5：已取消, 6：审核未通过")
    private Integer execStatus;

    @ApiModelProperty("批次总任务数")
    private Integer totalTimes;

    @ApiModelProperty("完成任务数")
    private Integer completeCount;

    @ApiModelProperty("电话接通人次")
    private Integer callConnectTimes;

    @ApiModelProperty("执行中数量")
    private Integer executingTimes;

    @ApiModelProperty("是否存在发送失败: 0：不存在, 1：存在")
    private Integer failTask;

    /**
     * 是否完成
     * @return
     */
    public boolean isFinished(){
        return ExecStatusEnum.COMPLETED.getCode().equals(execStatus);
    }
}

package com.iflytek.fpva.cdc.coordination.util;

import org.apache.commons.lang3.RandomUtils;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

public class TaskIdUtil {

    private static final NumberFormat nf = NumberFormat.getNumberInstance();

    public static String generateTaskId(String dateStr, List<String> taskIdList){
        List<Integer> numberList = new ArrayList<>();
        taskIdList.forEach(s -> {
            numberList.add(Integer.parseInt(s.substring(8)));
        });


        String randomNumber = getRandomNumber(numberList);

        return dateStr.concat(randomNumber);
    }

    private static String getRandomNumber(List<Integer> numberList){

        String result = "";
        for (int j = 0; j < 99999; j++) {
            int i = RandomUtils.nextInt(0, 99999);
            if(!numberList.contains(i)){
                result = getSortNumberStrFromSortNumber(i);
                break;
            }
        }
        return result;
    }

    /**
     * 根据排序顺序得到固定长度5位的排序号 例： 1 ->  00001
     *
     * @param i 排序顺序
     * */
    public static String getSortNumberStrFromSortNumber(int i){
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(5);
        nf.setMinimumIntegerDigits(5);
        return nf.format(i);
    }
}

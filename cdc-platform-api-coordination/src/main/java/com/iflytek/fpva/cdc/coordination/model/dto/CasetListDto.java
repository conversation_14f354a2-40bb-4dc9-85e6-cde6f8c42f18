package com.iflytek.fpva.cdc.coordination.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName CasetListDto
 * @Description 上报事件下的病例查询DTO
 * <AUTHOR>
 * @Date 2021/10/21
 */

@Data
public class CasetListDto {

    @NotBlank(message = "epidemicId不能为空")
    @ApiModelProperty("上报事件id")
    private String epidemicId;

    @NotBlank(message = "version不能为空")
    @ApiModelProperty("版本")
    private String version;

}

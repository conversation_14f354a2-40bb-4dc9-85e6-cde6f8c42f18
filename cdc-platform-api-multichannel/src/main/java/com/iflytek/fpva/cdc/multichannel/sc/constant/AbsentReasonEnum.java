package com.iflytek.fpva.cdc.multichannel.sc.constant;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum AbsentReasonEnum {

    ILLNESS ("0", "疾病"),
    DAMAGE("1", "伤害"),
    OTHER_REASON("2", "其他原因");

    private String code;
    private String name;

    AbsentReasonEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static List<AbsentReasonEnum> getAll(){

        AbsentReasonEnum[] values = AbsentReasonEnum.values();

        List list1 = CollectionUtils.arrayToList(values);
        List<AbsentReasonEnum> list = new ArrayList<AbsentReasonEnum>(list1);

        return list;
    }

    public static List<String> getAllName(){
        AbsentReasonEnum[] values = AbsentReasonEnum.values();

        List<String> list = new ArrayList<>();
        for(AbsentReasonEnum absentReasonEnum:values){
            list.add(absentReasonEnum.getName());
        }

        return list;
    }

    public static String getNameByCode(String code){
        Map<String,String> map = new HashMap<>();

        AbsentReasonEnum[] values =  AbsentReasonEnum.values();

        for ( AbsentReasonEnum value : values) {
            map.put(value.getCode(), value.getName());
        }
        return map.get(code);
    }
}

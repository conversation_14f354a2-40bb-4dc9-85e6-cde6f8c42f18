package com.iflytek.fpva.cdc.multichannel.sc.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
* Created by Mybatis Generator on 2021/08/16
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbCdcewScUser {
    /**
     * ���ݿ���������
     */
    @ApiModelProperty(value="���ݿ���������")
    private String id;

    /**
     * �û�����
     */
    @ApiModelProperty(value="�û�����")
    private String userName;

    /**
     * ֤������
     */
    @ApiModelProperty(value="֤������")
    private String cardType;

    /**
     * ֤������
     */
    @ApiModelProperty(value="֤������")
    private String cardNo;

    /**
     * �ֻ�����
     */
    @ApiModelProperty(value="�ֻ�����")
    private String phoneNo;

    /**
     * �༶/����id
     */
    @ApiModelProperty(value="�༶/����id")
    private String orgId;

    /**
     * ��ɫ����
     */
    @ApiModelProperty(value="��ɫ����")
    private String roleCode;

    /**
     * ����˽�ɫ����
     */
    @ApiModelProperty(value="����˽�ɫ����")
    private String glRoleCode;

    /**
     * ����ʱ��
     */
    @ApiModelProperty(value="����ʱ��")
    private Date createTime;

    /**
     * �����û�id
     */
    @ApiModelProperty(value="�����û�id")
    private String createUserId;

    /**
     * ����ʱ��
     */
    @ApiModelProperty(value="����ʱ��")
    private Date updateTime;

    /**
     * �����û�id
     */
    @ApiModelProperty(value="�����û�id")
    private String updateUserId;

    /**
     * �Ƿ���ɾ��
     */
    @ApiModelProperty(value="�Ƿ���ɾ��")
    private String isDelete;

    /**
     * ѧ������
     */
    @ApiModelProperty(value="ѧ������")
    private String studentType;

    /**
     * �Ƿ񴴽��˽�������
     */
    @ApiModelProperty(value="�Ƿ񴴽��˽�������")
    private String isHasDetail;

    /**
     * ѧУid
     */
    @ApiModelProperty(value="ѧУid")
    private String schoolId;

    /**
     * ѧ��
     */
    @ApiModelProperty(value="ѧ��")
    private String studentNo;

    /**
     * �Ա����
     */
    @ApiModelProperty(value="�Ա����")
    private String sexCode;

    /**
     * �Ա�����
     */
    @ApiModelProperty(value="�Ա�����")
    private String sexName;

    private String age;

    /**
     * ɾ��ʱ��
     */
    @ApiModelProperty(value="ɾ��ʱ��")
    private Date deleteTime;

    /**
     * ɾ���û�id
     */
    @ApiModelProperty(value="ɾ���û�id")
    private String deleteUserId;

    private String globalUserId;
}
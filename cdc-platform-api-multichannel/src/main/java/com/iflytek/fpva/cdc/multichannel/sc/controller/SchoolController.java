package com.iflytek.fpva.cdc.multichannel.sc.controller;

import com.iflytek.fpva.cdc.annotation.LogExportAnnotation;
import com.iflytek.fpva.cdc.model.vo.PageData;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.NameCodeVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolClockRecordVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolStatisticsVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.StatisticsResultVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.TimeAreaVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.TimeStatisticsVO;
import com.iflytek.fpva.cdc.multichannel.sc.service.CdcSchoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "学校信息接口")
public class SchoolController {

    @Resource
    private CdcSchoolService cdcSchoolService;

    @GetMapping("/pt/v1/school/getSchoolLevelList")
    @ApiOperation("获取学校类别")
    public List<NameCodeVO> getSchoolLevelList() {
        return cdcSchoolService.getSchoolLevelList();
    }

    @PostMapping ("/pt/v1/school/getStatisticalData")
    @ApiOperation("获取学校统计占比图")
    public StatisticsResultVO getStatisticsResult(@RequestBody SchoolQueryVO schoolQueryVO) {
        return  cdcSchoolService.getStatisticsResult(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/regionStat")
    @ApiOperation("获取学生健康打卡数据列表(区县级别)")
    public PageData<TimeAreaVO> getRegionHealthPunchData(@RequestBody SchoolQueryVO schoolQueryVO) {
        return cdcSchoolService.getRegionHealthPunchData(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/schoolStat")
    @ApiOperation("获取学生健康打卡数据列表(学校班级级别)")
    public List<TimeStatisticsVO> getSchoolHealthPunchData(@RequestBody SchoolQueryVO schoolQueryVO) {
        return cdcSchoolService.getSchoolHealthPunchData(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/getRecordDetail")
    @ApiOperation("获取学生健康打卡数据明细")
    public PageData<SchoolClockRecordVO> getRecordDetail(@RequestBody SchoolQueryVO schoolQueryVO) {
        return cdcSchoolService.getClockRecordDetail(schoolQueryVO);
    }

    @GetMapping("/pt/v1/school/getIndicatorList")
    @ApiOperation("获取学校表格指标集合")
    public List<String> getIndicatorList() {
        return cdcSchoolService.getIndicatorList();
    }

    @PostMapping("/pt/v1/school/getSchoolStatistics")
    @ApiOperation("获取学校统计指标")
    public SchoolStatisticsVO getSchoolStatistics(@RequestBody SchoolQueryVO schoolQueryVO) {
        return cdcSchoolService.getSchoolStatistics(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/fuzzyQuerySchoolList")
    @ApiOperation("获取学校列表-学校/班级名称模糊查询")
    public List<NameCodeVO> fuzzyQuerySchoolList(@RequestBody SchoolQueryVO schoolQueryVO){
        schoolQueryVO.setFuzzyQuery(true);
        return cdcSchoolService.fuzzyQuerySchoolList(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/export/regionStat")
    @ApiOperation("导出学生健康打卡数据列表(区县级别)")
    @LogExportAnnotation
    public ResponseEntity<byte[]> exportRegionHealthPunchData(@RequestBody SchoolQueryVO schoolQueryVO, @RequestParam String loginUserId) {
        return cdcSchoolService.exportRegionHealthPunchData(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/export/schoolStat")
    @ApiOperation("导出学生健康打卡数据列表(学校班级级别)")
    @LogExportAnnotation
    public ResponseEntity<byte[]> exportSchoolHealthPunchData(@RequestBody SchoolQueryVO schoolQueryVO, @RequestParam String loginUserId) {
        return cdcSchoolService.exportSchoolHealthPunchData(schoolQueryVO);
    }

    @PostMapping("/pt/v1/school/export/getRecordDetail")
    @ApiOperation("导出学生健康打卡数据明细")
    @LogExportAnnotation
    public ResponseEntity<byte[]> exportRecordDetail(@RequestBody SchoolQueryVO schoolQueryVO, @RequestParam String loginUserId) {
        return cdcSchoolService.exportClockRecordDetail(schoolQueryVO);
    }

}

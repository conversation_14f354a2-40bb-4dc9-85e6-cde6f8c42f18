package com.iflytek.fpva.cdc.multichannel.sc.mapper;

import com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.NameCodeVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* Created by Mybatis Generator on 2021/08/16
*/
@Mapper
public interface TbCdcewScOrgInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcewScOrgInfo record);

    int insertSelective(TbCdcewScOrgInfo record);

    TbCdcewScOrgInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcewScOrgInfo record);

    int updateByPrimaryKey(TbCdcewScOrgInfo record);

    /**
     * 获取学校列表
     * @param schoolQueryVO
     * @return
     */
    List<TbCdcewScOrgInfo> getSchoolList(SchoolQueryVO schoolQueryVO);

    List<NameCodeVO> getSchoolName(SchoolQueryVO schoolQueryVO);

    TbCdcewScOrgInfo getOrgBySchoolId(@Param("schoolId") String schoolId);

    List<TbCdcewScOrgInfo> getOrgListByDistrictCode(@Param("districtCode") String districtCode);

    List<TbCdcewScOrgInfo> getOrgListByDistrictCodeAndSchoolId(@Param("districtCode") String districtCode, @Param("schoolId") String schoolId);
}
package com.iflytek.fpva.cdc.multichannel.sc.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class SchoolClockRecordDto  {

    private String clockRecordId;
    private Date clockDate;

    private String provinceCode;
    private String provinceName;
    private String cityCode;
    private String cityName;
    private String districtCode;
    private String districtName;
    private String orgId;
    private Integer schoolLevel;
    private String schoolId;
    private String schoolName;
    private String grade;
    private String classes;
    private String orders;

    private String isHealth;
    private Integer isAbsent;
    private String absentReason;
    private String diagnose;
    private Date accidentDate;
    private Date outpatientDate;
    private String outpatientHospital;

    private String room;
    private String recordType;
    private String userId;
    private String userName;
    private String sexName;
    private String age;

    private String symptomType;
    private Integer absentDays;
    private String relatedPersonSymptomType;

    private String gradeClasses;

    // 聚合之后的数量
    private Integer count;

    public void setClasses(String classes) {
        this.classes = classes;
        this.gradeClasses = this.grade + classes;
    }
}

package com.iflytek.fpva.cdc.multichannel.sc.mapper;

import com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScUser;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.RecordDateCountVO;
import com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Created by Mybatis Generator on 2021/08/16
 */
@Mapper
public interface TbCdcewScUserMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcewScUser record);

    int insertSelective(TbCdcewScUser record);

    TbCdcewScUser selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcewScUser record);

    int updateByPrimaryKey(TbCdcewScUser record);

    /**
     * 根据机构相关条件查询学生人数
     *
     * @param schoolQueryVO
     * @return
     */
    int selectUserCountByOrgInfo(SchoolQueryVO schoolQueryVO);

    /**
     * 查询学生总数
     *
     * @param schoolQueryVO
     * @return
     */
    List<RecordDateCountVO> getTotalUserCount(SchoolQueryVO schoolQueryVO);


}
package com.iflytek.fpva.cdc.multichannel.sc.model.vo;

import com.iflytek.fpva.cdc.common.annotation.ExcelColumn;
import com.iflytek.fpva.cdc.multichannel.sc.constant.AbsentReasonEnum;
import com.iflytek.fpva.cdc.multichannel.sc.constant.RecordTypeEnum;
import com.iflytek.fpva.cdc.util.DateUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

@Data
public class SchoolClockRecordExcelVO {

    @ExcelColumn(name = "日期", column = 1)
    private String clockDate;

    @ExcelColumn(name = "宿舍", column = 2)
    private String room;

    @ExcelColumn(name = "检查方式", column = 3)
    private String recordType;

    @ExcelColumn(name = "姓名", column = 4)
    private String userName;

    @ExcelColumn(name = "性别", column = 5)
    private String sexName;

    @ExcelColumn(name = "年龄", column = 6)
    private String age;

    @ExcelColumn(name = "主要症状", column = 7)
    private String symptomType;

    @ExcelColumn(name = "缺勤原因", column = 8)
    private String absentReason;

    @ExcelColumn(name = "缺勤天数", column = 9)
    private Integer absentDays;

    @ExcelColumn(name = "发病日期", column = 10)
    private String accidentDate;

    @ExcelColumn(name = "就诊日期", column = 11)
    private String outpatientDate;

    @ExcelColumn(name = "就诊医院", column = 12)
    private String outpatientHospital;

    @ExcelColumn(name = "诊断结果", column = 13)
    private String diagnose;

    @ExcelColumn(name = "家庭共同生活人员相似症状", column = 14)
    private String relatedPersonSymptomType;

    public static SchoolClockRecordExcelVO fromRecordVO(SchoolClockRecordVO schoolClockRecordVO){
        SchoolClockRecordExcelVO schoolClockRecordExcelVO = new SchoolClockRecordExcelVO();

        BeanUtils.copyProperties(schoolClockRecordVO,schoolClockRecordExcelVO);

        schoolClockRecordExcelVO.setClockDate(DateUtils.parseDate(schoolClockRecordVO.getClockDate()));
        schoolClockRecordExcelVO.setRecordType(RecordTypeEnum.getNameByCode(schoolClockRecordVO.getRecordType()));
        schoolClockRecordExcelVO.setAbsentReason(AbsentReasonEnum.getNameByCode(schoolClockRecordVO.getAbsentReason()));
        schoolClockRecordExcelVO.setAccidentDate(DateUtils.parseDate(schoolClockRecordVO.getAccidentDate()));
        schoolClockRecordExcelVO.setOutpatientDate(DateUtils.parseDate(schoolClockRecordVO.getOutpatientDate()));

        setDefaultValue(schoolClockRecordExcelVO);

        return schoolClockRecordExcelVO;
    }

    public static void setDefaultValue(SchoolClockRecordExcelVO schoolClockRecordExcelVO) {
        Class<SchoolClockRecordExcelVO> schoolClockRecordExcelVOClass = SchoolClockRecordExcelVO.class;
        Field[] declaredFields = schoolClockRecordExcelVOClass.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            try {
                Object o = field.get(schoolClockRecordExcelVO);
                if (StringUtils.isEmpty(o)) {
                    field.set(schoolClockRecordExcelVO, "--");
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

    }

}

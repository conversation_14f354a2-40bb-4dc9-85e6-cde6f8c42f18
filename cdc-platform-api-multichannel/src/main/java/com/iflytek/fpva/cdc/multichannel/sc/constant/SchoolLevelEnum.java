package com.iflytek.fpva.cdc.multichannel.sc.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 学校级别
 *
 * <AUTHOR>
 */

public enum SchoolLevelEnum {

    //0-高中、1-初中、2-小学、3-托幼
    SENIOR_SCHOOL (0, "高中"),
    MIDDLE_SCHOOL (1, "初中"),
    PRIMARY_SCHOOL(2, "小学"),
    NURSERY_SCHOOL(3, "托幼");

    private Integer code;
    private String name;

    SchoolLevelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code){
        Map<Integer,String> map = new HashMap<>();

        SchoolLevelEnum[] values = SchoolLevelEnum.values();

        for (SchoolLevelEnum value : values) {
            map.put(value.getCode(), value.getName());
        }
        return map.get(code);
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.mr.mapper.TbCdcDrugPurchaserInfoMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcDrugPurchaserInfo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="identity_no" jdbcType="VARCHAR" property="identityNo"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="sojourn_history" jdbcType="VARCHAR" property="sojournHistory"/>
        <result column="is_fever" jdbcType="VARCHAR" property="isFever"/>
        <result column="is_cough" jdbcType="VARCHAR" property="isCough"/>
        <result column="is_headache" jdbcType="VARCHAR" property="isHeadache"/>
        <result column="is_diarrhea" jdbcType="VARCHAR" property="isDiarrhea"/>
        <result column="is_normal" jdbcType="VARCHAR" property="isNormal"/>
        <result column="is_other" jdbcType="VARCHAR" property="isOther"/>
        <result column="other_symptoms" jdbcType="VARCHAR" property="otherSymptoms"/>
        <result column="purchasing_purpose" jdbcType="VARCHAR" property="purchasingPurpose"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="pharmacy_id" jdbcType="VARCHAR" property="pharmacyId"/>
        <result column="sales_person_id" jdbcType="VARCHAR" property="salesPersonId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updator" jdbcType="VARCHAR" property="updator"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , "name", identity_no, telephone, sojourn_history, is_fever, is_cough,
    is_headache, is_diarrhea, is_normal, is_other, other_symptoms,
    purchasing_purpose, event_id, pharmacy_id, sales_person_id, creator, create_datetime, 
    updator, update_datetime, delete_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdc_drug_purchaser_info"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdc_drug_purchaser_info"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcDrugPurchaserInfo">
        insert into "tb_cdc_drug_purchaser_info" (id, "name", identity_no,
                                                  telephone, sojourn_history, is_fever,
                                                  is_cough, is_headache, is_diarrhea,
                                                  is_normal, is_other, other_symptoms,
                                                  purchasing_purpose, event_id, pharmacy_id,
                                                  sales_person_id, creator, create_datetime,
                                                  updator, update_datetime, delete_flag)
        values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{identityNo,jdbcType=VARCHAR},
                #{telephone,jdbcType=VARCHAR}, #{sojournHistory,jdbcType=VARCHAR}, #{isFever,jdbcType=VARCHAR},
                #{isCough,jdbcType=VARCHAR}, #{isHeadache,jdbcType=VARCHAR}, #{isDiarrhea,jdbcType=VARCHAR},
                #{isNormal,jdbcType=VARCHAR}, #{isOther,jdbcType=VARCHAR}, #{otherSymptoms,jdbcType=VARCHAR},
                #{purchasingPurpose,jdbcType=VARCHAR}, #{eventId,jdbcType=VARCHAR}, #{pharmacyId,jdbcType=VARCHAR},
                #{salesPersonId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createDatetime,jdbcType=TIMESTAMP},
                #{updator,jdbcType=VARCHAR}, #{updateDatetime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcDrugPurchaserInfo">
        insert into "tb_cdc_drug_purchaser_info"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                "name",
            </if>
            <if test="identityNo != null">
                identity_no,
            </if>
            <if test="telephone != null">
                telephone,
            </if>
            <if test="sojournHistory != null">
                sojourn_history,
            </if>
            <if test="isFever != null">
                is_fever,
            </if>
            <if test="isCough != null">
                is_cough,
            </if>
            <if test="isHeadache != null">
                is_headache,
            </if>
            <if test="isDiarrhea != null">
                is_diarrhea,
            </if>
            <if test="isNormal != null">
                is_normal,
            </if>
            <if test="isOther != null">
                is_other,
            </if>
            <if test="otherSymptoms != null">
                other_symptoms,
            </if>
            <if test="purchasingPurpose != null">
                purchasing_purpose,
            </if>
            <if test="eventId != null">
                event_id,
            </if>
            <if test="pharmacyId != null">
                pharmacy_id,
            </if>
            <if test="salesPersonId != null">
                sales_person_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
            <if test="updator != null">
                updator,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="identityNo != null">
                #{identityNo,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="sojournHistory != null">
                #{sojournHistory,jdbcType=VARCHAR},
            </if>
            <if test="isFever != null">
                #{isFever,jdbcType=VARCHAR},
            </if>
            <if test="isCough != null">
                #{isCough,jdbcType=VARCHAR},
            </if>
            <if test="isHeadache != null">
                #{isHeadache,jdbcType=VARCHAR},
            </if>
            <if test="isDiarrhea != null">
                #{isDiarrhea,jdbcType=VARCHAR},
            </if>
            <if test="isNormal != null">
                #{isNormal,jdbcType=VARCHAR},
            </if>
            <if test="symptomOther != null">
                #{isOther,jdbcType=VARCHAR},
            </if>
            <if test="otherSymptoms != null">
                #{otherSymptoms,jdbcType=VARCHAR},
            </if>
            <if test="purchasingPurpose != null">
                #{purchasingPurpose,jdbcType=VARCHAR},
            </if>
            <if test="eventId != null">
                #{eventId,jdbcType=VARCHAR},
            </if>
            <if test="pharmacyId != null">
                #{pharmacyId,jdbcType=VARCHAR},
            </if>
            <if test="salesPersonId != null">
                #{salesPersonId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                #{updator,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcDrugPurchaserInfo">
        update "tb_cdc_drug_purchaser_info"
        <set>
            <if test="name != null">
                "name" = #{name,jdbcType=VARCHAR},
            </if>
            <if test="identityNo != null">
                identity_no = #{identityNo,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                telephone = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="sojournHistory != null">
                sojourn_history = #{sojournHistory,jdbcType=VARCHAR},
            </if>
            <if test="isFever != null">
                is_fever = #{isFever,jdbcType=VARCHAR},
            </if>
            <if test="isCough != null">
                is_cough = #{isCough,jdbcType=VARCHAR},
            </if>
            <if test="isHeadache != null">
                is_headache = #{isHeadache,jdbcType=VARCHAR},
            </if>
            <if test="isDiarrhea != null">
                is_diarrhea = #{isDiarrhea,jdbcType=VARCHAR},
            </if>
            <if test="isNormal != null">
                is_normal = #{isNormal,jdbcType=VARCHAR},
            </if>
            <if test="isOther != null">
                is_other = #{isOther,jdbcType=VARCHAR},
            </if>
            <if test="otherSymptoms != null">
                other_symptoms = #{otherSymptoms,jdbcType=VARCHAR},
            </if>
            <if test="purchasingPurpose != null">
                purchasing_purpose = #{purchasingPurpose,jdbcType=VARCHAR},
            </if>
            <if test="eventId != null">
                event_id = #{eventId,jdbcType=VARCHAR},
            </if>
            <if test="pharmacyId != null">
                pharmacy_id = #{pharmacyId,jdbcType=VARCHAR},
            </if>
            <if test="salesPersonId != null">
                sales_person_id = #{salesPersonId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                updator = #{updator,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcDrugPurchaserInfo">
        update "tb_cdc_drug_purchaser_info"
        set "name"             = #{name,jdbcType=VARCHAR},
            identity_no        = #{identityNo,jdbcType=VARCHAR},
            telephone          = #{telephone,jdbcType=VARCHAR},
            sojourn_history    = #{sojournHistory,jdbcType=VARCHAR},
            is_fever           = #{isFever,jdbcType=VARCHAR},
            is_cough           = #{isCough,jdbcType=VARCHAR},
            is_headache        = #{isHeadache,jdbcType=VARCHAR},
            is_diarrhea        = #{isDiarrhea,jdbcType=VARCHAR},
            is_normal          = #{isNormal,jdbcType=VARCHAR},
            is_other           = #{isOther,jdbcType=VARCHAR},
            other_symptoms     = #{otherSymptoms,jdbcType=VARCHAR},
            purchasing_purpose = #{purchasingPurpose,jdbcType=VARCHAR},
            event_id           = #{eventId,jdbcType=VARCHAR},
            pharmacy_id        = #{pharmacyId,jdbcType=VARCHAR},
            sales_person_id    = #{salesPersonId,jdbcType=VARCHAR},
            creator            = #{creator,jdbcType=VARCHAR},
            create_datetime    = #{createDatetime,jdbcType=TIMESTAMP},
            updator            = #{updator,jdbcType=VARCHAR},
            update_datetime    = #{updateDatetime,jdbcType=TIMESTAMP},
            delete_flag        = #{deleteFlag,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="listStatResultVO" resultType="com.iflytek.fpva.cdc.multichannel.mr.model.vo.PharmacyStatResultVO">
        select b.create_datetime as statDate,
        a.province_code as provinceCode,
        a.province_name as provinceName,
        a.city_code as cityCode,
        a.city_name as cityName,
        a.district_code as districtCode,
        a.district_name as districtName,
        a.id as pharmacyId,
        a.area_name as pharmacyName,
        b.id as bid,
        b.is_fever as feverFlag,
        b.is_cough as coughFlag,
        b.is_headache as headacheFlag,
        b.is_diarrhea as diarrheaFlag,
        b.is_normal as normalFlag,
        b.is_other as otherFlag,
        b.purchasing_purpose as purchasingPurpose,
        COALESCE(c.amount, '0') as amount,
        COALESCE(c.is_antiviral, '0') as antiviralFlag,
        COALESCE(c.is_antibiotics, '0') as antibioticsFlag,
        COALESCE(c.is_antipyretic, '0') as antipyreticFlag,
        COALESCE(c.is_antidiarrheal, '0') as antidiarrhealFlag,
        COALESCE(c.is_relieve_cough, '0') as relieveCoughFlag
        from tb_cdc_drug_purchaser_info b
        left join tb_cdcmr_pharmacy_info a
        on b.pharmacy_id = a.id
        left join tb_cdc_medicine_equipment_info c
        on b.id = c.drug_purchaser_id
        where b.create_datetime::date &gt;= #{startDate}
        and b.create_datetime::date &lt;= #{endDate}
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and a.province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="("
                     separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and a.city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="("
                     separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and a.district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="("
                     separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pharmacyId != null and pharmacyId != ''">
            and a.id = #{pharmacyId, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="listResultList" resultType="com.iflytek.fpva.cdc.multichannel.mr.model.vo.PharmacyResultVO">
        select a.create_datetime                 as purchaseDate,
               a.event_id                        as eventId,
               a.purchasing_purpose              as purchasingPurpose,
               a.name                            as name,
               a.is_fever                        as feverFlag,
               a.is_cough                        as coughFlag,
               a.is_headache                     as headacheFlag,
               a.is_diarrhea                     as diarrheaFlag,
               a.is_normal                       as normalFlag,
               a.is_other                        as otherFlag,
               a.other_symptoms                  as otherSymptoms,
               COALESCE(b.is_antiviral, '0')     as antiviralFlag,
               COALESCE(b.is_antibiotics, '0')   as antibioticsFlag,
               COALESCE(b.is_antipyretic, '0')   as antipyreticFlag,
               COALESCE(b.is_antidiarrheal, '0') as antidiarrhealFlag,
               COALESCE(b.is_relieve_cough, '0') as relieveCoughFlag,
               COALESCE(b.name, '')              as drugName,
               COALESCE(b.amount, '0')           as drugAmount,
               c.city_code                       as cityCode,
               c.city_name                       as cityName,
               c.district_code                   as districtCode,
               c.district_name                   as districtName,
               c.id                              as pharmacyId,
               c.area_name                       as pharmacyName
        from tb_cdc_drug_purchaser_info a
                 left join tb_cdc_medicine_equipment_info b
                           on a.id = b.drug_purchaser_id
                 left join tb_cdcmr_pharmacy_info c
                           on a.pharmacy_id = c.id
        where a.create_datetime::date &gt;= #{startDate}
          and a.create_datetime:: date &lt;= #{endDate}
          and a.pharmacy_id = #{pharmacyId, jdbcType=VARCHAR}
    </select>

    <select id="selectByCondition" resultType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcDrugPurchaserInfo">
        select
        t1.*
        from "tb_cdc_drug_purchaser_info" as t1
        left join "tb_cdcmr_pharmacy_info" as t2
        on t1.pharmacy_id = t2.id
        where t1.delete_flag = '0'
        <if test="startDate!= null">
            and t1.create_datetime &gt;= #{startDate}
        </if>
        <if test="endDate!= null">
            and t1.create_datetime &lt; #{endDate}::timestamp + '1 day'
        </if>
        <if test="provinceCodeList!= null and provinceCodeList.size() &gt; 0">
            and t2.province_code in
            <foreach collection="provinceCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCodeList!= null and cityCodeList.size() &gt; 0">
            and t2.city_code in
            <foreach collection="cityCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCodeList!= null and districtCodeList.size() &gt; 0">
            and t2.district_code in
            <foreach collection="districtCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='pharmacyId!= null and !"".equals(pharmacyId) '>
            and t1.pharmacy_Id = #{pharmacyId}
        </if>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.mr.mapper.TbCdcmrPharmacyInfoMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrPharmacyInfo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="area_address" jdbcType="VARCHAR" property="areaAddress"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <result column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <result column="town_code" jdbcType="VARCHAR" property="townCode"/>
        <result column="town_name" jdbcType="VARCHAR" property="townName"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="coordinates_type" jdbcType="VARCHAR" property="coordinatesType"/>
        <result column="check_code" jdbcType="VARCHAR" property="checkCode"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updator" jdbcType="VARCHAR" property="updator"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , area_name, area_address, province_code, province_name, city_code, city_name,
    district_code, district_name, town_code, town_name, longitude, latitude, coordinates_type, 
    check_code, creator, create_datetime, updator, update_datetime, delete_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_pharmacy_info"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_pharmacy_info"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrPharmacyInfo">
        insert into "tb_cdcmr_pharmacy_info" (id, area_name, area_address,
                                              province_code, province_name, city_code,
                                              city_name, district_code, district_name,
                                              town_code, town_name, longitude,
                                              latitude, coordinates_type, check_code,
                                              creator, create_datetime, updator,
                                              update_datetime, delete_flag)
        values (#{id,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR}, #{areaAddress,jdbcType=VARCHAR},
                #{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
                #{cityName,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, #{districtName,jdbcType=VARCHAR},
                #{townCode,jdbcType=VARCHAR}, #{townName,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR},
                #{latitude,jdbcType=VARCHAR}, #{coordinatesType,jdbcType=VARCHAR}, #{checkCode,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR}, #{createDatetime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR},
                #{updateDatetime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrPharmacyInfo">
        insert into "tb_cdcmr_pharmacy_info"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="areaName != null">
                area_name,
            </if>
            <if test="areaAddress != null">
                area_address,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="provinceName != null">
                province_name,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="districtCode != null">
                district_code,
            </if>
            <if test="districtName != null">
                district_name,
            </if>
            <if test="townCode != null">
                town_code,
            </if>
            <if test="townName != null">
                town_name,
            </if>
            <if test="longitude != null">
                longitude,
            </if>
            <if test="latitude != null">
                latitude,
            </if>
            <if test="coordinatesType != null">
                coordinates_type,
            </if>
            <if test="checkCode != null">
                check_code,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
            <if test="updator != null">
                updator,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="areaName != null">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="areaAddress != null">
                #{areaAddress,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null">
                #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="townCode != null">
                #{townCode,jdbcType=VARCHAR},
            </if>
            <if test="townName != null">
                #{townName,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="coordinatesType != null">
                #{coordinatesType,jdbcType=VARCHAR},
            </if>
            <if test="checkCode != null">
                #{checkCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                #{updator,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrPharmacyInfo">
        update "tb_cdcmr_pharmacy_info"
        <set>
            <if test="areaName != null">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="areaAddress != null">
                area_address = #{areaAddress,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                province_code = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                district_code = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null">
                district_name = #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="townCode != null">
                town_code = #{townCode,jdbcType=VARCHAR},
            </if>
            <if test="townName != null">
                town_name = #{townName,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                longitude = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null">
                latitude = #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="coordinatesType != null">
                coordinates_type = #{coordinatesType,jdbcType=VARCHAR},
            </if>
            <if test="checkCode != null">
                check_code = #{checkCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                updator = #{updator,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrPharmacyInfo">
        update "tb_cdcmr_pharmacy_info"
        set area_name        = #{areaName,jdbcType=VARCHAR},
            area_address     = #{areaAddress,jdbcType=VARCHAR},
            province_code    = #{provinceCode,jdbcType=VARCHAR},
            province_name    = #{provinceName,jdbcType=VARCHAR},
            city_code        = #{cityCode,jdbcType=VARCHAR},
            city_name        = #{cityName,jdbcType=VARCHAR},
            district_code    = #{districtCode,jdbcType=VARCHAR},
            district_name    = #{districtName,jdbcType=VARCHAR},
            town_code        = #{townCode,jdbcType=VARCHAR},
            town_name        = #{townName,jdbcType=VARCHAR},
            longitude        = #{longitude,jdbcType=VARCHAR},
            latitude         = #{latitude,jdbcType=VARCHAR},
            coordinates_type = #{coordinatesType,jdbcType=VARCHAR},
            check_code       = #{checkCode,jdbcType=VARCHAR},
            creator          = #{creator,jdbcType=VARCHAR},
            create_datetime  = #{createDatetime,jdbcType=TIMESTAMP},
            updator          = #{updator,jdbcType=VARCHAR},
            update_datetime  = #{updateDatetime,jdbcType=TIMESTAMP},
            delete_flag      = #{deleteFlag,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectByRegionCodeAndName" resultType="com.iflytek.fpva.cdc.multichannel.mr.model.vo.NameCodeVO">
        select
        area_name as "name",
        id as "code"
        from "tb_cdcmr_pharmacy_info"
        where delete_flag = '0'
        <if test="provinceCodeList!= null and provinceCodeList.size() &gt; 0">
            and province_code in
            <foreach collection="provinceCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCodeList!= null and cityCodeList.size() &gt; 0">
            and city_code in
            <foreach collection="cityCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCodeList!= null and districtCodeList.size() &gt; 0">
            and district_code in
            <foreach collection="districtCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pharmacyName!= null">
            and area_name like '%'||#{pharmacyName}||'%'
        </if>
        <if test="endDate!= null">
            and create_datetime &lt; #{endDate} ::timestamp + '1 day'
        </if>
        <if test="totalLimit!= null">
            limit #{totalLimit}
        </if>

    </select>

    <select id="selectCountByDate" resultType="integer">
        select
        count(0)
        from "tb_cdcmr_pharmacy_info"
        where delete_flag = '0'
        <if test="provinceCodeList!= null and provinceCodeList.size() &gt; 0">
            and province_code in
            <foreach collection="provinceCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCodeList!= null and cityCodeList.size() &gt; 0">
            and city_code in
            <foreach collection="cityCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCodeList!= null and districtCodeList.size() &gt; 0">
            and district_code in
            <foreach collection="districtCodeList" close=")" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test='pharmacyId!= null and !"".equals(pharmacyId) '>
            and id = #{pharmacyId}
        </if>
        <if test="endDate!= null">
            and create_datetime &lt; #{endDate} ::timestamp + '1 day'
        </if>
    </select>
</mapper>
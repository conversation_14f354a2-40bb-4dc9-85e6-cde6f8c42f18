<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.mr.mapper.TbCdcmrSalesPersonInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrSalesPersonInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="identity_no" jdbcType="VARCHAR" property="identityNo" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="town_code" jdbcType="VARCHAR" property="townCode" />
    <result column="town_name" jdbcType="VARCHAR" property="townName" />
    <result column="living_address" jdbcType="VARCHAR" property="livingAddress" />
    <result column="pharmacy_id" jdbcType="VARCHAR" property="pharmacyId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, "name", identity_no, telephone, province_code, province_name, city_code, city_name, 
    district_code, district_name, town_code, town_name, living_address, pharmacy_id, 
    creator, create_datetime, updator, update_datetime, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_sales_person_info"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "tb_cdcmr_sales_person_info"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrSalesPersonInfo">
    insert into "tb_cdcmr_sales_person_info" (id, "name", identity_no, 
      telephone, province_code, province_name, 
      city_code, city_name, district_code, 
      district_name, town_code, town_name, 
      living_address, pharmacy_id, creator, 
      create_datetime, updator, update_datetime, 
      delete_flag)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{identityNo,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, 
      #{districtName,jdbcType=VARCHAR}, #{townCode,jdbcType=VARCHAR}, #{townName,jdbcType=VARCHAR}, 
      #{livingAddress,jdbcType=VARCHAR}, #{pharmacyId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createDatetime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR}, #{updateDatetime,jdbcType=TIMESTAMP}, 
      #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrSalesPersonInfo">
    insert into "tb_cdcmr_sales_person_info"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="identityNo != null">
        identity_no,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="districtCode != null">
        district_code,
      </if>
      <if test="districtName != null">
        district_name,
      </if>
      <if test="townCode != null">
        town_code,
      </if>
      <if test="townName != null">
        town_name,
      </if>
      <if test="livingAddress != null">
        living_address,
      </if>
      <if test="pharmacyId != null">
        pharmacy_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createDatetime != null">
        create_datetime,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateDatetime != null">
        update_datetime,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null">
        #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="districtName != null">
        #{districtName,jdbcType=VARCHAR},
      </if>
      <if test="townCode != null">
        #{townCode,jdbcType=VARCHAR},
      </if>
      <if test="townName != null">
        #{townName,jdbcType=VARCHAR},
      </if>
      <if test="livingAddress != null">
        #{livingAddress,jdbcType=VARCHAR},
      </if>
      <if test="pharmacyId != null">
        #{pharmacyId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateDatetime != null">
        #{updateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrSalesPersonInfo">
    update "tb_cdcmr_sales_person_info"
    <set>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null">
        identity_no = #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        district_code = #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="districtName != null">
        district_name = #{districtName,jdbcType=VARCHAR},
      </if>
      <if test="townCode != null">
        town_code = #{townCode,jdbcType=VARCHAR},
      </if>
      <if test="townName != null">
        town_name = #{townName,jdbcType=VARCHAR},
      </if>
      <if test="livingAddress != null">
        living_address = #{livingAddress,jdbcType=VARCHAR},
      </if>
      <if test="pharmacyId != null">
        pharmacy_id = #{pharmacyId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateDatetime != null">
        update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.mr.entity.TbCdcmrSalesPersonInfo">
    update "tb_cdcmr_sales_person_info"
    set "name" = #{name,jdbcType=VARCHAR},
      identity_no = #{identityNo,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      district_code = #{districtCode,jdbcType=VARCHAR},
      district_name = #{districtName,jdbcType=VARCHAR},
      town_code = #{townCode,jdbcType=VARCHAR},
      town_name = #{townName,jdbcType=VARCHAR},
      living_address = #{livingAddress,jdbcType=VARCHAR},
      pharmacy_id = #{pharmacyId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=VARCHAR},
      update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
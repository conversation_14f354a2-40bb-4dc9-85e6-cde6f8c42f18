<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.sc.mapper.TbCdcewScMedicalMappingMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScMedicalMapping">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="clock_record_id" jdbcType="VARCHAR" property="clockRecordId"/>
        <result column="symptom_type" jdbcType="VARCHAR" property="symptomType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , clock_record_id, symptom_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_symp_medical_mapping
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="findByClockRecordIds" resultType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScMedicalMapping">
        select * from tb_cdcew_symp_medical_mapping
        where clock_record_id in
        <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from tb_cdcew_symp_medical_mapping
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScMedicalMapping">
        insert into tb_cdcew_symp_medical_mapping (id, clock_record_id, symptom_type)
        values (#{id,jdbcType=VARCHAR}, #{clockRecordId,jdbcType=VARCHAR}, #{symptomType,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScMedicalMapping">
        insert into tb_cdcew_symp_medical_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="clockRecordId != null">
                clock_record_id,
            </if>
            <if test="symptomType != null">
                symptom_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="clockRecordId != null">
                #{clockRecordId,jdbcType=VARCHAR},
            </if>
            <if test="symptomType != null">
                #{symptomType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScMedicalMapping">
        update tb_cdcew_symp_medical_mapping
        <set>
            <if test="clockRecordId != null">
                clock_record_id = #{clockRecordId,jdbcType=VARCHAR},
            </if>
            <if test="symptomType != null">
                symptom_type = #{symptomType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScMedicalMapping">
        update tb_cdcew_symp_medical_mapping
        set clock_record_id = #{clockRecordId,jdbcType=VARCHAR},
            symptom_type    = #{symptomType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.sc.mapper.TbCdcewScOrgInfoMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="school_name" jdbcType="VARCHAR" property="schoolName"/>
        <result column="school_level" jdbcType="INTEGER" property="schoolLevel"/>
        <result column="grade" jdbcType="VARCHAR" property="grade"/>
        <result column="classes" jdbcType="VARCHAR" property="classes"/>
        <result column="teacher_name" jdbcType="VARCHAR" property="teacherName"/>
        <result column="teacher_phone" jdbcType="VARCHAR" property="teacherPhone"/>
        <result column="orders" jdbcType="VARCHAR" property="orders"/>
        <result column="create_user_card_no" jdbcType="VARCHAR" property="createUserCardNo"/>
        <result column="is_delete" jdbcType="VARCHAR" property="isDelete"/>
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime"/>
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name " jdbcType="VARCHAR" property="cityName"/>
        <result column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <result column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <result column="room" jdbcType="VARCHAR" property="room"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , create_time, school_id, school_name, school_level, grade, classes, teacher_name,
    teacher_phone, orders, create_user_card_no, is_delete, delete_time, delete_user_id, 
    province_code, province_name, city_code, city_name, district_code, district_name,
    room
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_sc_org_info
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from tb_cdcew_sc_org_info
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        insert into tb_cdcew_sc_org_info (id, create_time, school_id,
                                          school_name, school_level, grade,
                                          classes, teacher_name, teacher_phone,
                                          orders, create_user_card_no, is_delete,
                                          delete_time, delete_user_id, province_code,
                                          province_name, city_code, city_name,
                                          district_code, district_name, room)
        values (#{id,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{schoolId,jdbcType=VARCHAR},
                #{schoolName,jdbcType=VARCHAR}, #{schoolLevel,jdbcType=INTEGER}, #{grade,jdbcType=VARCHAR},
                #{classes,jdbcType=VARCHAR}, #{teacherName,jdbcType=VARCHAR}, #{teacherPhone,jdbcType=VARCHAR},
                #{orders,jdbcType=VARCHAR}, #{createUserCardNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=VARCHAR},
                #{deleteTime,jdbcType=TIMESTAMP}, #{deleteUserId,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR},
                #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR},
                #{districtCode,jdbcType=VARCHAR}, #{districtName,jdbcType=VARCHAR}, #{room,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        insert into tb_cdcew_sc_org_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="schoolId != null">
                school_id,
            </if>
            <if test="schoolName != null">
                school_name,
            </if>
            <if test="schoolLevel != null">
                school_level,
            </if>
            <if test="grade != null">
                grade,
            </if>
            <if test="classes != null">
                classes,
            </if>
            <if test="teacherName != null">
                teacher_name,
            </if>
            <if test="teacherPhone != null">
                teacher_phone,
            </if>
            <if test="orders != null">
                orders,
            </if>
            <if test="createUserCardNo != null">
                create_user_card_no,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="deleteTime != null">
                delete_time,
            </if>
            <if test="deleteUserId != null">
                delete_user_id,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="provinceName != null">
                province_name,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="districtCode != null">
                district_code,
            </if>
            <if test="districtName != null">
                district_name,
            </if>
            <if test="room != null">
                room,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="schoolId != null">
                #{schoolId,jdbcType=VARCHAR},
            </if>
            <if test="schoolName != null">
                #{schoolName,jdbcType=VARCHAR},
            </if>
            <if test="schoolLevel != null">
                #{schoolLevel,jdbcType=INTEGER},
            </if>
            <if test="grade != null">
                #{grade,jdbcType=VARCHAR},
            </if>
            <if test="classes != null">
                #{classes,jdbcType=VARCHAR},
            </if>
            <if test="teacherName != null">
                #{teacherName,jdbcType=VARCHAR},
            </if>
            <if test="teacherPhone != null">
                #{teacherPhone,jdbcType=VARCHAR},
            </if>
            <if test="orders != null">
                #{orders,jdbcType=VARCHAR},
            </if>
            <if test="createUserCardNo != null">
                #{createUserCardNo,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="deleteTime != null">
                #{deleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteUserId != null">
                #{deleteUserId,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null">
                #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="room != null">
                #{room,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        update tb_cdcew_sc_org_info
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="schoolId != null">
                school_id = #{schoolId,jdbcType=VARCHAR},
            </if>
            <if test="schoolName != null">
                school_name = #{schoolName,jdbcType=VARCHAR},
            </if>
            <if test="schoolLevel != null">
                school_level = #{schoolLevel,jdbcType=INTEGER},
            </if>
            <if test="grade != null">
                grade = #{grade,jdbcType=VARCHAR},
            </if>
            <if test="classes != null">
                classes = #{classes,jdbcType=VARCHAR},
            </if>
            <if test="teacherName != null">
                teacher_name = #{teacherName,jdbcType=VARCHAR},
            </if>
            <if test="teacherPhone != null">
                teacher_phone = #{teacherPhone,jdbcType=VARCHAR},
            </if>
            <if test="orders != null">
                orders = #{orders,jdbcType=VARCHAR},
            </if>
            <if test="createUserCardNo != null">
                create_user_card_no = #{createUserCardNo,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="deleteTime != null">
                delete_time = #{deleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteUserId != null">
                delete_user_id = #{deleteUserId,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                province_code = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                district_code = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null">
                district_name = #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="room != null">
                room = #{room,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        update tb_cdcew_sc_org_info
        set create_time         = #{createTime,jdbcType=TIMESTAMP},
            school_id           = #{schoolId,jdbcType=VARCHAR},
            school_name         = #{schoolName,jdbcType=VARCHAR},
            school_level        = #{schoolLevel,jdbcType=INTEGER},
            grade               = #{grade,jdbcType=VARCHAR},
            classes             = #{classes,jdbcType=VARCHAR},
            teacher_name        = #{teacherName,jdbcType=VARCHAR},
            teacher_phone       = #{teacherPhone,jdbcType=VARCHAR},
            orders              = #{orders,jdbcType=VARCHAR},
            create_user_card_no = #{createUserCardNo,jdbcType=VARCHAR},
            is_delete           = #{isDelete,jdbcType=VARCHAR},
            delete_time         = #{deleteTime,jdbcType=TIMESTAMP},
            delete_user_id      = #{deleteUserId,jdbcType=VARCHAR},
            province_code       = #{provinceCode,jdbcType=VARCHAR},
            province_name       = #{provinceName,jdbcType=VARCHAR},
            city_code           = #{cityCode,jdbcType=VARCHAR},
            city_name           = #{cityName,jdbcType=VARCHAR},
            district_code       = #{districtCode,jdbcType=VARCHAR},
            district_name       = #{districtName,jdbcType=VARCHAR},
            room                = #{room,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getSchoolList" parameterType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_sc_org_info
        where 1 = 1
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolLevel != null">
            and school_level = #{schoolLevel,jdbcType=INTEGER}
        </if>
        <if test="schoolId != null and schoolId != ''">
            and school_id = #{schoolId,jdbcType=VARCHAR}
        </if>
        <if test="schoolName != null and schoolName != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and school_name like concat('%', #{schoolName,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and school_name = #{schoolName,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="className != null and className != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and classes like concat('%', #{className,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and classes = #{className,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="internalStartDate != null">
            and  CAST(create_time AS DATE) <![CDATA[>=]]> #{internalStartDate}
        </if>
        <if test="internalEndDate != null">
            and  CAST(create_time AS DATE) <![CDATA[<=]]> #{internalEndDate}
        </if>
        order by school_name
        <if test="maxSize != null">
           limit #{maxSize,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getSchoolName" parameterType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO" resultType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.NameCodeVO">
        select distinct school_name as name, school_id as code
        from tb_cdcew_sc_org_info
        where 1 = 1
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolLevel != null">
            and school_level = #{schoolLevel,jdbcType=INTEGER}
        </if>
        <if test="schoolId != null and schoolId != ''">
            and school_id = #{schoolId,jdbcType=VARCHAR}
        </if>
        <if test="schoolName != null and schoolName != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and school_name like concat('%', #{schoolName,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and school_name = #{schoolName,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="className != null and className != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and classes like concat('%', #{className,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and classes = #{className,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="internalStartDate != null">
            and CAST(create_time AS DATE) <![CDATA[>=]]> #{internalStartDate}
        </if>
        <if test="internalEndDate != null">
            and CAST(create_time AS DATE) <![CDATA[<=]]> #{internalEndDate}
        </if>
        order by school_name
        <if test="maxSize != null">
            limit #{maxSize,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getOrgListByDistrictCode" resultType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        select * from tb_cdcew_sc_org_info
        where district_code = #{districtCode}
    </select>
    <select id="getOrgListByDistrictCodeAndSchoolId"
            resultType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        select * from tb_cdcew_sc_org_info
        where district_code = #{districtCode}
        and school_id = #{schoolId}
        order by grade,classes
    </select>
    <select id="getOrgBySchoolId" resultType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScOrgInfo">
        select * from tb_cdcew_sc_org_info
        where school_id = #{schoolId}
        limit 1
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.sc.mapper.TbCdcewScUserMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScUser">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="phone_no" jdbcType="VARCHAR" property="phoneNo"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="role_code" jdbcType="VARCHAR" property="roleCode"/>
        <result column="gl_role_code" jdbcType="VARCHAR" property="glRoleCode"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_delete" jdbcType="VARCHAR" property="isDelete"/>
        <result column="student_type" jdbcType="VARCHAR" property="studentType"/>
        <result column="is_has_detail" jdbcType="VARCHAR" property="isHasDetail"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="student_no" jdbcType="VARCHAR" property="studentNo"/>
        <result column="sex_code" jdbcType="VARCHAR" property="sexCode"/>
        <result column="sex_name" jdbcType="VARCHAR" property="sexName"/>
        <result column="age" jdbcType="VARCHAR" property="age"/>
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime"/>
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId"/>
        <result column="global_user_id" jdbcType="VARCHAR" property="globalUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_name, card_type, card_no, phone_no, org_id, role_code, gl_role_code, create_time,
    create_user_id, update_time, update_user_id, is_delete, student_type, is_has_detail, 
    school_id, student_no, sex_code, sex_name, age, delete_time, delete_user_id, global_user_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_sc_user
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from tb_cdcew_sc_user
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScUser">
        insert into tb_cdcew_sc_user (id, user_name, card_type,
                                      card_no, phone_no, org_id,
                                      role_code, gl_role_code, create_time,
                                      create_user_id, update_time, update_user_id,
                                      is_delete, student_type, is_has_detail,
                                      school_id, student_no, sex_code,
                                      sex_name, age, delete_time,
                                      delete_user_id, global_user_id)
        values (#{id,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{cardType,jdbcType=VARCHAR},
                #{cardNo,jdbcType=VARCHAR}, #{phoneNo,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR},
                #{roleCode,jdbcType=VARCHAR}, #{glRoleCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=VARCHAR},
                #{isDelete,jdbcType=VARCHAR}, #{studentType,jdbcType=VARCHAR}, #{isHasDetail,jdbcType=VARCHAR},
                #{schoolId,jdbcType=VARCHAR}, #{studentNo,jdbcType=VARCHAR}, #{sexCode,jdbcType=VARCHAR},
                #{sexName,jdbcType=VARCHAR}, #{age,jdbcType=VARCHAR}, #{deleteTime,jdbcType=TIMESTAMP},
                #{deleteUserId,jdbcType=VARCHAR}, #{globalUserId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScUser">
        insert into tb_cdcew_sc_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="cardNo != null">
                card_no,
            </if>
            <if test="phoneNo != null">
                phone_no,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="roleCode != null">
                role_code,
            </if>
            <if test="glRoleCode != null">
                gl_role_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="studentType != null">
                student_type,
            </if>
            <if test="isHasDetail != null">
                is_has_detail,
            </if>
            <if test="schoolId != null">
                school_id,
            </if>
            <if test="studentNo != null">
                student_no,
            </if>
            <if test="sexCode != null">
                sex_code,
            </if>
            <if test="sexName != null">
                sex_name,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="deleteTime != null">
                delete_time,
            </if>
            <if test="deleteUserId != null">
                delete_user_id,
            </if>
            <if test="globalUserId != null">
                global_user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null">
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="phoneNo != null">
                #{phoneNo,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="roleCode != null">
                #{roleCode,jdbcType=VARCHAR},
            </if>
            <if test="glRoleCode != null">
                #{glRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                #{updateUserId,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="studentType != null">
                #{studentType,jdbcType=VARCHAR},
            </if>
            <if test="isHasDetail != null">
                #{isHasDetail,jdbcType=VARCHAR},
            </if>
            <if test="schoolId != null">
                #{schoolId,jdbcType=VARCHAR},
            </if>
            <if test="studentNo != null">
                #{studentNo,jdbcType=VARCHAR},
            </if>
            <if test="sexCode != null">
                #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="sexName != null">
                #{sexName,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                #{age,jdbcType=VARCHAR},
            </if>
            <if test="deleteTime != null">
                #{deleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteUserId != null">
                #{deleteUserId,jdbcType=VARCHAR},
            </if>
            <if test="globalUserId != null">
                #{globalUserId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScUser">
        update tb_cdcew_sc_user
        <set>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null">
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="phoneNo != null">
                phone_no = #{phoneNo,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="roleCode != null">
                role_code = #{roleCode,jdbcType=VARCHAR},
            </if>
            <if test="glRoleCode != null">
                gl_role_code = #{glRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="studentType != null">
                student_type = #{studentType,jdbcType=VARCHAR},
            </if>
            <if test="isHasDetail != null">
                is_has_detail = #{isHasDetail,jdbcType=VARCHAR},
            </if>
            <if test="schoolId != null">
                school_id = #{schoolId,jdbcType=VARCHAR},
            </if>
            <if test="studentNo != null">
                student_no = #{studentNo,jdbcType=VARCHAR},
            </if>
            <if test="sexCode != null">
                sex_code = #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="sexName != null">
                sex_name = #{sexName,jdbcType=VARCHAR},
            </if>
            <if test="age != null">
                age = #{age,jdbcType=VARCHAR},
            </if>
            <if test="deleteTime != null">
                delete_time = #{deleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteUserId != null">
                delete_user_id = #{deleteUserId,jdbcType=VARCHAR},
            </if>
            <if test="globalUserId != null">
                global_user_id = #{globalUserId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScUser">
        update tb_cdcew_sc_user
        set user_name      = #{userName,jdbcType=VARCHAR},
            card_type      = #{cardType,jdbcType=VARCHAR},
            card_no        = #{cardNo,jdbcType=VARCHAR},
            phone_no       = #{phoneNo,jdbcType=VARCHAR},
            org_id         = #{orgId,jdbcType=VARCHAR},
            role_code      = #{roleCode,jdbcType=VARCHAR},
            gl_role_code   = #{glRoleCode,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            create_user_id = #{createUserId,jdbcType=VARCHAR},
            update_time    = #{updateTime,jdbcType=TIMESTAMP},
            update_user_id = #{updateUserId,jdbcType=VARCHAR},
            is_delete      = #{isDelete,jdbcType=VARCHAR},
            student_type   = #{studentType,jdbcType=VARCHAR},
            is_has_detail  = #{isHasDetail,jdbcType=VARCHAR},
            school_id      = #{schoolId,jdbcType=VARCHAR},
            student_no     = #{studentNo,jdbcType=VARCHAR},
            sex_code       = #{sexCode,jdbcType=VARCHAR},
            sex_name       = #{sexName,jdbcType=VARCHAR},
            age            = #{age,jdbcType=VARCHAR},
            delete_time    = #{deleteTime,jdbcType=TIMESTAMP},
            delete_user_id = #{deleteUserId,jdbcType=VARCHAR},
            global_user_id = #{globalUserId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectUserCountByOrgInfo" parameterType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO"
            resultType="java.lang.Integer">
        select count(1)
        from tb_cdcew_sc_user tcsu
        join tb_cdcew_sc_org_info tcsoi on tcsu.org_id = tcsoi.id
        where 1 = 1
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and tcsoi.province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and tcsoi.city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and tcsoi.district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolLevel != null">
            and tcsoi.school_level = #{schoolLevel,jdbcType=INTEGER}
        </if>
        <if test="schoolId != null and schoolId != ''">
            and tcsoi.school_id = #{schoolId,jdbcType=VARCHAR}
        </if>
        <if test="schoolName != null and schoolName != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.school_name like concat('%', #{schoolName,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.school_name = #{schoolName,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="className != null and className != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.classes like concat('%', #{className,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.classes = #{className,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="internalStartDate != null">
            and CAST(tcsoi.create_time AS DATE) <![CDATA[>=]]> #{internalStartDate}
        </if>
        <if test="internalEndDate != null">
            and CAST(tcsoi.create_time AS DATE) <![CDATA[<=]]> #{internalEndDate}
        </if>
    </select>

    <select id="getTotalUserCount" parameterType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO"
            resultType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.RecordDateCountVO">
        SELECT tcsoi.city_code as cityCode,
        tcsoi.district_code as districtCode,
        tcsu.school_id as schoolId,
        tcsoi.id as orgId,
        sc.day_short_desc as recordDate ,
        count(0) as totalCount
        from sys_calendar sc
        LEFT JOIN tb_cdcew_sc_user tcsu on tcsu.create_time <![CDATA[<=]]> sc.day_short_desc
        LEFT JOIN tb_cdcew_sc_org_info tcsoi on tcsu.org_id = tcsoi.id
        where 1 = 1
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and tcsoi.province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and tcsoi.city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and tcsoi.district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolLevel != null">
            and tcsoi.school_level = #{schoolLevel,jdbcType=INTEGER}
        </if>
        <if test="schoolId != null and schoolId != ''">
            and tcsoi.school_id = #{schoolId,jdbcType=VARCHAR}
        </if>
        <if test="schoolName != null and schoolName != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.school_name like concat('%', #{schoolName,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.school_name = #{schoolName,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="className != null and className != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.classes like concat('%', #{className,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.classes = #{className,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        and sc.day_short_desc BETWEEN #{internalStartDate} and #{internalEndDate}
        GROUP BY sc.day_short_desc,
        tcsoi.city_code,
        tcsoi.district_code,
        tcsu.school_id ,
        tcsoi.id
    </select>
</mapper>
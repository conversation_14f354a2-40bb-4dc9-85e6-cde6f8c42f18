<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.fpva.cdc.multichannel.sc.mapper.TbCdcewScClockRecordMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScClockRecord">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="is_health" jdbcType="VARCHAR" property="isHealth"/>
        <result column="health_status" jdbcType="VARCHAR" property="healthStatus"/>
        <result column="is_touch_virus" jdbcType="VARCHAR" property="isTouchVirus"/>
        <result column="is_touch_area" jdbcType="VARCHAR" property="isTouchArea"/>
        <result column="is_touch_country" jdbcType="VARCHAR" property="isTouchCountry"/>
        <result column="clock_date" jdbcType="DATE" property="clockDate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="record_type" jdbcType="VARCHAR" property="recordType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="create_user_card_no" jdbcType="VARCHAR" property="createUserCardNo"/>
        <result column="is_absent" jdbcType="INTEGER" property="isAbsent"/>
        <result column="absent_reason" jdbcType="VARCHAR" property="absentReason"/>
        <result column="check_temp" jdbcType="VARCHAR" property="checkTemp"/>
        <result column="is_abn_temp" jdbcType="INTEGER" property="isAbnTemp"/>
        <result column="director" jdbcType="VARCHAR" property="director"/>
        <result column="diagnose" jdbcType="VARCHAR" property="diagnose"/>
        <result column="accident_date" jdbcType="DATE" property="accidentDate"/>
        <result column="outpatient_date" jdbcType="DATE" property="outpatientDate"/>
        <result column="outpatient_hospital" jdbcType="VARCHAR" property="outpatientHospital"/>
        <result column="absent_days" jdbcType="INTEGER" property="absentDays"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, is_health, health_status, is_touch_virus, is_touch_area, is_touch_country,
    clock_date, create_time, record_type, create_user_id, create_user_name, create_user_card_no,
    is_absent, absent_reason, check_temp, is_abn_temp, director, diagnose, accident_date,
    outpatient_date, outpatient_hospital, absent_days
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcew_sc_clock_record
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="findByQueryVO" resultType="com.iflytek.fpva.cdc.multichannel.sc.model.dto.SchoolClockRecordDto" fetchSize="2000">
        select
        c.id clockRecordId,
        c.clock_date,
        c.is_health,
        c.is_absent,
        c.record_type,
        c.absent_reason,
        c.diagnose,
        c.accident_date,
        c.outpatient_date,
        c.outpatient_hospital,
        o.id orgId,
        o.province_code,
        o.province_name,
        o.city_code,
        o.city_name,
        o.district_code,
        o.district_name,
        o.school_id,
        o.school_name,
        o.school_level,
        o.grade,
        o.classes,
        o.room,
        o.orders,
        u.id userId,
        u.user_name,
        u.sex_name,
        u.age,
        m.symptom_type,
        c.absent_days
        from tb_cdcew_sc_clock_record c
        left join tb_cdcew_sc_user u
        on c.user_id = u.id
        left join tb_cdcew_sc_org_info o
        on u.org_id = o.id
        left join tb_cdcew_symp_medical_mapping m
        on c.id = m.clock_record_id
        where 1=1
        <if test="schoolQueryVO.internalStartDate != null">
            and #{schoolQueryVO.internalStartDate} &lt;= c.clock_date
        </if>
        <if test="schoolQueryVO.internalEndDate != null">
            and c.clock_date &lt;= #{schoolQueryVO.internalEndDate}
        </if>
        <if test="schoolQueryVO.provinceCode != null and schoolQueryVO.provinceCode.size() &gt; 0">
            and o.province_code in
            <foreach close=")" collection="schoolQueryVO.provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.cityCode != null and schoolQueryVO.cityCode.size() &gt; 0">
            and o.city_code in
            <foreach close=")" collection="schoolQueryVO.cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.districtCode != null and schoolQueryVO.districtCode.size() &gt; 0">
            and o.district_code in
            <foreach close=")" collection="schoolQueryVO.districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.schoolLevel != null">
            and o.school_level = #{schoolQueryVO.schoolLevel}
        </if>
        <if test="schoolQueryVO.schoolId != null and schoolQueryVO.schoolId != ''">
            and o.school_id = #{schoolQueryVO.schoolId}
        </if>
        <if test="schoolQueryVO.className != null and schoolQueryVO.className != ''">
            and o.classes = #{schoolQueryVO.className}
        </if>
        <if test="schoolQueryVO.internalSearchDate != null">
            and c.clock_date = #{schoolQueryVO.internalSearchDate}
        </if>
        <if test="schoolQueryVO.isHealth != null and schoolQueryVO.isHealth != ''">
            and c.is_health = #{schoolQueryVO.isHealth}
        </if>
        order by
        <choose>
            <when test="schoolQueryVO.sortType != null and schoolQueryVO.sortType == 1">
                c.clock_date, o.room
            </when>
            <otherwise>
                o.school_id,o.orders,o.classes
            </otherwise>
        </choose>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from tb_cdcew_sc_clock_record
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScClockRecord">
        insert into tb_cdcew_sc_clock_record (id, user_id, is_health,
                                              health_status, is_touch_virus, is_touch_area,
                                              is_touch_country, clock_date, create_time,
                                              record_type, create_user_id, create_user_name,
                                              create_user_card_no, is_absent, absent_reason,
                                              check_temp, is_abn_temp, director,
                                              diagnose, accident_date, outpatient_date,
                                              outpatient_hospital, absent_days)
        values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{isHealth,jdbcType=VARCHAR},
                #{healthStatus,jdbcType=VARCHAR}, #{isTouchVirus,jdbcType=VARCHAR}, #{isTouchArea,jdbcType=VARCHAR},
                #{isTouchCountry,jdbcType=VARCHAR}, #{clockDate,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP},
                #{recordType,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createUserName,jdbcType=VARCHAR},
                #{createUserCardNo,jdbcType=VARCHAR}, #{isAbsent,jdbcType=INTEGER}, #{absentReason,jdbcType=VARCHAR},
                #{checkTemp,jdbcType=VARCHAR}, #{isAbnTemp,jdbcType=INTEGER}, #{director,jdbcType=VARCHAR},
                #{diagnose,jdbcType=VARCHAR}, #{accidentDate,jdbcType=DATE}, #{outpatientDate,jdbcType=DATE},
                #{outpatientHospital,jdbcType=VARCHAR}, #{absentDays,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScClockRecord">
        insert into tb_cdcew_sc_clock_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="isHealth != null">
                is_health,
            </if>
            <if test="healthStatus != null">
                health_status,
            </if>
            <if test="isTouchVirus != null">
                is_touch_virus,
            </if>
            <if test="isTouchArea != null">
                is_touch_area,
            </if>
            <if test="isTouchCountry != null">
                is_touch_country,
            </if>
            <if test="clockDate != null">
                clock_date,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="recordType != null">
                record_type,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="createUserCardNo != null">
                create_user_card_no,
            </if>
            <if test="isAbsent != null">
                is_absent,
            </if>
            <if test="absentReason != null">
                absent_reason,
            </if>
            <if test="checkTemp != null">
                check_temp,
            </if>
            <if test="isAbnTemp != null">
                is_abn_temp,
            </if>
            <if test="director != null">
                director,
            </if>
            <if test="diagnose != null">
                diagnose,
            </if>
            <if test="accidentDate != null">
                accident_date,
            </if>
            <if test="outpatientDate != null">
                outpatient_date,
            </if>
            <if test="outpatientHospital != null">
                outpatient_hospital,
            </if>
            <if test="absentDays != null">
                absent_days,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="isHealth != null">
                #{isHealth,jdbcType=VARCHAR},
            </if>
            <if test="healthStatus != null">
                #{healthStatus,jdbcType=VARCHAR},
            </if>
            <if test="isTouchVirus != null">
                #{isTouchVirus,jdbcType=VARCHAR},
            </if>
            <if test="isTouchArea != null">
                #{isTouchArea,jdbcType=VARCHAR},
            </if>
            <if test="isTouchCountry != null">
                #{isTouchCountry,jdbcType=VARCHAR},
            </if>
            <if test="clockDate != null">
                #{clockDate,jdbcType=DATE},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="recordType != null">
                #{recordType,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createUserCardNo != null">
                #{createUserCardNo,jdbcType=VARCHAR},
            </if>
            <if test="isAbsent != null">
                #{isAbsent,jdbcType=INTEGER},
            </if>
            <if test="absentReason != null">
                #{absentReason,jdbcType=VARCHAR},
            </if>
            <if test="checkTemp != null">
                #{checkTemp,jdbcType=VARCHAR},
            </if>
            <if test="isAbnTemp != null">
                #{isAbnTemp,jdbcType=INTEGER},
            </if>
            <if test="director != null">
                #{director,jdbcType=VARCHAR},
            </if>
            <if test="diagnose != null">
                #{diagnose,jdbcType=VARCHAR},
            </if>
            <if test="accidentDate != null">
                #{accidentDate,jdbcType=DATE},
            </if>
            <if test="outpatientDate != null">
                #{outpatientDate,jdbcType=DATE},
            </if>
            <if test="outpatientHospital != null">
                #{outpatientHospital,jdbcType=VARCHAR},
            </if>
            <if test="absentDays != null">
                #{absentDays,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScClockRecord">
        update tb_cdcew_sc_clock_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="isHealth != null">
                is_health = #{isHealth,jdbcType=VARCHAR},
            </if>
            <if test="healthStatus != null">
                health_status = #{healthStatus,jdbcType=VARCHAR},
            </if>
            <if test="isTouchVirus != null">
                is_touch_virus = #{isTouchVirus,jdbcType=VARCHAR},
            </if>
            <if test="isTouchArea != null">
                is_touch_area = #{isTouchArea,jdbcType=VARCHAR},
            </if>
            <if test="isTouchCountry != null">
                is_touch_country = #{isTouchCountry,jdbcType=VARCHAR},
            </if>
            <if test="clockDate != null">
                clock_date = #{clockDate,jdbcType=DATE},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="recordType != null">
                record_type = #{recordType,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="createUserCardNo != null">
                create_user_card_no = #{createUserCardNo,jdbcType=VARCHAR},
            </if>
            <if test="isAbsent != null">
                is_absent = #{isAbsent,jdbcType=INTEGER},
            </if>
            <if test="absentReason != null">
                absent_reason = #{absentReason,jdbcType=VARCHAR},
            </if>
            <if test="checkTemp != null">
                check_temp = #{checkTemp,jdbcType=VARCHAR},
            </if>
            <if test="isAbnTemp != null">
                is_abn_temp = #{isAbnTemp,jdbcType=INTEGER},
            </if>
            <if test="director != null">
                director = #{director,jdbcType=VARCHAR},
            </if>
            <if test="diagnose != null">
                diagnose = #{diagnose,jdbcType=VARCHAR},
            </if>
            <if test="accidentDate != null">
                accident_date = #{accidentDate,jdbcType=DATE},
            </if>
            <if test="outpatientDate != null">
                outpatient_date = #{outpatientDate,jdbcType=DATE},
            </if>
            <if test="outpatientHospital != null">
                outpatient_hospital = #{outpatientHospital,jdbcType=VARCHAR},
            </if>
            <if test="absentDays != null">
                absent_days = #{absentDays,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.fpva.cdc.multichannel.sc.entity.TbCdcewScClockRecord">
        update tb_cdcew_sc_clock_record
        set user_id             = #{userId,jdbcType=VARCHAR},
            is_health           = #{isHealth,jdbcType=VARCHAR},
            health_status       = #{healthStatus,jdbcType=VARCHAR},
            is_touch_virus      = #{isTouchVirus,jdbcType=VARCHAR},
            is_touch_area       = #{isTouchArea,jdbcType=VARCHAR},
            is_touch_country    = #{isTouchCountry,jdbcType=VARCHAR},
            clock_date          = #{clockDate,jdbcType=DATE},
            create_time         = #{createTime,jdbcType=TIMESTAMP},
            record_type         = #{recordType,jdbcType=VARCHAR},
            create_user_id      = #{createUserId,jdbcType=VARCHAR},
            create_user_name    = #{createUserName,jdbcType=VARCHAR},
            create_user_card_no = #{createUserCardNo,jdbcType=VARCHAR},
            is_absent           = #{isAbsent,jdbcType=INTEGER},
            absent_reason       = #{absentReason,jdbcType=VARCHAR},
            check_temp          = #{checkTemp,jdbcType=VARCHAR},
            is_abn_temp         = #{isAbnTemp,jdbcType=INTEGER},
            director            = #{director,jdbcType=VARCHAR},
            diagnose            = #{diagnose,jdbcType=VARCHAR},
            accident_date       = #{accidentDate,jdbcType=DATE},
            outpatient_date     = #{outpatientDate,jdbcType=DATE},
            outpatient_hospital = #{outpatientHospital,jdbcType=VARCHAR},
            absent_days         = #{absentDays,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectRecordUserCountByOrgInfo" parameterType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO"
            resultType="java.lang.Integer">
        select count(distinct tcsu.id)
        from tb_cdcew_sc_clock_record tcscr
        join tb_cdcew_sc_user tcsu on tcscr.user_id = tcsu.id
        join tb_cdcew_sc_org_info tcsoi on tcsu.org_id = tcsoi.id
        where 1 = 1
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and tcsoi.province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and tcsoi.city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and tcsoi.district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolLevel != null">
            and tcsoi.school_level = #{schoolLevel,jdbcType=INTEGER}
        </if>
        <if test="schoolId != null and schoolId != ''">
            and tcsoi.school_id = #{schoolId,jdbcType=VARCHAR}
        </if>
        <if test="schoolName != null and schoolName != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.school_name like concat('%', #{schoolName,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.school_name = #{schoolName,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="className != null and className != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.classes like concat('%', #{className,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.classes = #{className,jdbcType=VARCHAR}
                </otherwise>
            </choose>

        </if>
        <if test="internalStartDate != null">
            and tcscr.clock_date <![CDATA[>=]]> #{internalStartDate}
        </if>
        <if test="internalEndDate != null">
            and tcscr.clock_date <![CDATA[<=]]> #{internalEndDate}
        </if>
        <if test="isHealth != null and isHealth != ''">
            and tcscr.is_health = #{isHealth}
        </if>
    </select>

    <select id="selectClockRecordDetailInfo" resultType="com.iflytek.fpva.cdc.multichannel.sc.model.dto.SchoolClockRecordDto">
        select
        c.id clockRecordId,
        c.clock_date,
        c.is_health,
        c.is_absent,
        c.record_type,
        c.absent_reason,
        c.diagnose,
        c.accident_date,
        c.outpatient_date,
        c.outpatient_hospital,
        o.id orgId,
        o.province_code,
        o.province_name,
        o.city_code,
        o.city_name,
        o.district_code,
        o.district_name,
        o.school_id,
        o.school_name,
        o.school_level,
        o.grade,
        o.classes,
        o.room,
        u.id userId,
        u.user_name,
        u.sex_name,
        u.age,
        c.absent_days
        from tb_cdcew_sc_clock_record c
        left join tb_cdcew_sc_user u on c.user_id = u.id
        left join tb_cdcew_sc_org_info o on u.org_id = o.id
        where c.user_id is not null
        and c.user_id != ''
        <if test="schoolQueryVO.internalStartDate != null">
            and #{schoolQueryVO.internalStartDate} &lt;= c.clock_date
        </if>
        <if test="schoolQueryVO.internalEndDate != null">
            and c.clock_date &lt;= #{schoolQueryVO.internalEndDate}
        </if>
        <if test="schoolQueryVO.provinceCode != null and schoolQueryVO.provinceCode.size() &gt; 0">
            and o.province_code in
            <foreach close=")" collection="schoolQueryVO.provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.cityCode != null and schoolQueryVO.cityCode.size() &gt; 0">
            and o.city_code in
            <foreach close=")" collection="schoolQueryVO.cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.districtCode != null and schoolQueryVO.districtCode.size() &gt; 0">
            and o.district_code in
            <foreach close=")" collection="schoolQueryVO.districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.schoolLevel != null">
            and o.school_level = #{schoolQueryVO.schoolLevel}
        </if>
        <if test="schoolQueryVO.schoolId != null and schoolQueryVO.schoolId != ''">
            and o.school_id = #{schoolQueryVO.schoolId}
        </if>
        <if test="schoolQueryVO.grade != null and schoolQueryVO.grade != ''">
            and o.grade = #{schoolQueryVO.grade}
        </if>
        <if test="schoolQueryVO.className != null and schoolQueryVO.className != ''">
            and o.classes = #{schoolQueryVO.className}
        </if>
        <if test="schoolQueryVO.internalSearchDate != null">
            and c.clock_date = #{schoolQueryVO.internalSearchDate}
        </if>
        <if test="schoolQueryVO.isHealth != null and schoolQueryVO.isHealth != ''">
            and c.is_health = #{schoolQueryVO.isHealth}
        </if>
        order by
        <choose>
            <when test="schoolQueryVO.sortType != null and schoolQueryVO.sortType == 1">
                c.clock_date, o.room
            </when>
            <otherwise>
                o.school_id, o.grade, o.classes
            </otherwise>
        </choose>
    </select>
    <select id="findDateBySchoolId" resultType="java.util.Date">
        select distinct clock_date
        from tb_cdcew_sc_clock_record c
                 left join tb_cdcew_sc_user u on c.user_id = u.id
                 left join tb_cdcew_sc_org_info o on u.org_id = o.id
        where c.user_id is not null
          and c.user_id != ''
          and c.clock_date &lt;= #{endDate}
          and c.clock_date >= #{startDate}
          and o.school_id = #{schoolId}
    </select>
    <select id="findCountByQueryVO" resultType="com.iflytek.fpva.cdc.multichannel.sc.model.dto.SchoolClockRecordDto">
        select
        c.clock_date,
        c.is_absent,
        c.absent_reason,
        c.diagnose,
        o.province_code,
        o.province_name,
        o.city_code,
        o.city_name,
        o.district_code,
        o.district_name,
        o.school_id,
        o.school_name,
        o.school_level,
        m.symptom_type,
        count(1) count
        from tb_cdcew_sc_clock_record c
        left join tb_cdcew_sc_user u
        on c.user_id = u.id
        left join tb_cdcew_sc_org_info o
        on u.org_id = o.id
        left join tb_cdcew_symp_medical_mapping m
        on c.id = m.clock_record_id
        where 1=1
        <if test="schoolQueryVO.internalStartDate != null">
            and #{schoolQueryVO.internalStartDate} &lt;= c.clock_date
        </if>
        <if test="schoolQueryVO.internalEndDate != null">
            and c.clock_date &lt;= #{schoolQueryVO.internalEndDate}
        </if>
        <if test="schoolQueryVO.provinceCode != null and schoolQueryVO.provinceCode.size() &gt; 0">
            and o.province_code in
            <foreach close=")" collection="schoolQueryVO.provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.cityCode != null and schoolQueryVO.cityCode.size() &gt; 0">
            and o.city_code in
            <foreach close=")" collection="schoolQueryVO.cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.districtCode != null and schoolQueryVO.districtCode.size() &gt; 0">
            and o.district_code in
            <foreach close=")" collection="schoolQueryVO.districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.schoolLevel != null">
            and o.school_level = #{schoolQueryVO.schoolLevel}
        </if>
        <if test="schoolQueryVO.schoolId != null and schoolQueryVO.schoolId != ''">
            and o.school_id = #{schoolQueryVO.schoolId}
        </if>
        <if test="schoolQueryVO.internalSearchDate != null">
            and c.clock_date = #{schoolQueryVO.internalSearchDate}
        </if>
        <if test="schoolQueryVO.isHealth != null and schoolQueryVO.isHealth != ''">
            and c.is_health = #{schoolQueryVO.isHealth}
        </if>
        GROUP BY c.clock_date,
        c.is_absent,
        c.absent_reason,
        c.diagnose,
        o.province_code,
        o.province_name,
        o.city_code,
        o.city_name,
        o.district_code,
        o.district_name,
        o.school_id,
        o.school_name,
        o.school_level,
        m.symptom_type
    </select>
    <select id="findClassCountByQueryVO" resultType="com.iflytek.fpva.cdc.multichannel.sc.model.dto.SchoolClockRecordDto">
        select
        c.clock_date,
        c.is_absent,
        c.absent_reason,
        c.diagnose,
        o.province_code,
        o.province_name,
        o.city_code,
        o.city_name,
        o.district_code,
        o.district_name,
        o.grade,
        o.classes,
        o.orders,
        m.symptom_type,
        count(1) count
        from tb_cdcew_sc_clock_record c
        left join tb_cdcew_sc_user u
        on c.user_id = u.id
        left join tb_cdcew_sc_org_info o
        on u.org_id = o.id
        left join tb_cdcew_symp_medical_mapping m
        on c.id = m.clock_record_id
        where 1=1
        <if test="schoolQueryVO.internalStartDate != null">
            and #{schoolQueryVO.internalStartDate} &lt;= c.clock_date
        </if>
        <if test="schoolQueryVO.internalEndDate != null">
            and c.clock_date &lt;= #{schoolQueryVO.internalEndDate}
        </if>
        <if test="schoolQueryVO.provinceCode != null and schoolQueryVO.provinceCode.size() &gt; 0">
            and o.province_code in
            <foreach close=")" collection="schoolQueryVO.provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.cityCode != null and schoolQueryVO.cityCode.size() &gt; 0">
            and o.city_code in
            <foreach close=")" collection="schoolQueryVO.cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.districtCode != null and schoolQueryVO.districtCode.size() &gt; 0">
            and o.district_code in
            <foreach close=")" collection="schoolQueryVO.districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolQueryVO.schoolLevel != null">
            and o.school_level = #{schoolQueryVO.schoolLevel}
        </if>
        <if test="schoolQueryVO.schoolId != null and schoolQueryVO.schoolId != ''">
            and o.school_id = #{schoolQueryVO.schoolId}
        </if>
        <if test="schoolQueryVO.internalSearchDate != null">
            and c.clock_date = #{schoolQueryVO.internalSearchDate}
        </if>
        <if test="schoolQueryVO.isHealth != null and schoolQueryVO.isHealth != ''">
            and c.is_health = #{schoolQueryVO.isHealth}
        </if>
        GROUP BY c.clock_date,
        c.is_absent,
        c.absent_reason,
        c.diagnose,
        o.province_code,
        o.province_name,
        o.city_code,
        o.city_name,
        o.district_code,
        o.district_name,
        o.grade,
        o.classes,
        o.orders,
        m.symptom_type
    </select>

    <select id="getRecordUserCount" parameterType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.SchoolQueryVO"
            resultType="com.iflytek.fpva.cdc.multichannel.sc.model.vo.RecordDateCountVO">
        SELECT tcsoi.city_code as cityCode,
        tcsoi.district_code as districtCode,
        tcsoi.id as orgId,
        tcsu.school_id as schoolId,
        sc.day_short_desc as recordDate ,
        count(0) as totalCount
        FROM
        sys_calendar sc
        LEFT JOIN tb_cdcew_sc_clock_record tcscr ON tcscr.clock_date = sc.day_short_desc
        LEFT JOIN tb_cdcew_sc_user tcsu ON tcsu.ID = tcscr.user_id
        LEFT JOIN tb_cdcew_sc_org_info tcsoi ON tcsu.org_id = tcsoi.ID
        where 1 = 1
        <if test="provinceCode != null and provinceCode.size() &gt; 0">
            and tcsoi.province_code in
            <foreach close=")" collection="provinceCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode.size() &gt; 0">
            and tcsoi.city_code in
            <foreach close=")" collection="cityCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCode != null and districtCode.size() &gt; 0">
            and tcsoi.district_code in
            <foreach close=")" collection="districtCode" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="schoolLevel != null">
            and tcsoi.school_level = #{schoolLevel,jdbcType=INTEGER}
        </if>
        <if test="schoolId != null and schoolId != ''">
            and tcsoi.school_id = #{schoolId,jdbcType=VARCHAR}
        </if>
        <if test="schoolName != null and schoolName != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.school_name like concat('%', #{schoolName,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.school_name = #{schoolName,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="className != null and className != ''">
            <choose>
                <when test="fuzzyQuery != null and fuzzyQuery">
                    and tcsoi.classes like concat('%', #{className,jdbcType=VARCHAR}, '%')
                </when>
                <otherwise>
                    and tcsoi.classes = #{className,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        and sc.day_short_desc BETWEEN #{internalStartDate} and #{internalEndDate}
        GROUP BY
        sc.day_short_desc,
        tcsoi.city_code,
        tcsoi.district_code,
        tcsu.school_id,
        tcsoi.id
    </select>
</mapper>